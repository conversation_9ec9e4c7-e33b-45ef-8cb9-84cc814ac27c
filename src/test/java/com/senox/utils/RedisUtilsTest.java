package com.senox.utils;


import com.senox.common.utils.RedisUtils;
import com.senox.web.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2021/1/8 15:59
 */
public class RedisUtilsTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtilsTest.class);

    @Test
    public void test() {
        RedisUtils.set("abc1", "123");
        String result = RedisUtils.get("abc1");
        logger.info("result: {}", result);
        Assertions.assertEquals("123", result);
        RedisUtils.del("abc1");
        Assertions.assertFalse(RedisUtils.hasKey("abc1"));
    }

}