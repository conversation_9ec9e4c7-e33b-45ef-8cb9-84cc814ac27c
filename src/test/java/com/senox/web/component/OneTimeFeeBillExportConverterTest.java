package com.senox.web.component;


import com.senox.common.utils.JsonUtils;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.OneTimeFeeBillTradeVo;
import com.senox.web.BaseTest;
import com.senox.web.convert.OneTimeFeeBillExportConverter;
import com.senox.web.vo.OneTimeFeeBillExportVo;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/3/31 17:14
 */
public class OneTimeFeeBillExportConverterTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(OneTimeFeeBillExportConverterTest.class);

    @Test
    public void toV() {
        OneTimeFeeBillTradeVo bill = new OneTimeFeeBillTradeVo();
        bill.setFee(randStr(10));
        bill.setCustomer(randStr(5));
        bill.setAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(100L), 2));
        bill.setRefundAmount(BigDecimal.ZERO);
        bill.setRemark(randStr(20));
        bill.setRealtySerial(randStr(6));
        bill.setRealtyName(randStr(10));
        bill.setBillYear(2022);
        bill.setBillMonth(2);
        bill.setOperator(randStr(3));
        bill.setCreator(randStr(3));
        bill.setDepartment(randStr(5));
        bill.setTollSerial(randStr(5));
        bill.setTollTime(LocalDateTime.now());
        bill.setRefundTime(null);
        bill.setStatus(BillStatus.PAID.getStatus());

        OneTimeFeeBillExportVo exportItem = OneTimeFeeBillExportConverter.INSTANCE.toV(bill);
        logger.info("bill: {}", JsonUtils.object2Json(bill));
        logger.info("exportItem: {}", JsonUtils.object2Json(exportItem));
    }
}