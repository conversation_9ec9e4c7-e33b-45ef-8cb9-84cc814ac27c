package com.senox.web.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.user.vo.DiningInformationVo;
import com.senox.web.BaseTest;
import com.senox.web.vo.LogisticOrderExcelVo;
import com.senox.web.vo.RealtyWeExcelVo;
import com.senox.web.vo.RefrigerationCustomerExcelVo;
import com.senox.web.vo.RefrigerationDayBillExcelVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/24 8:59
 */
class EasyExcelTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(EasyExcelTest.class);


    @Test
    void loadWeData() {
        InputStream in = getClass().getClassLoader().getResourceAsStream("202302月水电度数导入.xls");
        List<RealtyWeExcelVo> resultList = new ArrayList<>(200);
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RealtyWeExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        Assertions.assertDoesNotThrow(() -> {
            EasyExcelFactory.read(in, RealtyWeExcelVo.class, readListener).sheet().doRead();
        });

        logger.info("resultList: {}", JsonUtils.object2Json(resultList));

        RealtyWeExcelVo data = resultList.stream().filter(x -> Objects.equals("*********", x.getRealtySerial())).findFirst().get();
        Assertions.assertNull(data.getLastWaterReadings());
        Assertions.assertNull(data.getWaterReadings());
        Assertions.assertEquals(1, data.getLastElectricReadings());
        Assertions.assertEquals(1, data.getElectricReadings());
    }

    @Test
    void loadRefrigerationCustomer() {
        InputStream in = getClass().getClassLoader().getResourceAsStream("客户资料1209.xls");
        List<RefrigerationCustomerExcelVo> resultList = new ArrayList<>(200);
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RefrigerationCustomerExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        Assertions.assertDoesNotThrow(() -> {
            EasyExcelFactory.read(in, RefrigerationCustomerExcelVo.class, readListener).sheet().doRead();
        });

        logger.info("resultList: {}", JsonUtils.object2Json(resultList));
    }

    @Test
    void loadRefrigerationDayBill() {
        InputStream in = getClass().getClassLoader().getResourceAsStream("12-1费用.xlsx");
        List<RefrigerationDayBillExcelVo> resultList = new ArrayList<>(200);
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RefrigerationDayBillExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        Assertions.assertDoesNotThrow(() -> {
            EasyExcelFactory.read(in, RefrigerationDayBillExcelVo.class, readListener).sheet().doRead();
        });

        logger.info("resultList: {}", JsonUtils.object2Json(resultList));

        RefrigerationDayBillExcelVo item = resultList.stream().filter(x ->
            Objects.equals(LocalDate.of(2022, 12, 1), x.getBillDate()) && Objects.equals("1010", x.getCustomerSerial())
        ).findFirst().orElse(null);
        Assertions.assertNotNull(item);
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("516.02"), item.getTotalCharge()));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("89.6"), item.getStorageCharge()));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("204.12"), item.getDisposalCharge()));
    }

    @Test
    void loadRefrigerationDayBill2() {
        InputStream in = getClass().getClassLoader().getResourceAsStream("费用2203-2211.xlsx");
        List<RefrigerationDayBillExcelVo> resultList = new ArrayList<>(200);
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RefrigerationDayBillExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        Assertions.assertDoesNotThrow(() -> {
            EasyExcelFactory.read(in, RefrigerationDayBillExcelVo.class, readListener).sheet().doRead();
        });

        logger.info("resultList: {}", JsonUtils.object2Json(resultList));

        RefrigerationDayBillExcelVo item = resultList.stream().filter(x ->
                Objects.equals(LocalDate.of(2022, 10, 31), x.getBillDate()) && Objects.equals("1005", x.getCustomerSerial())
        ).findFirst().orElse(null);
        Assertions.assertNotNull(item);
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("1316"), item.getTotalCharge()));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("1080"), item.getStorageCharge()));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("236"), item.getHandlingCharge()));
    }

    @Test
    void loadLogisticOrder() {
        InputStream in = getClass().getClassLoader().getResourceAsStream("物流每日配送明细20231201.xlsx");

        List<LogisticOrderExcelVo> orderList = new ArrayList<>(200);
        PageReadListener<LogisticOrderExcelVo> orderListener = new PageReadListener<>(orderList::addAll);
        EasyExcelFactory.read(in).registerReadListener(orderListener).head(LogisticOrderExcelVo.class).headRowNumber(2).sheet(0).doRead();
        logger.info("orderList: {}", JsonUtils.object2Json(orderList));
    }

    @Test
    void test() {
        String s1 = new String("abc");
        String s2 = new String("abc");
        String s3 = "abc";
        String s4 = s1.intern();
        String s5 = "abc";


        System.out.println(s1 == s2);  // false
        System.out.println(s2 == s3);  // false
        System.out.println(s3 == s4);  // true
        System.out.println(s3 == s5);  // true


    }

    @Test
    void csvTest() {
        InputStream in = null;
        BufferedReader reader = null;
        try {
            in = getClass().getClassLoader().getResourceAsStream("recordList.csv");
            List<DiningInformationVo> resultList = new ArrayList<>(200);
            reader = new BufferedReader(new InputStreamReader(in,"gbk"));
            boolean sign = false;       //用来跳过第一行的名称
            while(reader.ready())
            {
                String line = reader.readLine();
                String[] arr = line.split(",[',]*");


                if (arr.length>=12 && sign)
                {
                    String[] date = arr[2].split("-");
                    String[] time = arr[3].split(":");
                    DiningInformationVo information = new DiningInformationVo();
                    information.setEmployeeName(arr[0]);
                    information.setMealDate(LocalDate.of(Integer.valueOf(date[0]),Integer.valueOf(date[1]),Integer.valueOf(date[2])));
                    information.setMealTime(LocalTime.of(Integer.valueOf(time[0]),Integer.valueOf(time[1]),Integer.valueOf(time[2])));
                    resultList.add(information);
                }else if(arr.length<12 && sign){
                    String[] date = arr[1].split("-");
                    String[] time = arr[2].split(":");
                    DiningInformationVo information = new DiningInformationVo();
                    information.setEmployeeName(arr[0]);
                    information.setMealDate(LocalDate.of(Integer.valueOf(date[0]),Integer.valueOf(date[1]),Integer.valueOf(date[2])));
                    information.setMealTime(LocalTime.of(Integer.valueOf(time[0]),Integer.valueOf(time[1]),Integer.valueOf(time[2])));
                    resultList.add(information);
                }
                else
                {
                    sign = true;
                }
            }
            List<DiningInformationVo> newList = resultList.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getMealDate() + ";" + o.getEmployeeName()))), ArrayList::new));
            System.out.println(newList.size());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        finally {
            try {
                reader.close();
                in.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Test
    void testSplit() {
//        String str = "\"叶日新\",'53,',2022-12-12,12:34:18,IN,1,5,75,未定义,否,未知,312501";
        String str = "\"\",',',2022-12-23,11:30:13,IN,1,5,76,未定义,否,未知,318385";
        String[] arr = str.split(",(',)*");
        for (String item : arr) {
            logger.info("item: {}", item);
        }
    }
}
