package com.senox.web.convert;

import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.RealtyPayoffVo;
import com.senox.web.BaseTest;
import com.senox.web.vo.RealtyPayoffExportVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.Month;

/**
 * <AUTHOR>
 * @date 2022/11/25 14:11
 */
class RealtyPayoffExcelConvertorTest extends BaseTest {

    @Autowired
    private RealtyPayoffExcelConvertor convertor;

    @Test
    void payoff2ExportVo() {
        RealtyPayoffVo payoff = mockPayoff();
        RealtyPayoffExportVo exportVo = convertor.payoff2ExportVo(payoff);

        Assertions.assertEquals(payoff.getContractNo(), exportVo.getContractNo());
        Assertions.assertEquals(payoff.getRealtySerial(), exportVo.getRealtySerial());
        Assertions.assertEquals(payoff.getRealtyName(), exportVo.getRealtyName());
        Assertions.assertEquals(payoff.getCustomerName(), exportVo.getCustomerName());
        Assertions.assertEquals(payoff.getBillYear(), exportVo.getBillYear());
        Assertions.assertEquals(payoff.getBillMonth(), exportVo.getBillMonth());
        Assertions.assertEquals(payoff.getRentAmount(), exportVo.getRentAmount());
        Assertions.assertEquals(BillStatus.fromStatus(payoff.getStatus()).getValue(), exportVo.getPaidStatus());
    }

    private RealtyPayoffVo mockPayoff() {
        RealtyPayoffVo result = new RealtyPayoffVo();
        result.setContractNo(randStr(12));
        result.setRealtySerial(randStr(8));
        result.setRealtyName(randStr(6));
        result.setCustomerName(randStr(10));
        result.setBillYear(randInt(2000, 2100));
        result.setBillMonth(randInt(Month.JANUARY.getValue(), Month.DECEMBER.getValue()));
        result.setRentAmount(randDecimal(BigDecimal.valueOf(100L), BigDecimal.valueOf(10000), 2));
        result.setAmount(result.getRentAmount());
        result.setStatus(BillStatus.values()[randInt(0, BillStatus.values().length - 1)].getStatus());

        return result;
    }
}