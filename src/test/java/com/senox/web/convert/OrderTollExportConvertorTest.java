package com.senox.web.convert;

import com.senox.common.utils.DecimalUtils;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderTollVo;
import com.senox.web.BaseTest;
import com.senox.web.vo.OrderTollExportVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/10/31 9:35
 */
class OrderTollExportConvertorTest extends BaseTest {

    @Autowired
    private OrderTollExportConvertor tollExportConvertor;

    @Test
    void toDo() {
        OrderTollExportVo v = new OrderTollExportVo();
        v.setPaidTime(LocalDateTime.now());
        v.setTollMan(randStr(10));
        v.setBillSerial(randStr(8));
        v.setBillYearMonth(randStr(6));
        v.setRealtySerial(randStr(12));
        v.setRealtyName(randStr(15));
        v.setCustomerName(randStr(7));
        v.setManageAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        v.setRentAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        v.setWaterAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        v.setElectricAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        v.setPenaltyAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        v.setTotalAmount(DecimalUtils.add(v.getManageAmount(), v.getRentAmount(), v.getWaterAmount(), v.getElectricAmount(), v.getPenaltyAmount()));
        v.setPayWay(PayWay.values()[randInt(1, PayWay.values().length) - 1].getDescription());
        v.setReceipt(randInt(0, 1) == 1);
        v.setReceiptRemark(randStr(20));
        v.setBackLease(randInt(0, 1) == 1);
        v.setReplaceLease(randInt(0, 1) == 1);
        v.setCollectionLease(randInt(0, 1) == 1);

        OrderTollVo d = tollExportConvertor.toDo(v);

        Assertions.assertEquals(v.getPaidTime(), d.getPaidTime());
        Assertions.assertEquals(v.getTollMan(), d.getTollMan());
        Assertions.assertEquals(v.getBillSerial(), d.getBillSerial());
        Assertions.assertEquals(v.getBillYearMonth(), d.getBillYearMonth());
        Assertions.assertEquals(v.getRealtySerial(), d.getRealtySerial());
        Assertions.assertEquals(v.getRealtyName(), d.getRealtyName());
        Assertions.assertEquals(v.getCustomerName(), d.getCustomerName());
        Assertions.assertEquals(v.getManageAmount(), d.getManageAmount());
        Assertions.assertEquals(v.getRentAmount(), d.getRentAmount());
        Assertions.assertEquals(v.getWaterAmount(), d.getWaterAmount());
        Assertions.assertEquals(v.getElectricAmount(), d.getElectricAmount());
        Assertions.assertEquals(v.getPenaltyAmount(), d.getPenaltyAmount());
        Assertions.assertEquals(v.getTotalAmount(), d.getTotalAmount());
        Assertions.assertEquals(v.getReceipt(), d.getReceipt());
        Assertions.assertEquals(v.getReceiptRemark(), d.getReceiptRemark());
        Assertions.assertEquals(v.getBackLease(), d.getBackLease());
        Assertions.assertEquals(v.getReplaceLease(), d.getReplaceLease());
        Assertions.assertEquals(v.getCollectionLease(), d.getCollectionLease());
        Assertions.assertEquals(v.getPayWay(), PayWay.fromValue(d.getPayWay()).getDescription());

        List<OrderTollExportVo> vList = Collections.singletonList(v);
        List<OrderTollVo> dList = tollExportConvertor.toDo(vList);
        Assertions.assertEquals(vList.get(0).getPaidTime(), dList.get(0).getPaidTime());
        Assertions.assertEquals(vList.get(0).getTollMan(), dList.get(0).getTollMan());
        Assertions.assertEquals(vList.get(0).getBillSerial(), dList.get(0).getBillSerial());
        Assertions.assertEquals(vList.get(0).getBillYearMonth(), dList.get(0).getBillYearMonth());
        Assertions.assertEquals(vList.get(0).getRealtySerial(), dList.get(0).getRealtySerial());
        Assertions.assertEquals(vList.get(0).getRealtyName(), dList.get(0).getRealtyName());
        Assertions.assertEquals(vList.get(0).getCustomerName(), dList.get(0).getCustomerName());
        Assertions.assertEquals(vList.get(0).getManageAmount(), dList.get(0).getManageAmount());
        Assertions.assertEquals(vList.get(0).getRentAmount(), dList.get(0).getRentAmount());
        Assertions.assertEquals(vList.get(0).getWaterAmount(), dList.get(0).getWaterAmount());
        Assertions.assertEquals(vList.get(0).getElectricAmount(), dList.get(0).getElectricAmount());
        Assertions.assertEquals(vList.get(0).getPenaltyAmount(), dList.get(0).getPenaltyAmount());
        Assertions.assertEquals(vList.get(0).getTotalAmount(), dList.get(0).getTotalAmount());
        Assertions.assertEquals(vList.get(0).getReceipt(), dList.get(0).getReceipt());
        Assertions.assertEquals(vList.get(0).getReceiptRemark(), dList.get(0).getReceiptRemark());
        Assertions.assertEquals(vList.get(0).getBackLease(), dList.get(0).getBackLease());
        Assertions.assertEquals(vList.get(0).getReplaceLease(), dList.get(0).getReplaceLease());
        Assertions.assertEquals(vList.get(0).getCollectionLease(), dList.get(0).getCollectionLease());
        Assertions.assertEquals(vList.get(0).getPayWay(), PayWay.fromValue(dList.get(0).getPayWay()).getDescription());
    }

    @Test
    void toV() {
        OrderTollVo d = new OrderTollVo();
        d.setPaidTime(LocalDateTime.now());
        d.setTollMan(randStr(10));
        d.setBillSerial(randStr(8));
        d.setBillYearMonth(randStr(6));
        d.setRealtySerial(randStr(12));
        d.setRealtyName(randStr(15));
        d.setCustomerName(randStr(7));
        d.setManageAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        d.setRentAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        d.setWaterAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        d.setElectricAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        d.setPenaltyAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(50000L), 2));
        d.setTotalAmount(DecimalUtils.add(d.getManageAmount(), d.getRentAmount(), d.getWaterAmount(), d.getElectricAmount(), d.getPenaltyAmount()));
        d.setPayWay(PayWay.values()[randInt(1, PayWay.values().length) - 1].getValue());
        d.setReceipt(randInt(0, 1) == 1);
        d.setReceiptRemark(randStr(20));
        d.setBackLease(randInt(0, 1) == 1);
        d.setReplaceLease(randInt(0, 1) == 1);
        d.setCollectionLease(randInt(0, 1) == 1);

        OrderTollExportVo v = tollExportConvertor.toV(d);

        Assertions.assertEquals(d.getPaidTime(), v.getPaidTime());
        Assertions.assertEquals(d.getTollMan(), v.getTollMan());
        Assertions.assertEquals(d.getBillSerial(), v.getBillSerial());
        Assertions.assertEquals(d.getBillYearMonth(), v.getBillYearMonth());
        Assertions.assertEquals(d.getRealtySerial(), v.getRealtySerial());
        Assertions.assertEquals(d.getRealtyName(), v.getRealtyName());
        Assertions.assertEquals(d.getCustomerName(), v.getCustomerName());
        Assertions.assertEquals(d.getManageAmount(), v.getManageAmount());
        Assertions.assertEquals(d.getRentAmount(), v.getRentAmount());
        Assertions.assertEquals(d.getWaterAmount(), v.getWaterAmount());
        Assertions.assertEquals(d.getElectricAmount(), v.getElectricAmount());
        Assertions.assertEquals(d.getPenaltyAmount(), v.getPenaltyAmount());
        Assertions.assertEquals(d.getTotalAmount(), v.getTotalAmount());
        Assertions.assertEquals(d.getReceipt(), v.getReceipt());
        Assertions.assertEquals(d.getReceiptRemark(), v.getReceiptRemark());
        Assertions.assertEquals(d.getBackLease(), v.getBackLease());
        Assertions.assertEquals(d.getReplaceLease(), v.getReplaceLease());
        Assertions.assertEquals(d.getCollectionLease(), v.getCollectionLease());
        Assertions.assertEquals(PayWay.fromValue(d.getPayWay()).getDescription(), v.getPayWay());


        List<OrderTollVo> dList = Collections.singletonList(d);
        List<OrderTollExportVo> vList = tollExportConvertor.toV(dList);
        Assertions.assertEquals(dList.get(0).getPaidTime(), vList.get(0).getPaidTime());
        Assertions.assertEquals(dList.get(0).getTollMan(), vList.get(0).getTollMan());
        Assertions.assertEquals(dList.get(0).getBillSerial(), vList.get(0).getBillSerial());
        Assertions.assertEquals(dList.get(0).getBillYearMonth(), vList.get(0).getBillYearMonth());
        Assertions.assertEquals(dList.get(0).getRealtySerial(), vList.get(0).getRealtySerial());
        Assertions.assertEquals(dList.get(0).getRealtyName(), vList.get(0).getRealtyName());
        Assertions.assertEquals(dList.get(0).getCustomerName(), vList.get(0).getCustomerName());
        Assertions.assertEquals(dList.get(0).getManageAmount(), vList.get(0).getManageAmount());
        Assertions.assertEquals(dList.get(0).getRentAmount(), vList.get(0).getRentAmount());
        Assertions.assertEquals(dList.get(0).getWaterAmount(), vList.get(0).getWaterAmount());
        Assertions.assertEquals(dList.get(0).getElectricAmount(), vList.get(0).getElectricAmount());
        Assertions.assertEquals(dList.get(0).getPenaltyAmount(), vList.get(0).getPenaltyAmount());
        Assertions.assertEquals(dList.get(0).getTotalAmount(), vList.get(0).getTotalAmount());
        Assertions.assertEquals(dList.get(0).getReceipt(), vList.get(0).getReceipt());
        Assertions.assertEquals(dList.get(0).getReceiptRemark(), vList.get(0).getReceiptRemark());
        Assertions.assertEquals(dList.get(0).getBackLease(), vList.get(0).getBackLease());
        Assertions.assertEquals(dList.get(0).getReplaceLease(), vList.get(0).getReplaceLease());
        Assertions.assertEquals(dList.get(0).getCollectionLease(), vList.get(0).getCollectionLease());
        Assertions.assertEquals(PayWay.fromValue(dList.get(0).getPayWay()).getDescription(), vList.get(0).getPayWay());
    }
}