package com.senox.web;

import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @Date 2021/1/6 16:28
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = WebApplication.class)
public class BaseTest {


    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String NUMBERS = "0123456789";


    public static String randStr(int length) {
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        while (length > 0) {
            builder.append(LETTERS.charAt(random.nextInt(LETTERS.length())));
            length--;
        }
        return builder.toString();
    }

    public static String randNumStr(int length) {
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        while (length > 0) {
            builder.append(NUMBERS.charAt(random.nextInt(NUMBERS.length())));
            length--;
        }
        return builder.toString();
    }

    public static Integer randInt(int start, int end) {
        return ThreadLocalRandom.current().nextInt(start, end);
    }

    public static Long randLong(long start, long end) {
        return ThreadLocalRandom.current().nextLong(start, end);
    }

    public static BigDecimal randDecimal(BigDecimal start, BigDecimal end, int scale) {
        BigDecimal randResult = start.add(new BigDecimal(Math.random()).multiply(end.subtract(start)));
        return randResult.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }
}
