package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import com.senox.web.service.AuthCredentialsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-3-4
 */
@Api(tags = "用户凭证")
@RestController
@RequestMapping("/web/auth/credentials")
@RequiredArgsConstructor
public class AuthCredentialsController {
    private final AuthCredentialsService authCredentialsService;

    @ApiOperation("添加凭证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/add/{userId}")
    public void add(@PathVariable Long userId) {
        authCredentialsService.add(userId);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<AuthCredentialsVo> listPage(@RequestBody AuthCredentialsSearchVo searchVo) {
        return authCredentialsService.listPage(searchVo);
    }
}
