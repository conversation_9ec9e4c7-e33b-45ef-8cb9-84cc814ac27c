package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.*;
import com.senox.web.service.FirefightingAccommodateInspectionService;
import com.senox.web.service.FirefightingSmallPlacesInspectionService;
import com.senox.web.service.FirefightingStoreInspectionService;
import com.senox.web.service.FirefightingUtilityInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/4/25 17:11
 */
@Api(tags = "商铺消防巡检记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/inspection")
public class FirefightingInspectionController extends BaseController {

    private final FirefightingUtilityInspectionService utilityInspectionService;
    private final FirefightingStoreInspectionService storeInspectionService;
    private final FirefightingSmallPlacesInspectionService smallPlacesInspectionService;
    private final FirefightingAccommodateInspectionService accommodateInspectionService;

    @ApiOperation("添加公共消防设施巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/utility/add")
    public Long addUtilityInspection(@Validated @RequestBody FirefightingUtilityInspectionVo inspection) {
        return utilityInspectionService.addUtilityInspection(inspection);
    }

    @ApiOperation("更新公共消防设施巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/utility/update")
    public void updateUtilityInspection(@Validated @RequestBody FirefightingUtilityInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        utilityInspectionService.updateUtilityInspection(inspection);
    }

    @ApiOperation("删除公共消防设施巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/utility/delete/{id}")
    public void deleteUtilityInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        utilityInspectionService.deleteUtilityInspection(id);
    }

    @ApiOperation("获取公共消防设施巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/utility/get/{id}")
    public FirefightingUtilityInspectionVo findUtilityInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return utilityInspectionService.findUtilityInspectionById(id);
    }

    @ApiOperation("公共消防设施巡检记录页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/utility/page")
    public PageResult<FirefightingUtilityInspectionVo> listUtilityInspectionPage(@RequestBody FirefightingUtilityInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return utilityInspectionService.listUtilityInspectionPage(search);
    }

    @ApiOperation("添加商铺消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/store/add")
    public Long addStoreInspection(@Validated(Add.class) @RequestBody FirefightingStoreInspectionVo inspection) {
        return storeInspectionService.addStoreInspection(inspection);
    }

    @ApiOperation("更新商铺消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/store/update")
    public void updateStoreInspection(@Validated(Update.class) @RequestBody FirefightingStoreInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        storeInspectionService.updateStoreInspection(inspection);
    }

    @ApiOperation("删除商铺消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/store/delete/{id}")
    public void deleteStoreInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        storeInspectionService.deleteStoreInspection(id);
    }

    @ApiOperation("获取商铺消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/store/get/{id}")
    public FirefightingStoreInspectionVo findStoreInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return storeInspectionService.findStoreInspectionById(id);
    }

    @ApiOperation("获取商铺消防巡检记录数统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/store/count")
    public int countStoreInspection(@RequestBody FirefightingStoreInspectionSearchVo search) {
        return storeInspectionService.countStoreInspection(search);
    }

    @ApiOperation("获取商铺消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/store/page")
    public PageResult<FirefightingStoreInspectionBriefVo> listStoreInspection(@RequestBody FirefightingStoreInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return storeInspectionService.listStoreInspectionPage(search);
    }

    @ApiOperation("添加三小场所、出租屋消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/smallPlaces/add")
    public Long addSmallPlacesInspection(@Validated(Add.class) @RequestBody FirefightingSmallPlacesInspectionVo inspection) {
        return smallPlacesInspectionService.addSmallPlacesInspection(inspection);
    }

    @ApiOperation("更新三小场所、出租屋消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/smallPlaces/update")
    public void updateSmallPlacesInspection(@Validated(Update.class) @RequestBody FirefightingSmallPlacesInspectionVo inspection) {
        smallPlacesInspectionService.updateSmallPlacesInspection(inspection);
    }

    @ApiOperation("删除三小场所、出租屋消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/smallPlaces/delete/{id}")
    public void deleteSmallPlacesInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        smallPlacesInspectionService.deleteSmallPlacesInspection(id);
    }

    @ApiOperation("获取三小场所、出租屋消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/smallPlaces/get/{id}")
    public FirefightingSmallPlacesInspectionVo findSmallPlacesInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return smallPlacesInspectionService.findSmallPlacesInspectionById(id);
    }

    @ApiOperation("三小场所、出租屋消防巡检记录数统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/smallPlaces/count")
    public int countSmallPlacesInspection(@RequestBody FirefightingSmallPlacesInspectionSearchVo search) {
        return smallPlacesInspectionService.countSmallPlacesInspection(search);
    }

    @ApiOperation("三小场所、出租屋消防巡检记录列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/smallPlaces/page")
    public PageResult<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspectionPage(@RequestBody FirefightingSmallPlacesInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return smallPlacesInspectionService.listSmallPlacesInspectionPage(search);
    }

    @ApiOperation("添加违规住人消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/accommodate/add")
    public Long addAccommodateInspection(@Validated(Add.class) @RequestBody FirefightingAccommodateInspectionVo inspection) {
        return accommodateInspectionService.addAccommodateInspection(inspection);
    }

    @ApiOperation("更新违规住人消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/accommodate/update")
    public void updateAccommodateInspection(@Validated(Update.class) @RequestBody FirefightingAccommodateInspectionVo inspection) {
        accommodateInspectionService.updateAccommodateInspection(inspection);
    }

    @ApiOperation("删除违规住人消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/accommodate/delete/{id}")
    public void deleteAccommodateInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        accommodateInspectionService.deleteAccommodateInspection(id);
    }

    @ApiOperation("获取违规住人消防巡检记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/accommodate/get/{id}")
    public FirefightingAccommodateInspectionVo findAccommodateInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return accommodateInspectionService.findAccommodateInspectionById(id);
    }

    @ApiOperation("违规住人消防巡检记录数统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/accommodate/count")
    public int countAccommodateInspection(@RequestBody FirefightingAccommodateInspectionSearchVo search) {
        return accommodateInspectionService.countAccommodateInspection(search);
    }

    @ApiOperation("违规住人消防巡检记录列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/accommodate/page")
    public PageResult<FirefightingAccommodateInspectionBriefVo> listAccommodateInspectionPage(@RequestBody FirefightingAccommodateInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return accommodateInspectionService.listAccommodateInspectionPage(search);
    }

}
