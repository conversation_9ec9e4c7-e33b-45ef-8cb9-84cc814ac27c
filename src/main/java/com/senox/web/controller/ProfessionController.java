package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.context.AdminContext;
import com.senox.user.vo.ProfessionVo;
import com.senox.web.service.ProfessionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/15 10:32
 */
@Api(tags = "基本信息 - 行业")
@RestController
@RequestMapping("/web/dictionary/profession")
public class ProfessionController {

    @Autowired
    private ProfessionService professionService;

    @ApiOperation("添加行业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addProfession(@Validated @RequestBody ProfessionVo profession) {
        return professionService.addProfession(profession);
    }

    @ApiOperation("更新行业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateProfession(@Validated @RequestBody ProfessionVo profession) {
        if (profession.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        professionService.updateProfession(profession);
    }

    @ApiOperation("删除行业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteProfession(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        professionService.deleteProfession(id);
    }

    @ApiOperation("获取行业信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ProfessionVo getProfession(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return professionService.findById(id);
    }

    @ApiOperation("行业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<ProfessionVo> listAll() {
        return professionService.listAll();
    }
}
