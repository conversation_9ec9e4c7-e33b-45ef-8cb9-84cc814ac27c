package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.realty.vo.*;
import com.senox.web.convert.RealtyWeExcelConvertor;
import com.senox.web.service.RealtyReadingsService;
import com.senox.web.vo.RealtyWeExcelVo;
import com.senox.web.vo.RealtyWeImportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/11/2 9:37
 */
@Api(tags = "水电读数")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/realty/readings")
public class RealtyReadingsController extends BaseController {

    private final RealtyReadingsService readingsService;
    private final RealtyWeExcelConvertor weExcelConvertor;

    @ApiOperation("上传添加水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/import")
    public void importWeReadings(@RequestPart("file") MultipartFile file, RealtyWeImportVo importWe) throws IOException {
        checkExcelFile(file);
        if (!WrapperClassUtils.biggerThanInt(importWe.getYear(), 0)
                || !WrapperClassUtils.biggerThanInt(importWe.getMonth(), 0)) {
            throw new InvalidParameterException();
        }

        // 水电读数
        List<RealtyWeExcelVo> weExcelList = loadReadingsFromExcel(file.getInputStream());
        weExcelList = weExcelList.stream().filter(x -> !StringUtils.isBlank(x.getRealtySerial())).collect(Collectors.toList());

        // 读数转换
        RealtyWeBatchVo batch = new RealtyWeBatchVo();
        batch.setYear(importWe.getYear());
        batch.setMonth(importWe.getMonth());
        batch.setOverWrite(importWe.getIsOverWrite());
        batch.setData(weExcelConvertor.excelToVo(weExcelList));
        addWeReadingsBatch(batch);
    }

    @ApiOperation("批量添加水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/add")
    public void addWeReadings(@Validated @RequestBody RealtyWeBatchVo weBatchReadings) {
        if (CollectionUtils.isEmpty(weBatchReadings.getData())) {
            throw new InvalidParameterException();
        }
        weBatchReadings.setOverWrite(Boolean.FALSE);
        addWeReadingsBatch(weBatchReadings);
    }

    @ApiOperation("更新水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/update")
    public void updateWeReadings(@RequestBody RealtyWeVo we) {
        if (StringUtils.isBlank(we.getRealtySerial())) {
            throw new InvalidParameterException();
        }

        readingsService.updateWeReadings(we);
    }

    @ApiOperation("删除水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/delete")
    public void deleteWeReadings(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        readingsService.deleteWeReadings(ids);
    }

    @ApiOperation("重置月水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/reset")
    public void resetWeReadings(@Validated @RequestBody BillMonthVo month) {
        readingsService.resetWeReadings(month);
    }

    @ApiOperation("校验月水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/check")
    public boolean checkWeReadings(@Validated @RequestBody BillMonthVo month) {
        return readingsService.checkWeReadings(month);
    }

    @ApiOperation("获取水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/we/get/{id}")
    public RealtyWeVo findWeReadingsById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return readingsService.findWeReadingsById(id);
    }

    @ApiOperation("水电读数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/list")
    public RealtyWePageResult<RealtyWeVo> listWeReadingsPage(@Validated @RequestBody RealtyWeSearchVo search) {
        return readingsService.listReadingsPage(search);
    }


    /**
     * 从 InputStream 读取数据
     * @param in
     * @return
     */
    private List<RealtyWeExcelVo> loadReadingsFromExcel(InputStream in) {
        List<RealtyWeExcelVo> resultList = new ArrayList<>(200);
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RealtyWeExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        EasyExcelFactory.read(in, RealtyWeExcelVo.class, readListener).sheet().doRead();
        return resultList;
    }

    /**
     * 批量添加水电数据
     * @param weBatch
     */
    private void addWeReadingsBatch(RealtyWeBatchVo weBatch) {
        // 默认覆盖重复数据
        if (weBatch.getOverWrite() == null) {
            weBatch.setOverWrite(true);
        }

        readingsService.batchAddWeReadings(weBatch);
    }


}
