package com.senox.web.controller;

import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.vo.AdminUserLoginVo;
import com.senox.web.service.AdminUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2021/1/6 10:00
 */
@Api(tags = "登录")
@RestController
@RequestMapping("/web")
public class LoginController {

    @Autowired
    private AdminUserService adminUserService;

    @ApiOperation("登录")
    @PostMapping("/login")
    public AdminUserDto login(@Validated @RequestBody AdminUserLoginVo loginVo) {
        return adminUserService.login(loginVo);
    }

    @ApiOperation("登出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/logout")
    public void logout(HttpServletRequest request) {
        String token = request.getHeader(AdminContext.HEADER_AUTHORIZATION);
        adminUserService.logout(token);
    }

    @ApiOperation("签名校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/checkToken")
    public void checkToken() {
    }
}
