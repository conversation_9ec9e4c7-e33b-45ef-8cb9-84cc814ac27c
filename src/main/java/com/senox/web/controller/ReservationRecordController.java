package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.user.constant.ReservationType;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.ReservationRecordService;
import com.senox.web.vo.ReservationRecordExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/27 18:45
 */
@Api(tags = "预约记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/reservation/record")
public class ReservationRecordController extends BaseController{

    private final ReservationRecordService reservationRecordService;

    @ApiOperation("根据id获取预约记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public ReservationRecordVo findById(@PathVariable Long id) {
        return reservationRecordService.findById(id);
    }

    @ApiOperation("预约记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageStatisticsResult<ReservationRecordVo, ReservationRecordVo> page(@RequestBody ReservationRecordSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<ReservationRecordVo> page = reservationRecordService.page(searchVo);
        ReservationRecordVo sumVo = reservationRecordService.sumReservationRecord(searchVo);
        return new PageStatisticsResult<>(page, sumVo);
    }

    @ApiOperation("导出预约记录")
    @GetMapping("/export")
    public void exportReservationRecord(HttpServletResponse response, ReservationRecordSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageResult<ReservationRecordVo> page = reservationRecordService.page(searchVo);
        ReservationRecordVo sumVo = reservationRecordService.sumReservationRecord(searchVo);
        List<ReservationRecordExportVo> exportList = page == null ? Collections.emptyList()
                : new ArrayList<>(page.getTotalSize());
        if (page != null && !CollectionUtils.isEmpty(page.getDataList())) {
            for (int index = 0; index < page.getDataList().size(); index++) {
                ReservationRecordVo recordVo = page.getDataList().get(index);
                ReservationRecordExportVo exportItem = newReservationRecordExportVo(recordVo);
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
            exportList.add(sumReservationRecordExportVo(sumVo));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_RESERVATION_RECORD, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ReservationRecordExportVo.class)
                .sheet(SenoxConst.Export.SHEET_RESERVATION_RECORD)
                .doWrite(exportList);
    }

    private ReservationRecordExportVo newReservationRecordExportVo(ReservationRecordVo recordVo) {
        ReservationRecordExportVo exportVo = new ReservationRecordExportVo();
        ReservationType type = ReservationType.fromValue(recordVo.getType());
        exportVo.setType(type == null ? StringUtils.EMPTY : type.getName());
        exportVo.setVisitorName(recordVo.getVisitorName());
        exportVo.setContact(recordVo.getContact());
        exportVo.setTogetherNum(recordVo.getTogetherNum());
        exportVo.setVisitTimeStart(recordVo.getVisitTimeStart());
        exportVo.setVisitTimeEnd(recordVo.getVisitTimeEnd());
        exportVo.setCarNo(StringUtils.EMPTY);
        if (!CollectionUtils.isEmpty(recordVo.getCarNoList())) {
            exportVo.setCarNo(String.join(",", recordVo.getCarNoList()));
        }
        return exportVo;
    }

    private ReservationRecordExportVo sumReservationRecordExportVo(ReservationRecordVo sumVo) {
        ReservationRecordExportVo exportVo = new ReservationRecordExportVo();
        exportVo.setVisitorName(SenoxConst.Export.COLUMN_SUM);
        exportVo.setTogetherNum(sumVo.getTogetherNum());
        return exportVo;
    }
}
