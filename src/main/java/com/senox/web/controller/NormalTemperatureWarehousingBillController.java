package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.cold.constant.NormalTemperatureWarehousingFee;
import com.senox.cold.vo.*;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.realty.constant.BillStatus;
import com.senox.user.vo.MerchantVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.NormalTemperatureWarehousingBillConvert;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.MerchantService;
import com.senox.web.service.NormalTemperatureWarehousingBillService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-20
 **/
@Api(tags = "冷藏干仓账单")
@RequiredArgsConstructor
@RequestMapping("/web/cold/normal/temperature/warehouse/bill")
@RestController
public class NormalTemperatureWarehousingBillController extends BaseController {
    private final MerchantService merchantService;
    private final NormalTemperatureWarehousingBillService billService;
    private final AdminUserService adminUserService;
    private final NormalTemperatureWarehousingBillConvert billConvert;

    @ApiOperation("添加账单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestParam(required = false) Boolean isDuplicate, @RequestParam(required = false) Boolean isOverride, @RequestBody NormalTemperatureWarehousingBillAddDto addDto) {
        MerchantVo merchant = merchantService.findByName(addDto.getMerchantName());
        if (null == merchant) {
            throw new BusinessException(String.format("未找到商户[%s]", addDto.getMerchantName()));
        }
        addDto.setMerchantId(merchant.getId());
        billService.add(addDto.getBillYear(), addDto.getBillMonth(), isDuplicate, isOverride, Collections.singletonList(addDto));
    }

    @ApiOperation("账单导入")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/import")
    public void billImport(@RequestParam Integer year, @RequestParam Integer month, @RequestPart("file") MultipartFile file) throws IOException {
        String lockKey = String.format(SenoxConst.Cache.KEY_COLD_NORMAL_TEMPERATURE_WAREHOUSING_BILL_IMPORT, getAdminUser().getUserId());
        if (!RedisUtils.lock(lockKey, SenoxConst.Cache.TTL_1H)) {
            throw new BusinessException("导入频繁，请稍后再试");
        }
        try {
            checkExcelFile(file);
            List<NormalTemperatureWarehousingBillImportExcelVo> excels = new ArrayList<>();
            PageReadListener<NormalTemperatureWarehousingBillImportExcelVo> readListener = new PageReadListener<>(excels::addAll);
            EasyExcelFactory.read(file.getInputStream(), NormalTemperatureWarehousingBillImportExcelVo.class, readListener).sheet().doRead();
            List<NormalTemperatureWarehousingBillAddDto> billAddList = billConvert.toVo(merchantService, excels
                    .stream()
                    .filter(e -> !StringUtils.isBlank(e.getMerchantName())).collect(Collectors.toList()));
            billService.add(year, month, false, false, billAddList);
        } finally {
            RedisUtils.del(lockKey);
        }
    }

    @ApiOperation("干仓账单导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response, NormalTemperatureWarehousingBillSearchVo search) throws IOException {
        List<NormalTemperatureWarehousingBillVo> bills = billService.list(search);
        List<NormalTemperatureWarehousingBillExportVo> exportList = toExport(bills);
        String fileName = SenoxConst.Export.COLD_NORMAL_TEMPERATURE_WAREHOUSING_BILL_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), NormalTemperatureWarehousingBillExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.COLD_NORMAL_TEMPERATURE_WAREHOUSING_BILL_SHEET)
                .doWrite(exportList);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/delete")
    public void deleteByIds(@RequestBody List<Long> ids) {
        billService.deleteByIds(ids);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@RequestBody NormalTemperatureWarehousingBillVo billVo) {
        billService.update(billVo);
    }


    @ApiOperation("检查重复")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/duplicate/check")
    public Boolean checkDuplicate(@RequestBody NormalTemperatureWarehousingBillAddDto bill) {
        MerchantVo merchant = merchantService.findByName(bill.getMerchantName());
        if (null == merchant) {
            throw new BusinessException(String.format("未找到商户[%s]", bill.getMerchantName()));
        }
        bill.setMerchantId(merchant.getId());
        return billService.checkDuplicate(bill);
    }

    @ApiOperation("更新备注")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/remark/update")
    public void updateRemark(@RequestBody NormalTemperatureWarehousingBillVo billVo) {
        billService.updateRemark(Collections.singletonList(billVo));
    }

    @ApiOperation("根据id获取账单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/findById/{billId}")
    public NormalTemperatureWarehousingBillVo findById(@PathVariable Long billId) {
        return billService.findById(billId);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageStatisticsResult<NormalTemperatureWarehousingBillVo, NormalTemperatureWarehousingStatisticsVo> pageList(@RequestBody NormalTemperatureWarehousingBillSearchVo search) {
        return billService.pageList(search);
    }

    @ApiOperation("账单下发")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/send")
    public void send(@RequestBody RefrigerationBillSendVo send) {
        billService.send(send);
    }

    @ApiOperation("打印账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        NormalTemperatureWarehousingBillVo bill = billService.findById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (bill.getStatus() != BillStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = billTollPrint(bill);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());
            // 更新账单票据号
            TollSerialVo tollSerial = new TollSerialVo();
            tollSerial.setBillId(id);
            tollSerial.setBillSerial(result.getBillSerial());
            tollSerial.setOperatorId(getAdminUserId());
            billService.updateSerial(tollSerial);
        }
        return result;
    }

    @ApiOperation("账单支付")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/pay")
    public void payBill(@RequestBody BillPayRequestVo payRequest) {
        validBillPayRequest(payRequest);
        payRequest.setTollMan(getAdminUserId());
        billService.payBill(payRequest);
    }

    @ApiOperation("月度账单通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/notify/wechat")
    public void notifyWechatBill(@RequestParam Integer year, @RequestParam Integer month) {
        billService.notifyBill(year, month);
    }

    private TollPrintVo billTollPrint(NormalTemperatureWarehousingBillVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getBillSerial());
        result.setPayer(bill.getMerchantName());
        result.setPayerDesc(bill.getMerchantName());
        result.setTollMan(bill.getTollManName());
        result.setTollTime(bill.getPaidTime());
        result.setTotalAmount(bill.getPaidAmount());

        // 费项明细
        List<TollPrintItemVo> list = newTollPrintItem(bill);
        if (!StringUtils.isBlank(bill.getRemark())) {
            list.get(0).setRemark(bill.getRemark());
        }
        result.setDetails(list.stream().filter(x -> !Objects.isNull(x)).collect(Collectors.toList()));
        return result;
    }

    private List<TollPrintItemVo> newTollPrintItem(NormalTemperatureWarehousingBillVo bill) {
        List<TollPrintItemVo> result = new ArrayList<>(bill.getItems().size());
        for (NormalTemperatureWarehousingBillItemVo item : bill.getItems()) {
            TollPrintItemVo printItem = new TollPrintItemVo();
            printItem.setFee(item.getFeeName());
            printItem.setPrice(item.getAmount());
            printItem.setAmount(item.getAmount());
            printItem.setTime(StringUtils.buildYearMonthStr(bill.getBillYear(), bill.getBillMonth()));
            result.add(printItem);
        }
        return result;
    }

    private List<NormalTemperatureWarehousingBillExportVo> toExport(List<NormalTemperatureWarehousingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return Collections.emptyList();
        }
        List<NormalTemperatureWarehousingBillExportVo> exportList = new ArrayList<>(bills.size());
        for (NormalTemperatureWarehousingBillVo bill : bills) {
            NormalTemperatureWarehousingBillExportVo export = new NormalTemperatureWarehousingBillExportVo();
            export.setBillYearMonth(bill.getBillYearMonth());
            export.setMerchantName(bill.getMerchantName());
            export.setHandlingAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.HANDLING));
            export.setLoadingAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.LOADING));
            export.setSortingAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.SORTING));
            export.setStorageAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.STORAGE));
            export.setFreightAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.FREIGHT));
            export.setOtherAmount(getBillItemAmount(bill, NormalTemperatureWarehousingFee.OTHER));
            export.setPenaltyAmount(bill.getPenaltyAmount());
            export.setPenaltyPaidAmount(bill.getPenaltyPaidAmount());
            export.setTotalAmount(bill.getAmount());
            export.setPaidAmount(bill.getPaidAmount());
            BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
            export.setPaidStatus(billStatus.getValue());
            PayWay payWay = PayWay.fromValue(bill.getPayWay());
            export.setPayWay(payWay.getDescription());
            export.setPaidTime(bill.getPaidTime());
            export.setRemark(bill.getRemark());
            exportList.add(export);
        }
        return exportList;
    }

    private BigDecimal getBillItemAmount(NormalTemperatureWarehousingBillVo bill, NormalTemperatureWarehousingFee fee) {
        for (NormalTemperatureWarehousingBillItemVo item : bill.getItems()) {
            if (fee.getFeeId().equals(item.getFeeId())) {
                return item.getAmount();
            }
        }
        return BigDecimal.ZERO;
    }
}
