package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import com.senox.web.service.BicycleSharesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-9-20
 */
@Api(tags = "三轮车 - 佣金")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/bicycle/shares")
public class BicycleSharesController {
    private final BicycleSharesService sharesService;


    @ApiOperation("添加佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addShares(@Validated({Add.class}) @RequestBody BicycleSharesVo sharesVo) {
        sharesService.addShares(sharesVo);
    }

    @ApiOperation("删除佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{sharesId}")
    public void deleteShares(@PathVariable Long sharesId) {
        sharesService.deleteShares(sharesId);
    }

    @ApiOperation("修改佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateShares(@RequestBody BicycleSharesVo sharesVo) {
        sharesService.updateShares(sharesVo);
    }

    @ApiOperation("佣金列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleSharesVo> listShares(@RequestBody BicycleSharesSearchVo searchVo) {
        return sharesService.listShares(searchVo);
    }
}
