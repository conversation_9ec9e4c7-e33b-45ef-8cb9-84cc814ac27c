package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import com.senox.user.constant.MerchantBillSettlePeriod;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.BicycleBillService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.BicycleBillExportVo;
import com.senox.web.vo.BicyclePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/9/22 16:30
 */
@Api(tags = "三轮车应收账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/bicycle/bill")
public class BicycleBillController extends BaseController{

    private final BicycleBillService bicycleBillService;

    @ApiOperation("获取三轮车应收账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleBillVo findBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return bicycleBillService.findBillById(id);
    }

    @ApiOperation("三轮车应收账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public BicyclePage<BicycleBillVo> listBillPage(@RequestBody BicycleBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicyclePage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        // list
        PageResult<BicycleBillVo> page = bicycleBillService.listBillPage(searchVo);
        BicyclePage<BicycleBillVo> resultPage = new BicyclePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        BicycleBillVo sumBill = bicycleBillService.sumBill(searchVo);
        if (sumBill != null) {
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumBill.getDeliveryAmount()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumBill.getOtherAmount()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumBill.getAmount()));
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumBill.getPieces()));
        }
        return resultPage;
    }


    @ApiOperation("三轮车应收账单导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,BicycleBillSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageResult<BicycleBillVo> pageResult = bicycleBillService.listBillPage(searchVo);
        List<BicycleBillVo> bills = pageResult.getDataList();
        List<BicycleBillExportVo> billExports = billToExport(bills);
        String fileName = SenoxConst.Export.TMS_BICYCLE_BILL_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleBillExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_BICYCLE_BILL_SHEET)
                .doWrite(billExports);
    }

    /**
     * 账单转报表
     *
     * @param billList 账单列表
     * @return 返回报表
     */
    private List<BicycleBillExportVo> billToExport(List<BicycleBillVo> billList) {
        if (CollectionUtils.isEmpty(billList)) {
            return Collections.emptyList();
        }
        List<BicycleBillExportVo> exportVos = billList.stream().map(b -> {
            BicycleBillExportVo be = new BicycleBillExportVo();
            be.setBillDate(b.getBillDate().toString());
            be.setBillYear(b.getBillYear());
            be.setBillMonth(b.getBillMonth());
            be.setDeliveryOrderSerialNo(b.getDeliveryOrderSerialNo());
            be.setOrderSerialNo(b.getOrderSerialNo());
            be.setMerchantName(b.getMerchantName());
            be.setRcSerial(b.getRcSerial());
            be.setSettlePeriod(MerchantBillSettlePeriod.fromNumber(b.getSettlePeriod()).getName());
            be.setDeliveryAmount(b.getDeliveryAmount());
            be.setOtherAmount(b.getOtherAmount());
            be.setAmount(b.getAmount());
            return be;
        }).collect(Collectors.toList());
        BicycleBillExportVo totalExport = new BicycleBillExportVo();
        totalExport.setMerchantName("合计");
        totalExport.setDeliveryAmount(exportVos.stream().map(BicycleBillExportVo::getDeliveryAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalExport.setOtherAmount(exportVos.stream().map(BicycleBillExportVo::getOtherAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalExport.setAmount(exportVos.stream().map(BicycleBillExportVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        exportVos.add(totalExport);
        return exportVos;
    }
}
