package com.senox.web.controller;


import com.senox.car.vo.SignalToneEvtVo;
import com.senox.car.vo.SignalToneResponse;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.*;
import com.senox.pm.vo.PaymentDeviceSearchVo;
import com.senox.pm.vo.PaymentDeviceVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-12-24
 */
@Slf4j
@Api(tags = "设备")
@RestController
@RequestMapping("/web/device")
@RequiredArgsConstructor
public class DeviceController extends BaseController {

    private final DeviceService deviceService;
    private final RabbitTemplate rabbitTemplate;

    @ApiOperation("添加防疫门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/add")
    public Long addAccessDevice(@Validated @RequestBody AccessDeviceVo device) {
        return deviceService.addAccessDevice(device);
    }

    @ApiOperation("更新防疫门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/update")
    public void updateAccessDevice(@RequestBody AccessDeviceVo device) {
        if (!WrapperClassUtils.biggerThanLong(device.getId(), 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        deviceService.updateAccessDevice(device);
    }

    @ApiOperation("删除防疫门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/delete/{id}")
    public void deleteAccessDevice(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        deviceService.deleteAccessDevice(id);
    }

    @ApiOperation("获取防疫门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/get/{id}")
    public AccessDeviceVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return deviceService.findAccessDeviceById(id);
    }

    @ApiOperation("防疫门禁设备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/list")
    public PageResult<AccessDeviceVo> accessDeviceList(@RequestBody AccessDeviceSearchVo search) {
        return deviceService.listAccessDevice(search);
    }

    @ApiOperation("防疫门禁设备数据上报开关")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/switchDataReport")
    public void accessDeviceReportState(@Validated @RequestBody Covid19DeviceReportSwitcherVo switcher) {
        deviceService.switchAccessDeviceDataReport(switcher);
    }

    @ApiOperation("上报防疫门禁设备数据")
    @PostMapping("/report/covid19Access")
    public Covid19AccessResponse reportCovid19AccessData(@RequestBody Covid19AccessDeviceDataVo data) {
        return deviceService.reportCovid19AccessData(data);
    }

    @ApiOperation("添加支付终端")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payment/add")
    public Long addPaymentDevice(@Validated @RequestBody PaymentDeviceVo device) {
        return deviceService.addPaymentDevice(device);
    }

    @ApiOperation("更新支付终端")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payment/update")
    public void updatePaymentDevice(@Validated @RequestBody PaymentDeviceVo device) {
        if (!WrapperClassUtils.biggerThanLong(device.getId(), 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        deviceService.updatePaymentDevice(device);
    }

    @ApiOperation("删除支付终端")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payment/delete/{id}")
    public void delPaymentDevice(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new BusinessException("无效的id");
        }
        deviceService.delPaymentDevice(id);
    }

    @ApiOperation("删除支付终端")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payment/get/{id}")
    public PaymentDeviceVo findPaymentDeviceById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new BusinessException("无效的id");
        }
        return deviceService.findPaymentDeviceById(id);
    }

    @ApiOperation("支付终端列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payment/list")
    public List<PaymentDeviceVo> listPaymentDevice(@RequestBody PaymentDeviceSearchVo search) {
        return deviceService.listPaymentDevice(search);
    }

    @ApiOperation("信路通事件监听")
    @RequestMapping("/signalTone/event/receive")
    public SignalToneResponse receiveSignalToneEvent(HttpServletRequest request) throws IOException {
        Map<String, String> heads = new HashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            String values = request.getHeader(name);
            heads.put(name, values);
        }

        log.info("request url:" + request.getRequestURL());
        log.info("request remoteAddr:" + request.getRemoteAddr());
        log.info("request method:" + request.getMethod());
        log.info("request headers:" + JsonUtils.object2Json(heads));
        log.info("request queryString:" + request.getQueryString());

        // get 参数
        Map<String, String> params = new HashMap<>();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);
            if (paramValues.length == 1) {
                if (paramValues[0].length() != 0) {
                    params.put(paramName, paramValues[0]);
                }
            }
        }
        log.info("request params:" + JsonUtils.object2Json(params));

        // post 参数
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        StringBuilder bodyContent = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            bodyContent.append(line);
        }
        log.info("request post params: {}", bodyContent);

        SignalToneEvtVo evtVo = JsonUtils.json2Object(bodyContent.toString(), SignalToneEvtVo.class);

        if (evtVo != null) {
            rabbitTemplate.convertAndSend(SenoxConst.MQ.MQ_CAR_AC_DEVICE, evtVo);
            log.info("设备发送事件: {}", JsonUtils.object2Json(evtVo));
        }

        return SignalToneResponse.respSuccess();
    }

}
