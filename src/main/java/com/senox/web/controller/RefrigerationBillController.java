package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.cold.constant.RefrigerationFee;
import com.senox.cold.vo.*;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.context.AdminContext;
import com.senox.pm.constant.OrderType;
import com.senox.pm.vo.OrderItemDetailSearchVo;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.OneTimeFeeDepositSearchVo;
import com.senox.realty.vo.OneTimeFeeDepositVo;
import com.senox.web.config.SenoxProperties;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.RefrigerationBillReceiptExcelConvertor;
import com.senox.web.convert.RefrigerationDayBillConvertor;
import com.senox.web.convert.RefrigerationMonthBillConvertor;
import com.senox.web.service.*;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.senox.web.constant.SenoxConst.Export.COLUMN_SUM;

/**
 * <AUTHOR>
 * @date 2022/12/16 9:59
 */
@Api(tags = "冷藏账单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/cold/bill")
public class RefrigerationBillController extends BaseController {

    private final RefrigerationDayBillService dayBillService;
    private final RefrigerationMonthBillService monthBillService;
    private final OneTimeFeeService oneTimeFeeService;
    private final RefrigerationDayBillConvertor dayBillConvertor;
    private final RefrigerationMonthBillConvertor monthBillConvertor;
    private final OrderService orderService;
    private final AdminUserService adminUserService;
    private final SenoxProperties senoxProperties;
    private final RefrigerationBillReceiptExcelConvertor excelConvertor;

    @ApiOperation("添加日账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/add")
    public Long addDayBill(@Validated(Add.class) @RequestBody RefrigerationDayBillDto dto) {
        return dayBillService.addDayBill(dto);
    }

    @ApiOperation("更新日账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/update")
    public void updateDayBill(@Validated(Update.class) @RequestBody RefrigerationDayBillDto dto) {
        if (!WrapperClassUtils.biggerThanLong(dto.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        dayBillService.updateDayBill(dto);
    }

    @ApiOperation("导入日账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/import")
    public String importDayBill(@RequestPart("file")MultipartFile file) throws IOException {
        checkExcelFile(file);

        // 日账单
        List<RefrigerationDayBillExcelVo> excelList = loadDayBillFromExcel(file.getInputStream());
        return dayBillService.saveDayBillBatch(dayBillConvertor.excelVo2Dto(excelList));
    }

    @ApiOperation("删除日账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/delete")
    public void deleteDayBill(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        dayBillService.deleteDayBill(ids);
    }

    @ApiOperation("获取日账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/day/get/{id}")
    public RefrigerationDayBillVo findDayBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return dayBillService.findDayBillById(id);
    }

    @ApiOperation("日账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/list")
    public RefrigerationBillPage<RefrigerationDayBillVo> listDayBillPage(@Validated @RequestBody RefrigerationDayBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefrigerationBillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<RefrigerationDayBillVo> page = dayBillService.listDayBill(search);
        RefrigerationBillPage<RefrigerationDayBillVo> resultPage = new RefrigerationBillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        RefrigerationDayBillVo sumBill = dayBillService.sumDayBill(search);
        if (sumBill != null) {
            resultPage.setStorageCharge(DecimalUtils.nullToZero(sumBill.getStorageCharge()));
            resultPage.setDisposalCharge(DecimalUtils.nullToZero(sumBill.getDisposalCharge()));
            resultPage.setHandlingCharge(DecimalUtils.nullToZero(sumBill.getHandlingCharge()));
            resultPage.setPassingCharge(DecimalUtils.nullToZero(sumBill.getPassingCharge()));
            resultPage.setMembraneCharge(DecimalUtils.nullToZero(sumBill.getMembraneCharge()));
            resultPage.setSortingCharge(DecimalUtils.nullToZero(sumBill.getSortingCharge()));
            resultPage.setOvertimeCharge(DecimalUtils.nullToZero(sumBill.getOvertimeCharge()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumBill.getOtherCharge()));
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumBill.getDeliveryCharge()));
            resultPage.setPenaltyCharge(DecimalUtils.nullToZero(sumBill.getPenaltyCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumBill.getTotalCharge()));
        }

        return resultPage;
    }

    @ApiOperation("日账单导出")
    @GetMapping("/day/export")
    public void exportDayBill(HttpServletResponse response, RefrigerationDayBillSearchVo search) throws IOException {
        // list
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<RefrigerationDayBillVo> page = dayBillService.listDayBill(search);
        List<RefrigerationDayBillExcelVo> exportList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            exportList = new ArrayList<>(page.getTotalSize() + 1);
            exportList.addAll(dayBillConvertor.vo2ExcelVo(page.getDataList()));

            // sum
            RefrigerationDayBillVo sumVo = dayBillService.sumDayBill(search);
            RefrigerationDayBillExcelVo sumItem = dayBillConvertor.vo2ExcelVo(sumVo);
            sumItem.setCustomerSerial(SenoxConst.Export.COLUMN_SUM);
            exportList.add(sumItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REFRIGERATION_DAY_BILL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RefrigerationDayBillExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_REFRIGERATION_DAY_BILL)
                .doWrite(exportList);
    }

    @ApiOperation("生成月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/generate")
    public void generateMonthBill(@Validated @RequestBody RefrigerationBillMonthVo billMonth) {
        monthBillService.generateMonthBill(billMonth);
    }

    @ApiOperation("更新月账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/updateRemark")
    public void updateMonthBillRemark(@Validated @RequestBody RefrigerationBillRemarkVo billRemark) {
        monthBillService.updateMonthBillRemark(billRemark);
    }

    @ApiOperation("支付冷藏账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return monthBillService.payBill(payRequest);
    }

    @ApiOperation("打印冷藏账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        RefrigerationMonthBillVo bill = monthBillService.findMonthBillById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (bill.getStatus() != BillStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = monthBill2TollPrintVo(bill);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            TollSerialVo tollSerial = new TollSerialVo();
            tollSerial.setBillId(id);
            tollSerial.setBillSerial(result.getBillSerial());
            tollSerial.setOperatorId(getAdminUserId());
            monthBillService.saveBillSerial(tollSerial);
        }
        return result;
    }

    @ApiOperation("下发月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/send")
    public void sendMonthBill(@RequestBody RefrigerationBillSendVo send) {
        if ((send.getBillYear() == null || send.getBillMonth() == null)
                && CollectionUtils.isEmpty(send.getBillIds())) {
            throw new InvalidParameterException("未明下发的冷藏账单");
        }
        monthBillService.sendMonthBill(send);
    }

    @ApiOperation("月度账单微信通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/wechatNotify")
    public void notifyWechatBill(@RequestParam Integer year, @RequestParam Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            throw new InvalidParameterException();
        }
        monthBillService.notifyMonthBill(year, month);
    }


    @ApiOperation("删除月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/delete/{id}")
    public void deleteMonthBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        monthBillService.deleteMonthBill(id);
    }

    @ApiOperation("获取月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/get/{id}")
    public RefrigerationMonthBillVo findMonthBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return monthBillService.findMonthBillById(id);
    }

    @ApiOperation("月账单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/dayList/{id}")
    public List<RefrigerationDayBillVo> listMonthDayBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return monthBillService.listMonthDayBill(id);
    }

    @ApiOperation("月账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/list")
    public RefrigerationBillPage<RefrigerationMonthBillWithDepositVo> listMonthPage(@Validated @RequestBody RefrigerationMonthBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefrigerationBillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<RefrigerationMonthBillVo> page = monthBillService.listMonthBill(search);
        RefrigerationBillPage<RefrigerationMonthBillWithDepositVo> resultPage = new RefrigerationBillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(listMonthBillWithDeposit(page.getDataList()));

        // sum
        RefrigerationMonthBillVo sumBill = monthBillService.sumMonthBill(search);
        if (sumBill != null) {
            resultPage.setStorageCharge(DecimalUtils.nullToZero(sumBill.getStorageCharge()));
            resultPage.setDisposalCharge(DecimalUtils.nullToZero(sumBill.getDisposalCharge()));
            resultPage.setHandlingCharge(DecimalUtils.nullToZero(sumBill.getHandlingCharge()));
            resultPage.setPassingCharge(DecimalUtils.nullToZero(sumBill.getPassingCharge()));
            resultPage.setMembraneCharge(DecimalUtils.nullToZero(sumBill.getMembraneCharge()));
            resultPage.setSortingCharge(DecimalUtils.nullToZero(sumBill.getSortingCharge()));
            resultPage.setOvertimeCharge(DecimalUtils.nullToZero(sumBill.getOvertimeCharge()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumBill.getOtherCharge()));
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumBill.getDeliveryCharge()));
            resultPage.setPenaltyCharge(DecimalUtils.nullToZero(sumBill.getPenaltyCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumBill.getTotalCharge()));
            resultPage.setDiscountAmount(DecimalUtils.nullToZero(sumBill.getDiscountAmount()));
            resultPage.setBadDebtAmount(DecimalUtils.nullToZero(sumBill.getBadDebtAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumBill.getPaidAmount()));
            resultPage.setPenaltyPaid(DecimalUtils.nullToZero(sumBill.getPenaltyPaid()));
        }
        return resultPage;
    }

    @ApiOperation("导出月账单")
    @GetMapping("/month/export")
    public void exportMonthBill(HttpServletResponse response, RefrigerationMonthBillSearchVo search) throws IOException {
        // list
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<RefrigerationMonthBillVo> page = monthBillService.listMonthBill(search);
        List<RefrigerationMonthBillWithDepositVo> list = listMonthBillWithDeposit(page.getDataList());
        List<RefrigerationMonthBillExcelVo> exportList = Collections.emptyList();

        if (!CollectionUtils.isEmpty(list)) {
            int index = 1;
            exportList = new ArrayList<>(page.getTotalSize() + 1);
            for (RefrigerationMonthBillWithDepositVo item : list) {
                RefrigerationMonthBillExcelVo exportItem = monthBillConvertor.depositVo2ExcelVo(item);
                exportItem.setIndex(index++);
                exportList.add(exportItem);
            }

            // sum
            RefrigerationMonthBillVo sumVo = monthBillService.sumMonthBill(search);
            RefrigerationMonthBillExcelVo sumItem = monthBillConvertor.vo2ExcelVo(sumVo);
            sumItem.setCustomerSerial(SenoxConst.Export.COLUMN_SUM);
            exportList.add(sumItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REFRIGERATION_MONTH_BILL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RefrigerationMonthBillExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_REFRIGERATION_MONTH_BILL)
                .doWrite(exportList);
    }

    @ApiOperation("冷藏发票账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/receipt/list")
    public PageStatisticsResult<RefrigerationMonthBillVo ,RefrigerationMonthBillVo> listReceiptBill(@RequestBody RefrigerationMonthBillSearchVo search) {
        return monthBillService.listReceiptBill(search);
    }

    @ApiOperation("导出发票账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/exportReceipt")
    public void exportReceiptBill(HttpServletResponse response, RefrigerationMonthBillSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        PageStatisticsResult<RefrigerationMonthBillVo, RefrigerationMonthBillVo> result = monthBillService.listReceiptBill(search);
        List<RefrigerationBillReceiptExportVo> exportVoList = new ArrayList<>(result.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(result.getDataList())) {
            int serial = 1;
            for (RefrigerationMonthBillVo billVo : result.getDataList()) {
                RefrigerationBillReceiptExportVo exportVo = excelConvertor.toExcelVo(billVo);
                exportVo.setSerialNo(serial++);
                exportVoList.add(exportVo);
            }
        }

        exportVoList.add(sumRefrigerationBillReceiptExport(result.getStatistics()));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REFRIGERATION_BILL_RECEIPT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RefrigerationBillReceiptExportVo.class)
                .sheet(SenoxConst.Export.SHEET_REFRIGERATION_BILL_RECEIPT).doWrite(exportVoList);
    }

    private RefrigerationBillReceiptExportVo sumRefrigerationBillReceiptExport(RefrigerationMonthBillVo monthBillVo) {
        RefrigerationBillReceiptExportVo sumVo = new RefrigerationBillReceiptExportVo();
        sumVo.setCustomerSerial(COLUMN_SUM);
        sumVo.setStorageCharge(monthBillVo.getStorageCharge());
        sumVo.setDisposalCharge(monthBillVo.getDisposalCharge());
        sumVo.setHandlingCharge(monthBillVo.getHandlingCharge());
        sumVo.setPassingCharge(monthBillVo.getPassingCharge());
        sumVo.setMembraneCharge(monthBillVo.getMembraneCharge());
        sumVo.setSortingCharge(monthBillVo.getSortingCharge());
        sumVo.setOvertimeCharge(monthBillVo.getOvertimeCharge());
        sumVo.setOtherCharge(monthBillVo.getOtherCharge());
        sumVo.setTotalCharge(monthBillVo.getTotalCharge());
        return sumVo;
    }

    /**
     * 从输入流读取数据
     * @param in
     * @return
     */
    private List<RefrigerationDayBillExcelVo> loadDayBillFromExcel(InputStream in) {
        List<RefrigerationDayBillExcelVo> resultList = new ArrayList<>();
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<RefrigerationDayBillExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        EasyExcelFactory.read(in, RefrigerationDayBillExcelVo.class, readListener).sheet().doRead();
        return resultList;
    }

    /**
     * 带押金的账单列表
     * @param list
     * @return
     */
    private List<RefrigerationMonthBillWithDepositVo> listMonthBillWithDeposit(List<RefrigerationMonthBillVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<String> customers = list.stream().map(RefrigerationMonthBillVo::getCustomerSerial).distinct().collect(Collectors.toList());
        List<OneTimeFeeDepositVo> depositList = listRefrigerationDeposit(customers);
        return combineMonthBillWithDeposit(list, depositList);
    }

    /**
     * 冷藏押金列表
     * @param customers
     * @return
     */
    private List<OneTimeFeeDepositVo> listRefrigerationDeposit(List<String> customers) {
        OneTimeFeeDepositSearchVo depositSearch = new OneTimeFeeDepositSearchVo();
        depositSearch.setDepositFee(senoxProperties.getRefrigerationDepositFee());
        depositSearch.setCustomers(customers);
        return oneTimeFeeService.listOneTimeFeeDeposit(depositSearch);
    }

    /**
     * 合并押金
     * @param list
     * @param depositList
     * @return
     */
    private List<RefrigerationMonthBillWithDepositVo> combineMonthBillWithDeposit(List<RefrigerationMonthBillVo> list,
                                                                                  List<OneTimeFeeDepositVo> depositList) {
        Map<String, OneTimeFeeDepositVo> depositMap = depositList.stream()
                .collect(Collectors.toMap(OneTimeFeeDepositVo::getCustomerSerial, Function.identity()));

        List<RefrigerationMonthBillWithDepositVo> resultList = new ArrayList<>(list.size());
        for (RefrigerationMonthBillVo bill : list) {
            RefrigerationMonthBillWithDepositVo item = new RefrigerationMonthBillWithDepositVo(bill);
            OneTimeFeeDepositVo deposit = depositMap.get(item.getCustomerSerial());
            item.setDepositAmount(DecimalUtils.nullToZero(deposit == null ? null : deposit.getDepositAmount()));
            resultList.add(item);
        }
        return resultList;
    }

    private TollPrintVo monthBill2TollPrintVo(RefrigerationMonthBillVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getTollSerial());
        result.setPayer(bill.getCustomerName());
        result.setPayerDesc(bill.getCustomerSerial());
        result.setTollMan(bill.getTollMan());
        result.setTollTime(bill.getPaidTime());
        result.setTotalAmount(bill.getPaidAmount());

        List<OrderItemDetailVo> details = Collections.emptyList();
        if (DecimalUtils.isPositive(bill.getBadDebtAmount())) {
            OrderItemDetailSearchVo search = new OrderItemDetailSearchVo();
            search.setBillId(bill.getId());
            search.setOrderType(OrderType.REFRIGERATION.getValue());
            details = orderService.listOrderItemDetail(search);
        }
        // 费项明细
        List<TollPrintItemVo> list = Arrays.asList(
                newTollPrintItem(bill, details, RefrigerationFee.STORAGE), newTollPrintItem(bill, details, RefrigerationFee.DISPOSAL),
                newTollPrintItem(bill, details, RefrigerationFee.HANDLING), newTollPrintItem(bill, details, RefrigerationFee.PASSING),
                newTollPrintItem(bill, details, RefrigerationFee.MEMBRANE), newTollPrintItem(bill, details, RefrigerationFee.SORTING),
                newTollPrintItem(bill, details, RefrigerationFee.OVERTIME), newTollPrintItem(bill, details, RefrigerationFee.PENALTY),
                newTollPrintItem(bill, details, RefrigerationFee.OTHER), newTollPrintItem(bill, details, RefrigerationFee.DELIVERY)
        );
        if (!StringUtils.isBlank(bill.getRemark())) {
            list.get(0).setRemark(bill.getRemark());
        }
        result.setDetails(list.stream().filter(x -> !Objects.isNull(x)).collect(Collectors.toList()));
        return result;
    }

    private TollPrintItemVo newTollPrintItem(RefrigerationMonthBillVo bill, List<OrderItemDetailVo> details, RefrigerationFee fee) {
        BigDecimal amount = bill.getCharge(fee);
        if (DecimalUtils.equals(BigDecimal.ZERO, amount)) {
            return null;
        }
        if (fee == RefrigerationFee.PENALTY && bill.checkPenaltyIgnore()) {
            return null;
        }

        BigDecimal actualPaid = details.stream().filter(x -> Objects.equals(x.getFeeId(), fee.getFeeId()))
                .map(OrderItemDetailVo::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        TollPrintItemVo result = new TollPrintItemVo();
        result.setFee(fee.getName());
        result.setPrice(amount);
        result.setAmount(amount);
        if (DecimalUtils.isPositive(bill.getBadDebtAmount())) {
            result.setDiscountAmount(DecimalUtils.subtract(amount, actualPaid));
        }
        result.setTime(StringUtils.buildYearMonthStr(bill.getBillYear(), bill.getBillMonth()));
        return result;
    }


}
