package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.senox.car.constant.CarFee;
import com.senox.car.vo.*;
import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.CycleService;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.CycleBillExportVo;
import com.senox.web.vo.CycleExportVo;
import com.senox.web.vo.TollPrintItemVo;
import com.senox.web.vo.TollPrintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:32
 */
@Api(tags = "三轮车管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/cycle")
public class CycleController extends BaseController {

    private final CycleService cycleService;
    private final AdminUserService adminUserService;

    @ApiOperation("添加三轮车信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addCycle(@Validated(Add.class) @RequestBody CycleVo cycle) {
        return cycleService.addCycle(cycle);
    }

    @ApiOperation("更新三轮车信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateCycle(@Validated(Update.class) @RequestBody CycleVo cycle) {
        if (!WrapperClassUtils.biggerThanLong(cycle.getId(), 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        cycleService.updateCycle(cycle);
    }

    @ApiOperation("删除三轮车信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete")
    public void deleteCycle(@Validated @RequestBody CycleDeleteVo cycle) {
        if (!WrapperClassUtils.biggerThanLong(cycle.getId(), 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        cycleService.deleteCycle(cycle);
    }

    @ApiOperation("支付三轮车费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public void payCycleFee(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest, @RequestParam Boolean isFree) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        cycleService.payBill(payRequest, isFree);
    }

    @ApiOperation("撤销支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/revokePay/{id}")
    public void revokeCyclePay(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        cycleService.revokeCycleBillPayment(id);
    }

    @ApiOperation("获取三轮车信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public CycleVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return cycleService.findById(id);
    }

    @ApiOperation("三轮车列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageStatisticsResult<CycleVo, CycleVo> listCyclePage(@RequestBody CycleSearchVo searchVo) {
        return cycleService.listCyclePage(searchVo);
    }

    @ApiOperation("导出三轮车列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportCycle(HttpServletResponse response, CycleSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageStatisticsResult<CycleVo, CycleVo> pageResult = cycleService.listCyclePage(searchVo);
        List<CycleExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (CycleVo item : pageResult.getDataList()) {
                CycleExportVo exportItem = cycle2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
            exportList.add(cycle2SumVo(pageResult.getStatistics()));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_CYCLE, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcel.write(response.getOutputStream(), CycleExportVo.class)
                .sheet(SenoxConst.Export.SHEET_CYCLE)
                .doWrite(exportList);
    }

    @ApiOperation("打印三轮车收费票据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/print/{id}")
    public TollPrintVo printCycleBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        CycleVo bill = cycleService.findById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        BillStatus billStatus = BillStatus.fromValue(bill.getStatus());
        if (billStatus == null || billStatus == BillStatus.INIT) {
            throw  new BusinessException("账单未缴费");
        }

        // 票据号
        TollPrintVo result = cycleBill2TollPrintVo(bill);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            com.senox.common.vo.TollSerialVo tollSerial = new TollSerialVo();
            tollSerial.setBillId(id);
            tollSerial.setBillSerial(result.getBillSerial());
            tollSerial.setOperatorId(getAdminUserId());
            cycleService.updateCycleTollSerial(tollSerial);
        }
        return result;
    }

    @ApiOperation("三轮车账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/list")
    public CycleBillPageResult<CycleBillVo> listCycleBillPage(@RequestBody CycleBillSearchVo searchVo) {
        return cycleService.listCycleBillPage(searchVo);
    }

    @ApiOperation("导出三轮车账单")
    @GetMapping("/bill/export")
    public void exportCycleBill(HttpServletResponse response, CycleBillSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        CycleBillPageResult<CycleBillVo> pageResult = cycleService.listCycleBillPage(searchVo);
        List<CycleBillExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (CycleBillVo item : pageResult.getDataList()) {
                CycleBillExportVo exportItem = cycleBill2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumCycleBillItem(pageResult.getTotal(), pageResult.getTotalActual()));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_CYCLE_BILL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcel.write(response.getOutputStream(), CycleBillExportVo.class)
                .sheet(SenoxConst.Export.SHEET_CYCLE_BILL)
                .doWrite(exportList);

    }

    @ApiOperation("生成号牌")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/license/gen")
    public String genLicense(String prefix) {
        if (StringUtils.isBlank(prefix) || prefix.length() < 4) {
            throw new InvalidParameterException("无效的号牌前缀");
        }
        return cycleService.genLicense(prefix);
    }

    @ApiOperation("校验号牌")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/license/check")
    public boolean checkLicenseExist(String license, @RequestParam(required = false) Long id) {
        if (StringUtils.isBlank(license) || license.length() < 8) {
            throw new InvalidParameterException("无效的号牌");
        }
        return cycleService.checkLicenseExists(license, id);
    }

    @ApiOperation("换新车牌")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/renew")
    public Long renewCycle(@RequestBody CycleVo cycleVo) {
        return cycleService.renewCycle(cycleVo);
    }

    private CycleExportVo cycle2ExportVo(CycleVo cycle) {
        CycleExportVo result = new CycleExportVo();
        result.setLicense(cycle.getLicense());
        result.setOldLicense(cycle.getOldLicense());
        result.setAmount(cycle.getAmount());
        result.setActualAmount(cycle.getActualAmount());
        result.setRegion(cycle.getRegion());
        result.setRealtySerial(cycle.getRealtySerial());
        if (!StringUtils.isBlank(cycle.getDepartmentName()) && StringUtils.isBlank(cycle.getRealtyName())) {
            result.setRealtyName(cycle.getDepartmentName());
        } else {
            result.setRealtyName(cycle.getRealtyName());
        }
        result.setOwner(cycle.getOwner());
        result.setContact(cycle.getContact());
        result.setInputMan(cycle.getInputManName());
        result.setTollMan(cycle.getTollManName());
        result.setInputTime(cycle.getCreateTime());
        result.setPaidTime(cycle.getPaidTime());
        return result;
    }

    private CycleExportVo cycle2SumVo(CycleVo cycle) {
        CycleExportVo result = new CycleExportVo();
        result.setLicense(SenoxConst.Export.COLUMN_SUM);
        result.setAmount(cycle.getAmount());
        result.setActualAmount(cycle.getActualAmount());
        return result;
    }

    private CycleBillExportVo cycleBill2ExportVo(CycleBillVo bill) {
        CycleBillExportVo result = new CycleBillExportVo();
        BeanUtils.copyProperties(bill, result);
        if (!StringUtils.isBlank(bill.getDepartmentName()) && StringUtils.isBlank(bill.getRealtyName())) {
            result.setRealtyName(bill.getDepartmentName());
        }
        if (bill.getPayWay() != null) {
            PayWay payWay = PayWay.fromValue(bill.getPayWay());
            result.setPayWay(payWay.getDescription());
        }
        return result;
    }

    private CycleBillExportVo sumCycleBillItem(BigDecimal total, BigDecimal actualTotal) {
        CycleBillExportVo result = new CycleBillExportVo();
        result.setBillSerial("合计");
        result.setAmount(total == null ? BigDecimal.ZERO : total);
        result.setActualAmount(actualTotal == null ? BigDecimal.ZERO : actualTotal);
        return result;
    }

    private TollPrintVo cycleBill2TollPrintVo(CycleVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getBillSerial());
        result.setPayer(bill.getOwner());
        result.setPayerDesc(bill.getLicense());
        result.setPayer2(bill.getRealtyName());
        result.setTollMan(bill.getTollManName());
        result.setTollTime(bill.getPaidTime());

        BigDecimal productionFee = BooleanUtils.isTrue(bill.getProductionFee()) ? BigDecimal.valueOf(38) : BigDecimal.ZERO;
        BigDecimal installFee = BooleanUtils.isTrue(bill.getInstallationFee()) ? BigDecimal.valueOf(22) : BigDecimal.ZERO;
        result.setTotalAmount(DecimalUtils.add(productionFee, installFee));

        // 费项明细
        List<TollPrintItemVo> list = Arrays.asList(
                newTollPrintItem(productionFee, CarFee.CYCLE_LICENSE_PRODUCTION_FEE),
                newTollPrintItem(installFee, CarFee.CYCLE_LICENSE_INSTALLATION_FEE)
        );
        result.setDetails(list);

        return result;
    }

    private TollPrintItemVo newTollPrintItem(BigDecimal amount, CarFee fee) {
        if (!DecimalUtils.isPositive(amount)) {
            return null;
        }

        TollPrintItemVo result = new TollPrintItemVo();
        result.setFee(fee.getName());
        result.setPrice(amount);
        result.setAmount(amount);
        return result;
    }
}
