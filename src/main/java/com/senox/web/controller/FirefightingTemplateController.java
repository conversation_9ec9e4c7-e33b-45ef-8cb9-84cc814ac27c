package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.FireFightingTemplateSearchVo;
import com.senox.realty.vo.FirefightingFormTemplateVo;
import com.senox.realty.vo.FirefightingTemplateVo;
import com.senox.web.service.FireFightingTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 8:34
 */
@Api(tags = "消防模板")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/template")
public class FirefightingTemplateController extends BaseController {

    private final FireFightingTemplateService templateService;

    @ApiOperation("添加消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addTemplate(@Validated(Add.class) @RequestBody FirefightingTemplateVo template,
                            @RequestParam(required = false, defaultValue = "true") Boolean newVersion) {
        return templateService.addTemplate(template, newVersion);
    }

    @ApiOperation("更新消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateTemplate(@Validated(Update.class) @RequestBody FirefightingTemplateVo template) {
        templateService.updateTemplate(template);
    }

    @ApiOperation("启用消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/enable/{id}")
    public void enableTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        templateService.enableTemplate(id);
    }

    @ApiOperation("停用消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/disable/{id}")
    public void disableTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        templateService.disableTemplate(id);
    }

    @ApiOperation("删除消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        templateService.deleteTemplate(id);
    }

    @ApiOperation("获取消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/get/{id}")
    public FirefightingTemplateVo findTemplateById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return templateService.findTemplateById(id);
    }

    @ApiOperation("获取最新消防模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/latest")
    public FirefightingTemplateVo findLatestTemplateByCode(@RequestBody FireFightingTemplateSearchVo search) {
        if (StringUtils.isBlank(search.getCode())) {
            throw new InvalidParameterException();
        }

        return templateService.findLatestTemplateByCode(search);
    }

    @ApiOperation("消防模板页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<FirefightingTemplateVo> listTemplatePage(@RequestBody FireFightingTemplateSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return templateService.listTemplatePage(search);
    }

    @ApiOperation("添加消防表单模板")
    @PostMapping("/from/add")
    public Long addFormTemplate(@Validated @RequestBody FirefightingFormTemplateVo formTemplate) {
        return templateService.addFormTemplate(formTemplate);
    }

    @ApiOperation("修改消防表单模板")
    @PostMapping("/form/update")
    public void updateFormTemplate(@Validated @RequestBody FirefightingFormTemplateVo formTemplate) {
        if (!WrapperClassUtils.biggerThanLong(formTemplate.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        templateService.updateFormTemplate(formTemplate);
    }

    @ApiOperation("删除消防表单模板")
    @PostMapping("/form/delete/{id}")
    public void deleteFormTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        templateService.deleteFormTemplate(id);
    }

    @ApiOperation("获取消防表单模板")
    @GetMapping("/form/get/{id}")
    public FirefightingFormTemplateVo findFormTemplateById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return templateService.findFormTemplateById(id);
    }

    @ApiOperation("消防表单模板列表")
    @PostMapping("/form/list")
    public List<FirefightingFormTemplateVo> listFormTemplate(@RequestParam String form) {
        if (StringUtils.isBlank(form)) {
            throw new InvalidParameterException();
        }

        return templateService.listFormTemplate(form);
    }
}
