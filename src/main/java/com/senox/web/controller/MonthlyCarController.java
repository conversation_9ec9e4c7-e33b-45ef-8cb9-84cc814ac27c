package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.car.constant.CarFee;
import com.senox.car.constant.MonthlyCarSignType;
import com.senox.car.constant.MonthlyCarStatus;
import com.senox.car.vo.*;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderSerialVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.MonthlyCarService;
import com.senox.web.service.OrderService;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/1 10:12
 */
@Api(tags = "月卡车")
@RestController
@RequestMapping("/web/monthlyCar")
public class MonthlyCarController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MonthlyCarController.class);

    @Autowired
    private MonthlyCarService monthlyCarService;
    @Autowired
    private AdminUserService adminUserService;
    @Autowired
    private OrderService orderService;

    @ApiOperation("添加车型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/type/add")
    public Long addCarType(@Validated(Add.class) @RequestBody CarTypeVo carType) {
        if (carType.getAmount() == null || carType.getAmount().compareTo(BigDecimal.ZERO) < 1) {
            throw new InvalidParameterException("无效的车型金额");
        }
        return monthlyCarService.addCarType(carType);
    }

    @ApiOperation("更新车型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/type/update")
    public void updateCarType(@Validated(Update.class) @RequestBody CarTypeVo carType) {
        if (!WrapperClassUtils.biggerThanLong(carType.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        monthlyCarService.updateCarType(carType);
    }

    @ApiOperation("删除车型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/type/delete/{id}")
    public void deleteCarType(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        monthlyCarService.deleteCarType(id);
    }

    @ApiOperation("获取车型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/type/get/{id}")
    public CarTypeVo findTypeById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return monthlyCarService.findCarTypeById(id);
    }

    @ApiOperation("车型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/type/list")
    public PageResult<CarTypeVo> listType(@RequestBody CarTypeSearchVo search) {
        return monthlyCarService.listCarType(search);
    }

    @ApiOperation("添加月卡车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addMonthlyCar(@Validated @RequestBody MonthlyCarEditVo car) {
        // 校验签约类型
        validateMonthlyCarSignType(car);
        return monthlyCarService.addMonthlyCar(car);
    }

    @ApiOperation("更新月卡车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateMonthlyCar(@RequestBody MonthlyCarEditVo car) {
        if (!WrapperClassUtils.biggerThanLong(car.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        // 校验签约类型
        validateMonthlyCarSignType(car);
        monthlyCarService.updateMonthlyCar(car);
    }

    @ApiOperation("作废月卡车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel")
    public void cancelMonthlyCar(@Validated @RequestBody MonthlyCarCancelVo cancel) {
        monthlyCarService.cancelMonthlyCar(cancel);
    }

    @ApiOperation("删除月卡车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteMonthlyCar(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        monthlyCarService.deleteMonthlyCar(id);
    }

    @ApiOperation("支付月卡车账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payMonthlyCar(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return monthlyCarService.payMonthlyCar(payRequest);
    }

    @ApiOperation("撤销支付月卡车账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payRevoke/{id}")
    public void revokeMonthlyCarPay(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        monthlyCarService.revokeMonthlyCarPaid(id);
    }

    @ApiOperation("退费月卡车账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund")
    public OrderResultVo refundMonthlyCar(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        if (CollectionUtils.isEmpty(payRequest.getBillIds())) {
            throw new InvalidParameterException("无效的账单id");
        }

        if (payRequest.getPayWay() != PayWay.CASH) {
            throw new BusinessException("退费只支持现金结算");
        }

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        payRequest.setRefund(Boolean.TRUE);
        return monthlyCarService.payMonthlyCar(payRequest);
    }

    @ApiOperation("撤销月卡车账单退费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refundRevoke/{id}")
    public void revokeMonthlyCarRefund(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        monthlyCarService.revokeMonthlyCarRefund(id);
    }

    @ApiOperation("根据id查找月卡车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public MonthlyCarVo findMonthlyCarById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return monthlyCarService.findMonthlyCarById(id);
    }

    @ApiOperation("根据车牌查找最近的月卡车记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findRecentByCarNo")
    public MonthlyCarVo findRecentMonthlyCarByCarNo(@RequestParam String carNo) {
        if (StringUtils.isBlank(carNo)) {
            throw new InvalidParameterException("无效的车牌");
        }

        return monthlyCarService.findRecentMonthlyCarByCarNo(carNo);
    }

    @ApiOperation("打印票据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printMonthlyCarToll(@PathVariable Long id,
                                           @RequestParam(required = false) Boolean refund,
                                           @RequestParam(required = false) Boolean refreshSerial) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        MonthlyCarVo carBill = monthlyCarService.findMonthlyCarPayById(id);
        if (carBill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到月卡车账单");
        }

        // 打印信息
        TollPrintVo result = monthlyCar2TollPrintVo(carBill, BooleanUtils.isTrue(refund));
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            OrderSerialVo orderSerial = new OrderSerialVo();
            orderSerial.setOrderId(BooleanUtils.isTrue(refund) ? carBill.getRefundOrderId() : carBill.getTollOrderId());
            orderSerial.setProductId(carBill.getId());
            orderSerial.setBillSerial(result.getBillSerial());
            logger.info("update orderSerial {}", JsonUtils.object2Json(orderSerial));
            orderService.updateOrderSerial(orderSerial);
        }
        return result;
    }

    @ApiOperation("月卡车列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<MonthlyCarVo> listMonthlyCar(@RequestBody MonthlyCarSearchVo search) {
        return monthlyCarService.listMonthlyCar(search);
    }

    @ApiOperation("月卡车账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/billList")
    public MonthlyCarPageResult<MonthlyCarVo> listMonthlyCarBill(@RequestBody MonthlyCarSearchVo search) {
        return monthlyCarService.listMonthlyCarBill(search);
    }

    @ApiOperation("导出月卡车列表")
    @GetMapping("/export")
    public void exportMonthlyCar(HttpServletResponse response, MonthlyCarSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<MonthlyCarVo> pageResult = monthlyCarService.listMonthlyCar(search);
        List<MonthlyCarExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (MonthlyCarVo item : pageResult.getDataList()) {
                MonthlyCarExportVo exportItem = monthlyCar2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // export
       String fileName = String.format(SenoxConst.Export.FILE_MONTHLY_CAR, LocalDate.now());
       prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
       EasyExcelFactory.write(response.getOutputStream(), MonthlyCarExportVo.class)
               .sheet(SenoxConst.Export.SHEET_MONTHLY_CAR)
               .doWrite(exportList);
    }

    @ApiOperation("导出月卡车账单列表")
    @GetMapping("/exportBill")
    public void exportMonthlyCarBill(HttpServletResponse response, MonthlyCarSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        MonthlyCarPageResult<MonthlyCarVo> pageResult = monthlyCarService.listMonthlyCarBill(search);
        List<MonthlyCarBillExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (MonthlyCarVo item : pageResult.getDataList()) {
                MonthlyCarBillExportVo exportItem = monthlyCarBill2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }

            // 合计
            exportList.add(sumMonthlyCarBill(pageResult));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_MONTHLY_CAR_BILL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MonthlyCarBillExportVo.class)
                .sheet(SenoxConst.Export.SHEET_MONTHLY_CAR_BILL)
                .doWrite(exportList);
    }

    /**
     * 校验月卡车签约类型
     * @param car
     */
    private void validateMonthlyCarSignType(MonthlyCarEditVo car) {
        if (car.getSignType() == null) {
            return;
        }
        MonthlyCarSignType signType = MonthlyCarSignType.fromValue(car.getSignType());
        if (signType == null) {
            throw new InvalidParameterException("无效的月卡车签约类型");
        }
        if (signType == MonthlyCarSignType.TRANSFER && StringUtils.isBlank(car.getOldCarNo())) {
            throw new InvalidParameterException("无效的原车牌");
        }
    }

    /**
     * 月卡车收费票据
     * @param car
     * @param isRefund
     * @return
     */
    private TollPrintVo monthlyCar2TollPrintVo(MonthlyCarVo car, boolean isRefund) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(isRefund ? car.getRefundSerial() : car.getTollSerial());
        result.setPayer(car.getCustomerName());
        result.setPayer2(car.getRealtyName());

        MonthlyCarSignType signType = MonthlyCarSignType.fromValue(car.getSignType());
        result.setPayerDesc(signType == MonthlyCarSignType.TRANSFER
                ? String.format(SenoxConst.TITLE_MONTHLY_CAR_TRANS, car.getOldCarNo(), car.getCarNo()) : car.getCarNo());

        result.setTollMan(isRefund ? car.getRefundMan() : car.getTollMan());
        result.setTollTime(isRefund ? car.getRefundTime() : car.getTollTime());
        result.setTotalAmount(isRefund ? car.getRefundAmount() : car.getTotalAmount());

        // 费项明细
        List<TollPrintItemVo> list = new ArrayList<>(2);
        list.add(newTolePrintItem(car, CarFee.MONTHLY_RENT, isRefund));
        if (!isRefund && !DecimalUtils.equals(car.getServiceCharged(), BigDecimal.ZERO)) {
            list.add(newTolePrintItem(car, signType == MonthlyCarSignType.TRANSFER ? CarFee.MONTHLY_TRANS_CHARGED : CarFee.MONTHLY_CHARGED, Boolean.FALSE));
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 月卡车收费票据明细
     * @param car
     * @param carFee
     * @param isRefund
     * @return
     */
    private TollPrintItemVo newTolePrintItem(MonthlyCarVo car, CarFee carFee, boolean isRefund) {
        TollPrintItemVo result = new TollPrintItemVo();
        result.setFee(carFee.getName());
        if (carFee == CarFee.MONTHLY_CHARGED || carFee == CarFee.MONTHLY_TRANS_CHARGED) {
            result.setPrice(car.getServiceCharged());
            result.setAmount(car.getServiceCharged());

            result.setTime(DateUtils.formatDateTime(car.getCreateTime(), "yyyy-MM"));
            result.setRemark(car.getRemark());
        } else if (carFee == CarFee.MONTHLY_RENT) {
            result.setPrice(car.getPrice());
            result.setAmount(isRefund ? car.getRefundAmount() : DecimalUtils.subtract(car.getAmount(), car.getDiscountAmount()));
            result.setDiscountAmount(car.getDiscountPrice());
            result.setTime(car.getStartDate().toString().concat(" - ").concat(car.getEndDate().toString()));
            result.setRemark(car.getRemark());
        }
        return result;
    }

    /**
     * 月卡车转导出视图
     * @param car
     * @return
     */
    private MonthlyCarExportVo monthlyCar2ExportVo(MonthlyCarVo car) {
        MonthlyCarExportVo result = new MonthlyCarExportVo();
        result.setCarNo(car.getCarNo());
        result.setCarType(car.getCarTypeName());
        result.setStartDate(car.getStartDate());
        result.setEndDate(car.getEndDate());
        result.setServiceCharged(car.getServiceCharged());
        result.setPrice(car.getPrice());
        result.setAmount(car.getAmount());
        result.setFinalAmount(car.getFinalAmount());
        result.setTotalAmount(car.getTotalAmount());
        result.setOldCarNo(car.getOldCarNo());
        result.setCustomerName(car.getCustomerName());
        result.setCustomerIdno(car.getCustomerIdno());
        result.setCustomerContact(car.getCustomerContact());
        result.setRealtyAddress(car.getRealtyAddress());
        result.setInboard(BooleanUtils.isTrue(car.getInboard()) ? "场内" : "场外");
        result.setCreator(car.getCreator());
        result.setCreateTime(car.getCreateTime());
        result.setDays(getMonthlyCarRentDays(car.getStartDate()));
        result.setMonths(result.getDays() > 0 ? car.getMonths() - 1 : car.getMonths());

        MonthlyCarSignType signType = MonthlyCarSignType.fromValue(car.getSignType());
        if (signType != null) {
            result.setSignType(signType.getName());
        }
        return result;
    }

    /**
     * 月卡车账单转导出对象
     * @param car
     * @return
     */
    private MonthlyCarBillExportVo monthlyCarBill2ExportVo(MonthlyCarVo car) {
        MonthlyCarBillExportVo result = new MonthlyCarBillExportVo();
        result.setCarNo(car.getCarNo());
        result.setCarType(car.getCarTypeName());
        result.setStartDate(car.getStartDate());
        result.setEndDate(car.getEndDate());
        result.setServiceCharged(car.getServiceCharged());
        result.setPrice(car.getPrice());
        result.setAmount(car.getAmount());
        result.setFinalAmount(car.getFinalAmount());
        result.setTotalAmount(car.getTotalAmount());
        result.setOldCarNo(car.getOldCarNo());
        result.setCustomerName(car.getCustomerName());
        result.setCustomerIdno(car.getCustomerIdno());
        result.setCustomerContact(car.getCustomerContact());
        result.setRealtyAddress(car.getRealtyAddress());
        result.setInboard(BooleanUtils.isTrue(car.getInboard()) ? "场内" : "场外");
        result.setCreator(car.getCreator());
        result.setCreateTime(car.getCreateTime());
        result.setDays(getMonthlyCarRentDays(car.getStartDate()));
        result.setMonths(result.getDays() > 0 ? car.getMonths() - 1 : car.getMonths());
        result.setTollSerial(car.getTollSerial());
        result.setTollMan(car.getTollMan());
        result.setTollTime(car.getTollTime());
        result.setRefundAmount(car.getRefundAmount());
        result.setRefundSerial(car.getRefundSerial());
        result.setRefundMan(car.getRefundMan());
        result.setRefundTime(car.getRefundTime());
        result.setTotal(car.getTotal());

        MonthlyCarSignType signType = MonthlyCarSignType.fromValue(car.getSignType());
        if (signType != null) {
            result.setSignType(signType.getName());
        }
        MonthlyCarStatus status = MonthlyCarStatus.fromStatus(car.getStatus());
        result.setStatus(status.getName());

        return result;
    }

    /**
     * 月卡车账单合计
     * @param page
     * @return
     */
    private MonthlyCarBillExportVo sumMonthlyCarBill(MonthlyCarPageResult<MonthlyCarVo> page) {
        MonthlyCarBillExportVo result = new MonthlyCarBillExportVo();
        result.setCarNo("合计");
        result.setServiceCharged(page.getServiceCharged());
        result.setAmount(page.getAmount());
        result.setFinalAmount(page.getFinalAmount());
        result.setTotalAmount(page.getTotalAmount());
        result.setRefundAmount(page.getRefundAmount());
        result.setTotal(page.getTotal());
        return result;
    }

    /**
     * 月租天数（未满一个月）
     * @param date
     * @return
     */
    private int getMonthlyCarRentDays(LocalDate date) {
        int day = date.getDayOfMonth();
        if (day == 1) {
            return 0;
        }

        LocalDate monthLastDate = DateUtils.getFirstDateInMonth(date.plusMonths(1L)).minusDays(1L);
        return (int)DateUtils.getDaysBetween(date, monthLastDate) + 1;
    }
}
