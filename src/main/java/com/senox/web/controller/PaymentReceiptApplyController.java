package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.web.service.PaymentReceiptApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-7-21
 */
@Api(tags = "发票申请")
@RestController
@RequestMapping("/web/payment/receipt/apply")
@RequiredArgsConstructor
public class PaymentReceiptApplyController extends BaseController {
    private final PaymentReceiptApplyService receiptApplyService;

    @ApiOperation("申请列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<ReceiptApplyVo> list(@RequestBody ReceiptApplySearchVo search) {
        return receiptApplyService.list(search);
    }

    @ApiOperation("根据单据编号查询发票申请")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findBySerialNo/{serialNo}")
    public ReceiptApplyVo findBySerialNo(@PathVariable String serialNo) {
        return receiptApplyService.findBySerialNo(serialNo);
    }

    @ApiOperation("查询抬头最近发票申请")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/top/{taxHeader}")
    public ReceiptApplyVo top(@PathVariable String taxHeader) {
        return receiptApplyService.top(taxHeader);
    }
}
