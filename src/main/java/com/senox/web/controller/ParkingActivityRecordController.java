package com.senox.web.controller;

import com.senox.car.vo.ParkingActivityRecordSearchVo;
import com.senox.car.vo.ParkingActivityRecordVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.ParkingActivityRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@Api(tags = "车场活动记录")
@RestController
@RequestMapping("/web/parking/activity/record")
@RequiredArgsConstructor
public class ParkingActivityRecordController {
    private final ParkingActivityRecordService activityRecordService;

    @ApiOperation("添加活动记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addActivityRecord(@RequestBody ParkingActivityRecordVo activityRecordVo) {
        return activityRecordService.addActivityRecord(activityRecordVo);
    }

    @ApiOperation("更新活动记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateActivityRecord(@RequestBody ParkingActivityRecordVo activityRecordVo) {
        activityRecordService.updateActivityRecord(activityRecordVo);
    }

    @ApiOperation("根据id获取活动记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public ParkingActivityRecordVo findActivityRecordById(@PathVariable Long id) {
        return activityRecordService.findActivityRecordById(id);
    }

    @ApiOperation("根据id删除活动记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteById/{id}")
    public void deleteActivityRecordById(@PathVariable Long id) {
        activityRecordService.deleteActivityRecordById(id);
    }

    @ApiOperation("活动记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ParkingActivityRecordVo> activityRecordListPage(@RequestBody ParkingActivityRecordSearchVo searchVo) {
        return activityRecordService.activityRecordListPage(searchVo);
    }
}
