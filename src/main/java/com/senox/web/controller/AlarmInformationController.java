package com.senox.web.controller;

import com.senox.car.vo.AlarmInformationSearchVo;
import com.senox.car.vo.AlarmInformationVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.AlarmInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/8/9 10:55
 */
@RestController
@RequestMapping("/web/alarm")
@Api(tags = "停车告警信息")
public class AlarmInformationController extends BaseController{

    @Autowired
    private AlarmInformationService alarmInformationService;

    @ApiOperation("更新告警信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateAlarmInformation(@Validated(Update.class) @RequestBody AlarmInformationVo alarmInformationVo) {
        alarmInformationVo.setHandler(getAdminUserId());
        alarmInformationService.updateAlarmInformation(alarmInformationVo);
    }

    @ApiOperation("根据id获取告警信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public AlarmInformationVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return alarmInformationService.findById(id);
    }

    @ApiOperation("告警信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<AlarmInformationVo> list(@RequestBody AlarmInformationSearchVo search) {
        return alarmInformationService.list(search);
    }
}
