package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import com.senox.web.service.RealtyBillWeService;
import com.senox.web.vo.RealtyBillWePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:40
 */
@Api(tags = "水电账单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/realty/bill/we")
public class RealtyBillWeController extends BaseController {

    private final RealtyBillWeService billWeService;

    @ApiOperation("生成/更新水电账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generateBill(@Validated @RequestBody BillMonthVo month) {
        billWeService.generate(month);
    }

    @ApiOperation("更新水电账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBill(@RequestBody RealtyBillWeVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        billWeService.updateBill(bill);
    }

    @ApiOperation("删除水电账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        billWeService.deleteBill(id);
    }

    @ApiOperation("同步水电账单至应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/syncOne/{id}")
    public void syncWeBill2RealtyBill(@PathVariable Long id, @RequestBody BillMonthVo billMonth) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        billWeService.syncWeBill2RealtyBill(id, billMonth);
    }

    @ApiOperation("获取水电账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RealtyBillWeVo findBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billWeService.findBillById(id);
    }

    @ApiOperation("水电账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public RealtyBillWePage<RealtyBillWeVo> listPage(@RequestBody RealtyBillWeSearchVo search) {
        return billWeService.listPage(search);
    }
}
