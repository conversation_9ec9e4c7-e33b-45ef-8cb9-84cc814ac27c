package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.TaxCategory;
import com.senox.pm.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.ReceiptOrderExportConvertor;
import com.senox.web.service.ReceiptService;
import com.senox.web.vo.ReceiptAuditVo;
import com.senox.web.vo.ReceiptOrderExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/19 8:58
 */
@Api(tags = "发票")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/receipt")
public class ReceiptController extends BaseController {

    private final ReceiptService receiptService;

    @ApiOperation("添加发票抬头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/header/add")
    public Long addHeader(@Validated @RequestBody TaxHeaderVo header) {
        // 校验发票抬头
        checkTaxHeader(header.getCategory(), header.getSerial());

        return receiptService.addHeader(header);
    }

    @ApiOperation("更新发票抬头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/header/update")
    public void updateHeader(@Validated @RequestBody TaxHeaderVo header) {
        if (!WrapperClassUtils.biggerThanLong(header.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        // 校验发票抬头
        checkTaxHeader(header.getCategory(), header.getSerial());
        receiptService.updateHeader(header);
    }

    @ApiOperation("删除发票抬头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/header/delete")
    public void delHeader(@RequestBody TaxHeaderBatchVo headerBatch) {
        if (CollectionUtils.isEmpty(headerBatch.getIds())) {
            throw new InvalidParameterException();
        }

        receiptService.delHeader(headerBatch);
    }

    @ApiOperation("获取发票抬头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/header/get/{id}")
    public TaxHeaderVo getTaxHeader(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        TaxHeaderVo result = receiptService.findHeaderById(id);
        return result != null && Objects.equals(result.getOpenid(), StringUtils.EMPTY) ? result : null;
    }

    @ApiOperation("发票抬头列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/header/list")
    public List<TaxHeaderVo> listHeader(@RequestBody TaxHeaderSearchVo search) {
        return receiptService.listHeader(search);
    }

    private void checkTaxHeader(Integer category, String serial) {
        TaxCategory taxCategory = TaxCategory.fromValue(category);
        if (taxCategory == null) {
            throw new InvalidParameterException();
        }

        if (taxCategory == TaxCategory.COMPANY && StringUtils.isBlank(serial)) {
            throw new InvalidParameterException();
        }
    }

}
