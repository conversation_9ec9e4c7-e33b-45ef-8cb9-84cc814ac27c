package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.CustomerService;
import com.senox.web.vo.CustomerExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/15 14:04
 */
@Api(tags = "客户")
@RestController
@RequestMapping("/web/customer")
public class CustomerController extends BaseController {

    @Autowired
    private CustomerService customerService;

    @ApiOperation("添加客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addCustomer(@Validated({Add.class}) @RequestBody CustomerVo customer) {
        return customerService.addCustomer(customer);
    }

    @ApiOperation("更新客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateCustomer(@Validated({Update.class}) @RequestBody CustomerVo customer) {
        if (customer.getId() < 1L) {
            throw new InvalidParameterException();
        }
        customerService.updateCustomer(customer);
    }

    @ApiOperation("删除客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteCustomer(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        customerService.deleteCustomer(id);
    }

    @ApiOperation("根据id获取客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public CustomerVo getCustomer(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return customerService.findById(id);
    }

    @ApiOperation("根据证件号获取客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByIdcard")
    public CustomerVo getCustomerByIdcard(@RequestParam String idcard) {
        if (StringUtils.isBlank(idcard)) {
            throw new InvalidParameterException("无效的证件号");
        }
        return customerService.findByIdcard(idcard);
    }

    @ApiOperation("客户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<CustomerVo> listCustomer(@RequestBody CustomerSearchVo search) {
        return customerService.listCustomerPage(search);
    }

    @ApiOperation("导出")
    @GetMapping("/export")
    public void exportCustomers(HttpServletResponse response, CustomerSearchVo search) throws IOException {
        // list
        List<CustomerVo> customerList = customerService.listCustomer(search);
        List<CustomerExportVo> exportList = customerList == null ? Collections.emptyList()
                : customerList.stream().map(this::customer2ExportVo).collect(Collectors.toList());

        // export
        String fileName = String.format(SenoxConst.Export.FILE_CUSTOMER, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcel.write(response.getOutputStream(), CustomerExportVo.class)
                .sheet(SenoxConst.Export.SHEET_CUSTOMER)
                .doWrite(exportList);
    }

    /**
     * 客户转导出对象
     * @param customer
     * @return
     */
    private CustomerExportVo customer2ExportVo(CustomerVo customer) {
        CustomerExportVo result = new CustomerExportVo();
        BeanUtils.copyProperties(customer, result);
        return result;
    }
}
