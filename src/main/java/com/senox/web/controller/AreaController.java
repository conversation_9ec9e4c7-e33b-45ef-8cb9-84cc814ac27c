package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.user.vo.AreaVo;
import com.senox.web.service.AreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/14 16:58
 */
@Api(tags = "基本信息 - 地区")
@RestController
@RequestMapping("/web/dictionary/area")
public class AreaController {

    @Autowired
    private AreaService areaService;

    @ApiOperation("添加地区")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addArea(@Validated({Add.class}) @RequestBody AreaVo area) {
        return areaService.addArea(area);
    }

    @ApiOperation("更新地区")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateArea(@Validated({Update.class}) @RequestBody AreaVo area) {
        if (area.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        areaService.updateArea(area);
    }

    @ApiOperation("删除地区")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteArea(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        areaService.delete(id);
    }

    @ApiOperation("获取地区信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/get/{id}")
    public AreaVo getArea(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return areaService.findById(id);
    }

    @ApiOperation("省/直辖市列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/province/list")
    public List<AreaVo> listProvince() {
        return areaService.listProvince();
    }

    @ApiOperation("市列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/city/list/{id}")
    public List<AreaVo> listCity(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return areaService.listCity(id);
    }

}
