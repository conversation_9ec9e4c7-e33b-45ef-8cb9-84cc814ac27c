package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.AuditVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import com.senox.user.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.MerchantExcelConverter;
import com.senox.web.service.BicycleChargesService;
import com.senox.web.service.MerchantService;
import com.senox.web.vo.MerchantExcelVo;
import com.senox.web.vo.MerchantExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-11-2
 */
@Api(tags = "商户")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/merchant")
public class MerchantController extends BaseController{
    private final MerchantService merchantService;
    private final BicycleChargesService chargesService;
    private final MerchantExcelConverter merchantExcelConverter;

    @ApiOperation("添加商户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addMerchant(@Validated @RequestBody MerchantVo merchant) {
        return merchantService.addMerchant(merchant);
    }

    @ApiOperation("更新商户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateMerchant(@RequestBody MerchantVo merchant) {
        if (!WrapperClassUtils.biggerThanLong(merchant.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        merchantService.update(merchant);
    }

    @ApiOperation("根据手机号查询商户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByContact")
    public List<MerchantVo> findByContact(@RequestParam String contact) {
        if (StringUtils.isBlank(contact)) {
            throw new InvalidParameterException();
        }
        return merchantService.findByContact(contact);
    }

    @ApiOperation("删除商户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteMerchant(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        merchantService.delete(id);
    }

    @ApiOperation("根基id获取商户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public MerchantVo findMerchantById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return merchantService.findById(id);
    }

    @ApiOperation("商户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<MerchantVo> listMerchant(@RequestBody MerchantSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return merchantService.list(search);
    }

    @ApiOperation("商户地址保存")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/address/save")
    public void saveMerchantAddress(@RequestBody MerchantAddressVo addressVo) {
        merchantService.saveMerchantAddress(addressVo);
    }

    @ApiOperation("商户地址获取")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/address/get/{id}")
    public MerchantAddressVo findMerchantAddress(@PathVariable Long id) {
        return merchantService.findMerchantAddress(id);
    }

    @ApiOperation("商户地址删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/address/delete/{id}")
    public void deleteMerchantAddress(@PathVariable Long id) {
        merchantService.deleteMerchantAddress(id);
    }

    @ApiOperation("根据商户id获取地址")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/address/listByMerchantId")
    public List<MerchantAddressVo> listByMerchantId(@RequestParam Long merchantId) {
        return merchantService.listByMerchantId(merchantId);
    }

    @ApiOperation("商户地址分页")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/address/page")
    public PageResult<MerchantAddressVo> pageMerchantAddress(@RequestBody MerchantAddressSearchVo searchVo) {
        return merchantService.pageMerchantAddress(searchVo);
    }

    @ApiOperation("导出商户")
    @GetMapping("/export")
    public void exportMerchant(HttpServletResponse response, MerchantSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageResult<MerchantVo> page = merchantService.list(searchVo);

        List<MerchantExportVo> exportList = page == null ? Collections.emptyList() : new ArrayList<>(page.getTotalSize());
        List<BicycleChargesVo> chargesVos = listCharges();
        if (page != null && !CollectionUtils.isEmpty(page.getDataList())) {
            for (int index = 0; index < page.getDataList().size(); index++) {
                MerchantVo merchantVo = page.getDataList().get(index);
                MerchantExportVo exportVo = newMerchantExportVo(merchantVo, chargesVos);
                exportVo.setSerialNo(index + 1);
                exportList.add(exportVo);
            }
        }

        Set<String> excludeColumnNames = new HashSet<>();
        //不显示列
        setPropertyExclude(excludeColumnNames, searchVo);

        String fileName = String.format(SenoxConst.Export.FILE_MERCHANT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MerchantExportVo.class).excludeColumnFiledNames(excludeColumnNames)
                .sheet(SenoxConst.Export.SHEET_MERCHANT)
                .doWrite(exportList);
    }

    private static void setPropertyExclude(Set<String> excludeColumnNames, MerchantSearchVo search) {
        if (BooleanUtils.isFalse(search.getContainBicycleAuth())) {
            excludeColumnNames.add("settlePeriod");
            excludeColumnNames.add("bicycleAuth");
            excludeColumnNames.add("bicycleCharges");
        }
        if (BooleanUtils.isFalse(search.getContainRc())) {
            excludeColumnNames.add("rcAuth");
            excludeColumnNames.add("rcTaxHeader");
            excludeColumnNames.add("rcSerial");
        }
        if (BooleanUtils.isFalse(search.getContainDryAuth())) {
            excludeColumnNames.add("dryAuth");
        }
        if (BooleanUtils.isFalse(search.getContainUnloadingAuth())) {
            excludeColumnNames.add("unloadingAuth");
        }
    }

    @ApiOperation("导入商户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public List<MerchantVo> importMerchant(@RequestPart("file") MultipartFile file, MerchantImportAuthVo authVo) throws IOException {
        checkExcelFile(file);
        List<MerchantExcelVo> excelVoList = EasyExcelFactory.read(file.getInputStream()).head(MerchantExcelVo.class).sheet().doReadSync();
        if (CollectionUtils.isEmpty(excelVoList)) {
            throw new BusinessException("未读取到数据！");
        }
        excelVoList.forEach(item -> {
            if ((BooleanUtils.isTrue(authVo.getRc()) || BooleanUtils.isTrue(authVo.getDuoduo())) && StringUtils.isBlank(item.getRcSerial())) {
                throw new BusinessException("冷藏商户或者云仓商户必须有编码！商户名是：" + item.getName());
            }
            if (BooleanUtils.isTrue(authVo.getDuoduo())) {
                item.setDuoduo(authVo.getDuoduo());
            }
            if (BooleanUtils.isTrue(authVo.getBicycleAuth())) {
                item.setBicycleAuth(authVo.getBicycleAuth());
            }
            if (BooleanUtils.isTrue(authVo.getDryAuth())) {
                item.setDryAuth(authVo.getDryAuth());
            }
        });
        List<MerchantVo> merchantVos = merchantExcelConverter.toVoList(excelVoList);
        return merchantService.batchAddMerchant(merchantVos);
    }

    @ApiOperation("新增权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/auth/apply/add")
    public Long addAuthApply(@Validated @RequestBody MerchantAuthApplyEditVo apply) {
        return merchantService.addAuthApply(apply);
    }

    @ApiOperation("更新权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/auth/apply/update")
    public void updateAuthApply(@Validated @RequestBody MerchantAuthApplyEditVo apply) {
        if (!WrapperClassUtils.biggerThanLong(apply.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        merchantService.updateAuthApply(apply);
    }

    @ApiOperation("删除权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/auth/apply/delete/{id}")
    public void deleteAuthApply(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        merchantService.deleteAuthApply(id);
    }

    @ApiOperation("权限申请审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/auth/apply/audit")
    public MerchantVo auditAuthApply(@Validated @RequestBody AuditVo audit) {
        return merchantService.auditAuthApply(audit);
    }

    @ApiOperation("获取权限申请详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/auth/apply/get/{id}")
    public MerchantAuthApplyVo findAuthApplyById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return merchantService.findAuthApplyById(id);
    }

    @ApiOperation("权限申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/auth/apply/list")
    public PageResult<MerchantAuthApplyListVo> applyList(@RequestBody MerchantAuthApplySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return merchantService.applyList(search);
    }

    private MerchantExportVo newMerchantExportVo(MerchantVo merchantVo, List<BicycleChargesVo> chargesVos) {
        MerchantExportVo exportVo = new MerchantExportVo();
        exportVo.setName(merchantVo.getName());
        exportVo.setContact(merchantVo.getContact());
        exportVo.setIdcard(merchantVo.getIdcard());
        exportVo.setAddress(merchantVo.getAddress());
        exportVo.setRcSerial(merchantVo.getRcSerial());
        exportVo.setRcTaxHeader(merchantVo.getRcTaxHeader());
        exportVo.setSettlePeriod(merchantVo.getSettlePeriod().getName());
        exportVo.setBicycleAuth(BooleanUtils.isTrue(merchantVo.getBicycleAuth()) ? "是" : "否");
        exportVo.setDryAuth(BooleanUtils.isTrue(merchantVo.getDryAuth()) ? "是" : "否");
        exportVo.setUnloadingAuth(BooleanUtils.isTrue(merchantVo.getUnloadingAuth()) ? "是" : "否");
        exportVo.setRcAuth(!StringUtils.isBlank(merchantVo.getRcSerial()) ? "是" : "否");
        if (merchantVo.getBicycleChargesId() == 0L) {
            BicycleChargesVo chargesVo = chargesVos.stream().filter(x -> BooleanUtils.isTrue(x.getDefaultEffective())).findFirst().orElse(null);
            exportVo.setBicycleCharges(chargesVo == null ? StringUtils.EMPTY : chargesVo.getName());
        } else {
            BicycleChargesVo chargesVo = chargesVos.stream().filter(x -> Objects.equals(x.getId(), merchantVo.getBicycleChargesId())).findFirst().orElse(null);
            exportVo.setBicycleCharges(chargesVo == null ? StringUtils.EMPTY : chargesVo.getName());
        }
        return exportVo;
    }

    private List<BicycleChargesVo> listCharges() {
        BicycleChargesSearchVo searchVo = new BicycleChargesSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(30);
        searchVo.setStatus(BicycleStatus.ENABLED);
        PageResult<BicycleChargesVo> pageResult = chargesService.chargesListPage(searchVo);
        if (pageResult == null) {
            return Collections.emptyList();
        }
        return pageResult.getDataList();
    }

}
