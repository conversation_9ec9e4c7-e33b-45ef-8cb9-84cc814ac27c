package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.*;
import com.senox.web.config.ExcelFillCellMergeStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.CompanyService;
import com.senox.web.service.DiningInformationService;
import com.senox.web.service.EmployeeService;
import com.senox.web.service.HolidayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

@Api(tags = "报餐")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/user/diningInformation")
public class DiningInformationController extends BaseController{

    private final DiningInformationService diningInformationService;

    @Autowired
    private CompanyService companyService;
    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private HolidayService holidayService;


    @ApiOperation("导入报餐记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void importDiningInformation(@RequestPart("file") MultipartFile file) throws IOException {
//        checkExcelFile(file);
        List<DiningInformationVo> diningInformationVos = loadDiningInformationFromCsv(file.getInputStream());

        //筛选在指定时间的数据
        diningInformationVos = diningInformationVos.stream()
                .filter(x -> x.getMealTime().isAfter(LocalTime.of(11,30,00))&&(x.getMealTime().isBefore(LocalTime.of(12,30,00))))
                .collect(Collectors.toList());
        diningInformationService.addBatchDiningInformation(diningInformationVos);
    }

    @ApiOperation("添加报餐记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addBatchDiningInformation(@RequestBody DiningInformationVo diningInformationVo) {
        //筛选在指定时间的数据
        if (diningInformationVo.getMealTime().isAfter(LocalTime.of(11,30,00)) && diningInformationVo.getMealTime().isBefore(LocalTime.of(12,30,00))){
            diningInformationService.addDiningInformation(diningInformationVo);
        }
    }

    @ApiOperation("更新报餐记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateDiningInformation(@Validated({Update.class}) @RequestBody DiningInformationVo diningInformationVo) {
        diningInformationService.updateDiningInformation(diningInformationVo);
    }

    @ApiOperation("报餐列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<DiningInformationVo> listCustomer(@RequestBody DiningInformationSearchVo search) {
        return diningInformationService.listDiningInformation(search);
    }

    @ApiOperation("根据id获取报餐记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public DiningInformationVo getDiningInformation(@PathVariable Long id) {
        return diningInformationService.getDiningInformation(id);
    }

    @ApiOperation("删除报餐记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteDiningInformation(@PathVariable Long id) {
        diningInformationService.deleteDiningInformation(id);
    }

    @ApiOperation("导出")
    @GetMapping("/export")
    public void exportDiningInformationMonthDetail(HttpServletResponse response, int year, int month) throws IOException {
        // 工作日
        List<LocalDate> weekdays = prepareWeekdays(LocalDate.of(year, month, 1));
        // 公司列表
        List<CompanyVo> companyList = companyService.listCompany();

        // export
        String yearMonth = weekdays.get(0).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String fileName = String.format(SenoxConst.Export.FILE_DINING_INFORMATION, yearMonth);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));


        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        for (int index = 0; index < companyList.size(); index++) {
            // export content
            List<List<Object>> contents = buildContent(companyList.get(index), weekdays);
            // export sheet
            WriteSheet sheet = buildSheet(index, companyList.get(index), weekdays, contents.size());
            excelWriter.write(contents, sheet);
        }
        // 关闭流
        excelWriter.finish();
    }

    /**
     * 构建表单
     * @param sheetIndex
     * @param company
     * @param weekdays
     * @return
     */
    private WriteSheet buildSheet(int sheetIndex, CompanyVo company, List<LocalDate> weekdays, int contentRows) {
        List<List<String>> headTitles = buildTitle(company, weekdays);
        int lastRowIndex = headTitles.get(0).size() + contentRows;
        return EasyExcel.writerSheet(sheetIndex, company.getCompanyName())
                .head(headTitles)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(lastRowIndex - 1, lastRowIndex - 1, 0, 2))
                .build();

    }

    /**
     * excel 标题
     * @param company
     * @param weekdays
     * @return
     */
    private List<List<String>> buildTitle(CompanyVo company, List<LocalDate> weekdays) {
        List<List<String>> result = new ArrayList<>(weekdays.size() + 4);
        // 标题
        String title = String.format(SenoxConst.Export.TITLE_DINING_INFORMATION, weekdays.get(0).getYear(), company.getCompanyName(), weekdays.get(0).getMonthValue());

        // data header
        result.add(Arrays.asList(title, SenoxConst.Export.COLUMN_BOOKING_MEAL_SERIAL, SenoxConst.Export.COLUMN_BOOKING_MEAL_SERIAL));
        result.add(Arrays.asList(title, SenoxConst.Export.COLUMN_BOOKING_MEAL_NAME, SenoxConst.Export.COLUMN_BOOKING_MEAL_NAME));
        result.add(Arrays.asList(title, SenoxConst.Export.COLUMN_BOOKING_MEAL_COMPANY, SenoxConst.Export.COLUMN_BOOKING_MEAL_COMPANY));
        result.add(Arrays.asList(title, SenoxConst.Export.COLUMN_BOOKING_MEAL_DEPT, SenoxConst.Export.COLUMN_BOOKING_MEAL_DEPT));
        for (LocalDate date : weekdays) {
            result.add(Arrays.asList(title, String.valueOf(date.getDayOfMonth()), date.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.CHINA)));
        }
        result.add(Arrays.asList(title, SenoxConst.Export.COLUMN_SUM, SenoxConst.Export.COLUMN_SUM));
        return result;
    }

    /**
     * 导出内容
     * @param company
     * @param weekdays
     * @return
     */
    private List<List<Object>> buildContent(CompanyVo company, List<LocalDate> weekdays) {
        // 员工列表
        EmployeeSearchVo employeeSearch = new EmployeeSearchVo();
        employeeSearch.setCompanyName(company.getCompanyName());
        employeeSearch.setPageNo(1);
        employeeSearch.setPageSize(500);
        List<EmployeeVo> employees = employeeService.listEmployeePage(employeeSearch).getDataList();

        // 报餐信息
        DiningInformationSearchVo diningInformationSearch = new DiningInformationSearchVo();
        diningInformationSearch.setPageNo(1);
        diningInformationSearch.setPageSize(5000);
        diningInformationSearch.setCompany(company.getCompanyName());
        diningInformationSearch.setDateStart(weekdays.get(0));
        diningInformationSearch.setDateEnd(weekdays.get(weekdays.size() - 1));
        List<DiningInformationVo> diningInformationVos = diningInformationService.listDiningInformation(diningInformationSearch).getDataList();

        // 表格套娃
        List<List<Object>> resultList = new ArrayList<>(employees.size() + 1);
        for (int i = 0; i < employees.size(); i++) {
            // user data
            EmployeeVo employee = employees.get(i);
            List<Object> content = new ArrayList<>(weekdays.size() + 4);
            content.add(i + 1);
            content.add(employee.getUsername());
            content.add(employee.getCompanyName());
            content.add(employee.getDepartmentName());

            for (LocalDate date : weekdays) {
                final LocalDate dateItem = date;
                Optional<DiningInformationVo> bookingOp = diningInformationVos.stream()
                        .filter(x -> Objects.equals(x.getEmployeeName(), employee.getUsername()) && Objects.equals(x.getMealDate(), dateItem))
                        .findFirst();
                if (bookingOp.isPresent() && WrapperClassUtils.biggerThanInt(bookingOp.get().getDining(), 0)) {
                    content.add(bookingOp.get().getDining());
                } else {
                    content.add(StringUtils.EMPTY);
                }
            }
            content.add(diningInformationVos.stream().filter(x -> Objects.equals(x.getEmployeeName(), employee.getUsername())).map(DiningInformationVo::getDining).reduce(0, Integer::sum));

            resultList.add(content);
        }

        // 合计栏
        List<Object> summaryRow = new ArrayList<>(weekdays.size() + 4);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(StringUtils.EMPTY);
        for (LocalDate date : weekdays) {
            final LocalDate dateItem = date;
            summaryRow.add(diningInformationVos.stream().filter(x -> Objects.equals(x.getMealDate(), dateItem)).map(DiningInformationVo::getDining).reduce(0, Integer::sum));
        }
        summaryRow.add(diningInformationVos.stream().map(DiningInformationVo::getDining).reduce(0, Integer::sum));

        resultList.add(summaryRow);
        return resultList;
    }

    /**
     * 工作日处理
     * @param monthStartDate
     * @return
     */
    private List<LocalDate> prepareWeekdays(LocalDate monthStartDate) {
        LocalDate monthEndDate = monthStartDate.plusMonths(1L).minusDays(1L);

        // holiday
        HolidaySearchVo holidaySearch = new HolidaySearchVo();
        holidaySearch.setStartDate(monthStartDate);
        holidaySearch.setEndDate(monthEndDate);
        List<HolidayVo> holidays = holidayService.listHoliday(holidaySearch);

        // result
        List<LocalDate> resultList = new ArrayList<>((int)DAYS.between(monthStartDate, monthEndDate));
        for (LocalDate date = monthStartDate; !date.isAfter(monthEndDate); date = date.plusDays(1L)) {
            final LocalDate dateItem = date;
            if (holidays != null && holidays.stream().anyMatch(x -> Objects.equals(x.getHoliday(), dateItem))) {
                continue;
            }
            resultList.add(date);
        }
        return resultList;
    }

    /**
     * 解析CSV
     * @param in
     * @return
     * @throws IOException
     */
    private List<DiningInformationVo> loadDiningInformationFromCsv(InputStream in) throws IOException {
        List<DiningInformationVo> resultList = new ArrayList<>(200);
        BufferedReader reader = new BufferedReader(new InputStreamReader(in,"gbk"));
        boolean sign = false;       //用来跳过第一行的名称
        while(reader.ready()) {
            String line = reader.readLine();
            String[] arr = line.split(",[',]*");
            if (arr.length >= 12 && sign) {
                DiningInformationVo information = new DiningInformationVo();
                information.setEmployeeName(arr[0].replace("\"",""));
                information.setMealDate(LocalDate.parse(arr[2], DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                information.setMealTime(LocalTime.parse(arr[3], DateTimeFormatter.ofPattern("HH:mm:ss")));
                resultList.add(information);
            } else {
                sign = true;
            }
        }
        return resultList;
    }


}
