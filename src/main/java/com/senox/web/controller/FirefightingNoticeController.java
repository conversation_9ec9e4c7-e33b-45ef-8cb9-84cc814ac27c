package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.FirefightingNoticeSearchVo;
import com.senox.realty.vo.FirefightingNoticeVo;
import com.senox.web.service.FirefightingNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/4/23 17:19
 */
@Api(tags = "店铺消防安全告知单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/notice")
public class FirefightingNoticeController extends BaseController {

    private final FirefightingNoticeService noticeService;

    @ApiOperation("添加店铺消防安全责任告知单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addNotice(@Validated(Add.class) @RequestBody FirefightingNoticeVo notice) {
        return noticeService.addNotice(notice);
    }

    @ApiOperation("更新店铺消防安全责任告知单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateNotice(@Validated(Update.class) @RequestBody FirefightingNoticeVo notice) {
        noticeService.updateNotice(notice);
    }

    @ApiOperation("删除店铺消防安全责任告知单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteNotice(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        noticeService.deleteNotice(id);
    }

    @ApiOperation("获取店铺消防安全责任告知单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public FirefightingNoticeVo findNoticeById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return noticeService.findNoticeById(id);
    }

    @ApiOperation("店铺消防安全责任告知单页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<FirefightingNoticeVo> listNoticePage(@RequestBody FirefightingNoticeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return noticeService.listNoticePage(search);
    }



}
