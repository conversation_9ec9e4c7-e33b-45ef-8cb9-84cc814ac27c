package com.senox.web.controller;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.context.AdminContext;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.PayWay;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.BicycleBillSettlementService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.BicycleBillSettlementExportVo;
import com.senox.web.vo.TollPrintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "三轮车结算单")
@RestController
@RequestMapping("/web/bicycle/bill/settlement")
@RequiredArgsConstructor
public class BicycleBillSettlementController extends BaseController {
    private final BicycleBillSettlementService billSettlementService;

    @ApiOperation("根据id查询结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/findById/{id}")
    public BicycleBillSettlementVo findById(@PathVariable Long id) {
        return billSettlementService.findById(id);
    }

    @ApiOperation("支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay/{payWay}")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BicycleBillOrderVo billOrderVo, @PathVariable PayWay payWay) {
        billOrderVo.setPayWay(payWay.getValue());

        if (CollectionUtils.isEmpty(billOrderVo.getSettlementIds())) {
            throw new BusinessException("无效的结算单");
        }

        if (payWay.getOrderStatus() != OrderStatus.PAID) {
            throw new InvalidParameterException("管理后台缴费只支持现金或转账结算");
        }

        billOrderVo.setRequestIp(RequestUtils.getIpAddr(request));
        billOrderVo.setTollManId(getAdminUserId());
        return billSettlementService.payBill(billOrderVo);
    }

    @ApiOperation("结算单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public BicycleBillSettlementPageResult<BicycleBillSettlementVo> list(@RequestBody BicycleBillSettlementSearchVo searchVo) {
        return billSettlementService.listPage(searchVo);
    }

    @ApiOperation("结算单详情列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/{settlementId}")
    public BicycleBillSettlementDetailVo detailBySettlement(@PathVariable Long settlementId) {
        return billSettlementService.detailBySettlement(settlementId);
    }

    @ApiOperation("结算单下发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send")
    public void send(@RequestBody BicycleBillSettlementSendVo sendVo) {
        billSettlementService.send(sendVo);
    }

    @ApiOperation("结算单下发通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send/notify")
    public void notifySend(@RequestBody BicycleBillSettlementSendVo sendVo) {
        billSettlementService.notifySend(sendVo);
    }

    @ApiOperation("打印结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(defaultValue = "false") Boolean refreshSerial) {
        return billSettlementService.printBill(id, refreshSerial);
    }

    @ApiOperation("结算单生成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generate(@RequestBody BicycleBillSettlementSendVo sendVo) {
        billSettlementService.generate(sendVo);
    }

    @ApiOperation("结算单导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response, BicycleBillSettlementSearchVo searchVo) throws IOException {
        List<BicycleBillSettlementVo> billSettlements = billSettlementService.list(searchVo);
        List<BicycleBillSettlementExportVo> billSettlementExports = billSettlementToExport(billSettlements);
        String fileName = SenoxConst.Export.TMS_BICYCLE_BILL_SETTLEMENT_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleBillSettlementExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_BICYCLE_BILL_SETTLEMENT_SHEET)
                .doWrite(billSettlementExports);
    }

    /**
     * 结算单转报表
     *
     * @param billSettlements 结算单列表
     * @return 返回报表
     */
    private List<BicycleBillSettlementExportVo> billSettlementToExport(List<BicycleBillSettlementVo> billSettlements) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return Collections.emptyList();
        }
        List<BicycleBillSettlementExportVo> exportVos = billSettlements.stream().map(b -> {
            BicycleBillSettlementExportVo be = new BicycleBillSettlementExportVo();
            be.setId(String.valueOf(b.getId()));
            be.setBillYearMonth(b.getBillYearMonth());
            be.setMerchantName(b.getMerchantName());
            be.setRcSerial(b.getRcSerial());
            be.setAmount(b.getAmount());
            be.setPaidAmount(b.getPaidAmount());
            be.setPaidTime(b.getPaidTime());
            be.setStatus(BooleanUtils.isTrue(b.getStatus()) ? "是" : "否");
            be.setPayWay(PayWay.fromValue(b.getPayWay()).getDescription());
            be.setTollManName(b.getTollManName());
            return be;
        }).collect(Collectors.toList());
        BicycleBillSettlementExportVo totalExport = new BicycleBillSettlementExportVo();
        totalExport.setRcSerial("合计");
        totalExport.setAmount(exportVos.stream().map(BicycleBillSettlementExportVo::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        totalExport.setPaidAmount(exportVos.stream().map(BicycleBillSettlementExportVo::getPaidAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        exportVos.add(totalExport);
        return exportVos;
    }

}
