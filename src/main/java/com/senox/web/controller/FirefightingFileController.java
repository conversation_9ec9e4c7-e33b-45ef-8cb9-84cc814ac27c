package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.FirefightingFileBriefVo;
import com.senox.realty.vo.FirefightingFileSearchVo;
import com.senox.realty.vo.FirefightingFileVo;
import com.senox.web.service.FirefightingFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/4/22 15:41
 */
@Api(tags = "消防安全档案")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/file")
public class FirefightingFileController extends BaseController {

    private final FirefightingFileService firefightingFileService;

    @ApiOperation("添加店铺消防安全档案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addFile(@Validated(Add.class) @RequestBody FirefightingFileVo file) {
        return firefightingFileService.addFile(file);
    }

    @ApiOperation("更新店铺消防安全档案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateFile(@Validated(Update.class) @RequestBody FirefightingFileVo file) {
        if (!WrapperClassUtils.biggerThanLong(file.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        firefightingFileService.updateFile(file);
    }

    @ApiOperation("删除店铺消防安全档案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteFile(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        firefightingFileService.deleteFile(id);
    }

    @ApiOperation("获取店铺消防安全档案信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public FirefightingFileVo findFileById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return firefightingFileService.findFileById(id);
    }

    @ApiOperation("店铺消防安全档案信息合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public int countFile(@RequestBody FirefightingFileSearchVo search) {
        return firefightingFileService.countFile(search);
    }

    @ApiOperation("店铺消防安全档案信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/breif/page")
    public PageResult<FirefightingFileBriefVo> listFileBriefPage(@RequestBody FirefightingFileSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return firefightingFileService.listFileBriefPage(search);
    }
}
