package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.senox.cold.vo.*;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import com.senox.web.config.ExcelFillCellMergeStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.SkuConvertor;
import com.senox.web.listener.ExcelReadEventListener;
import com.senox.web.service.DeliveryService;
import com.senox.web.service.MerchantService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.SkuDataRead;
import com.senox.web.vo.SkuExcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/27 14:50
 */
@Api(tags = "配送")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/web/delivery")
public class DeliveryController extends BaseController {

    private final DeliveryService deliveryService;

    private final SkuConvertor skuConvertor;

    private final MerchantService merchantService;

    @ApiOperation("添加物流员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/employee/add")
    public Long addDeliveryEmployee(@Validated(Add.class) @RequestBody DeliveryEmployeeVo employeeVo) {
        return deliveryService.addDeliveryEmployee(employeeVo);
    }

    @ApiOperation("更新物流员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/employee/update")
    public void updateDeliveryEmployee(@Validated(Update.class) @RequestBody DeliveryEmployeeVo employeeVo) {
        if (employeeVo.getId() < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.updateDeliveryEmployee(employeeVo);
    }

    @ApiOperation("更新物流员工密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/employee/password/change")
    public void changePassword(@Validated @RequestBody DeliveryEmployeeChangePwdVo changePwd) {
        deliveryService.changePassword(changePwd);
    }

    @ApiOperation("删除物流员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/employee/delete/{id}")
    public void deleteDeliveryEmployee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.deleteDeliveryEmployee(id);
    }

    @ApiOperation("获取物流员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/employee/get/{id}")
    public DeliveryEmployeeVo deliveryEmployeeById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return deliveryService.deliveryEmployeeById(id);
    }

    @ApiOperation("物流员工列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/employee/list")
    public PageResult<DeliveryEmployeeVo> deliveryEmployeeList(@RequestBody DeliveryEmployeeSearchVo search) {
        return deliveryService.deliveryEmployeeList(search);
    }

    @ApiOperation("添加物流退货单(草稿状态)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/add")
    public Long addDeliveryReturn(@Validated(Add.class) @RequestBody DeliveryReturnVo returnVo) {
        return deliveryService.addDeliveryReturn(returnVo);
    }

    @ApiOperation("更新物流退货单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/update")
    public void updateDeliveryReturn(@Validated(Update.class) @RequestBody DeliveryReturnVo returnVo) {
        if (returnVo.getId() < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.updateDeliveryReturn(returnVo);
    }

    @ApiOperation("更新物流退货单状态为正常")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/update/state/{id}")
    public void updateDeliveryReturnState(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.updateDeliveryReturnState(id);
    }

    @ApiOperation("删除物流退货单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/delete/{id}")
    public void deleteDeliveryReturn(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.deleteDeliveryReturn(id);
    }

    @ApiOperation("获取物流退货单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/return/get/{id}")
    public DeliveryReturnVo findDeliveryReturnById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return deliveryService.deliveryReturnById(id);
    }

    @ApiOperation("物流退货单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/list")
    public PageResult<DeliveryReturnVo> listDeliveryReturn(@RequestBody DeliveryReturnSearchVo search) {
        return deliveryService.deliveryReturnList(search);
    }

    @ApiOperation("最近一天未提交的退货单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/count")
    public Integer countStatusByDraft() {
        return deliveryService.countStatusByDraft();
    }

    @ApiOperation("添加仓库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/warehouse/add")
    public Long addDeliveryWarehouse(@Validated(Add.class) @RequestBody DeliveryWarehouseVo warehouseVo) {
        return deliveryService.addDeliveryWarehouse(warehouseVo);
    }


    @ApiOperation("更新仓库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/warehouse/update")
    public void updateDeliveryWarehouse(@Validated(Update.class) @RequestBody DeliveryWarehouseVo warehouseVo) {
        if (warehouseVo.getId() < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.updateDeliveryWarehouse(warehouseVo);
    }

    @ApiOperation("删除仓库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/warehouse/delete/{id}")
    public void deleteDeliveryWarehouse(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        deliveryService.deleteDeliveryWarehouse(id);
    }

    @ApiOperation("获取仓库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/warehouse/get/{id}")
    public DeliveryWarehouseVo findDeliveryWarehouseById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return deliveryService.findDeliveryWarehouseById(id);
    }

    @ApiOperation("仓库列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/warehouse/list")
    public PageResult<DeliveryWarehouseVo> listDeliveryWarehouse(@Validated @RequestBody DeliveryWarehouseSearchVo search) {
        return deliveryService.listDeliveryWarehouse(search);
    }

    @ApiOperation("添加商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sku/add")
    public Long addSku(@Validated(Add.class) @RequestBody SkuVo skuVo) {
        return deliveryService.addSku(skuVo);
    }


    @ApiOperation("更新商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sku/update")
    public void updateSku(@Validated(Update.class) @RequestBody SkuVo skuVo) {
        if (skuVo.getId() < 1L) {
            throw new InvalidParameterException();
        }
        deliveryService.updateSku(skuVo);
    }

    @ApiOperation("删除商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sku/delete/{id}")
    public void deleteSku(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        deliveryService.deleteSku(id);
    }

    @ApiOperation("获取商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/sku/get/{id}")
    public SkuVo findSkuById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return deliveryService.findSkuById(id);
    }

    @ApiOperation("商品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sku/list")
    public PageResult<SkuVo> listSku(@Validated @RequestBody SkuSearchVo search) {
        return deliveryService.listSku(search);
    }


    @ApiOperation("添加物流订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/save")
    public Long addDeliveryOrder(@Validated(Add.class) @RequestBody DeliveryOrderVo orderVo) {
        return deliveryService.addDeliveryOrder(orderVo);
    }

    @ApiOperation("发货")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/send/{orderId}")
    public void sendDeliveryOrder(@PathVariable Long orderId) {
        deliveryService.sendDeliveryOrder(orderId);
    }

    @ApiOperation("删除物流订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/delete/{id}")
    public void deleteDeliveryOrder(@PathVariable Long id) {
        deliveryService.deleteDeliveryOrder(id);
    }

    @ApiOperation("物流订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/list")
    public PageResult<DeliveryOrderVo> orderList(@RequestBody DeliveryOrderSearchVo search) {
        return deliveryService.orderList(search);
    }

    @ApiOperation("最近一天未发货的订单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/count")
    public Integer countStatusByUnSend(){
        return deliveryService.countStatusByUnSend();
    }

    @ApiOperation("根据id获取物流订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/info/get/{orderId}")
    public DeliveryOrderVo deliveryOrderById(@PathVariable Long orderId) {
        return deliveryService.deliveryOrderById(orderId);
    }

    @ApiOperation("导出物流订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportDeliveryOrder(HttpServletResponse response, DeliveryOrderSearchVo searchVo) throws IOException {
        // export
        String fileName = String.format(SenoxConst.Export.FILE_DAILY_SCHEDULE, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        List<DeliveryWarehouseVo> listDeliveryWarehouse = warehouseVoList();

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        // export content
        List<List<Object>> contents = buildContent(listDeliveryWarehouse, searchVo, Boolean.FALSE, Collections.emptyList());
        // export sheet
        WriteSheet sheet = buildSheet(SenoxConst.Export.TITLE_DAILY_SCHEDULE, contents.size(), listDeliveryWarehouse, searchVo, Boolean.FALSE, Collections.emptyList());
        excelWriter.write(contents, sheet);
        // 关闭流
        excelWriter.finish();
    }

    @ApiOperation("导出物流详情订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/info/export")
    public void exportDeliveryOrderInfo(HttpServletResponse response,  Long[] ids) throws IOException {
        // export
        String fileName = String.format(SenoxConst.Export.FILE_DELIVERY_ORDER_INFO, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        List<DeliveryWarehouseVo> listDeliveryWarehouse = warehouseVoList();

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        // export content
        List<List<Object>> contents = buildContent(listDeliveryWarehouse, null, Boolean.TRUE, Arrays.asList(ids));
        // export sheet
        WriteSheet sheet = buildSheet(SenoxConst.Export.TITLE_DELIVERY_ORDER_INFO, contents.size(), listDeliveryWarehouse, null, Boolean.TRUE, Arrays.asList(ids));
        excelWriter.write(contents, sheet);
        // 关闭流
        excelWriter.finish();
    }

    @ApiOperation(("导入商品"))
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sku/import")
    public void importSku(@RequestPart("file") MultipartFile file) throws IOException {
        List<SkuExcelVo> totalDataList =
                EasyExcelFactory.read(file.getInputStream()).head(SkuExcelVo.class).sheet().doReadSync();
        //获取箱规
        totalDataList.forEach(x -> x.setQuantity(Integer.parseInt(x.getName().substring(x.getName().lastIndexOf("*") + 1).replaceAll("[^0-9]", ""))));
        //商品列表
        List<SkuVo> skuVos = new ArrayList<>();
        totalDataList.forEach(excelVo -> {
            MerchantVo merchantVo = merchantService.findByRc(excelVo.getCustomerSerial());
            if (merchantVo != null) {
                SkuVo skuVo = skuConvertor.toDo(excelVo);
                skuVo.setCustomerId(merchantVo.getId());
                skuVo.setCustomerSerial(merchantVo.getRcSerial());
                skuVos.add(skuVo);
            }
        });
        //保存商品
        deliveryService.batchAddSku(skuVos);
    }

    /**
     * 构建表单
     *
     * @param title
     * @param contentRows
     * @param listDeliveryWarehouse
     * @param searchVo
     * @param detail
     * @param ids
     * @return
     */
    private WriteSheet buildSheet(String title, int contentRows, List<DeliveryWarehouseVo> listDeliveryWarehouse, DeliveryOrderSearchVo searchVo, Boolean detail, List<Long> ids) {
        List<List<String>> headTitles = buildTitle(listDeliveryWarehouse, searchVo, detail, ids);

        int lastRowIndex = headTitles.get(0).size() + contentRows;
        return EasyExcelFactory.writerSheet(title)
                .head(headTitles)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(lastRowIndex - 1, lastRowIndex - 1, 0, 3))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .build();
    }

    /**
     * 导出内容
     *
     * @param listDeliveryWarehouse
     * @param searchVo
     * @param detail
     * @param ids
     * @return
     */
    private List<List<Object>> buildContent(List<DeliveryWarehouseVo> listDeliveryWarehouse, DeliveryOrderSearchVo searchVo, Boolean detail, List<Long> ids) {
        List<DeliveryOrderItemVo> orderItemVos = new ArrayList<>();
        Map<LocalDate, List<DeliveryOrderVo>> map = new HashMap<>();
        List<DeliveryOrderVo> orderVos;
        //判断是否获取订单详情
        if (BooleanUtils.isTrue(detail) && !CollectionUtils.isEmpty(ids)) {
            orderVos = deliveryService.deliveryOrderByIds(ids);
        } else {
            orderVos = deliveryService.orderInfoList(searchVo);
        }
        for (DeliveryOrderVo orderVo : orderVos) {
            if (!map.containsKey(orderVo.getDeliveryDate())) {
                map.put(orderVo.getDeliveryDate(), new ArrayList<>());
            }
            map.get(orderVo.getDeliveryDate()).add(orderVo);
        }
        for (DeliveryOrderVo orderVo : orderVos) {
            orderItemVos.addAll(orderVo.getDeliveryOrderItemVos());
        }

        Map<Long, MerchantVo> merchantVoMap = merchantVoMap();
        Map<Long, SkuVo> skuVoMap = skuVoMap();

        //记录合计总数
        List<Integer> count = new ArrayList<>();

        //生成一个仓库id为key的map集合
        Map<Long, List<DeliveryOrderItemVo>> collect = orderItemVos.stream().collect(Collectors.groupingBy(DeliveryOrderItemVo::getSendWarehouseId));
        List<List<Object>> resultList = new ArrayList<>();
        for (Map.Entry<LocalDate, List<DeliveryOrderVo>> entry : map.entrySet()) {
            Map<Long, List<DeliveryOrderItemVo>> itemMap = new LinkedHashMap<>();
            for (DeliveryOrderVo vo : entry.getValue()) {
                for (DeliveryOrderItemVo itemVo : vo.getDeliveryOrderItemVos()) {
                    if (!itemMap.containsKey(itemVo.getSkuId())) {
                        itemMap.put(itemVo.getSkuId(), new ArrayList<>());
                    }
                    itemMap.get(itemVo.getSkuId()).add(itemVo);
                }
            }
            for (Map.Entry<Long, List<DeliveryOrderItemVo>> listEntry : itemMap.entrySet()) {
                List<DeliveryOrderItemVo> value = listEntry.getValue();
                Map<Long, List<DeliveryOrderItemVo>> warehouseMap = new HashMap<>();
                for (DeliveryOrderItemVo orderItemVo : value) {
                    if (!warehouseMap.containsKey(orderItemVo.getSendWarehouseId())) {
                        warehouseMap.put(orderItemVo.getSendWarehouseId(), new ArrayList<>());
                    }
                    warehouseMap.get(orderItemVo.getSendWarehouseId()).add(orderItemVo);
                }
                List<DeliveryOrderItemVo> itemVos = new ArrayList<>();
                for (Map.Entry<Long, List<DeliveryOrderItemVo>> longListEntry : warehouseMap.entrySet()) {
                    DeliveryOrderItemVo vo = getDeliveryOrderItemVo(longListEntry);
                    itemVos.add(vo);
                }
                List<Object> content = new ArrayList<>();
                //获取商品和客户信息
                SkuVo skuVo = skuVoMap.get(value.get(0).getSkuId());
                MerchantVo merchantVo = merchantVoMap.get(value.get(0).getCustomerId());
                content.add(merchantVo.getName());
                content.add(merchantVo.getRcSerial());
                content.add(skuVo.getName());
                content.add(skuVo.getCode());
                List<Integer> sum = new ArrayList<>();
                for (DeliveryWarehouseVo warehouseVo : listDeliveryWarehouse) {
                    boolean flag = true;
                    for (DeliveryOrderItemVo orderItemVo : itemVos) {
                        if (warehouseVo.getId().equals(orderItemVo.getSendWarehouseId())) {
                            content.add(orderItemVo.getSendQuantity() > 0 ? orderItemVo.getSendQuantity() : StringUtils.EMPTY);
                            content.add(orderItemVo.getSendPackages().compareTo(BigDecimal.ZERO) == 0 ? StringUtils.EMPTY : orderItemVo.getSendPackages());
                            content.add(orderItemVo.getReloadQuantity() > 0 ? orderItemVo.getReloadQuantity() : StringUtils.EMPTY);
                            content.add(orderItemVo.getReturnQuantity() > 0 ? orderItemVo.getReturnQuantity() : StringUtils.EMPTY);
                            content.add(entry.getKey().toString());
                            sum.add(orderItemVo.getSendQuantity());
                            flag = false;
                        }
                    }
                    if (flag) {
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                    }
                }
                //商品箱规以及合计
                content.add(skuVo.getQuantity());
                content.add(sum.stream().mapToInt(Integer::intValue).sum());
                count.addAll(sum);
                resultList.add(content);
            }
        }

        List<Object> summaryRow = new ArrayList<>();
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);

        listDeliveryWarehouse.forEach(x -> {
            List<DeliveryOrderItemVo> list = collect.get(x.getId());
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryOrderItemVo::getSendQuantity).sum());
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().map(DeliveryOrderItemVo::getSendPackages).reduce(BigDecimal.ZERO, BigDecimal::add));
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryOrderItemVo::getReloadQuantity).sum());
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryOrderItemVo::getReturnQuantity).sum());
            summaryRow.add(StringUtils.EMPTY);
        });
        summaryRow.add(StringUtils.EMPTY);
        summaryRow.add(count.stream().mapToInt(Integer::intValue).sum());

        resultList.add(summaryRow);
        return resultList;
    }

    @NotNull
    private static DeliveryOrderItemVo getDeliveryOrderItemVo(Map.Entry<Long, List<DeliveryOrderItemVo>> longListEntry) {
        DeliveryOrderItemVo vo = new DeliveryOrderItemVo();
        for (DeliveryOrderItemVo orderItemVo : longListEntry.getValue()) {
            vo.setSendWarehouseId(orderItemVo.getSendWarehouseId());
            vo.setSendQuantity(orderItemVo.getSendQuantity());
            vo.setReloadQuantity(orderItemVo.getReloadQuantity());
            vo.setSendPackages(orderItemVo.getSendPackages());
            vo.setReturnQuantity(orderItemVo.getReturnQuantity());
        }
        return vo;
    }

    /**
     * excel 标题
     *
     * @param listDeliveryWarehouse
     * @param searchVo
     * @param detail
     * @param ids
     * @return
     */
    private List<List<String>> buildTitle(List<DeliveryWarehouseVo> listDeliveryWarehouse, DeliveryOrderSearchVo searchVo, Boolean detail, List<Long> ids) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String formattedDate = StringUtils.EMPTY;
        if (BooleanUtils.isTrue(detail) && !CollectionUtils.isEmpty(ids)) {
            List<DeliveryOrderVo> orderVos = deliveryService.deliveryOrderByIds(ids);
            orderVos.sort(Comparator.comparing(DeliveryOrderVo::getDeliveryDate));
            formattedDate = formattedDate.concat(orderVos.get(0).getDeliveryDate().format(formatter).concat("-").concat(orderVos.get(orderVos.size() - 1).getDeliveryDate().format(formatter)));
        } else {
            formattedDate = formattedDate.concat(searchVo.getDeliveryDateStart().format(formatter).concat("-").concat(searchVo.getDeliveryDateEnd().format(formatter)));
        }
        //创建表头标题
        List<List<String>> headers = buildHeadTitle(formattedDate);
        for (DeliveryWarehouseVo vo : listDeliveryWarehouse) {
            headers.add(Arrays.asList(vo.getName(), "一、送货", "送货份数"));
            headers.add(Arrays.asList(vo.getName(), "一、送货", "送货件数"));
            headers.add(Arrays.asList(vo.getName(), "二、退货", "退转入"));
            headers.add(Arrays.asList(vo.getName(), "二、退货", "回云仓"));
            headers.add(Arrays.asList(vo.getName(), "日期", "日期"));
        }

        headers.add(Arrays.asList(SenoxConst.Export.BOX_GAUGE, SenoxConst.Export.BOX_GAUGE, SenoxConst.Export.BOX_GAUGE));
        headers.add(Arrays.asList(SenoxConst.Export.COLUMN_SUM, SenoxConst.Export.COLUMN_SUM, SenoxConst.Export.COLUMN_SUM));
        return headers;
    }

    @ApiOperation("添加退货计划单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/add")
    public void addDeliveryReturnPlan(@Validated(Add.class) @RequestBody DeliveryReturnPlanVo planVo) {
        deliveryService.addDeliveryReturnPlan(planVo);
    }

    @ApiOperation("更新退货计划单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/update")
    public void updateDeliveryReturnPlan(@Validated(Update.class) @RequestBody DeliveryReturnPlanVo planVo) {
        deliveryService.updateDeliveryReturnPlan(planVo);
    }

    @ApiOperation("根据id删除计划单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/delete/{id}")
    public void deleteReturnPlan(@PathVariable Long id) {
        deliveryService.deleteReturnPlan(id);
    }

    @ApiOperation("批量删除计划单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/delete")
    public void deleteBatchByIds(@RequestBody List<Long> ids) {
        deliveryService.deleteBatchByIds(ids);
    }

    @ApiOperation("退货计划单日期列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/date/list")
    public PageResult<DeliveryReturnPlanDateVo> localDatePlanList(@RequestBody DeliveryReturnPlanSearchVo searchVo) {
        return deliveryService.localDatePlanList(searchVo);
    }

    @ApiOperation("退货计划单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/list")
    public List<DeliveryReturnPlanVo> listReturnPlan(@RequestBody DeliveryReturnPlanSearchVo searchVo) {
        return deliveryService.listReturnPlan(searchVo);
    }

    @ApiOperation("导入退货计划单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/return/plan/import")
    public Map<String, List<String>> importReturnPlan(@RequestPart("file") MultipartFile file) throws IOException {
        DeliveryReturnPlanSearchVo searchVo = new DeliveryReturnPlanSearchVo();
        searchVo.setIsCompleted(false);
        List<DeliveryReturnPlanVo> list = deliveryService.listReturnPlan(searchVo);
        if (!CollectionUtils.isEmpty(list)) {
            throw new BusinessException("当前有未完成的退货计划单，请完成后再导入！");
        }

        LocalDate date = LocalDate.now();

        //忽略前三行表头
        ExcelReadEventListener<SkuDataRead> listener = new ExcelReadEventListener<>(3);
        EasyExcelFactory.read(file.getInputStream(), SkuDataRead.class, listener).extraRead(CellExtraTypeEnum.MERGE).sheet(0).doRead();

        List<SkuDataRead> excelDataList = listener.getList();
        List<CellExtra> cellExtraList = listener.getCellExtraList();
        if (!CollectionUtils.isEmpty(cellExtraList)) {
            //把合并单元格的数据进行处理
            listener.mergeExcelData(excelDataList, cellExtraList);
        }
        //过滤为0的数据
        List<SkuDataRead> filteredList = excelDataList.stream().filter(skuDataRead -> skuDataRead.getTotalNumber() != null && skuDataRead.getTotalNumber() != 0).collect(Collectors.toList());
        log.info("过滤后集合数据:{}", JsonUtils.object2Json(filteredList));
        Map<String, List<SkuDataRead>> collect = filteredList.stream().collect(Collectors.groupingBy(SkuDataRead::getCustomerName, LinkedHashMap::new, Collectors.toList()));
        log.info("根据客户名分组之后的数据:{}", JsonUtils.object2Json(collect));

        Map<String, List<DeliveryReturnPlanVo>> map = buildData(collect, date);
        List<DeliveryReturnPlanVo> planVoList = map.get("planVoList");
        List<DeliveryReturnPlanVo> noExistsCustomerList = map.get("noExistsCustomerList");
        List<DeliveryReturnPlanVo> noExistsSkuList = map.get("noExistsSkuList");

        Map<String, List<String>> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(planVoList)) {
            log.info("需要插入的数据为:{}", JsonUtils.object2Json(planVoList));
            deliveryService.batchAddDeliveryReturnPlan(planVoList);
        }
        if (!CollectionUtils.isEmpty(noExistsCustomerList)) {
            List<String> customerList = noExistsCustomerList.stream().map(DeliveryReturnPlanVo::getCustomerName).collect(Collectors.toList());
            log.warn("{}, 以上导入的客户不存在", JsonUtils.object2Json(customerList));
            resultMap.put("noExistsCustomer", customerList);
        }
        if (!CollectionUtils.isEmpty(noExistsSkuList)) {
            List<String> skuList = noExistsSkuList.stream().map(DeliveryReturnPlanVo::getSkuName).collect(Collectors.toList());
            log.warn("{}, 以上导入的商品不存在", JsonUtils.object2Json(skuList));
            resultMap.put("noExistsSku", skuList);
        }
        return resultMap;
    }

    /**
     * 构建退货计划单
     * @param collect
     * @param date
     * @return
     */
    private Map<String, List<DeliveryReturnPlanVo>> buildData(Map<String, List<SkuDataRead>> collect, LocalDate date) {
        //需要插入的数据
        List<DeliveryReturnPlanVo> planVoList = new ArrayList<>();
        //不存在的客户
        List<DeliveryReturnPlanVo> noExistsCustomerList = new ArrayList<>();
        //不存在的商品
        List<DeliveryReturnPlanVo> noExistsSkuList = new ArrayList<>();
        //根据客户名遍历
        for (Map.Entry<String, List<SkuDataRead>> entry : collect.entrySet()) {
            String key = entry.getKey();
            List<SkuDataRead> value = entry.getValue();
            //根据客户名查询多多客户
            MerchantVo merchantVo = merchantService.findByName(StringUtils.trimToEmpty(key));
            if (merchantVo == null || !BooleanUtils.isTrue(merchantVo.getDuoduo())) {
                DeliveryReturnPlanVo planVo = new DeliveryReturnPlanVo();
                planVo.setCustomerName(key);
                noExistsCustomerList.add(planVo);
                //客户不存在,记录，然后重新进入下一次遍历
                continue;
            }
            SkuSearchVo searchVo = new SkuSearchVo();
            searchVo.setPageNo(1);
            searchVo.setPageSize(500);
            searchVo.setCustomerId(merchantVo.getId());
            List<SkuVo> dataList = deliveryService.listSku(searchVo).getDataList();
            //存在的商品
            List<SkuDataRead> dataReads = value.stream()
                    .filter(v -> dataList.stream().anyMatch(data -> data.getQuantity().equals(v.getQuantity()) && data.getCode().equals(v.getSkuCode())))
                    .collect(Collectors.toList());
            //不存在的商品
            List<SkuDataRead> notInDataReadList  = value.stream().filter(data -> !dataReads.contains(data)).collect(Collectors.toList());
            notInDataReadList.forEach(notInDataRead->{
                DeliveryReturnPlanVo planVo = new DeliveryReturnPlanVo();
                planVo.setSkuName(notInDataRead.getSkuName());
                //记录不存在的商品
                noExistsSkuList.add(planVo);
            });

            dataReads.forEach(dataRead-> {
                //记录需要插入的数据
                List<DeliveryReturnPlanVo> deliveryReturnPlanVoList = buildReturnPlan(date, dataRead, merchantVo, dataList);
                planVoList.addAll(deliveryReturnPlanVoList);
            });
        }
        //封装数据
        Map<String, List<DeliveryReturnPlanVo>> map = new HashMap<>();
        map.put("planVoList", planVoList);
        map.put("noExistsCustomerList", noExistsCustomerList);
        map.put("noExistsSkuList", noExistsSkuList);
        return map;
    }


    private List<DeliveryReturnPlanVo> buildReturnPlan(LocalDate date, SkuDataRead dataRead, MerchantVo merchantVo, List<SkuVo> skuVoList) {
        List<DeliveryReturnPlanVo> list = new ArrayList<>();
        SkuVo sku = skuVoList.stream()
                .filter(skuVo -> skuVo.getQuantity().equals(dataRead.getQuantity()) && skuVo.getCode().equals(dataRead.getSkuCode()))
                .findFirst().orElse(null);
        if (sku == null) {
            return Collections.emptyList();
        }
        DeliveryWarehouseSearchVo warehouseSearchVo = new DeliveryWarehouseSearchVo();
        warehouseSearchVo.setPageNo(1);
        warehouseSearchVo.setPageSize(100);
        List<DeliveryWarehouseVo> warehouseVoList = deliveryService.listDeliveryWarehouse(warehouseSearchVo).getDataList();
        //根据仓库名，进行匹配
        for (DeliveryWarehouseVo warehouseVo : warehouseVoList) {
            if ("东莞仓".equals(warehouseVo.getName()) && (dataRead.getDgReturnWarehouse() > 0
                    || dataRead.getDgSendNumber() > 0 || dataRead.getDgSendPiece() > 0
                    || dataRead.getDgReturn() > 0 )) {
                //初始化数据
                DeliveryReturnPlanVo planVo = initDeliveryReturnPlanVo(date, warehouseVo, merchantVo, sku);

                planVo.setSendQuantity(dataRead.getDgSendNumber());
                planVo.setSendPackages(BigDecimal.valueOf(dataRead.getDgSendPiece()));
                planVo.setReloadQuantity(dataRead.getDgReturn());
                planVo.setReturnQuantity(dataRead.getDgReturnWarehouse());
                list.add(planVo);
            }
            else if ("水濂山1仓".equals(warehouseVo.getName()) && (dataRead.getSl1ReturnWarehouse() > 0
                    || dataRead.getSl1SendNumber() > 0  || dataRead.getSl1SendPiece() > 0
                    || dataRead.getSl1Return() > 0 )) {
                //初始化数据
                DeliveryReturnPlanVo planVo = initDeliveryReturnPlanVo(date, warehouseVo, merchantVo, sku);

                planVo.setSendQuantity(dataRead.getSl1SendNumber());
                planVo.setSendPackages(BigDecimal.valueOf(dataRead.getSl1SendPiece()));
                planVo.setReloadQuantity(dataRead.getSl1Return());
                planVo.setReturnQuantity(dataRead.getSl1ReturnWarehouse());
                list.add(planVo);
            }
            else if ("水濂山2仓".equals(warehouseVo.getName()) && (dataRead.getSl2ReturnWarehouse() > 0
                    || dataRead.getSl2SendNumber() > 0  || dataRead.getSl2SendPiece() > 0
                    || dataRead.getSl2Return() > 0 )) {
                //初始化数据
                DeliveryReturnPlanVo planVo = initDeliveryReturnPlanVo(date, warehouseVo, merchantVo, sku);

                planVo.setSendQuantity(dataRead.getSl2SendNumber());
                planVo.setSendPackages(BigDecimal.valueOf(dataRead.getSl2SendPiece()));
                planVo.setReloadQuantity(dataRead.getSl2Return());
                planVo.setReturnQuantity(dataRead.getSl2ReturnWarehouse());
                list.add(planVo);
            }
            else if ("惠州1仓".equals(warehouseVo.getName()) && (dataRead.getHz1ReturnWarehouse() > 0
                    || dataRead.getHz1SendNumber() > 0  || dataRead.getHz1SendPiece() > 0
                    || dataRead.getHz1Return() > 0 )) {
                //初始化数据
                DeliveryReturnPlanVo planVo = initDeliveryReturnPlanVo(date, warehouseVo, merchantVo, sku);

                planVo.setSendQuantity(dataRead.getHz1SendNumber());
                planVo.setSendPackages(BigDecimal.valueOf(dataRead.getHz1SendPiece()));
                planVo.setReloadQuantity(dataRead.getHz1Return());
                planVo.setReturnQuantity(dataRead.getHz1ReturnWarehouse());
                list.add(planVo);
            }
            else if ("惠州2仓".equals(warehouseVo.getName()) && (dataRead.getHz2ReturnWarehouse() > 0
                    || dataRead.getHz2SendNumber() > 0  || dataRead.getHz2SendPiece() > 0
                    || dataRead.getHz2Return() > 0 )) {
                //初始化数据
                DeliveryReturnPlanVo planVo = initDeliveryReturnPlanVo(date, warehouseVo, merchantVo, sku);

                planVo.setSendQuantity(dataRead.getHz2SendNumber());
                planVo.setSendPackages(BigDecimal.valueOf(dataRead.getHz2SendPiece()));
                planVo.setReloadQuantity(dataRead.getHz2Return());
                planVo.setReturnQuantity(dataRead.getHz2ReturnWarehouse());
                list.add(planVo);
            }
        }
        return list;
    }

    private DeliveryReturnPlanVo initDeliveryReturnPlanVo(LocalDate date, DeliveryWarehouseVo warehouseVo, MerchantVo merchantVo, SkuVo sku) {
        DeliveryReturnPlanVo planVo = new DeliveryReturnPlanVo();
        planVo.setPlanDate(date);
        planVo.setWarehouseId(warehouseVo.getId());
        planVo.setWarehouseName(warehouseVo.getName());
        planVo.setSkuId(sku.getId());
        planVo.setSkuName(sku.getName());
        planVo.setCustomerId(merchantVo.getId());
        planVo.setCustomerName(merchantVo.getName());
        return planVo;
    }

    @ApiOperation("导出物流退货单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/return/export")
    public void exportDeliveryReturn(HttpServletResponse response, DeliveryReturnSearchVo searchVo) throws IOException {
        // export
        String fileName = String.format(SenoxConst.Export.FILE_DELIVERY_RETURN, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        List<DeliveryWarehouseVo> listDeliveryWarehouse = warehouseVoList();

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        // export content
        List<List<Object>> contents = buildContent(listDeliveryWarehouse, searchVo, Collections.emptyList());
        // export sheet
        WriteSheet sheet = buildSheet(SenoxConst.Export.TITLE_DELIVERY_RETURN, contents.size(), listDeliveryWarehouse, searchVo, Collections.emptyList());
        excelWriter.write(contents, sheet);
        // 关闭流
        excelWriter.finish();
    }

    @ApiOperation("根据退货单id导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/return/batch/export")
    public void exportDeliveryReturnV2(HttpServletResponse response,  Long[] ids) throws IOException {
        // export
        String fileName = String.format(SenoxConst.Export.FILE_DELIVERY_RETURN, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        List<DeliveryWarehouseVo> listDeliveryWarehouse = warehouseVoList();

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        // export content
        List<List<Object>> contents = buildContent(listDeliveryWarehouse, null, Arrays.asList(ids));
        // export sheet
        WriteSheet sheet = buildSheet(SenoxConst.Export.TITLE_DELIVERY_RETURN, contents.size(), listDeliveryWarehouse, null, Arrays.asList(ids));
        excelWriter.write(contents, sheet);
        // 关闭流
        excelWriter.finish();
    }

    /**
     * 导出内容
     *
     * @param warehouseVoList
     * @param searchVo
     * @param ids
     * @return
     */
    private List<List<Object>> buildContent(List<DeliveryWarehouseVo> warehouseVoList, DeliveryReturnSearchVo searchVo, List<Long> ids) {
        List<DeliveryReturnItemVo> returnItemVos = new ArrayList<>();
        //退货单集合
        List<DeliveryReturnVo> returnVoList;
        if (!CollectionUtils.isEmpty(ids)) {
            returnVoList = deliveryService.deliveryReturnByIds(ids);
        } else {
            returnVoList = deliveryService.returnInfoList(searchVo);
        }
        Map<LocalDate, List<DeliveryReturnVo>> map = new HashMap<>();
        for (DeliveryReturnVo returnVo : returnVoList) {
            if (!map.containsKey(returnVo.getReturnDate())) {
                map.put(returnVo.getReturnDate(), new ArrayList<>());
            }
            map.get(returnVo.getReturnDate()).add(returnVo);
        }
        for (DeliveryReturnVo returnVo : returnVoList) {
            returnItemVos.addAll(returnVo.getDeliveryReturnItemVos());
        }

        Map<Long, MerchantVo> merchantVoMap = merchantVoMap();
        Map<Long, SkuVo> skuVoMap = skuVoMap();

        List<Integer> count = new ArrayList<>();//总合计
        List<Integer> goodCount = new ArrayList<>();//良品总合计
        List<Integer> badCount = new ArrayList<>();//损品总合计
        List<Integer> differenceCount = new ArrayList<>();//多多差异总合计

        //生成一个仓库id为key的map集合
        Map<Long, List<DeliveryReturnItemVo>> collect = returnItemVos.stream().collect(Collectors.groupingBy(DeliveryReturnItemVo::getWarehouseId));

        List<List<Object>> resultList = new ArrayList<>();
        //日期维度
        for (Map.Entry<LocalDate, List<DeliveryReturnVo>> entry : map.entrySet()) {
            Map<Long, List<DeliveryReturnItemVo>> skuMap = new LinkedHashMap<>();
            for (DeliveryReturnVo vo : entry.getValue()) {
                for (DeliveryReturnItemVo itemVo : vo.getDeliveryReturnItemVos()) {
                    if (!skuMap.containsKey(itemVo.getSkuId())) {
                        skuMap.put(itemVo.getSkuId(), new ArrayList<>());
                    }
                    skuMap.get(itemVo.getSkuId()).add(itemVo);
                }
            }
            //商品维度
            for (Map.Entry<Long, List<DeliveryReturnItemVo>> listEntry : skuMap.entrySet()) {
                List<Object> content = new ArrayList<>();
                //获取商品和客户信息
                SkuVo skuVo = skuVoMap.get(listEntry.getKey());
                MerchantVo customerVo = merchantVoMap.get(skuVo.getCustomerId());
                content.add(customerVo.getName());
                content.add(customerVo.getRcSerial());
                content.add(skuVo.getName());
                content.add(skuVo.getCode());
                Map<Long, List<DeliveryReturnItemVo>> warehouseMap = new HashMap<>();
                //仓库维度
                for (DeliveryReturnItemVo itemVo : listEntry.getValue()) {
                    if (!warehouseMap.containsKey(itemVo.getWarehouseId())) {
                        warehouseMap.put(itemVo.getWarehouseId(), new ArrayList<>());
                    }
                    warehouseMap.get(itemVo.getWarehouseId()).add(itemVo);
                }
                List<DeliveryReturnItemVo> itemVos = new ArrayList<>();
                for (Map.Entry<Long, List<DeliveryReturnItemVo>> item : warehouseMap.entrySet()) {
                    Map<String, List<DeliveryReturnItemVo>> listMap = item.getValue().stream().collect(Collectors
                            .groupingBy(t -> t.getWarehouseId() + "-" + t.getEmployeeId() + "-" + t.getCustomerId() + "-" + t.getSkuId()));
                    for (Map.Entry<String, List<DeliveryReturnItemVo>> stringListEntry : listMap.entrySet()) {
                        List<DeliveryReturnItemVo> entryValue = stringListEntry.getValue();
                        DeliveryReturnItemVo itemVo = entryValue.get(0);
                        DeliveryReturnItemVo vo = new DeliveryReturnItemVo();
                        vo.setEmployeeId(itemVo.getEmployeeId());
                        vo.setEmployeeName(itemVo.getEmployeeName());
                        vo.setSkuId(itemVo.getSkuId());
                        vo.setWarehouseId(itemVo.getWarehouseId());
                        vo.setReturnId(itemVo.getReturnId());
                        vo.setReceivedQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getReceivedQuantity).sum());
                        vo.setReloadQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getReloadQuantity).sum());
                        vo.setReturnQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getReturnQuantity).sum());
                        vo.setBadReturnQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getBadReturnQuantity).sum());
                        vo.setDifferenceQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getDifferenceQuantity).sum());
                        vo.setUnNumberedQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getUnNumberedQuantity).sum());
                        vo.setPlanSendQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getPlanSendQuantity).sum());
                        vo.setPlanReturnQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getPlanReturnQuantity).sum());
                        vo.setPlanReloadQuantity(entryValue.stream().mapToInt(DeliveryReturnItemVo::getPlanReloadQuantity).sum());
                        vo.setRemark(entryValue.stream().map(DeliveryReturnItemVo::getRemark).filter(Objects::nonNull).collect(Collectors.joining("  ")));
                        itemVos.add(vo);
                    }
                }
                List<Integer> sum = new ArrayList<>();//合计
                List<Integer> goodSum = new ArrayList<>();//良品合计
                List<Integer> badSum = new ArrayList<>();//损品合计
                List<Integer> differenceSum = new ArrayList<>();//差异合计
                for (DeliveryWarehouseVo warehouseVo : warehouseVoList) {
                    boolean flag = true;
                    for (DeliveryReturnItemVo returnItemVo : itemVos) {
                        if (warehouseVo.getId().equals(returnItemVo.getWarehouseId())) {
                            content.add(returnItemVo.getPlanSendQuantity());
                            content.add(returnItemVo.getPlanReturnQuantity());
                            content.add(returnItemVo.getPlanReloadQuantity());
                            content.add(returnItemVo.getReceivedQuantity());
                            content.add(returnItemVo.getReloadQuantity());
                            content.add(returnItemVo.getReturnQuantity());
                            content.add(returnItemVo.getBadReturnQuantity());
                            content.add(returnItemVo.getUnNumberedQuantity());
                            content.add(returnItemVo.getDifferenceQuantity());
                            content.add(returnItemVo.getEmployeeName());
                            content.add(returnItemVo.getRemark());
                            content.add(entry.getKey().toString());
                            sum.add(returnItemVo.getReturnQuantity() + returnItemVo.getBadReturnQuantity());
                            //良品和损品数
                            goodSum.add(returnItemVo.getReturnQuantity());
                            badSum.add(returnItemVo.getBadReturnQuantity());
                            differenceSum.add(returnItemVo.getDifferenceQuantity());
                            flag = false;
                        }
                    }
                    if (flag) {
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                        content.add(StringUtils.EMPTY);
                    }
                }
                //商品箱规以及合计
                sum(content, sum, goodSum, badSum, differenceSum);
                //合计
                count.addAll(sum);
                goodCount.addAll(goodSum);
                badCount.addAll(badSum);
                differenceCount.addAll(differenceSum);
                resultList.add(content);
            }
        }

        List<Object> summaryRow = new ArrayList<>();
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);
        summaryRow.add(SenoxConst.Export.COLUMN_SUM);

        warehouseVoList.forEach(x -> {
            List<DeliveryReturnItemVo> list = collect.get(x.getId());
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryReturnItemVo::getReturnQuantity).sum());
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryReturnItemVo::getBadReturnQuantity).sum());
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(list == null ? StringUtils.EMPTY : list.stream().mapToInt(DeliveryReturnItemVo::getDifferenceQuantity).sum());
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
            summaryRow.add(StringUtils.EMPTY);
        });
        //良品总计
        sum(summaryRow, count, goodCount, badCount, differenceCount);

        resultList.add(summaryRow);
        return resultList;
    }

    private void sum(List<Object> content, List<Integer> sum, List<Integer> goodSum, List<Integer> badSum, List<Integer> differenceSum) {
        content.add(goodSum.stream().mapToInt(Integer::intValue).sum());
        content.add(badSum.stream().mapToInt(Integer::intValue).sum());
        content.add(differenceSum.stream().mapToInt(Integer::intValue).sum());
        content.add(sum.stream().mapToInt(Integer::intValue).sum());
    }

    /**
     * 构建表单
     *
     * @param title
     * @param contentRows
     * @param listDeliveryWarehouse
     * @param searchVo
     * @param ids
     * @return
     */
    private WriteSheet buildSheet(String title, int contentRows, List<DeliveryWarehouseVo> listDeliveryWarehouse, DeliveryReturnSearchVo searchVo, List<Long> ids) {
        List<List<String>> headTitles = buildTitle(listDeliveryWarehouse, searchVo, ids);

        int lastRowIndex;
        lastRowIndex = headTitles.get(0).size() + contentRows;
        return EasyExcelFactory.writerSheet(title)
                .head(headTitles)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(lastRowIndex - 1, lastRowIndex - 1, 0, 3))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .build();
    }

    /**
     * excel
     *
     * @param listDeliveryWarehouse
     * @param searchVo
     * @param ids
     * @return
     */
    private List<List<String>> buildTitle(List<DeliveryWarehouseVo> listDeliveryWarehouse, DeliveryReturnSearchVo searchVo, List<Long> ids) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String formattedDate = StringUtils.EMPTY;
        if (!CollectionUtils.isEmpty(ids)) {
            List<DeliveryReturnVo> returnVos = deliveryService.deliveryReturnByIds(ids);
            returnVos.sort(Comparator.comparing(DeliveryReturnVo::getReturnDate));
            formattedDate = formattedDate.concat(returnVos.get(0).getReturnDate().format(formatter).concat("-").concat(returnVos.get(returnVos.size() - 1).getReturnDate().format(formatter)));
        } else {
            formattedDate = formattedDate.concat(searchVo.getReturnDateStart().format(formatter).concat("-").concat(searchVo.getReturnDateEnd().format(formatter)));
        }
        //创建表头标题
        List<List<String>> headers = buildHeadTitle(formattedDate);
        for (DeliveryWarehouseVo vo : listDeliveryWarehouse) {
            headers.add(Arrays.asList(vo.getName(), "计划", "送货数"));
            headers.add(Arrays.asList(vo.getName(), "计划", "退转入"));
            headers.add(Arrays.asList(vo.getName(), "计划", "回云仓"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "入库数"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "退转入"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "退回良品"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "退回损品"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "存放多多数"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "差异数"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "操作人员"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "备注"));
            headers.add(Arrays.asList(vo.getName(), "实际配送", "日期"));
        }

        headers.add(Arrays.asList(SenoxConst.Export.GOOD_COLUMN_SUM, SenoxConst.Export.GOOD_COLUMN_SUM, SenoxConst.Export.GOOD_COLUMN_SUM));
        headers.add(Arrays.asList(SenoxConst.Export.BAD_COLUMN_SUM, SenoxConst.Export.BAD_COLUMN_SUM, SenoxConst.Export.BAD_COLUMN_SUM));
        headers.add(Arrays.asList(SenoxConst.Export.DUODUO_DIFFERENCE_SUM, SenoxConst.Export.DUODUO_DIFFERENCE_SUM, SenoxConst.Export.DUODUO_DIFFERENCE_SUM));
        headers.add(Arrays.asList(SenoxConst.Export.COLUMN_SUM, SenoxConst.Export.COLUMN_SUM, SenoxConst.Export.COLUMN_SUM));
        return headers;
    }

    private List<List<String>> buildHeadTitle(String formattedDate) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList(formattedDate, formattedDate, SenoxConst.Export.DUODUO_CUSTOMER_NAME));
        headers.add(Arrays.asList(formattedDate, formattedDate, SenoxConst.Export.DUODUO_CUSTOMER_CODE));
        headers.add(Arrays.asList(formattedDate, formattedDate, SenoxConst.Export.DUODUO_SUK_NAME));
        headers.add(Arrays.asList(formattedDate, formattedDate, SenoxConst.Export.DUODUO_SUK_CODE));
        return headers;
    }

    private List<DeliveryWarehouseVo> warehouseVoList() {
        DeliveryWarehouseSearchVo warehouseSearchVo = new DeliveryWarehouseSearchVo();
        warehouseSearchVo.setPageNo(1);
        warehouseSearchVo.setPageSize(100);
        List<DeliveryWarehouseVo> listDeliveryWarehouse = deliveryService.listDeliveryWarehouse(warehouseSearchVo).getDataList();
        listDeliveryWarehouse = listDeliveryWarehouse.stream().sorted(Comparator.comparing(DeliveryWarehouseVo::getId)).collect(Collectors.toList());
        return listDeliveryWarehouse;
    }

    private Map<Long, MerchantVo> merchantVoMap() {
        List<MerchantVo> duoduoMerchantList = listDuoduoMerchant();
        return duoduoMerchantList.stream().collect(Collectors.toMap(MerchantVo::getId, customerVo -> customerVo));
    }

    private Map<Long, SkuVo> skuVoMap() {
        List<SkuVo> skuVoList = deliveryService.listSkuAll();
        return skuVoList.stream().collect(Collectors.toMap(SkuVo::getId, skuVo -> skuVo));
    }

    private List<MerchantVo> listDuoduoMerchant() {
        MerchantSearchVo searchVo = new MerchantSearchVo();
        searchVo.setDuoduo(true);
        searchVo.setRc(true);
        searchVo.setPageNo(1);
        searchVo.setPageSize(500);
        searchVo.setPage(false);
        return merchantService.list(searchVo).getDataList();
    }
}
