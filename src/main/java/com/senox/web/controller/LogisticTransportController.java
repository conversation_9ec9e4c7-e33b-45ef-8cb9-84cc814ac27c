package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.ExcelRowReadListener;
import com.senox.web.service.LogisticTransportService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Api(tags = "城际运输")
@RestController
@RequestMapping("/web/logistic/transport")
@RequiredArgsConstructor
public class LogisticTransportController extends BaseController {
    private final LogisticTransportService transportService;

    @ApiOperation("添加订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/add")
    public void addOrder(@RequestBody List<LogisticTransportOrderVo> orders) {
        transportService.addOrder(orders);
    }

    @ApiOperation("更新订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/update")
    public void updateOrder(@RequestBody List<LogisticTransportOrderVo> orders) {
        transportService.updateOrder(orders);
    }

    @ApiOperation("订单审核")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/audit")
    public void auditOrder(@RequestBody LogisticTransportOrderAuditVo orderAudit) {
        transportService.auditOrder(orderAudit);
    }

    @ApiOperation("根据id查找订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/findById/{orderId}")
    public LogisticTransportOrderVo findOrderById(@PathVariable Long orderId) {
        return transportService.findOrderById(orderId);
    }

    @ApiOperation("根据编号查找订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/findBySerialNo/{orderSerialNo}")
    public LogisticTransportOrderVo findOrderBySerialNo(@PathVariable String orderSerialNo) {
        return transportService.findOrderBySerialNo(orderSerialNo);
    }

    @ApiOperation("根据编号删除订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/deleteBySerialNo/{orderSerialNo}")
    public void deleteOrderBySerialNo(@PathVariable String orderSerialNo) {
        transportService.deleteOrderBySerialNo(orderSerialNo);
    }

    @ApiOperation("订单分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/list/page")
    public PageStatisticsResult<LogisticTransportOrderVo, LogisticTransportOrderStatisticsVo> orderPageList(@RequestBody LogisticTransportOrderSearchVo search) {
        return transportService.orderPageList(search);
    }

    @ApiOperation("生成结算单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/generate")
    public List<Long> generateBillSettlement(@RequestBody LogisticTransportBillGenerateVo billGenerate) {
        return transportService.generateBillSettlement(billGenerate);
    }

    @ApiOperation("根据账单id删除账单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/deleteById/{billId}")
    public void billDeleteById(@PathVariable Long billId) {
        transportService.billDeleteById(billId);
    }

    @ApiOperation("账单分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/list/page")
    public PageResult<LogisticTransportBillVo> billPageList(@RequestBody LogisticTransportBillSearchVo search) {
        return transportService.billPageList(search);
    }

    @ApiOperation("结算单分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/list/page")
    public PageStatisticsResult<LogisticTransportBillSettlementVo, LogisticTransportBillSettlementStatisticsVo> billSettlementList(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        return transportService.billSettlementPageList(search);
    }

    @ApiOperation("结算单下发")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/send")
    public void billSettlementSend(@RequestBody LogisticTransportBillSettlementSendVo send) {
        transportService.billSettlementSend(send);
    }

    @ApiOperation("结算单删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlementDeleteById")
    public void billSettlementDeleteById(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        transportService.billSettlementDeleteById(search.getIds());
    }

    @ApiOperation("结算单详情")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/settlement/detail/{settlementId}")
    public LogisticTransportBillSettlementDetailVo billSettlementDetail(@PathVariable Long settlementId) {
        return transportService.billSettlementDetail(settlementId);
    }

    @ApiOperation("根据id查找结算单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/settlementFindById/{settlementId}")
    public LogisticTransportBillSettlementVo billSettlementFinById(@PathVariable Long settlementId) {
        return transportService.billSettlementFindById(settlementId);
    }

    @ApiOperation("账单支付")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/pay")
    public OrderResultVo billPay(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        validBillPayRequest(payRequest);
        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return transportService.billPay(payRequest);
    }

    @ApiOperation("订单导入")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/import")
    public void importOrder(@RequestPart("file") MultipartFile file) throws IOException {
        String lockKey = String.format(SenoxConst.Cache.KEY_LOGISTIC_TRANSPORT_IMPORT, getAdminUserId());
        if (!RedisUtils.lock(lockKey, SenoxConst.Cache.TTL_1H)) {
            throw new BusinessException("导入频繁，请稍后再试");
        }
        try {
            ExcelAnalysisResult<LogisticTransportOrderImportExcel> result = analysisOrderExcel(file.getInputStream(), excels -> excels.stream().map(this::excelDataToOrderData).collect(Collectors.toList()));
            String orderImportResult = transportService.orderImport(result.getExcelDataMap());
            if (!StringUtils.isBlank(orderImportResult)) {
                throw new BusinessException(orderImportResult);
            }
        } finally {
            RedisUtils.del(lockKey);
        }
    }

    @ApiOperation("账单导出")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/export")
    public void exportBIll(HttpServletResponse response, LogisticTransportBillSearchVo search) throws IOException {
        List<LogisticTransportBillVo> bills = transportService.billList(search);
        List<LogisticTransportBillExportVo> billExports = billToExport(bills);
        String fileName = SenoxConst.Export.TMS_LOGISTIC_TRANSPORT_BILL_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticTransportBillExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_LOGISTIC_TRANSPORT_BILL_SHEET)
                .doWrite(billExports);
    }

    @ApiOperation("订单导出")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/export")
    public void exportOrder(HttpServletResponse response, LogisticTransportOrderSearchVo search) throws IOException {
        List<LogisticTransportOrderVo> orders = transportService.orderList(search);
        List<LogisticTransportOrderExportVo> billExports = orderToExport(orders);
        String fileName = SenoxConst.Export.TMS_LOGISTIC_TRANSPORT_ORDER_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticTransportOrderExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_LOGISTIC_TRANSPORT_ORDER_SHEET)
                .doWrite(billExports);
    }

    /**
     * 解析excel
     *
     * @param in 输入流
     * @return excel数据集
     */
    public <T> ExcelAnalysisResult<T> analysisOrderExcel(InputStream in, Function<List<LogisticTransportOrderImportRawData>, List<T>> function) {
        int headRowNumber = 1;
        MultiValueMap<String, T> excelDataMap = new LinkedMultiValueMap<>();
        MultiValueMap<String, CellExtra> extraMap = new LinkedMultiValueMap<>();
        EasyExcelFactory.read(in, LogisticTransportOrderImportRawData.class, new ExcelRowReadListener<LogisticTransportOrderImportRawData>(headRowNumber, result -> {
            List<LogisticTransportOrderImportRawData> dataList = result.getDataList();
            excelDataMap.addAll(result.getSheetName(), function.apply(dataList));
            extraMap.addAll(result.getSheetName(), result.getCellExtraList());
        }, (data, context) -> {
            if (null == data || data.isEmpty()) {
                return null;
            }
            return data;
        })).extraRead(CellExtraTypeEnum.MERGE).headRowNumber(headRowNumber).doReadAll();
        return new ExcelAnalysisResult<>(headRowNumber, excelDataMap, extraMap);
    }

    private LogisticTransportOrderImportExcel excelDataToOrderData(LogisticTransportOrderImportRawData rawData) {
        LogisticTransportOrderImportExcel orderExcel = new LogisticTransportOrderImportExcel();
        excelDataToOrderData(rawData, orderExcel);
        return orderExcel;
    }

    private void excelDataToOrderData(LogisticTransportOrderImportRawData rawData, LogisticTransportOrderImportExcel orderExcel) {
        orderExcel.setConsignorName(rawData.getColumn5());
        orderExcel.setConsignorCode(rawData.getColumn6());
        orderExcel.setConsignorPhone(rawData.getColumn7());

        if (!StringUtils.isBlank(rawData.getColumn8())) {
            orderExcel.setCategory(LogisticTransportCategory.fromName(rawData.getColumn8()));
        }
        orderExcel.setDriverName(rawData.getColumn9());
        orderExcel.setLicensePlateNumber(rawData.getColumn10());
        if (!StringUtils.isBlank(rawData.getColumn11())) {
            orderExcel.setCharter(rawData.getColumn11().equals("是"));
        }
        orderExcel.setDepartureStation(rawData.getColumn12());
        orderExcel.setDestinationStation(rawData.getColumn13());
        if (!StringUtils.isBlank(rawData.getColumn14())) {
            orderExcel.setPieces(Integer.valueOf(rawData.getColumn14()));
        }
        if (!StringUtils.isBlank(rawData.getColumn15())) {
            orderExcel.setLoadingWeight(new BigDecimal(rawData.getColumn15()));
        }
        if (!StringUtils.isBlank(rawData.getColumn16())) {
            orderExcel.setFreightCharge(new BigDecimal(rawData.getColumn16()));
        }
        if (!StringUtils.isBlank(rawData.getColumn17())) {
            orderExcel.setOtherCharge(new BigDecimal(rawData.getColumn17()));
        }
        if (!StringUtils.isBlank(rawData.getColumn18())) {
            orderExcel.setReceivableFreightCharge(new BigDecimal(rawData.getColumn18()));
        }
        if (!StringUtils.isBlank(rawData.getColumn19())) {
            orderExcel.setPayer(LogisticTransportOrderPayer.fromName(rawData.getColumn19()));
        }
        orderExcel.setRemark(rawData.getColumn23());
    }

    public List<LogisticTransportBillExportVo> billToExport(List<LogisticTransportBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return Collections.emptyList();
        }
        List<LogisticTransportBillExportVo> billExports = new ArrayList<>(bills.size());
        for (LogisticTransportBillVo bill : bills) {
            LogisticTransportBillExportVo billExport = new LogisticTransportBillExportVo();
            billExport.setPaidTime(bill.getPaidTime());
            billExport.setOrderSerialNo(bill.getOrderSerialNo());
            billExport.setOrderYearMonthDay(bill.getOrderYearMonthDay());
            billExport.setMerchantName(bill.getMerchantName());
            billExport.setPayer(bill.getPayer().getName());
            billExport.setDriverName(bill.getDriverName());
            billExport.setLicensePlateNumber(bill.getLicensePlateNumber());
            billExport.setCharter(BooleanUtils.isTrue(bill.getCharter()) ? "是" : "否");
            billExport.setDepartureStation(bill.getDepartureStation());
            billExport.setDestinationStation(bill.getDestinationStation());
            billExport.setPieces(bill.getPieces());
            billExport.setLoadingWeight(bill.getLoadingWeight());
            billExport.setAmount(bill.getReceivableFreightCharge());
            billExport.setPayWay(PayWay.fromValue(bill.getPayWay()).getDescription());
            billExport.setPaidAmount(BooleanUtils.isTrue(bill.getStatus()) ? bill.getReceivableFreightCharge() : BigDecimal.ZERO);
            billExport.setPaidStillAmount(BooleanUtils.isTrue(bill.getStatus()) ? BigDecimal.ZERO : bill.getReceivableFreightCharge());
            billExport.setRemark(bill.getOrderRemark());
            billExports.add(billExport);
        }
        return billExports;
    }

    private List<LogisticTransportOrderExportVo> orderToExport(List<LogisticTransportOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<LogisticTransportOrderExportVo> orderExports = new ArrayList<>(orders.size());
        for (LogisticTransportOrderVo order : orders) {
            LogisticTransportOrderExportVo orderExport = new LogisticTransportOrderExportVo();
            orderExport.setSerialNo(order.getSerialNo());
            orderExport.setYearMonthDay(order.getYearMonthDay());
            orderExport.setConsignorName(order.getConsignorName());
            orderExport.setConsignorCode(order.getConsignorCode());
            orderExport.setConsignorPhone(order.getConsignorPhone());
            orderExport.setCategory(order.getCategory().getName());
            orderExport.setDriverName(order.getDriverName());
            orderExport.setLicensePlateNumber(order.getLicensePlateNumber());
            orderExport.setCharter(BooleanUtils.isTrue(order.getCharter()) ? "是" : "否");
            orderExport.setDepartureStation(order.getDepartureStation());
            orderExport.setDestinationStation(order.getDestinationStation());
            orderExport.setPieces(order.getPieces());
            orderExport.setLoadingWeight(order.getLoadingWeight());
            orderExport.setFreightCharge(order.getFreightCharge());
            orderExport.setOtherCharge(order.getOtherCharge());
            orderExport.setReceivableFreightCharge(order.getReceivableFreightCharge());
            orderExport.setPayer(null != order.getPayer() ? order.getPayer().getName() : StringUtils.EMPTY);
            orderExport.setRemark(order.getRemark());
            orderExport.setAuditorName(null != order.getAuditorName() ? order.getAuditorName() : StringUtils.EMPTY);
            orderExport.setAuditTime(order.getAuditTime());
            orderExports.add(orderExport);
        }
        return orderExports;
    }
}
