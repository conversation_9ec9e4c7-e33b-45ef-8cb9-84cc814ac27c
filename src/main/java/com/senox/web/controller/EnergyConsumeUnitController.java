package com.senox.web.controller;

import com.senox.common.constant.device.EnergyType;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.realty.vo.EnergyConsumeUnitVo;
import com.senox.web.service.EnergyConsumeUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:04
 */
@Api(tags = "能源消费单元")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/energy/consumeUnit")
public class EnergyConsumeUnitController extends BaseController {

    private final EnergyConsumeUnitService unitService;

    @ApiOperation("添加能源消费单元")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addConsumeUnit(@Validated @RequestBody EnergyConsumeUnitVo unit) {
        return unitService.addConsumeUnit(unit);
    }

    @ApiOperation("更新能源消费单元")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateConsumeUnit(@Validated @RequestBody EnergyConsumeUnitVo unit) {
        if (!WrapperClassUtils.biggerThanLong(unit.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        unitService.updateConsumeUnit(unit);
    }

    @ApiOperation("获取能源消费单元详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public EnergyConsumeUnitVo findConsumeUnitById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return unitService.findById(id);
    }

    @ApiOperation("能源消费单元列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<EnergyConsumeUnitVo> listConsumeUnit(@RequestParam Integer type) {
        EnergyType energyType = EnergyType.fromValue(type);
        if (energyType == null) {
            throw new InvalidParameterException();
        }

        return unitService.list(type);
    }
}
