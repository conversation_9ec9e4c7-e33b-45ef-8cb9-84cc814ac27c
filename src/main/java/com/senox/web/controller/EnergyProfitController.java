package com.senox.web.controller;

import com.senox.common.constant.device.EnergyType;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.EnergyProfitEditVo;
import com.senox.realty.vo.EnergyProfitItemVo;
import com.senox.realty.vo.EnergyProfitSearchVo;
import com.senox.realty.vo.EnergyProfitVo;
import com.senox.web.service.EnergyProfitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/26 14:37
 */
@Api(tags = "能源损益")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/energy/profit")
public class EnergyProfitController extends BaseController {

    private final EnergyProfitService profitService;

    @ApiOperation("生成能源损益")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public EnergyProfitVo generate(@RequestBody BillTimeVo billTime, @RequestParam EnergyType type) {
        if (!WrapperClassUtils.biggerThanInt(billTime.getYear(), 0)
                || !WrapperClassUtils.biggerThanInt(billTime.getMonth(), 0)) {
            throw new InvalidParameterException();
        }

        return profitService.generateProfits(billTime, type);
    }

    @ApiOperation("保存能源损益")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public EnergyProfitVo save(@Validated @RequestBody EnergyProfitEditVo profitEdit) {
        return profitService.saveProfit(profitEdit);
    }

    @ApiOperation("获取能源损益")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public EnergyProfitVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return profitService.findProfitById(id);
    }

    @ApiOperation("能源损益页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<EnergyProfitVo> listProfitPage(EnergyProfitSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return profitService.listProfitPage(search);
    }

    @ApiOperation("能源损益明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/item/list/{profitId}")
    public List<EnergyProfitItemVo> listProfitItem(@PathVariable Long profitId) {
        if (!WrapperClassUtils.biggerThanLong(profitId, 0L)) {
            throw new InvalidParameterException();
        }

        return profitService.listProfitItem(profitId);
    }
}
