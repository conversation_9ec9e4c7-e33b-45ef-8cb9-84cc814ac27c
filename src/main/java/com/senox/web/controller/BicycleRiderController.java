package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.*;
import com.senox.web.service.BicycleRiderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 15:42
 */
@Api(tags = "三轮车配送骑手")
@RestController
@RequestMapping("/web/bicycle/rider")
@RequiredArgsConstructor
public class BicycleRiderController {

    private final BicycleRiderService bicycleRiderService;

    @ApiOperation("添加三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleRider(@Validated(Add.class) @RequestBody BicycleRiderVo bicycleRiderVo) {
        return bicycleRiderService.addBicycleRider(bicycleRiderVo);
    }

    @ApiOperation("修改三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicycleRider(@Validated(Update.class) @RequestBody BicycleRiderVo bicycleRiderVo) {
        bicycleRiderService.updateBicycleRider(bicycleRiderVo);
    }

    @ApiOperation("根据id获取三轮车骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleRiderVo findById(@PathVariable Long id) {
        return bicycleRiderService.findById(id);
    }

    @ApiOperation("删除三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBicycleRider(@PathVariable Long id) {
        bicycleRiderService.deleteBicycleRider(id);
    }

    @ApiOperation("三轮车配送骑手列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleRiderVo> listRider(@RequestBody BicycleRiderSearchVo searchVo) {
        return bicycleRiderService.listRider(searchVo);
    }

    @ApiOperation("根据id获取三轮车骑手考勤信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/attendance/get/{id}")
    public BicycleRiderAttendanceVo findAttendanceById(@PathVariable Long id) {
        return bicycleRiderService.findAttendanceById(id);
    }

    @ApiOperation("三轮车骑手考勤信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/attendance/list")
    public PageResult<BicycleRiderAttendanceVo> listRiderAttendance(@RequestBody BicycleRiderAttendanceSearchVo searchVo) {
        return bicycleRiderService.listRiderAttendance(searchVo);
    }

    @ApiOperation("三轮车配送骑手信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/info")
    public List<BicycleRiderInfoVo> listRider() {
        return bicycleRiderService.listRiderInfo();
    }
}
