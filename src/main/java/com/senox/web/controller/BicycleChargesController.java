package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import com.senox.web.service.BicycleChargesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-19
 */
@Api(tags = "三轮车-收费标准")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/bicycle/charges")
public class BicycleChargesController extends BaseController {
    private final BicycleChargesService chargesService;

    @ApiOperation("添加收费标准")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void addCharges(@Validated({Add.class}) @RequestBody BicycleChargesVo chargesVo) {
        chargesService.addCharges(chargesVo);
    }

    @ApiOperation("根据id查询收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{chargesId}")
    public BicycleChargesVo getChargesById(@PathVariable Long chargesId) {
        return chargesService.getChargesById(chargesId);
    }


    @ApiOperation("删除收费标准")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/delete/{id}")
    public void deleteCharges(@PathVariable Long id) {
        chargesService.deleteCharges(id);
    }

    @ApiOperation("修改收费标准")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void updateCharges(@Validated({Update.class}) @RequestBody BicycleChargesVo chargesVo) {
        chargesService.updateCharges(chargesVo);
    }

    @ApiOperation("收费标准列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<BicycleChargesVo> chargesListPage(@RequestBody BicycleChargesSearchVo searchVo) {
        return chargesService.chargesListPage(searchVo);
    }

    @ApiOperation("添加收费标准明细")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/detail/add")
    public void addDetail(@Validated({Add.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo) {
        chargesService.addChargesDetail(chargesDetailVo);
    }

    @ApiOperation("修改收费标准明细")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/detail/update")
    public void updateDetail(@Validated({Update.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo) {
        chargesService.updateChargesDetail(chargesDetailVo);
    }

    @ApiOperation("删除收费标准明细")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/detail/delete")
    public void deleteDetail(@RequestBody List<Long> chargesDetailIds) {
        chargesService.deleteChargesDetail(chargesDetailIds);
    }

    @ApiOperation("根据收费标准查询明细列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/detail/list/{chargesId}")
    public List<BicycleChargesDetailVo> listDetailByCharges(@PathVariable Long chargesId) {
        return chargesService.listDetailByCharges(chargesId);
    }
}
