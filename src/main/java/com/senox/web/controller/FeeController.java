package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.realty.constant.FeeCategory;
import com.senox.realty.vo.FeeVo;
import com.senox.web.service.FeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/1/19 8:33
 */
@Api(tags = "基本信息 - 费项")
@RestController
@RequestMapping("/web/dictionary/fee")
public class FeeController {

    @Autowired
    private FeeService feeService;

    @ApiOperation("添加费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addFee(@Validated({Add.class}) @RequestBody FeeVo fee) {
        return feeService.addFee(fee);
    }

    @ApiOperation("更新费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateFee(@Validated({Update.class}) @RequestBody FeeVo fee) {
        if (fee.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        feeService.updateFee(fee);
    }

    @ApiOperation("删除费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteFee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        feeService.deleteFee(id);
    }

    @ApiOperation("获取费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public FeeVo getFee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return feeService.findFeeById(id);
    }

    @ApiOperation("费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<FeeVo> listFee(@RequestParam(required = false) Integer category) {
        if (category != null) {
            FeeCategory feeCategory = FeeCategory.fromValue(category);
            if (feeCategory == null) {
                throw new InvalidParameterException("无效的费项类别");
            }
        }
        return feeService.listFee(category);
    }
}
