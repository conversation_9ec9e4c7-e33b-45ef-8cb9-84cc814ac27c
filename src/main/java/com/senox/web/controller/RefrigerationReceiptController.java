package com.senox.web.controller;

import com.senox.cold.vo.*;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.web.service.RefrigerationReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/2/20 13:49
 */
@Api(tags = "冷藏发票")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/refrigeration/receipt")
public class RefrigerationReceiptController {

    private final RefrigerationReceiptService receiptService;


    @ApiOperation("冷藏发票申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply")
    public void apply(@Validated(Add.class) @RequestBody RefrigerationReceiptManagerVo managerVo) {
        receiptService.apply(managerVo);
    }

    @ApiOperation("冷藏发票状态重置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/refresh/{id}")
    public void refreshRefrigerationReceipt(@PathVariable Long id) {
        receiptService.refreshRefrigerationReceipt(id);
    }

    @ApiOperation("冷藏发票分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<RefrigerationReceiptVo> page(@RequestBody RefrigerationReceiptSearchVo searchVo) {
        return receiptService.page(searchVo);
    }

    @ApiOperation("冷藏账单发票信息详细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/info/bill/list/{id}")
    public List<RefrigerationMonthBillReceiptInfoVo> applyBillInfoList(@PathVariable Long id) {
        return receiptService.applyBillInfoList(id);
    }

    @ApiOperation("冷藏发票列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/info/list/{id}/{detail}")
    public List<ReceiptApplyVo> applyInfoList(@PathVariable Long id, @PathVariable Boolean detail) {
        return receiptService.applyInfoList(id, detail);
    }

    @ApiOperation("根据客户编号查询最近开票抬头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/lastTax")
    public TaxHeaderVo lastTaxByCustomerSerial(@RequestParam String customerSerial) {
        return receiptService.lastTaxByCustomerSerial(customerSerial);
    }
}
