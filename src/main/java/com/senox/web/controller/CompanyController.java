package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.user.vo.CompanyVo;
import com.senox.web.service.CompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 11:03
 */
@Api(tags = "企业")
@RestController
@RequestMapping("/web/company")
public class CompanyController extends BaseController {

    @Autowired
    private CompanyService companyService;

    @ApiOperation("添加企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addCompany(@Validated({Add.class}) @RequestBody CompanyVo company) {
        return companyService.addCompany(company);
    }

    @ApiOperation("更新企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateCompany(@Validated(Update.class) @RequestBody CompanyVo company) {
        if (company.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        companyService.updateCompany(company);
    }

    @ApiOperation("删除企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteCompany(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        companyService.deleteCompany(id);
    }

    @ApiOperation("根据id获取企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public CompanyVo getCompany(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return companyService.findById(id);
    }

    @ApiOperation("企业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<CompanyVo> listCompany() {
        return companyService.listCompany();
    }
}
