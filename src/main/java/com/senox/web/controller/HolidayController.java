package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.context.AdminContext;
import com.senox.user.vo.HolidaySearchVo;
import com.senox.user.vo.HolidayVo;
import com.senox.web.service.HolidayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/19 11:55
 */
@Api(tags = "假期")
@RestController
@RequestMapping("/web/holiday")
public class HolidayController extends BaseController {

    @Autowired
    private HolidayService holidayService;

    @ApiOperation("保存假期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public void saveHolidays(@RequestBody List<HolidayVo> holidays) {
        if (CollectionUtils.isEmpty(holidays)) {
            throw new InvalidParameterException("无效的参数");
        }
        if (holidays.stream().anyMatch(x -> x.getHoliday() == null)) {
            throw new InvalidParameterException("无效的参数，假期日期不能为空");
        }
        if (holidays.stream().map(HolidayVo::getHoliday).distinct().count() < holidays.size()) {
            throw new InvalidParameterException("无效的参数，假期日期不能重复");
        }
        holidayService.saveHolidays(holidays);
    }

    @ApiOperation("删除假期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete")
    public void deleteHolidays(@RequestBody List<LocalDate> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            throw new InvalidParameterException("无效的参数");
        }
        holidayService.deleteHolidays(dateList);
    }

    @ApiOperation("假期列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<HolidayVo> listHoliday(@RequestBody HolidaySearchVo searchVo) {
        if (searchVo.getStartDate() == null || searchVo.getEndDate() == null) {
            throw new InvalidParameterException("无效的参数");
        }
        return holidayService.listHoliday(searchVo);
    }
}
