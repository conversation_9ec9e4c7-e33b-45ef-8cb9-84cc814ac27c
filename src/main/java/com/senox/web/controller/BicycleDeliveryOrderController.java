package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.BusinessException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.BicycleDeliveryOrderService;
import com.senox.web.vo.BicycleRiderCountExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/21 14:01
 */
@Api(tags = "三轮车配送单")
@RestController
@RequestMapping("/web/bicycle/delivery/order")
@RequiredArgsConstructor
public class BicycleDeliveryOrderController extends BaseController{

    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;

    @ApiOperation("添加三轮车配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleDeliveryOrder(@Validated(Add.class) @RequestBody BicycleDeliveryOrderVo orderVo) {
        return bicycleDeliveryOrderService.addBicycleDeliveryOrder(orderVo);
    }

    @ApiOperation("添加费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/addCharge")
    public void addBicycleOrderCharge(@Validated(Add.class) @RequestBody BicycleOrderChargeVo chargeVo) {
        bicycleDeliveryOrderService.addBicycleOrderCharge(chargeVo);
    }

    @ApiOperation("更新货物明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/goodsDetail")
    public void updateBicycleOrderDetail(@RequestBody List<BicycleOrderGoodsDetailVo> goodsDetailVos) {
        bicycleDeliveryOrderService.updateBicycleOrderDetail(goodsDetailVos);
    }

    @ApiOperation("根据id获取配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleDeliveryOrderVo findDeliveryOrderById(@PathVariable Long id) {
        return bicycleDeliveryOrderService.findDeliveryOrderById(id);
    }

    @ApiOperation("根据id获取配送详细单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/get/{id}")
    public BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(@PathVariable Long id) {
        return bicycleDeliveryOrderService.findDeliveryOrderDetailById(id);
    }

    @ApiOperation("根据订单流水号获取配送详细单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/getByOrderSerialNo/{orderSerialNo}")
    public List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(@PathVariable String orderSerialNo) {
        return bicycleDeliveryOrderService.findDeliveryOrderDetailByOrderSerialNo(orderSerialNo);
    }

    @ApiOperation("指定骑手配送三轮车配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/appoint")
    public void appointBicycleDeliveryOrderReider(@Validated(Update.class) @RequestBody BicycleDeliveryOrderVo orderVo) {
        bicycleDeliveryOrderService.appointBicycleDeliveryOrderReider(orderVo);
    }

    @ApiOperation("取消骑手配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/cancel/{id}")
    public void cancelRiderDeliveryOrderById(@PathVariable Long id) {
        bicycleDeliveryOrderService.cancelRiderDeliveryOrderById(id);
    }

    @ApiOperation("查询骑手情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/statistics")
    public BicycleDayCountRiderVo dayCountRider(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleDeliveryOrderService.riderStatistics(searchVo);
    }

    @ApiOperation("订单统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics")
    public List<BicycleOrderCountVo> listOrderCount(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleDeliveryOrderService.statistics(searchVo);
    }

    @ApiOperation("合并配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/merged")
    public void merged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList, @RequestParam(required = false) String deliveryOrderSerialNo) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            throw new BusinessException("订单流水号集合不能为空！");
        }
        bicycleDeliveryOrderService.merged(orderSerialNoList, deliveryOrderSerialNo);
    }

    @ApiOperation("取消合并")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel/merged")
    public void cancelMerged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            throw new BusinessException("订单流水号集合不能为空！");
        }
        bicycleDeliveryOrderService.cancelMerged(orderSerialNoList);
    }

    @ApiOperation("今日骑手完成单量统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/count/list")
    public PageStatisticsResult<BicycleRiderCountVo, BicycleRiderCountVo> riderCountList(@RequestBody BicycleRiderCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        // page
        PageResult<BicycleRiderCountVo> page = bicycleDeliveryOrderService.riderCountList(searchVo);
        //sum
        BicycleRiderCountVo sumVo = bicycleDeliveryOrderService.sumRiderCount(searchVo);
        return new PageStatisticsResult<>(page, sumVo);
    }

    @ApiOperation("骑手历史完成单量统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/history/count/list")
    public PageStatisticsResult<BicycleRiderCountVo, BicycleRiderCountVo> riderHistoryCountList(@RequestBody BicycleRiderCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        // page
        PageResult<BicycleRiderCountVo> page = bicycleDeliveryOrderService.riderHistoryCountList(searchVo);
        //sum
        BicycleRiderCountVo sumVo = bicycleDeliveryOrderService.sumRiderHistoryCount(searchVo);
        return new PageStatisticsResult<>(page, sumVo);
    }

    @ApiOperation("导出骑手历史完成单量")
    @GetMapping("/rider/history/count/export")
    public void exportRiderHistoryCount(HttpServletResponse response, BicycleRiderCountSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<BicycleRiderCountVo> dataList = bicycleDeliveryOrderService.riderHistoryCountList(searchVo).getDataList();
        List<BicycleRiderCountExportVo> exportList =  new ArrayList<>(dataList.size() + 1);
        BicycleRiderCountVo sumVo = bicycleDeliveryOrderService.sumRiderHistoryCount(searchVo);

        if (!CollectionUtils.isEmpty(dataList)) {
            for (int index = 0; index < dataList.size(); index++) {
                BicycleRiderCountVo riderCountVo = dataList.get(index);
                BicycleRiderCountExportVo excelVo = buildRiderExcelVo(riderCountVo);
                excelVo.setSerial(index + 1);
                exportList.add(excelVo);
            }
            exportList.add(sumRiderExcelVo(sumVo));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_RIDER_HISTORY_COUNT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleRiderCountExportVo.class)
                .sheet(SenoxConst.Export.TITLE_RIDER_HISTORY_COUNT)
                .doWrite(exportList);
    }


    @ApiOperation("配送单提货信息分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/info/page")
    public PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(@RequestBody BicycleDeliveryInfoSearchVo searchVo) {
        return bicycleDeliveryOrderService.deliveryInfoPage(searchVo);
    }

    @ApiOperation("根据订单编号查询配送订单详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deliveryDetailInfo")
    public List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(@RequestParam String orderSerialNo) {
        return bicycleDeliveryOrderService.deliveryDetailInfoByOrderSerialNo(orderSerialNo);
    }

    private BicycleRiderCountExportVo sumRiderExcelVo(BicycleRiderCountVo sumVo) {
        BicycleRiderCountExportVo sumExportVo = new BicycleRiderCountExportVo();
        sumExportVo.setBillDate(SenoxConst.Export.COLUMN_SUM);
        sumExportVo.setTodayCount(sumVo.getTodayCount());
        sumExportVo.setAvgDeliveryTime(sumVo.getAvgDeliveryTimeDescribe());
        sumExportVo.setTodayPieces(sumVo.getTodayPieces());
        sumExportVo.setIncome(sumVo.getTodayIncome());
        return sumExportVo;
    }

    private BicycleRiderCountExportVo buildRiderExcelVo(BicycleRiderCountVo riderCountVo) {
        BicycleRiderCountExportVo exportVo = new BicycleRiderCountExportVo();
        exportVo.setBillDate(riderCountVo.getBillDate().toString());
        exportVo.setRiderName(riderCountVo.getRiderName());
        exportVo.setTodayCount(riderCountVo.getTodayCount());
        exportVo.setAvgDeliveryTime(riderCountVo.getAvgDeliveryTimeDescribe());
        exportVo.setTodayPieces(riderCountVo.getTodayPieces());
        exportVo.setIncome(riderCountVo.getTodayIncome());
        return exportVo;
    }
}
