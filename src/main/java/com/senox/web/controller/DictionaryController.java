package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.user.vo.BusinessCategoryVo;
import com.senox.web.service.BusinessCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8 15:35
 */
@Api(tags = "数据字典")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/dictionary")
public class DictionaryController extends BaseController {

    private final BusinessCategoryService categoryService;

    @ApiOperation("添加经营范围")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/businessCategory/add")
    public Long addBusinessCategory(@Validated @RequestBody BusinessCategoryVo category) {
        return categoryService.addBusinessCategory(category);
    }

    @ApiOperation("更新经营范围")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/businessCategory/update")
    public void updateBusinessCategory(@Validated @RequestBody BusinessCategoryVo category) {
        if (!WrapperClassUtils.biggerThanLong(category.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        categoryService.updateBusinessCategory(category);
    }

    @ApiOperation("删除经营范围")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/businessCategory/delete/{id}")
    public void deleteBusinessCategory(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        categoryService.deleteBusinessCategory(id);
    }

    @ApiOperation("经营范围列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/businessCategory/list")
    public List<BusinessCategoryVo> listBusinessCategory() {
        return categoryService.listBusinessCategory();
    }
}
