package com.senox.web.controller;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.AntiFraudWorkRecordSearchVo;
import com.senox.realty.vo.AntiFraudWorkRecordStatistics;
import com.senox.realty.vo.AntiFraudWorkRecordVo;
import com.senox.web.service.AntiFraudWorkRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "反诈宣传工作记录")
@RequiredArgsConstructor
@RequestMapping("/web/anti/fraud/work/record")
@RestController
public class AntiFraudWorkRecordController extends BaseController {
    private final AntiFraudWorkRecordService workRecordService;

    @ApiOperation("添加")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestBody AntiFraudWorkRecordVo workRecord) {
        workRecordService.add(workRecord);
    }

    @ApiOperation("修改")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@RequestBody AntiFraudWorkRecordVo workRecord) {
        workRecordService.update(workRecord);
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/findById/{id}")
    public AntiFraudWorkRecordVo findById(@PathVariable Long id) {
        return workRecordService.findById(id);
    }

    @ApiOperation("根据id批量删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/deleteById")
    public void deleteByIds(@RequestBody List<Long> ids) {
        workRecordService.deleteByIds(ids);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list/page")
    public PageStatisticsResult<AntiFraudWorkRecordVo, AntiFraudWorkRecordStatistics> pageList(@RequestBody AntiFraudWorkRecordSearchVo search) {
        return workRecordService.pageList(search);
    }
}
