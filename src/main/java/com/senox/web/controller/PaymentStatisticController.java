package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.car.vo.*;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.vo.TollManDailyStatisticVo;
import com.senox.pm.vo.TollPayWayExtVo;
import com.senox.pm.vo.TollStatisticSearchVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.TollStatisticConvertor;
import com.senox.web.service.PaymentStatisticService;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/29 15:24
 */
@Api(tags = "收费统计")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/statistic/")
public class PaymentStatisticController extends BaseController{

    private final PaymentStatisticService statisticService;
    private final TollStatisticConvertor statisticConvertor;

    @ApiOperation("收费员日收费统计页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/tollMan/daily/page")
    public PageStatisticsResult<PaymentTollManDailyStatisticVo, PaymentTollManDailyStatisticVo> listTollManDailyStatisticPage(@RequestBody TollStatisticSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<PaymentTollManDailyStatisticVo> page = statisticService.listTollManDailyStatisticPage(search);
        PaymentTollManDailyStatisticVo sum = statisticService.sumTollManDailyStatistic(search);
        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出收费员日收费统计列表")
    @GetMapping("/order/tollMan/daily/export")
    public void exportTollManDailyStatistic(HttpServletResponse response, TollStatisticSearchVo search) throws IOException {
        // list
        List<PaymentTollManDailyStatisticVo> list = statisticService.buildListPaymentTollManStatistic(search);

        int index = 1;
        List<TollManDailyStatisticExportVo> exportList = new ArrayList<>(list.size());
        for (PaymentTollManDailyStatisticVo item : list) {
            TollManDailyStatisticExportVo exportItem = statisticConvertor.tollManStatistic2ExcelVo(item);
            exportItem.setSerialNo(index++);
            exportList.add(exportItem);
        }

        // sum
        PaymentTollManDailyStatisticVo sum = statisticService.sumTollManDailyStatistic(search);
        TollManDailyStatisticExportVo exportSum = statisticConvertor.tollManStatistic2ExcelVo(sum);
        if (exportSum != null) {
            exportSum.setTollMan(SenoxConst.Export.COLUMN_SUM);
            exportList.add(exportSum);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_STATISTIC_TOLL_MAN_DAILY, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), TollManDailyStatisticExportVo.class)
                .sheet(SenoxConst.Export.SHEET_STATISTIC_TOLL_MAN_DAILY)
                .doWrite(exportList);
    }

    @ApiOperation("支付通道日统计页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/payWay/daily/page")
    public PageStatisticsResult<PaymentDailyWayStatisticVo, PaymentDailyWayStatisticVo> listPayWayDailyStatisticPage(@RequestBody TollStatisticSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<PaymentDailyWayStatisticVo> page = statisticService.listPayWayDailyStatisticPage(search);
        PaymentDailyWayStatisticVo sum = statisticService.sumPayWayDailyStatistic(search);
        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出支付通道日统计列表")
    @GetMapping("/order/payWay/daily/export")
    public void exportPayWayDailyStatistic(HttpServletResponse response, TollStatisticSearchVo search) throws IOException {
        // list
        List<PaymentDailyWayStatisticVo> list = statisticService.buildListPayWayDailyStatistic(search);

        int index = 1;
        List<PayWayDailyStatisticExportVo> exportList = new ArrayList<>(list.size());
        for (PaymentDailyWayStatisticVo item : list) {
            PayWayDailyStatisticExportVo exportItem = statisticConvertor.payWayStatistic2ExcelVo(item);
            exportItem.setSerialNo(String.valueOf(index));
            index++;
            exportList.add(exportItem);
        }

        // sum
        PaymentDailyWayStatisticVo sum = statisticService.sumPayWayDailyStatistic(search);
        PayWayDailyStatisticExportVo exportSum = statisticConvertor.payWayStatistic2ExcelVo(sum);
        if (exportSum != null) {
            exportSum.setSerialNo(SenoxConst.Export.COLUMN_SUM);
            exportList.add(exportSum);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_STATISTIC_PAY_WAY_DAILY, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), PayWayDailyStatisticExportVo.class)
                .sheet(SenoxConst.Export.SHEET_STATISTIC_PAY_WAY_DAILY)
                .doWrite(exportList);
    }

    @ApiOperation("支付通道日明细备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/payWay/toll/remark")
    public void listTollPayWayStatisticPage(@Validated  @RequestBody TollPayWayExtVo ext) {
        statisticService.remarkTollPayWay(ext);
    }

    @ApiOperation("支付通道日明细页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/payWay/toll/page")
    public PageStatisticsResult<PaymentWayStatisticVo, PaymentWayStatisticVo> listTollPayWayStatisticPage(@RequestBody TollStatisticSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<PaymentWayStatisticVo> page = statisticService.listPaymentWayStatisticPage(search);
        PaymentWayStatisticVo sum = statisticService.sumPaymentWayStatistic(search);
        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出支付通道日明细")
    @GetMapping("/order/payWay/toll/export")
    public void exportTollPayWayStatistic(HttpServletResponse response, TollStatisticSearchVo search) throws IOException {
        // list
        List<PaymentWayStatisticVo> list = statisticService.listPaymentWayStatistic(search);

        int index = 1;
        List<PaymentWayStatisticExportVo> exportList = new ArrayList<>(list.size());
        for (PaymentWayStatisticVo item : list) {
            PaymentWayStatisticExportVo exportItem = statisticConvertor.paymentWayStatistic2ExcelVo(item);
            exportItem.setSerialNo(String.valueOf(index));
            index++;
            exportList.add(exportItem);
        }

        // sum
        PaymentWayStatisticVo sum = statisticService.sumPaymentWayStatistic(search);
        PaymentWayStatisticExportVo exportSum = statisticConvertor.paymentWayStatistic2ExcelVo(sum);
        if (exportSum != null) {
            exportSum.setSerialNo(SenoxConst.Export.COLUMN_SUM);
            exportList.add(exportSum);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_STATISTIC_PAY_WAY_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), PaymentWayStatisticExportVo.class)
                .sheet(SenoxConst.Export.SHEET_STATISTIC_PAY_WAY_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("停车缴费统计备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/parking/payment/statistic/remark")
    public void remarkParkingPaymentStatistic(@Validated @RequestBody ParkingPaymentRemarkVo remark) {
        statisticService.remarkParkingPaymentStatistic(remark);
    }

    @ApiOperation("停车缴费统计页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/parking/payment/statistic/page")
    public PageStatisticsResult<ParkingPaymentStatisticVo, ParkingPaymentStatisticVo> listParkingPaymentStatistic(@RequestBody ParkingPaymentSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<ParkingPaymentStatisticVo> page = statisticService.listParkingPaymentStatisticPage(search);
        ParkingPaymentStatisticVo sum = statisticService.sumParkingPaymentStatistic(search);
        return new PageStatisticsResult<>(page, sum);
    }


    @ApiOperation("导出停车缴费统计")
    @GetMapping("/parking/payment/statistic/export")
    public void exportParkingPaymentStatistic(HttpServletResponse response, ParkingPaymentSearchVo search) throws IOException {
        // list
        List<ParkingPaymentStatisticVo> list = statisticService.listParkingPaymentStatistic(search);

        int index = 1;
        List<ParkingPaymentStatisticExportVo> exportList = new ArrayList<>(list.size());
        for (ParkingPaymentStatisticVo item : list) {
            ParkingPaymentStatisticExportVo exportItem = statisticConvertor.parkingPaymentStatistic2ExcelVo(item);
            exportItem.setSerialNo(String.valueOf(index));
            index++;
            exportList.add(exportItem);
        }

        // sum
        ParkingPaymentStatisticVo sum = statisticService.sumParkingPaymentStatistic(search);
        ParkingPaymentStatisticExportVo exportSum = statisticConvertor.parkingPaymentStatistic2ExcelVo(sum);
        if (exportSum != null) {
            exportSum.setSerialNo(SenoxConst.Export.COLUMN_SUM);
            exportList.add(exportSum);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_STATISTIC_PARKING_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ParkingPaymentStatisticExportVo.class)
                .sheet(SenoxConst.Export.SHEET_STATISTIC_PARKING_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("停车系统统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/parking/statistic")
    public ParkingStatisticsVo parkingPaymentStatistic(@RequestBody ParkingPaymentSearchVo search) {
        return statisticService.parkingPaymentStatistic(search);
    }

    @ApiOperation("停车系统大屏统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/parking/statistic/screen")
    public List<ParkingStatisticsVo> parkingPaymentStatisticScreen(@RequestBody ParkingPaymentStatisticSearchVo searchVo) {
        return statisticService.parkingPaymentStatisticScreen(searchVo);
    }

    @ApiOperation("入场登记车辆大屏统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/parking/vehicle/statistic/screen")
    public List<ParkingVehicleDayReportVo> parkingVehicleStatisticScreen(@RequestBody ParkingVehicleStatisticsSearchVo searchVo) {
        return statisticService.parkingVehicleStatisticScreen(searchVo);
    }
}
