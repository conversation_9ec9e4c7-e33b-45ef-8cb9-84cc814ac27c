package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.FirefightingInspectPropertyTaskVo;
import com.senox.realty.vo.FirefightingInspectTaskItemSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import com.senox.realty.vo.FirefightingTaskItemDropVo;
import com.senox.web.service.FirefightingTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/15 15:45
 */
@Api(tags = "消防巡检任务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/task")
public class FirefightingTaskController extends BaseController {

    private final FirefightingTaskService taskService;

    @ApiOperation("添加巡检任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addInspectTask(@Validated @RequestBody FirefightingInspectTaskVo task) {
        return taskService.addInspectTask(task);
    }

    @ApiOperation("更新巡检任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateInspectTask(@RequestBody FirefightingInspectTaskVo task) {
        if (!WrapperClassUtils.biggerThanLong(task.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        taskService.updateInspectTask(task);
    }

    @ApiOperation("获取巡检任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public FirefightingInspectTaskVo findInspectTaskById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return taskService.findInspectTaskById(id);
    }

    @ApiOperation("巡检任务页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<FirefightingInspectTaskVo> listInspectTaskPage(@RequestBody FirefightingInspectTaskSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return taskService.listInspectTaskPage(search);
    }

    @ApiOperation("删除巡检子任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/item/delete")
    public void deleteInspectTaskItem(@Validated @RequestBody FirefightingTaskItemDropVo drop) {
        if (drop.getTaskItems() == null || CollectionUtils.isEmpty(drop.getTaskItems())) {
            throw new InvalidParameterException();
        }

        taskService.deleteInspectTaskItem(drop);
    }

    @ApiOperation("巡检子任务页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/item/page")
    public PageResult<FirefightingInspectPropertyTaskVo> listTaskItemPage(@RequestBody FirefightingInspectTaskItemSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return taskService.listTaskItemPage(search);
    }

}
