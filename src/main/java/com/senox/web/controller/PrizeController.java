package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.PrizeRecordsSearchVo;
import com.senox.user.vo.PrizeRecordsVo;
import com.senox.user.vo.PrizeSearchVo;
import com.senox.user.vo.PrizeVo;
import com.senox.web.service.PrizeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/15 15:49
 */
@Api(tags = "奖品")
@RestController
@RequestMapping("/web/prize")
@RequiredArgsConstructor
public class PrizeController {

    private final PrizeService prizeService;

    @ApiOperation("添加抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addPrize(@RequestBody PrizeVo prizeVo) {
        return prizeService.addPrize(prizeVo);
    }

    @ApiOperation("更新抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updatePrize(@RequestBody PrizeVo prizeVo) {
        prizeService.updatePrize(prizeVo);
    }

    @ApiOperation("根据id获取抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public PrizeVo findById(@PathVariable Long id) {
        return prizeService.findById(id);
    }

    @ApiOperation("根据id删除抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        prizeService.deleteById(id);
    }

    @ApiOperation("抽奖奖品分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<PrizeVo> pagePrize(@RequestBody PrizeSearchVo searchVo) {
        return prizeService.pagePrize(searchVo);
    }

    @ApiOperation("根据id获取抽奖记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/records/get/{id}")
    public PrizeRecordsVo findRecordsById(@PathVariable Long id) {
        return prizeService.findRecordsById(id);
    }

    @ApiOperation("抽奖记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/records/page")
    public PageResult<PrizeRecordsVo> pageRecords(@RequestBody PrizeRecordsSearchVo searchVo) {
        return prizeService.pageRecords(searchVo);
    }

    @ApiOperation("根据uuid兑奖")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/verify/prize")
    public void verifyPrize(@RequestParam String uuid) {
        prizeService.verifyPrize(uuid);
    }
}
