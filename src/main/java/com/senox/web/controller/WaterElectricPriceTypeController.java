package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.realty.constant.PriceType;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import com.senox.web.service.FeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/9 16:29
 */
@Api(tags = "基本信息 - 水/电价类别")
@RestController
@RequestMapping("/web/dictionary/waterElectricPriceType")
public class WaterElectricPriceTypeController {

    @Autowired
    private FeeService feeService;

    @ApiOperation("添加水/电价类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addPriceType(@Validated({Add.class}) @RequestBody WaterElectricPriceTypeVo priceType) {
        return feeService.addPriceType(priceType);
    }

    @ApiOperation("更新水/电价类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updatePriceType(@Validated({Update.class}) @RequestBody WaterElectricPriceTypeVo priceType) {
        if (priceType.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        feeService.updatePriceType(priceType);
    }

    @ApiOperation("删除水/电价类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deletePriceType(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        feeService.deletePriceType(id);
    }

    @ApiOperation("获取水/电价类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/get/{id}")
    public WaterElectricPriceTypeVo getPriceType(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return feeService.findPriceTypeById(id);
    }

    @ApiOperation("水/电价类别列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<WaterElectricPriceTypeVo> listPriceType(@RequestParam(required = false) Integer type) {
        if (type != null) {
            PriceType priceType = PriceType.fromValue(type);
            if (priceType == null) {
                throw new InvalidParameterException("无效的类别");
            }
        }
        return feeService.listPriceType(type);
    }
}
