package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.user.vo.RoleVo;
import com.senox.web.service.RoleCosService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/29 10:24
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/web/role")
public class RoleController extends BaseController {

    @Autowired
    private RoleCosService roleCosService;

    @ApiOperation("添加角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addRole(@Validated({Add.class}) @RequestBody RoleVo role) {
        return roleCosService.addRole(role);
    }

    @ApiOperation("更新角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateRole(@Validated({Update.class}) @RequestBody RoleVo role) {
        if (role.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        roleCosService.updateRole(role);
    }

    @ApiOperation("删除角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteRole(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        roleCosService.deleteRole(id);
    }

    @ApiOperation("根据id获取角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RoleVo getRole(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return roleCosService.findRoleById(id);
    }

    @ApiOperation("角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<RoleVo> listRole() {
        return roleCosService.listRole();
    }
}
