package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.flow.vo.*;
import com.senox.web.service.WorkFlowTaskService;
import com.senox.web.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 14:26
 */
@Api(tags = "流程管理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/work/flow")
public class WorkFlowController {
    private final WorkflowService workflowService;
    private final WorkFlowTaskService workFlowTaskService;

    @ApiOperation("流程添加")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void addFlow(@RequestBody WorkflowVo workflow) {
        workflowService.add(workflow);
    }

    @ApiOperation("根据流程id删除流程")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/add/{flowId}")
    public void deleteByFlowId(@PathVariable Long flowId) {
        workflowService.delete(flowId);
    }

    @ApiOperation("更新流程")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void updateFlow(@RequestBody WorkflowVo workflow) {
        workflowService.update(workflow);
    }

    @ApiOperation("根据流程编码查找流程")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findByCode/{flowCode}")
    public WorkflowVo findByCode(@PathVariable String flowCode) {
        return workflowService.findByCode(flowCode);
    }

    @ApiOperation("流程列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<WorkflowVo> flowList(@RequestBody WorkflowSearchVo search) {
        return workflowService.pageList(search);
    }

    @ApiOperation("根据实例id查询任务历史")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/task/history/instance/{instanceId}")
    public List<WorkflowTaskVo> taskHistoryByInstanceId(@PathVariable Long instanceId) {
        return workFlowTaskService.taskHistoryByInstanceId(instanceId);
    }

    @ApiOperation("根据任务id查询任务历史")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/task/history/{taskId}")
    public WorkflowTreeNode<WorkflowTaskVo> taskHistory(@PathVariable Long taskId) {
        return workFlowTaskService.taskHistory(taskId);
    }

    @ApiOperation("根据实例id查询当前处理人任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/task/currentAssigneeByInstanceId/{instanceId}")
    public WorkflowTaskVo currentAssigneeFindByInstanceId(@PathVariable Long instanceId) {
        if (!WrapperClassUtils.biggerThanLong(instanceId, 0L)) {
            throw new InvalidParameterException();
        }
        return workFlowTaskService.currentAssigneeFindByInstanceId(instanceId);
    }

    @ApiOperation("根据任务id查询当前处理人任务")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/task/currentAssigneeByTaskId/{taskId}")
    public WorkflowTaskVo currentAssigneeFindByTaskId(@PathVariable Long taskId) {
        return workFlowTaskService.currentAssigneeFindByTaskId(taskId);
    }

    @ApiOperation("待办任务")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/task/todoList")
    public PageResult<WorkflowTaskItemVo> todoList(@RequestBody WorkflowTaskItemSearchVo search) {
        return workFlowTaskService.todoList(search);
    }

    @ApiOperation("待办任务数")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/task/todoList/count")
    public Integer countTodoList(@RequestBody WorkflowTaskItemSearchVo search) {
        return workFlowTaskService.countTodoList(search);
    }

    @ApiOperation("已办任务")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/task/doneList")
    public PageResult<WorkflowTaskItemVo> doneList(@RequestBody WorkflowTaskItemSearchVo search) {
        return workFlowTaskService.doneList(search);
    }
}
