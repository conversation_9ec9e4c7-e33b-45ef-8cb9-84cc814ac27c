package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.realty.vo.StreetVo;
import com.senox.web.service.StreetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/14 16:58
 */
@Api(tags = "基本信息 - 街道")
@RestController
@RequestMapping("/web/dictionary/street")
public class StreetController {

    @Autowired
    private StreetService streetService;

    @ApiOperation("添加街道")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addStreet(@Validated({Add.class}) @RequestBody StreetVo street) {
        return streetService.addStreet(street);
    }

    @ApiOperation("更新街道")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateStreet(@Validated({Update.class}) @RequestBody StreetVo street) {
        if (street.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        streetService.updateStreet(street);
    }

    @ApiOperation("删除街道")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteStreet(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        streetService.deleteStreet(id);
    }

    @ApiOperation("获取街道")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/get/{id}")
    public StreetVo getStreet(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return streetService.findById(id);
    }

    @ApiOperation("区域街道列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/listRS/{regionId}")
    public List<StreetVo> listRegionStreet(@PathVariable Long regionId) {
        if (!WrapperClassUtils.biggerThanLong(regionId, 0L)) {
            throw new InvalidParameterException("无效的区域id");
        }
        return streetService.listRegionStreet(regionId);
    }

    @ApiOperation("街道列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<StreetVo> listStreet() {
        return streetService.listAll();
    }

}
