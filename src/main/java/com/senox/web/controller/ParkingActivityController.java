package com.senox.web.controller;

import com.senox.car.vo.ParkingActivitySearchVo;
import com.senox.car.vo.ParkingActivityVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.ParkingActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车场活动")
@RestController
@RequestMapping("/web/parking/activity")
@RequiredArgsConstructor
public class ParkingActivityController {
    private final ParkingActivityService activityService;

    @ApiOperation("添加活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addActivity(@RequestBody ParkingActivityVo activityVo) {
        activityService.addActivity(activityVo);
    }


    @ApiOperation("更新活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateActivity(@RequestBody ParkingActivityVo activityVo) {
        activityService.updateActivity(activityVo);
    }

    @ApiOperation("根据id获取活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public ParkingActivityVo findActivityById(@PathVariable Long id) {
        return activityService.findActivityById(id);
    }

    @ApiOperation("根据id获取活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteById/{id}")
    public void deleteActivityById(@PathVariable Long id) {
        activityService.deleteActivityById(id);
    }

    @ApiOperation("活动列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ParkingActivityVo> activityListPage(@RequestBody ParkingActivitySearchVo searchVo) {
        return activityService.activityListPage(searchVo);
    }

}
