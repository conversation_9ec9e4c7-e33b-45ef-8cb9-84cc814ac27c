package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.pm.constant.RefundWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.RefundOrderVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.BillTollVo;
import com.senox.realty.vo.RealtyDepositSearchVo;
import com.senox.realty.vo.RealtyDepositVo;
import com.senox.realty.vo.RefundBillPageResult;
import com.senox.realty.vo.TollSerialVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.RealtyDepositConvertor;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.RealtyDepositService;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.MixPayRequestVo;
import com.senox.web.vo.RealtyDepositExportVo;
import com.senox.web.vo.RealtyDepositRefundExportVo;
import com.senox.web.vo.RealtyDepositTollExportVo;
import com.senox.web.vo.TollPrintItemVo;
import com.senox.web.vo.TollPrintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/4 11:11
 */
@Api(tags = "物业押金")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/realtyDeposit")
public class RealtyDepositController extends BaseController {

    private final AdminUserService adminUserService;
    private final RealtyDepositService depositService;
    private final RealtyDepositConvertor depositConvertor;

    @ApiOperation("添加物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addDeposit(@RequestBody RealtyDepositVo deposit) {
        checkDeposit(deposit);
        return depositService.addDeposit(deposit);
    }

    @ApiOperation("批量添加物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batchAdd")
    public void batchAddDeposit(@RequestBody List<RealtyDepositVo> depositList) {
        depositList = depositList.stream().filter(x -> DecimalUtils.isPositive(x.getAmount())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(depositList)) {
            throw new InvalidParameterException();
        }

        depositService.batchAddDeposit(depositList);
    }

    @ApiOperation("添加物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateDeposit(@RequestBody RealtyDepositVo deposit) {
        if (!WrapperClassUtils.biggerThanLong(deposit.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        depositService.updateDeposit(deposit);
    }

    @ApiOperation("删除物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteDeposit(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        depositService.deleteDeposit(id);
    }

    @ApiOperation("添加物业押金票据号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/serial/update")
    public void updateDepositSerial(@Validated @RequestBody TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        depositService.updateDepositSerial(serial);
    }

    @ApiOperation("支付物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay/{id}")
    public void payDeposit(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        toll.setOperator(getAdminUserId());
        depositService.payDeposit(id, toll);
    }

    @ApiOperation("支付物业押金V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/V2/pay")
    public OrderResultVo payDepositBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return depositService.payDeposit(payRequest);
    }

    @ApiOperation("混合支付物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payMix")
    public List<OrderResultVo> mixPayBill(HttpServletRequest request, @Validated @RequestBody MixPayRequestVo payRequest) {
        // 校验支付参数
        validateMixPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return depositService.mixPayDeposit(payRequest);
    }

    @ApiOperation("撤销支付物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payRevoke/{id}")
    public void revokeDepositPayment(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        depositService.revokeDepositPayment(id);
    }

    @ApiOperation("退档物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund/{id}")
    public void refundDeposit(HttpServletRequest request, @PathVariable Long id, @RequestParam(required = false) RefundWay refundWay) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        if (refundWay == RefundWay.ORIGIN) {
            throw new BusinessException("暂不支持原路退回");
        }

        RefundOrderVo refundOrder = new RefundOrderVo();
        refundOrder.setTollMan(getAdminUserId());
        refundOrder.setRequestIp(RequestUtils.getIpAddr(request));
        refundOrder.setRefundWay(refundWay == null ? RefundWay.CASH : refundWay);
        depositService.refundDeposit(id, refundOrder);
    }

    @ApiOperation("撤销退档物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refundRevoke/{id}")
    public void revokeDepositRefund(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        depositService.revokeDepositRefund(id);
    }

    @ApiOperation("查找物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RealtyDepositVo findDepositById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return depositService.findById(id);
    }

    @ApiOperation("查找合同物业押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/contractDeposit/list/{contractId}")
    public List<RealtyDepositVo> listContractDeposit(@PathVariable Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            throw new InvalidParameterException();
        }
        return depositService.listContractDeposit(contractId);
    }

    @ApiOperation("打印物业押金票据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printDeposit(@PathVariable Long id,
                                    @RequestParam(required = false) Boolean refund,
                                    @RequestParam(required = false) Boolean refreshSerial) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        // 物业押金
        RealtyDepositVo deposit = depositService.findById(id);
        if (deposit == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到押金记录");
        }
        BillStatus billStatus = BillStatus.fromStatus(deposit.getStatus());
        if (BooleanUtils.isTrue(refund)) {
            if (billStatus != BillStatus.REFUND) {
                throw new BusinessException("账单未退费");
            }
        } else {
            if (billStatus == null || billStatus == BillStatus.INIT) {
                throw  new BusinessException("账单未缴费");
            }
        }

        // 票据号
        boolean newSerial = false;
        String serial = BooleanUtils.isTrue(refund) ? deposit.getRefundSerial() : deposit.getTollSerial();
        if (StringUtils.isBlank(serial) || BooleanUtils.isTrue(refreshSerial)) {
            newSerial = true;
            serial = adminUserService.getAndIncAdminTollSerial();
        }
        if (StringUtils.isBlank(serial)) {
            throw new BusinessException("无效的票据号");
        }

        // 打印信息
        TollPrintVo result = deposit2TollPrintVo(deposit, serial, BooleanUtils.isTrue(refund));
        if (newSerial) {
            TollSerialVo billSerial = new TollSerialVo();
            billSerial.setId(id);
            billSerial.setSerial(serial);
            billSerial.setRefund(BooleanUtils.isTrue(refund));
            depositService.updateDepositSerial(billSerial);
        }
        return result;
    }

    @ApiOperation("物业押金列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public RefundBillPageResult<RealtyDepositVo> listDepositPage(@RequestBody RealtyDepositSearchVo search) {
        return depositService.listDepositPage(search);
    }

    @ApiOperation("物业押金导出")
    @GetMapping("/export")
    public void exportDeposit(HttpServletResponse response, RealtyDepositSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RefundBillPageResult<RealtyDepositVo> page = depositService.listDepositPage(search);
        List<RealtyDepositExportVo> exportList = new ArrayList<>(page.getTotalSize());
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int serial = 1;
            for (RealtyDepositVo deposit : page.getDataList()) {
                RealtyDepositExportVo exportItem = depositConvertor.voToExcelVo(deposit);
                // 已退费的，金额显示为退费的金额
                BillStatus billStatus = BillStatus.fromStatus(deposit.getStatus());
                if (billStatus == BillStatus.REFUND) {
                    exportItem.setTollSerial(deposit.getRefundSerial());
                    exportItem.setAmount(deposit.getRefundAmount());
                }
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_DEPOSIT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyDepositExportVo.class)
                .sheet(SenoxConst.Export.SHEET_REALTY_DEPOSIT)
                .doWrite(exportList);
    }

    @ApiOperation("物业押金收费明细导出")
    @GetMapping("/exportToll")
    public void exportDepositToll(HttpServletResponse response, RealtyDepositSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RefundBillPageResult<RealtyDepositVo> page = depositService.listDepositPage(search);
        List<RealtyDepositTollExportVo> exportList = new ArrayList<>(page.getTotalSize());
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int serial = 1;
            for (RealtyDepositVo deposit : page.getDataList()) {
                RealtyDepositTollExportVo exportItem = depositConvertor.voToTollExcelVo(deposit);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_DEPOSIT_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyDepositTollExportVo.class)
                .sheet(SenoxConst.Export.SHEET_REALTY_DEPOSIT_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("物业押金退费明细导出")
    @GetMapping("/exportRefund")
    public void exportDepositRefund(HttpServletResponse response, RealtyDepositSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RefundBillPageResult<RealtyDepositVo> page = depositService.listDepositPage(search);
        List<RealtyDepositRefundExportVo> exportList = new ArrayList<>(page.getTotalSize());
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int serial = 1;
            for (RealtyDepositVo deposit : page.getDataList()) {
                RealtyDepositRefundExportVo exportItem = depositConvertor.voToRefundExcelVo(deposit);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_DEPOSIT_REFUND, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyDepositRefundExportVo.class)
                .sheet(SenoxConst.Export.SHEET_REALTY_DEPOSIT_REFUND)
                .doWrite(exportList);
    }

    private TollPrintVo deposit2TollPrintVo(RealtyDepositVo deposit, String serial, boolean refund) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(serial);
        result.setPayer(deposit.getCustomerName());
        result.setPayerDesc(deposit.getRealtyName());

        if (refund) {
            result.setTotalAmount(deposit.getRefundAmount());
            result.setTollTime(deposit.getRefundTime());
        } else {
            result.setTotalAmount(deposit.getAmount());
            result.setTollTime(deposit.getTollTime());
        }

        // 账单明细
        TollPrintItemVo printItem = new TollPrintItemVo();
        printItem.setSerial(deposit.getRealtySerial());
        printItem.setFee(deposit.getFeeName());
        printItem.setPrice(refund ? deposit.getRefundAmount() : deposit.getAmount());
        printItem.setAmount(refund ? deposit.getRefundAmount() : deposit.getAmount());
        printItem.setRemark(deposit.getContractNo());
        result.setDetails(Collections.singletonList(printItem));

        return result;
    }

    private void checkDeposit(RealtyDepositVo deposit) {
        // 物业、客户判定
        if (!WrapperClassUtils.biggerThanLong(deposit.getContractId(), 0L)) {
            if (!WrapperClassUtils.biggerThanLong(deposit.getCustomerId(), 0L)) {
                throw new InvalidParameterException("无效的客户信息");
            }
            if (!WrapperClassUtils.biggerThanLong(deposit.getRealtyId(), 0L)) {
                throw new InvalidParameterException("无效的物业信息");
            }
        }

        if (!DecimalUtils.isPositive(deposit.getAmount())) {
            throw new InvalidParameterException("无效的押金金额");
        }
    }


}
