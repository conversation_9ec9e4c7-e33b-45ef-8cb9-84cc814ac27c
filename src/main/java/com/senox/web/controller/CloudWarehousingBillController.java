package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.cold.constant.CloudWarehousingFee;
import com.senox.cold.vo.*;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.realty.constant.BillStatus;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.CloudWarehousingBillConvertor;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.CloudWarehousingBillService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/14 11:05
 */
@Api(tags = "云仓账单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/cloud/warehousingBill")
public class CloudWarehousingBillController extends BaseController{

    private final CloudWarehousingBillService warehousingBillService;
    private final AdminUserService adminUserService;
    private final CloudWarehousingBillConvertor warehousingBillConvertor;


    @ApiOperation("添加云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addCloudWarehousingBill(@Validated(Add.class) @RequestBody CloudWarehousingBillVo billVo) {
        return warehousingBillService.addCloudWarehousingBill(billVo);
    }

    @ApiOperation("导入云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void importBill(@RequestPart("file") MultipartFile file, Integer month) throws IOException {
        checkExcelFile(file);
        List<CloudWarehousingBillExcelVo> excelVos = EasyExcelFactory.read(file.getInputStream()).head(CloudWarehousingBillExcelVo.class).sheet().doReadSync();
        if (CollectionUtils.isEmpty(excelVos)) {
            throw new BusinessException("未读取到数据！");
        }
        //过滤客户编码或客户名为空的账单
        excelVos = excelVos.stream().filter(x -> !StringUtils.isBlank(x.getRcSerial()) && !StringUtils.isBlank(x.getName())).collect(Collectors.toList());
        //当前年份
        int currentYear = LocalDate.now().getYear();
        YearMonth yearMonth = YearMonth.of(currentYear, month);
        //账单日
        LocalDate billDate = yearMonth.atDay(1);
        List<CloudWarehousingBillVo> billVoList = warehousingBillConvertor.toVoList(excelVos);
        billVoList.forEach(bill -> bill.setBillDate(billDate));
        warehousingBillService.batchAdd(billVoList);
    }

    @ApiOperation("云仓账单导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,CloudWarehousingBillSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        PageResult<CloudWarehousingBillVo> pageResult = warehousingBillService.listBill(search);
        List<CloudWarehousingBilLExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            for (CloudWarehousingBillVo item : pageResult.getDataList()) {
                CloudWarehousingBilLExportVo exportItem = toExport(item);
                exportList.add(exportItem);
            }
        }
        String fileName = SenoxConst.Export.COLD_CLOUD_WAREHOUSING_BILL_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), CloudWarehousingBilLExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.COLD_CLOUD_WAREHOUSING_BILL_SHEET)
                .doWrite(exportList);
    }

    @ApiOperation("更新云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateCloudWarehousingBill(@Validated(Update.class) @RequestBody CloudWarehousingBillVo billVo) {
        warehousingBillService.updateCloudWarehousingBill(billVo);
    }

    @ApiOperation("月度账单微信通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/wechatNotify")
    public void notifyWechatBill(@RequestParam Integer year, @RequestParam Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            throw new InvalidParameterException();
        }
        warehousingBillService.notifyBill(year, month);
    }

    @ApiOperation("根据id获取云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public CloudWarehousingBillVo findById(@PathVariable Long id) {
        return warehousingBillService.findById(id);
    }

    @ApiOperation("批量删除云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete")
    public void deleteByIds(@RequestBody List<Long> ids) {
        warehousingBillService.deleteByIds(ids);
    }

    @ApiOperation("更新账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/remark/update")
    public void updateBillRemark(@Validated @RequestBody RefrigerationBillRemarkVo billRemark) {
        warehousingBillService.updateBillRemark(billRemark);
    }

    @ApiOperation("下发云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send")
    public void sendBill(@RequestBody RefrigerationBillSendVo send) {
        warehousingBillService.sendBill(send);
    }

    @ApiOperation("云仓账单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageStatisticsResult<CloudWarehousingBillVo, CloudWarehousingBillVo> listBillPage(@RequestBody CloudWarehousingBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<CloudWarehousingBillVo> pageResult = warehousingBillService.listBill(search);
        CloudWarehousingBillVo sumBill = warehousingBillService.sumBill(search);
        return new PageStatisticsResult<>(pageResult, sumBill);
    }

    @ApiOperation("支付云仓账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return warehousingBillService.payBill(payRequest);
    }

    @ApiOperation("打印云仓账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        CloudWarehousingBillVo billVo = warehousingBillService.findById(id);
        if (billVo == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (billVo.getStatus() != BillStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = bill2TollPrintVo(billVo);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            TollSerialVo tollSerial = new TollSerialVo();
            tollSerial.setBillId(id);
            tollSerial.setBillSerial(result.getBillSerial());
            tollSerial.setOperatorId(getAdminUserId());
            warehousingBillService.updateBillSerial(tollSerial);
        }
        return result;
    }


    private TollPrintVo bill2TollPrintVo(CloudWarehousingBillVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getTollSerial());
        //客户名字前缀处理
        result.setPayer(bill.getName());
        result.setPayerDesc(bill.getRcSerial());
        result.setTollMan(bill.getTollMan());
        result.setTollTime(bill.getPaidTime());
        result.setTotalAmount(bill.getPaidAmount());

        // 费项明细
        List<TollPrintItemVo> list = Arrays.asList(
                newTollPrintItem(bill, CloudWarehousingFee.REFUND_SERVICE), newTollPrintItem(bill, CloudWarehousingFee.RENT),
                newTollPrintItem(bill, CloudWarehousingFee.DELIVERY), newTollPrintItem(bill, CloudWarehousingFee.TECHNICAL_SERVICE),
                newTollPrintItem(bill, CloudWarehousingFee.MID_REPLENISHMENT), newTollPrintItem(bill, CloudWarehousingFee.CODA_REPLENISHMENT),
                newTollPrintItem(bill, CloudWarehousingFee.OTHER), newTollPrintItem(bill, CloudWarehousingFee.OTHER),
                newTollPrintItem(bill, CloudWarehousingFee.PENALTY)
        );
        if (!StringUtils.isBlank(bill.getRemark())) {
            list.get(0).setRemark(bill.getRemark());
        }
        result.setDetails(list.stream().filter(x -> !Objects.isNull(x)).collect(Collectors.toList()));
        return result;
    }

    private TollPrintItemVo newTollPrintItem(CloudWarehousingBillVo bill, CloudWarehousingFee fee) {
        BigDecimal amount = bill.getCharge(fee);
        if (DecimalUtils.equals(BigDecimal.ZERO, amount)) {
            return null;
        }
        if (fee == CloudWarehousingFee.PENALTY && bill.checkPenaltyIgnore()) {
            return null;
        }

        TollPrintItemVo result = new TollPrintItemVo();
        result.setFee(fee.getName());
        result.setPrice(amount);
        result.setAmount(amount);
        result.setTime(StringUtils.buildYearMonthStr(bill.getBillYear(), bill.getBillMonth()));
        return result;
    }

    /**
     * 去除客户名前缀的A
     * @param name
     * @return
     */
    private String handlerReplaceName(String name) {
        return name.replaceFirst("^A", "");
    }

    private CloudWarehousingBilLExportVo toExport(CloudWarehousingBillVo bill) {
        CloudWarehousingBilLExportVo export = new CloudWarehousingBilLExportVo();
        export.setBillYearMonth(String.format("%s-%s", bill.getBillYear(), bill.getBillMonth()));
        export.setRcSerial(bill.getRcSerial());
        export.setName(bill.getName());
        export.setRefundServiceCharge(bill.getRefundServiceCharge());
        export.setRentCharge(bill.getRentCharge());
        export.setDeliveryCharge(bill.getDeliveryCharge());
        export.setTechnicalServiceCharge(bill.getTechnicalServiceCharge());
        export.setMidReplenishmentCharge(bill.getMidReplenishmentCharge());
        export.setCodaReplenishmentCharge(bill.getCodaReplenishmentCharge());
        export.setOtherCharge(bill.getOtherCharge());
        export.setPenaltyAmount(bill.getPenaltyCharge());
        export.setPenaltyPaidAmount(bill.getPenaltyPaid());
        export.setTotalAmount(bill.getTotalCharge());
        export.setPaidAmount(bill.getPaidAmount());
        BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
        export.setPaidStatus(billStatus.getValue());
        PayWay payWay = PayWay.fromValue(bill.getPayWay());
        export.setPayWay(payWay.getDescription());
        export.setPaidTime(bill.getPaidTime());
        export.setRemark(bill.getRemark());
        return export;
    }
}
