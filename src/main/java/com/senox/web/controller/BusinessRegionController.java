package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.realty.vo.BusinessRegionVo;
import com.senox.web.service.BusinessRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 11:27
 */
@Api(tags = "基本信息 - 经营区域")
@RestController
@RequestMapping("/web/dictionary/region")
public class BusinessRegionController {

    @Autowired
    private BusinessRegionService regionService;

    @ApiOperation("添加经营区域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addRegion(@Validated({Add.class}) @RequestBody BusinessRegionVo region) {
        return regionService.addRegion(region);
    }

    @ApiOperation("更新经营区域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateRegion(@Validated({Update.class}) @RequestBody BusinessRegionVo region) {
        if (region.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        regionService.updateRegion(region);
    }

    @ApiOperation("删除经营区域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteRegion(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        regionService.deleteRegion(id);
    }

    @ApiOperation("获取经营区域")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BusinessRegionVo getRegion(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return regionService.findById(id);
    }

    @ApiOperation("经营区域列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BusinessRegionVo> listAll() {
        return regionService.listAll();
    }


}
