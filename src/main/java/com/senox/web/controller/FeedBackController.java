package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import com.senox.web.service.FeedBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/6/1 14:01
 */
@Api(tags = "建议反馈")
@RestController
@RequestMapping("/web/feed/back")
@RequiredArgsConstructor
public class FeedBackController extends BaseController{

    private final FeedBackService feedBackService;

    @ApiOperation("获取意见反馈")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}/{isDetail}")
    public FeedBackVo findFeedBackById(@PathVariable Long id, @PathVariable Boolean isDetail) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return feedBackService.findFeedBackById(id, isDetail);
    }

    @ApiOperation("意见反馈列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<FeedBackVo> listFeedBack(@RequestBody FeedBackSearchVo search) {
        return feedBackService.listFeedBack(search);
    }

    @ApiOperation("添加建议回复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/reply/add")
    public Long addFeedBackReply(@Validated({Add.class}) @RequestBody FeedBackReplyVo feedBackReplyVo) {
        feedBackReplyVo.setName(getAdminUser().getRealName());
        return feedBackService.addFeedBackReply(feedBackReplyVo);
    }

    @ApiOperation("获取建议回复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/reply/get/{id}")
    public FeedBackReplyVo findFeedBackReplyById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return feedBackService.findFeedBackReplyById(id);
    }

}
