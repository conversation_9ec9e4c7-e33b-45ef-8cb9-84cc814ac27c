package com.senox.web.controller;


import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.vo.*;
import com.senox.web.service.RealtyReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-21
 */
@Api(tags = "物业发票")
@RestController
@RequestMapping("/web/realty/receipt")
@RequiredArgsConstructor
public class RealtyReceiptController extends BaseController {
    private final RealtyReceiptService realtyReceiptService;


    @ApiOperation("物业发票申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply")
    public void apply(@Validated @RequestBody RealtyReceiptMangerVo receiptManger) {
        receiptManger.setApplyUserName(getAdminUser().getRealName());
        realtyReceiptService.apply(receiptManger);
    }

    @ApiOperation("物业发票申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply/list")
    public PageResult<RealtyReceiptVo> applyList(@RequestBody RealtyReceiptSearchVo search) {
        return realtyReceiptService.applyList(search);
    }

    @ApiOperation("物业发票申请账单信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/apply/bill/info/list/{id}")
    public List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(@PathVariable Long id) {
        return realtyReceiptService.applyBillInfoList(id);

    }


    @ApiOperation("发票申请信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/apply/info/list/{id}/{detail}")
    public List<ReceiptApplyVo> applyInfoList(@PathVariable Long id, @PathVariable Boolean detail) {
        return realtyReceiptService.applyInfoList(id, detail);
    }

    @ApiOperation("物业发票申请审核")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply/audit")
    public void applyAudit(@RequestBody ReceiptApplyAuditVo receiptApplyAudit) {
        realtyReceiptService.applyAudit(receiptApplyAudit);
    }

}
