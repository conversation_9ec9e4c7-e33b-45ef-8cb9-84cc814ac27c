package com.senox.web.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.MaterialOutRecordInfoConvertor;
import com.senox.web.service.WmsService;
import com.senox.web.vo.MaterialExportVo;
import com.senox.web.vo.MaterialOutRecordInfoExportVo;
import com.senox.wms.dto.KuaimaiPrintDto;
import com.senox.wms.dto.MaterialDictFixedAssetsScrapBillDto;
import com.senox.wms.dto.MaterialDictFixedAssetsTransferBillDto;
import com.senox.wms.dto.MaterialDto;
import com.senox.wms.vo.*;
import com.senox.wms.vo.dict.DictMaterialCodeVo;
import com.senox.wms.vo.dict.DictTypeVo;
import com.senox.wms.vo.dict.DictUnitSearchVo;
import com.senox.wms.vo.dict.DictUnitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


/**
 * <AUTHOR>
 * @date 2022-1-10
 */
@Api(tags = "仓储管理")
@RestController
@RequestMapping("/web/wms")
@RequiredArgsConstructor
public class WmsController extends BaseController {
    private final WmsService wmsService;
    private final MaterialOutRecordInfoConvertor materialOutRecordInfoConvertor;

    @ApiOperation("添加物料编码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/material/code/add")
    public void addMaterialCode(@Validated @RequestBody DictMaterialCodeVo materialCode) {
        wmsService.addMaterialCode(materialCode);
    }

    @ApiOperation("删除物料编码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/material/code/delete")
    public void deleteMaterialCode(@RequestBody Long[] ids) {
        if (ids == null || ids.length < 1) {
            throw new InvalidParameterException();
        }
        wmsService.deleteMaterialCode(ids);
    }

    @ApiOperation("更新物料编码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/material/code/update")
    public void updateMaterialCode(@RequestBody DictMaterialCodeVo materialCode) {
        if (!WrapperClassUtils.biggerThanLong(materialCode.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        wmsService.updateMaterialCode(materialCode);
    }

    @ApiOperation("获取当前用户物料编码列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/material/code/list")
    public PageResult<DictMaterialCodeVo> getMaterialCode(@RequestBody DictMaterialSearchVo materialSearch) {
        return wmsService.getMaterialCode(materialSearch);
    }


    @ApiOperation("新增物料类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/type/add")
    public void addDictType(@Validated @RequestBody DictTypeVo dictType) {
        wmsService.addDictType(dictType);
    }


    @ApiOperation("删除物料类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/type/delete")
    public void deleteDictType(@RequestBody Long[] ids) {
        if (ids == null || ids.length < 1) {
            throw new InvalidParameterException();
        }
        wmsService.deleteDictType(ids);
    }


    @ApiOperation("更新物料类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/type/update")
    public void updateDictType(@RequestBody DictTypeVo dictType) {
        if (!WrapperClassUtils.biggerThanLong(dictType.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        wmsService.updateDictType(dictType);
    }


    @ApiOperation("获取当前用户物料类型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "warehouseId", value = "仓id", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/dict/type/list")
    public List<DictTypeVo> listDictTypeAndWarehouse(Long warehouseId) {
        return wmsService.listDictTypeAndWarehouse(warehouseId);
    }


    @ApiOperation("新增单位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/unit/add")
    public void addDictUnit(@Validated @RequestBody DictUnitVo unit) {
        wmsService.addDictUnit(unit);
    }

    @ApiOperation("删除单位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/unit/delete")
    public void deleteDictUnit(@RequestBody Long[] unitIds) {
        if (unitIds == null || unitIds.length < 1) {
            throw new InvalidParameterException();
        }
        wmsService.deleteDictUnit(unitIds);
    }


    @ApiOperation("修改单位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/unit/update")
    public void updateDictUnit(@RequestBody DictUnitVo unit) {
        if (!WrapperClassUtils.biggerThanLong(unit.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        wmsService.updateDictUnit(unit);
    }


    @ApiOperation("获取单位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/unit/list")
    public PageResult<DictUnitVo> getDictUnit(@RequestBody DictUnitSearchVo dictUnitSearchVo) {
        return wmsService.getDictUnit(dictUnitSearchVo);
    }


    @ApiOperation("获取对应用户仓列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/warehouse/user/list")
    public List<WarehouseTreeNode> warehouseUserList() {
        return wmsService.warehouseUserList();
    }

    @ApiOperation("查询该角色对仓的权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/warehouse/role/list/{roleId}")
    public List<WarehouseTreeNode> roleWarehouseList(@PathVariable Long roleId) {
        return wmsService.roleWarehouseList(roleId);
    }

    @ApiOperation("更新角色仓权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/warehouse/role/add/{roleId}")
    public void warehouseRoleAdd(@PathVariable Long roleId, @RequestBody List<Long> warehouseList) {
        wmsService.warehouseRoleAdd(roleId, warehouseList);
    }


    @ApiOperation("入仓")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/single/bill/add")
    public EnterWarehouseCollectVo materialSingleBillAdd(@RequestBody MaterialEnterBillFormVo form) {
        if (CollectionUtils.isEmpty(form.getMaterialList())) {
            throw new InvalidParameterException("无效的参数");
        }

        if (!WrapperClassUtils.biggerThanLong(form.getDepartmentId(), 0L)) {
            throw new InvalidParameterException("无效的部门");
        }
        return wmsService.addEnterBill(form);
    }

    @ApiOperation("获取入仓单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
    })
    @PostMapping("/material/single/bill/list")
    public PageResult<MaterialEnterBillVo> enterBillList(@RequestBody MaterialEnterBillSearchVo search) {
        return wmsService.enterBillList(search);
    }

    @ApiOperation("入仓单号获取物料记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "入仓单号", paramType = "path")
    })
    @GetMapping("/material/single/bill/materialList/{bill}")
    public List<MaterialVo> materialListByEnterBill(@PathVariable Long bill) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new InvalidParameterException("无效的入仓单号id");
        }
        return wmsService.materialListByEnterBill(bill);
    }

    @ApiOperation("创建物料出仓单")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/single/out/add")
    public MaterialOutConfirmFormVo createOutBill(@RequestBody MaterialOutRecordFormVo form) {
        return wmsService.createOutBill(form);
    }

    @ApiOperation("确认出仓")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/single/out/confirmAdd")
    public OutWarehouseCollectVo confirmOutBill(@RequestBody MaterialOutConfirmFormVo form) {
        return wmsService.confirmOutBill(form);
    }

    @ApiOperation("获取出仓单列表")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/single/out/list")
    public PageResult<MaterialOutBillVo> billOutList(@RequestBody MaterialOutBillSearchVo search) {
        return wmsService.billOutList(search);
    }

    @ApiOperation("出仓单号查询出仓记录列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class), @ApiImplicitParam(name = "outSingleId", value = "出仓单号", paramType = "path", dataTypeClass = Long.class)})
    @GetMapping("/material/single/out/materialList/{bill}")
    public List<MaterialOutRecordVo> recordListByBill(@PathVariable Long bill) {
        return wmsService.recordListByBill(bill);
    }

    @ApiOperation("物料盘点")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/inventory/list")
    public MaterialPageResult<MaterialVo> materialInventoryList(@RequestBody MaterialInventorySearchVo materialInventorySearch) {
        return wmsService.materialInventoryList(materialInventorySearch);
    }


    @ApiOperation("导出物料盘点")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @GetMapping("/material/inventory/list/export")
    public void materialInventoryExport(HttpServletResponse response, MaterialInventorySearchVo materialInventorySearch) throws IOException {
        materialInventorySearch.setPageNo(1);
        materialInventorySearch.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        String fileName = String.format(SenoxConst.Export.WMS_MATERIAL_INVENTORY_BILL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        MaterialPageResult<MaterialVo> materialVoPageResult = wmsService.materialInventoryList(materialInventorySearch);
        List<MaterialExportVo> materialExportVoList = new ArrayList<>(materialVoPageResult.getDataList().size());
        AtomicReference<BigDecimal> totalPrice = new AtomicReference<>(new BigDecimal("0"));
        materialVoPageResult.getDataList().forEach(materialVo -> {
            MaterialExportVo materialExportVo = new MaterialExportVo();
            materialExportVo.setMaterialDictId(materialVo.getDictMaterialCode().getId());
            materialExportVo.setMaterialName(materialVo.getDictMaterialCode().getMaterialName());
            materialExportVo.setMaterialNorms(materialVo.getDictMaterialCode().getMaterialNorms());
            materialExportVo.setBillNumber(materialVo.getBillNumber());
            materialExportVo.setOutNumber(materialVo.getOutNumber());
            materialExportVo.setSurplusNumber(materialVo.getSurplusNumber());
            materialExportVo.setUnitName(materialVo.getDictMaterialCode().getDictUnit().getUnitName());
            materialExportVo.setTotalPrice(materialVo.getTotalPrice());
            materialExportVo.setUnitPrice(materialVo.getTotalPrice().divide(new BigDecimal(materialVo.getBillNumber().toString()), 2, RoundingMode.HALF_UP));
            totalPrice.updateAndGet(v -> totalPrice.get().add(materialVo.getTotalPrice()));
            materialExportVoList.add(materialExportVo);

        });
        MaterialExportVo materialExportVo = new MaterialExportVo();
        materialExportVo.setMaterialName("合计：");
        materialExportVo.setBillNumber(materialVoPageResult.getTotalBillNumber());
        materialExportVo.setOutNumber(materialVoPageResult.getTotalOutNumber());
        materialExportVo.setSurplusNumber(materialVoPageResult.getTotalSurplusNumber());
        materialExportVo.setTotalPrice(totalPrice.get());
        materialExportVoList.add(materialExportVo);
        EasyExcel.write(response.getOutputStream(), MaterialExportVo.class)
                .sheet(fileName).doWrite(materialExportVoList);
    }

    @ApiOperation("物料编码库存列表")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/dict/material/code/stockList")
    public PageResult<DictMaterialCodeVo> materialCodeStockList(@RequestBody DictMaterialSearchVo dictMaterialSearch) {
        return wmsService.materialCodeStockList(dictMaterialSearch);
    }

    @ApiOperation("创建调拨单")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/allocation/add")
    public void materialAllocationAdd(@RequestBody MaterialAllocationSingleVo materialAllocationSingleVo) {
        wmsService.materialAllocationAdd(materialAllocationSingleVo);
    }

    @ApiOperation("调拨单列表")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/allocation/list")
    public PageResult<MaterialAllocationSingleVo> materialAllocationSingleList(@RequestBody MaterialAllocationSingleSearchVo materialAllocationSingleSearch) {
        return wmsService.materialAllocationSingleList(materialAllocationSingleSearch);
    }

    @ApiOperation("审核调拨单")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
                    @ApiImplicitParam(name = "id", value = "调拨单id", paramType = "path", dataTypeClass = Long.class),
                    @ApiImplicitParam(name = "state", value = "状态(0:未审核,1:已审核,2:驳回)", paramType = "path", dataTypeClass = Integer.class)
            }
    )
    @GetMapping("/material/allocation/examine/{id}/{state}")
    public void materialAllocationExamine(@PathVariable Long id, @PathVariable Integer state) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的调拨单id");
        }
        wmsService.materialAllocationExamine(id, state);
    }

    @ApiOperation("获取单个固定资产使用记录")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
                    @ApiImplicitParam(name = "fixedAssetsUseId", value = "固定资产使用记录id", paramType = "path", dataTypeClass = Long.class)
            }
    )
    @GetMapping("/material/fixed/getMaterialFixedAssetsUse/{fixedAssetsUseId}")
    public MaterialFixedAssetsUseVo getFixedAssetsUse(@PathVariable Long fixedAssetsUseId) {
        if (!WrapperClassUtils.biggerThanLong(fixedAssetsUseId, 0L)) {
            throw new InvalidParameterException("无效的固定资产使用记录");
        }
        return wmsService.getFixedAssetsUse(fixedAssetsUseId);
    }

    @ApiOperation("固定资产使用记录列表")
    @ApiImplicitParams(
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    )
    @PostMapping("/material/fixed/assetsUse/list")
    public PageResult<MaterialFixedAssetsUseVo> assetsUseList(@RequestBody MaterialFixedAssetsUseSearchVo search) {
        return wmsService.assetsUseList(search);
    }


    @ApiOperation("固定资产报废")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/fixed/assetsScrap/add")
    public MaterialDictFixedAssetsScrapBillVo assetsScrapAdd(@RequestBody MaterialDictFixedAssetsScrapBillDto materialDictFixedAssetsScrapBillDto) {
        return wmsService.assetsScrapAdd(materialDictFixedAssetsScrapBillDto);
    }

    @ApiOperation("固定资产报废列表")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @GetMapping("/material/fixed/assetsScrap/list")
    public List<MaterialDictFixedAssetsScrapBillVo> assetsScrapList() {
        return wmsService.assetsScrapList();
    }

    @ApiOperation("创建固定资产转移单")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @PostMapping("/material/fixed/assetsTransfer/add")
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferAdd(@RequestBody List<MaterialDictFixedAssetsTransferBillDto> transferBillDtoList) {
        return wmsService.assetsTransferAdd(transferBillDtoList);
    }

    @ApiOperation("固定资产转移列表")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @GetMapping("/material/fixed/assetsTransfer/list")
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferList() {
        return wmsService.assetsTransferList();
    }

    @ApiOperation("生成仓报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "month", value = "生成月份(1-12)", paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "reportType", value = "类型(1:月报表,2:季报表,3:年报表)", paramType = "query", dataTypeClass = Integer.class)
    })
    @GetMapping("/material/warehouse/report/create/{year}/{month}/{reportType}")
    public void createWarehouseReport(@PathVariable Integer year, @PathVariable Integer month, @PathVariable Integer reportType) {
        wmsService.createWarehouseReport(year, month, reportType);
    }


    @ApiOperation("获取仓报表记录")
    @ApiImplicitParams(@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class))
    @GetMapping("/material/warehouse/report/record/list")
    public List<WarehouseReportRecordVo> reportRecordList() {
        return wmsService.reportRecordList();
    }

    @ApiOperation("获取仓报表记录文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "time", value = "日期", paramType = "path", dataTypeClass = String.class)
    })
    @GetMapping("/material/warehouse/report/file/list/{time}")
    public List<WarehouseReportFileVo> reportFileList(@PathVariable String time) {
        return wmsService.reportFileList(time);
    }

    @ApiOperation("根据入仓单号返回入仓单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/material/bill/enter/get/{bill}")
    public EnterWarehouseCollectVo getMaterialEnterBill(@PathVariable Long bill,@RequestParam(defaultValue = "false") Boolean merge) {
        return wmsService.getMaterialEnterBill(bill,merge);
    }

    @ApiOperation("入仓单数据列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
    })
    @PostMapping("/material/bill/enter/data/list")
    public EnterWarehouseCollectVo enterDataList(@RequestBody MaterialEnterBillSearchVo search) {
        return wmsService.enterDataList(search);
    }

    @ApiOperation("根据单号撤回入仓")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/material/bill/enter/cancel/{bill}")
    public void enterBillCancel(@PathVariable Long bill) {
        wmsService.enterBillCancel(bill);
    }

    @ApiOperation("根据出仓单号返回出仓单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/material/bill/out/get/{bill}")
    public OutWarehouseCollectVo getOutBill(@PathVariable Long bill,@RequestParam(defaultValue = "false") Boolean merge) {
        return wmsService.getOutBill(bill,merge);
    }

    @ApiOperation("出仓单数据列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
    })
    @PostMapping("/material/bill/out/data/list")
    public OutWarehouseCollectVo outDataList(@RequestBody MaterialOutBillSearchVo search) {
        return wmsService.outDataList(search);
    }

    @ApiOperation("撤销出仓")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "billId", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/material/bill/out/cancel/{billId}")
    public void outBillCancel(@PathVariable Long billId) {
        wmsService.outBillCancel(billId);
    }

    @ApiOperation("删除入仓单中的物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "billId", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @PostMapping("/material/bill/enter/record/delete/{billId}")
    public void enterBillRecordDelete(@PathVariable Long billId, @RequestBody Long[] materialIds) {
        wmsService.enterBillRecordDelete(billId, materialIds);
    }

    @ApiOperation("增加入仓单的物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/bill/enter/record/add/{bill}")
    public void enterBillRecordAdded(@PathVariable Long bill, @RequestBody List<MaterialVo> materialList) {
        wmsService.enterBillRecordAdded(bill, materialList);
    }

    @ApiOperation("修改入仓单的物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "billId", value = "单号", paramType = "query", dataTypeClass = Long.class)
    })
    @PostMapping("/material/bill/enter/record/update/{billId}")
    public void enterBillRecordUpdate(@PathVariable Long billId, @Valid @RequestBody MaterialDto materialDto) {
        wmsService.enterBillRecordUpdate(billId, materialDto);
    }

    @ApiOperation("删除出仓单中的物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
    })
    @PostMapping("/material/bill/out/record/delete/{bill}")
    public void deleteBillOutRecord(@PathVariable Long bill, @RequestBody Long[] materialOutRecordIds) {
        wmsService.deleteBillOutRecord(bill, materialOutRecordIds);
    }

    @ApiOperation("更新出仓物料数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "单号", paramType = "query", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "outRecordId", value = "记录id", paramType = "query", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "number", value = "数量", paramType = "query", dataTypeClass = Long.class)
    })
    @GetMapping("/material/bill/out/record/update/{bill}/{outRecordId}/{number}")
    public void updateBillOutRecord(@PathVariable Long bill, @PathVariable Long outRecordId, @PathVariable Long number) {
        wmsService.updateBillOutRecord(bill, outRecordId, number);
    }

    @ApiOperation("新增出仓物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "bill", value = "出仓单号", paramType = "query", dataTypeClass = Long.class)
    })
    @PostMapping("/material/bill/out/record/add/{bill}")
    public void outBillRecordAdded(@PathVariable Long bill, @RequestBody List<MaterialOutRecordVo> materialOutRecordList) {
        wmsService.outBillRecordAdded(bill, materialOutRecordList);
    }

    @ApiOperation("打印物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sn", value = "设备sn", paramType = "query", dataTypeClass = String.class)
    })
    @PostMapping("/dict/material/code/kuaimai/print/{sn}")
    public void kuaimaiPrint(@PathVariable String sn, @RequestBody List<KuaimaiPrintDto> kuaimaiPrintDtos) {
        wmsService.kuaimaiPrint(sn, kuaimaiPrintDtos);
    }

    @ApiOperation("打印机列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/dict/material/code/kuaimai/print/list")
    public List<KuaimaiPrintVo> kuaimaiPrintList() {
        return wmsService.kuaimaiPrintList();
    }

    @ApiOperation("盘点单记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/inventory/record/list")
    public PageResult<MaterialInventoryBillRecordVo> materialInventoryRecordList(@RequestBody MaterialInventoryBillRecordSearchVo search) {
        return wmsService.materialInventoryRecordList(search);
    }

    @ApiOperation("快速盘点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/inventory/record/quick")
    public void recordQuick(@Validated(Update.class) @RequestBody MaterialInventoryBillRecordSearchVo search) {
        wmsService.recordQuick(search);
    }

    @ApiOperation("物料绑定gtin")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/material/code/bind/gtin/{materialCode}")
    public void materialBindGtin(@PathVariable Long materialCode, @RequestParam String gtin) {
        wmsService.materialBindGtin(materialCode, gtin);
    }

    @ApiOperation("入仓记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/enter/record/list")
    public PageResult<MaterialEnterRecordInfoVo> enterRecordList(@RequestBody MaterialEnterRecordSearchVo search) {
        return wmsService.enterRecordList(search);
    }

    @ApiOperation("出仓记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/out/record/list")
    public MaterialOutRecordPageResult<MaterialOutRecordInfoVo> outRecordList(@RequestBody MaterialOutRecordSearchVo search) {
        return wmsService.outRecordList(search);
    }

    @ApiOperation("出仓记录导出")
    @GetMapping("/material/out/record/export")
    public void outRecordExport(HttpServletResponse response,MaterialOutRecordSearchVo search) throws IOException {
        search.setPage(false);
        PageResult<MaterialOutRecordInfoVo> outRecordPageResult = wmsService.outRecordList(search);
        if (CollectionUtils.isEmpty(outRecordPageResult.getDataList())){
            return;
        }
        List<MaterialOutRecordInfoExportVo> outRecordExportList = materialOutRecordInfoConvertor.toExport(outRecordPageResult.getDataList());
        if (CollectionUtils.isEmpty(outRecordExportList)) {
            return;
        }
        String fileName = SenoxConst.Export.WMS_MATERIAL_OUT_RECORD_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MaterialOutRecordInfoExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(cellBorder())
                .sheet(SenoxConst.Export.WMS_MATERIAL_OUT_RECORD_INFO_SHEET)
                .doWrite(outRecordExportList);

    }

    @ApiOperation("出仓记录打印")
    @GetMapping("/material/out/record/print/{outBill}")
    public void materialOutRecordPrint(@PathVariable Long outBill,@RequestParam(defaultValue = "false") Boolean merge) {
        wmsService.materialOutRecordPrint(outBill,merge);
    }

    public  HorizontalCellStyleStrategy cellBorder() {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setFillForegroundColor((short) 9);
        contentWriteCellStyle.setWrapped(true);
        contentWriteCellStyle.setShrinkToFit(true);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteFont.setFontName("宋体");
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

}
