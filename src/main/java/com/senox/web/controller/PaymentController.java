package com.senox.web.controller;

import com.senox.context.AdminContext;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.BodSecretKeyVo;
import com.senox.pm.vo.PayWayDeleteVo;
import com.senox.pm.vo.PayWaySettingSearchVo;
import com.senox.pm.vo.PayWaySettingVo;
import com.senox.web.service.PaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17 9:09
 */
@AllArgsConstructor
@Api(tags = "支付")
@RestController
@RequestMapping("/web/payment")
public class PaymentController extends BaseController {

    private final PaymentService paymentService;

    @ApiOperation("莞付平台密钥")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bod/secretKey/get")
    public BodSecretKeyVo getBodSecretKey() {
        return paymentService.getBodSecretKey();
    }
    @ApiOperation("新增/修改日期支付方式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/way/setting/save")
    public void save(@RequestBody List<PayWaySettingVo> payWaySettingVoList) {
        //默认先添加的都为物业类型(后续需要调整)
        payWaySettingVoList.forEach(p -> {
            if (null != p.getOrderType()) {
                return;
            }
            p.setOrderType(OrderType.REALTY);
        });
        paymentService.payWaySettingSave(payWaySettingVoList);
    }

    @ApiOperation("删除支付方式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/way/setting/delete")
    public void delete(@RequestBody List<PayWayDeleteVo> payWayDeleteVoList) {
        paymentService.payWaySettingDelete(payWayDeleteVoList);
    }

    @ApiOperation("获取支付方式列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/way/setting/list")
    public List<PayWaySettingVo> list(@RequestBody PayWaySettingSearchVo search) {
        return paymentService.payWaySettingList(search);
    }

    @ApiOperation("获取支付方式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "日期(yyyy-mm-dd)", paramType = "query", dataTypeClass = LocalDate.class)
    })
    @PostMapping("/way/setting/get")
    public PayWay get(@RequestBody LocalDate date) {
        return paymentService.payWaySettingGet(null,date);
    }

}
