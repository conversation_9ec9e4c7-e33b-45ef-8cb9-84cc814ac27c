package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.AccessControlAlarmTimeoutVo;
import com.senox.dm.vo.AccessControlCommandSearchVo;
import com.senox.dm.vo.AccessControlCommandVo;
import com.senox.dm.vo.AccessControlMaintainVo;
import com.senox.dm.vo.AccessControlSearchVo;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.AuthorizedGateSearchVo;
import com.senox.dm.vo.AuthorizedGateVo;
import com.senox.web.service.AccessControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 14:01
 */
@Api(tags = "海康门禁设备")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/control")
public class AccessControlController {

    private final AccessControlService accessControlService;

    @ApiOperation("添加海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/addBatch")
    public void addBatchAccessControl(@Validated(Add.class) @RequestBody List<AccessControlVo> accessControlVoList) {
        accessControlService.addBatchAccessControl(accessControlVoList);
    }


    @ApiOperation("更新海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/update")
    public void updateAccessControl(@RequestBody AccessControlVo accessControlVo) {
        if (accessControlVo.getId() < 1L) {
            throw new InvalidParameterException();
        }
        accessControlService.updateAccessControl(accessControlVo);
    }

    @ApiOperation("设备维修")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/maintain")
    public void maintainAccessControl(@Validated @RequestBody AccessControlMaintainVo maintain) {
        accessControlService.maintainAccessControl(maintain);
    }

    @ApiOperation("保存延时报警时长")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/alarmTimeout/save")
    public void saveAlarmTimeout(@Validated @RequestBody AccessControlAlarmTimeoutVo acTimeout) {
        accessControlService.saveAlarmTimeout(acTimeout);
    }

    @ApiOperation("布防海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/deploy")
    public void deployAccessControl(@RequestParam String ip) {
        if (StringUtils.isBlank(ip)) {
            throw new InvalidParameterException();
        }

        accessControlService.deployAccessControl(ip);
    }

    @ApiOperation("取消布防海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/undeploy")
    public void undeployAccessControl(@RequestParam String ip) {
        if (StringUtils.isBlank(ip)) {
            throw new InvalidParameterException();
        }

        accessControlService.undeployAccessControl(ip);
    }

    @ApiOperation("重新布防海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/redeploy")
    public void redeployAccessControl(@RequestParam String ip) {
        if (StringUtils.isBlank(ip)) {
            throw new InvalidParameterException();
        }

        accessControlService.undeployAccessControl(ip);
        accessControlService.deployAccessControl(ip);
    }

    @ApiOperation("删除海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/delete/{id}")
    public void deleteAccessControl(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        accessControlService.deleteAccessControl(id);
    }

    @ApiOperation("根据id获取海康门禁设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/get/{id}")
    public AccessControlVo findAccessControlById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return accessControlService.findAccessControlById(id);
    }

    @ApiOperation("海康门禁设备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/list")
    public PageResult<AccessControlVo> listAccessControl(@RequestBody AccessControlSearchVo search) {
        return accessControlService.listAccessControl(search);
    }

    @ApiOperation("根据物业编号查询设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/getByRealtySerial")
    public List<AccessControlVo> getByRealtySerial(@RequestParam String realtySerial) {
        return accessControlService.getAccessControlByRealtySerial(realtySerial);
    }

    @ApiOperation("授权的报警门禁列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/authorized/list")
    public List<AuthorizedGateVo> listAuthorizedGate(@RequestBody AuthorizedGateSearchVo search) {
        return accessControlService.listAuthorizedGate(search);
    }

    @ApiOperation("远程开门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/remoteControl")
    public void remoteControlGate(@RequestParam String deviceIp){
        accessControlService.remoteControlGate(deviceIp);
    }

    @ApiOperation("执行命令")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/command/execute/{id}")
    public void executeCommand(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        accessControlService.executeCommand(id);
    }

    @ApiOperation("恢复命令")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/command/enable/{id}")
    public void enableCommand(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        accessControlService.enableCommand(id);
    }

    @ApiOperation("作废命令")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/command/disable/{id}")
    public void disableCommand(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        accessControlService.disableCommand(id);
    }

    @ApiOperation("命令列表")
    @PostMapping("/access/command/list")
    public PageResult<AccessControlCommandVo> listCommand(@RequestBody AccessControlCommandSearchVo search){
        return accessControlService.listCommand(search);
    }

}
