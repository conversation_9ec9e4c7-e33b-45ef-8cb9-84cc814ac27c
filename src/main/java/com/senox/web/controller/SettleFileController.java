package com.senox.web.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.SettleFileSearchVo;
import com.senox.pm.vo.SettleFileVo;
import com.senox.web.service.SettleFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/11/17 10:55
 */
@Api(tags = "结算文件")
@RestController
@RequestMapping("/web/settleFile")
public class SettleFileController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SettleFileController.class);

    @Autowired
    private SettleFileService settleFileService;

    @ApiOperation("下载结算文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/download/drc/{settleType}/{settleDate}/{payWay}")
    public void downloadDrcSettleFile(HttpServletResponse response,
                                      @PathVariable String settleDate,
                                      @PathVariable String settleType,
                                      @PathVariable PayWay payWay) throws Exception {
        if (StringUtils.isBlank(settleDate) || StringUtils.isBlank(settleType)) {
            throw new InvalidParameterException();
        }

        // 日期判断
        LocalDate date = null;
        try {
            date = DateUtils.parseDate(settleDate, "yyyyMMdd");
        } catch (Exception e) {
            // ignore
        }
        if (date == null) {
            throw new InvalidParameterException();
        }

        // 对账文件路径
        String path = settleFileService.getDrcSettleFilePath(settleDate, settleType, payWay);
        if (StringUtils.isBlank(path)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "文件不存在");
        }

        // 文件
        File file = new File(path);
        if (!file.exists()) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "文件不存在");
        }

        // 清空缓冲区，状态码和响应头(headers)
        response.reset();
        // 设置ContentType，响应内容为二进制数据流，编码为utf-8，此处设定的编码是文件内容的编码
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentLength((int) file.length());
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());

        // 输出流
        BufferedInputStream in = null;
        OutputStream out = null;
        byte[] buffer = new byte[10240];
        try {
            in = new BufferedInputStream(new FileInputStream(file));
            out = response.getOutputStream();
            int len = 0;

            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
                out.flush();
            }
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.warn("关闭输出流失败", e);
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                   logger.warn("关闭输入流失败", e);
                }
            }
        }

    }

    @ApiOperation("结算文件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/drc/list")
    public PageResult<SettleFileVo> listSettleFile(@RequestBody SettleFileSearchVo search) {
        return settleFileService.listDrcSettleFile(search);
    }
}
