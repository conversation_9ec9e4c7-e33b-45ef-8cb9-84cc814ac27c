package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import com.senox.web.service.AdminRemoteAccessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023/5/26 15:18
 */
@Api(tags = "远程开门权限")
@RestController
@RequestMapping("/web/remote")
public class AdminRemoteAccessController {

    @Autowired
    private AdminRemoteAccessService adminRemoteAccessService;


    @ApiOperation("添加远程开门权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/add")
    public void addRemoteAccess(@Validated(Add.class) @RequestBody AdminRemoteAccessVo adminRemoteAccessVo) {
        adminRemoteAccessService.addRemoteAccess(adminRemoteAccessVo);
    }

    @ApiOperation("删除远程开门权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/delete/{id}")
    public void deleteRemoteAccess(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        adminRemoteAccessService.deleteRemoteAccess(id);
    }

    @ApiOperation("根据id获取远程开门权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/get/{id}")
    public AdminRemoteAccessVo findRemoteAccessById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return adminRemoteAccessService.findRemoteAccessById(id);
    }

    @ApiOperation("远程开门权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/list")
    public PageResult<AdminRemoteAccessVo> listAccessControl(@RequestBody AdminRemoteAccessSearchVo search) {
        return adminRemoteAccessService.remoteAccessList(search);
    }
}
