package com.senox.web.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.PayWay;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/25 16:55
 */
public class BaseController {

    /**
     * 导出excel response处理
     * @param response
     * @param fileName
     */
    protected void prepareExcelResponse(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
    }

    /**
     * 获取登录用户
     * @return
     */
    protected AdminUserDto getAdminUser() {
        return AdminContext.getUser();
    }

    /**
     * 获取登录用户id
     * @return
     */
    protected Long getAdminUserId() {
        AdminUserDto user = getAdminUser();
        if (user == null) {
            throw new BusinessException(ResultConst.USER_NOT_AUTHENTICATED);
        }
        return user.getUserId();
    }

    /**
     * 校验账单支付参数
     * @param payRequest
     */
    protected void validBillPayRequest(BillPayRequestVo payRequest) {
        if (!CollectionUtils.isEmpty(payRequest.getBills())) {
            payRequest.setBillIds(payRequest.getBills().stream().map(BillDiscountVo::getId).collect(Collectors.toList()));

        }
        if (CollectionUtils.isEmpty(payRequest.getBillIds())) {
            throw new InvalidParameterException("无效的账单");
        }

        // 支付方式校验
        checkPayWay(payRequest.getPayWay(), payRequest.getAuthCode());
    }

    /**
     * 混合支付校验
     * @param payRequest
     */
    protected void validateMixPayRequest(MixPayRequestVo payRequest) {
        if (payRequest.getDetails().stream().map(PayAmountVo::getPayWay).distinct().count() != payRequest.getDetails().size()) {
            throw new InvalidParameterException("重复的支付方式");
        }

        if (!WrapperClassUtils.biggerThanLong(payRequest.getBillId(), 0L) && CollectionUtils.isEmpty(payRequest.getBillIds())) {
            throw new InvalidParameterException("无效的账单");
        }

        if (WrapperClassUtils.biggerThanLong(payRequest.getBillId(), 0L)) {
            payRequest.setBillIds(Collections.singletonList(payRequest.getBillId()));
        }

        payRequest.getDetails().forEach(x -> checkPayWay(x.getPayWay(), x.getAuthCode()));
    }

    /**
     * 校验文件
     * @param file
     */
    protected void checkExcelFile(MultipartFile file) {
        String originFileName = file.getOriginalFilename();
        int dotIndex = originFileName == null ? -1 : originFileName.lastIndexOf(".");

        String fileType = originFileName == null || dotIndex < 0  ? StringUtils.EMPTY : originFileName.substring(dotIndex);
        if (!SenoxConst.EXCEL_TYPES.contains(fileType)) {
            throw new InvalidParameterException("无效的文件");
        }
    }

    /**
     * 校验支付方式
     * @param payWay
     * @param authCode
     */
    private void checkPayWay(PayWay payWay, String authCode) {
        if (payWay == null) {
            throw new InvalidParameterException("无效的支付方式");
        }

        if (payWay.getOrderStatus() != OrderStatus.PAID && payWay != PayWay.DRC || payWay == PayWay.WITHHOLD) {
            throw new InvalidParameterException("管理后台缴费只支持现金或扫码结算");
        }

        if (payWay == PayWay.DRC && StringUtils.isBlank(authCode)) {
            throw new InvalidParameterException("无效的付款码");
        }
    }
}
