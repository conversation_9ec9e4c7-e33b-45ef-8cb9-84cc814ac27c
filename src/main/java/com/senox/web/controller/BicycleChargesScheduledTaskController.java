package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import com.senox.web.service.BicycleChargesScheduledTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "三轮车费用标准计划任务")
@RestController
@RequestMapping("/web/bicycle/charges/task")
@RequiredArgsConstructor
public class BicycleChargesScheduledTaskController {
    private final BicycleChargesScheduledTaskService taskService;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@RequestBody BicycleChargesScheduledTaskVo task) {
        taskService.add(task);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody BicycleChargesScheduledTaskVo task) {
        taskService.update(task);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{taskId}")
    public void delete(@PathVariable Long taskId) {
        taskService.delete(taskId);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleChargesScheduledTaskVo> list(@RequestBody BicycleChargesScheduledTaskSearchVo searchVo) {
        return taskService.listPage(searchVo);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/find/{id}")
    public BicycleChargesScheduledTaskVo find(@PathVariable Long id) {
        return taskService.findById(id);
    }
}
