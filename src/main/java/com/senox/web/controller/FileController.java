package com.senox.web.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.context.AdminContext;
import com.senox.dm.vo.FileVo;
import com.senox.web.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/12/10 15:23
 */
@Api(tags = "文件")
@RestController
@RequestMapping("/web/file")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

    @ApiOperation("上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FileVo uploadFile(@RequestParam("t") String type,
                             @RequestParam(required = false) String prefix,
                             @RequestPart("file") MultipartFile file) {
        if (StringUtils.isBlank(type)) {
            throw new BusinessException("无效的文件类别");
        }
        if (file.isEmpty()) {
            throw new BusinessException("上传文件失败");
        }

        FileVo result = fileService.uploadFile(type, prefix, file);
        if (result == null) {
            logger.warn("上传文件失败...");
            throw new BusinessException("上传文件失败");
        }
        return result;
    }
}
