package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import com.senox.web.service.WxUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7 16:13
 */
@Api(tags = "微信客户")
@RestController
@RequestMapping("/web/wxUser")
public class WxUserController {

    @Autowired
    private WxUserService wxUserService;

    @ApiOperation("设置灰度用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/gray/set/{userId}")
    public void setGrayUser(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的用户id");
        }
        wxUserService.setGrayUser(userId);
    }

    @ApiOperation("取消灰度用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/gray/cancel/{userId}")
    public void cancelGrayUser(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的用户id");
        }
        wxUserService.cancelGrayUser(userId);
    }

    @ApiOperation("微信客户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<WxUserVo> listWxUserPage(@Validated @RequestBody WxUserSearchVo searchVo) {
        return wxUserService.listWxUserPage(searchVo);
    }

    @ApiOperation("绑定物业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/bind")
    public void bindRealty(@RequestBody WxUserRealtyVo userRealty) {
        if (!WrapperClassUtils.biggerThanLong(userRealty.getUserId(), 0L)
                || StringUtils.isBlank(userRealty.getContractNo())) {
            throw new InvalidParameterException("无效的参数");
        }
        wxUserService.bindWxUserRealty(userRealty);
    }

    @ApiOperation("解绑物业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/unbind")
    public void unbindRealty(@RequestBody WxUserRealtyVo userRealty) {
        if (!WrapperClassUtils.biggerThanLong(userRealty.getUserId(), 0L)
                || !WrapperClassUtils.biggerThanLong(userRealty.getRealtyId(), 0L)) {
            throw new InvalidParameterException("无效的参数");
        }
        wxUserService.unbindWxUserRealty(userRealty);
    }

    @ApiOperation("微信客户物业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/listRealty/{userId}")
    public List<WxUserRealtyVo> listWxUserRealty(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的用户id");
        }
        return wxUserService.listWxUserRealty(userId);
    }

    @ApiOperation("微信用户备注更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/remark")
    public void updateWxUserRemark(@RequestBody WxUserRemarkVo remarkVo) {
        wxUserService.updateWxUserRemark(remarkVo);
    }
}
