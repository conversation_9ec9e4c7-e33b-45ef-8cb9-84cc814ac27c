package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.constant.BillStatus;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.LogisticsFreightService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.LogisticsFreightExcelVo;
import com.senox.web.vo.LogisticsFreightExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.ResolverStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:31
 */
@Api(tags = "物流货运")
@RestController
@RequestMapping("/web/logistic/freight")
@RequiredArgsConstructor
public class LogisticsFreightController extends BaseController {
    private final LogisticsFreightService freightService;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@RequestBody LogisticsFreightVo freightVo) {
        freightService.addBatch(Collections.singletonList(freightVo));
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticsFreightVo findById(@PathVariable Long id) {
        return freightService.findById(id);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody LogisticsFreightVo freightVo) {
        freightService.update(freightVo);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        freightService.deleteById(id);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(@RequestBody LogisticsFreightSearchVo searchVo) {
        return freightService.listPage(searchVo);
    }

    @ApiOperation("支付珠三角账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return freightService.payBill(payRequest);
    }

    @ApiOperation("导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void importFreight(@RequestPart("file") MultipartFile file) throws IOException {
        checkExcelFile(file);
        List<LogisticsFreightExcelVo> excels = new ArrayList<>();
        PageReadListener<LogisticsFreightExcelVo> readListener = new PageReadListener<>(excels::addAll);
        EasyExcelFactory.read(file.getInputStream(), LogisticsFreightExcelVo.class, readListener).sheet().doRead();
        List<LogisticsFreightVo> list = toVo(excels.stream()
                .filter(f -> !StringUtils.isBlank(f.getReceivingNo())).collect(Collectors.toList()));
        freightService.importData(list);
    }

    private List<LogisticsFreightVo> toVo(List<LogisticsFreightExcelVo> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return Collections.emptyList();
        }
        List<LogisticsFreightVo> list = new ArrayList<>(excels.size());
        for (LogisticsFreightExcelVo excelData : excels) {
            LogisticsFreightVo data = new LogisticsFreightVo();
            data.setOperationsDepartment("货运部");
            final String pattern = "yyyy/M/d";
            data.setReceivingDate(DateUtils.parseDate(excelData.getReceivingDate(),pattern, ResolverStyle.LENIENT));
            data.setReceivingNo(excelData.getReceivingNo());
            data.setSenderCustomerName(excelData.getSenderCustomerName());
            data.setSenderCustomerContact(excelData.getSenderCustomerContact());
            data.setSenderPieces(Integer.valueOf(excelData.getSenderPieces()));
            data.setSenderFreightCharge(DecimalUtils.nullToZero(excelData.getSenderFreightCharge() == null ? null
                    : new BigDecimal(excelData.getSenderFreightCharge())));
            data.setSenderSettlementType(excelData.getSenderSettlementType().equals("到付") ? 1 : 2);
            data.setReceivingCustomerName(excelData.getReceivingCustomerName());
            data.setReceivingCustomerAddress(excelData.getReceivingCustomerAddress());
            data.setReceivingCustomerContact(excelData.getReceivingCustomerContact());
            data.setTransferLogisticsCompany(excelData.getTransferLogisticsCompany());
            data.setTransferLogisticsNo(excelData.getTransferLogisticsNo());
            data.setTransferCharge(DecimalUtils.nullToZero(excelData.getTransferCharge() == null ? null
                    : new BigDecimal(excelData.getTransferCharge())));
            data.setProfitAmount(DecimalUtils.subtract(data.getSenderFreightCharge(), data.getTransferCharge()));
            list.add(data);
        }
        return list;
    }

    @ApiOperation("导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response, LogisticsFreightSearchVo searchVo) throws IOException {
        List<LogisticsFreightVo> list = freightService.list(searchVo);
        List<LogisticsFreightExportVo> exportList = toExport(list);
        prepareExcelResponse(response, URLEncoder.encode(SenoxConst.Export.TMS_LOGISTICS_FREIGHT_INFO, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticsFreightExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_LOGISTICS_FREIGHT_SHEET)
                .doWrite(exportList);
    }

    private List<LogisticsFreightExportVo> toExport(List<LogisticsFreightVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LogisticsFreightExportVo> exportList = new ArrayList<>();
        list.forEach(l -> {
            LogisticsFreightExportVo export = new LogisticsFreightExportVo();
            export.setOperationsDepartment(l.getOperationsDepartment());
            export.setReceivingDate(DateUtils.formatYearMonth(l.getReceivingDate(), DateUtils.PATTERN_FULL_DATE));
            export.setReceivingNo(l.getReceivingNo());
            export.setSenderCustomerName(l.getSenderCustomerName());
            export.setSenderCustomerContact(l.getSenderCustomerContact());
            export.setSenderPieces(l.getSenderPieces());
            export.setSenderFreightCharge(l.getSenderFreightCharge());
            export.setSenderSettlementType(l.getSenderSettlementType().equals(1) ? "到付" : "现付");
            export.setReceivingCustomerName(l.getReceivingCustomerName());
            export.setReceivingCustomerAddress(l.getReceivingCustomerAddress());
            export.setReceivingCustomerContact(l.getReceivingCustomerContact());
            export.setTransferLogisticsCompany(l.getTransferLogisticsCompany());
            export.setTransferLogisticsNo(l.getTransferLogisticsNo());
            export.setTransferCharge(l.getTransferCharge());
            export.setProfitAmount(l.getProfitAmount());
            export.setCreateTime(DateUtils.formatDateTime(l.getCreateTime(), DateUtils.PATTERN_FULL_DATE_TIME));
            export.setCreatorName(l.getCreatorName());
            export.setRemark(l.getRemark());
            export.setPaidAmount(l.getPaidAmount());
            export.setPaidTime(l.getPaidTime());
            BillStatus status = BillStatus.fromValue(l.getStatus());
            export.setStatus(status == null ? "未知" : status.getDescription());
            export.setPaidRemark(l.getPaidRemark());
            PayWay payWay = PayWay.fromValue(l.getPayWay());
            export.setPayWay(payWay == null ? "未知" : payWay.getDescription());
            export.setTollMan(l.getTollMan());
            exportList.add(export);

        });

        return exportList;
    }
}
