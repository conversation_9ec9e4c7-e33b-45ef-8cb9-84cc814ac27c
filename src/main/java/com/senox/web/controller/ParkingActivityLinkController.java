package com.senox.web.controller;

import com.senox.car.vo.ParkingActivityItemSearchVo;
import com.senox.car.vo.ParkingActivityItemVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.ParkingActivityLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车场活动链接")
@RestController
@RequestMapping("/web/parking/activity/link")
@RequiredArgsConstructor
public class ParkingActivityLinkController {
    private final ParkingActivityLinkService activityLinkService;


    @ApiOperation("添加链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public String addActivityLink(@RequestBody ParkingActivityItemVo activityItemVo) {
        return activityLinkService.addActivityLink(activityItemVo);
    }

    @ApiOperation("更新链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateActivityLink(@RequestBody ParkingActivityItemVo activityItemVo) {
        activityLinkService.updateActivityLink(activityItemVo);
    }

    @ApiOperation("根据id获取链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public ParkingActivityItemVo findActivityLinkById(@PathVariable Long id) {
        return activityLinkService.findActivityLinkById(id);
    }

    @ApiOperation("根据code获取链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findByCode/{code}")
    public ParkingActivityItemVo findActivityLinkByCode(@PathVariable String code) {
        return activityLinkService.findActivityLinkByCode(code);
    }

    @ApiOperation("根据id删除链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteById/{id}")
    public void deleteActivityLinkById(@PathVariable Long id) {
        activityLinkService.deleteActivityLinkById(id);
    }

    @ApiOperation("链接列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ParkingActivityItemVo> activityLinkListPage(@RequestBody ParkingActivityItemSearchVo searchVo) {
        return activityLinkService.activityLinkListPage(searchVo);
    }
}
