package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import com.senox.tms.vo.LogisticPayoffVo;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.LogisticOrderExcelConvertor;
import com.senox.web.convert.LogisticPayoffExcelConvertor;
import com.senox.web.service.LogisticOrderService;
import com.senox.web.service.LogisticPayoffService;
import com.senox.web.vo.LogisticExcelCheckResult;
import com.senox.web.vo.LogisticOrderExcelVo;
import com.senox.web.vo.LogisticOrderExportExcelVo;
import com.senox.web.vo.LogisticOrderPage;
import com.senox.web.vo.LogisticPayoffExcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/5 14:40
 */
@Slf4j
@Validated
@Api(tags = "物流每日订单明细")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/logistic")
public class LogisticController extends BaseController {

    private final LogisticOrderService orderService;
    private final LogisticPayoffService payoffService;
    private final LogisticOrderExcelConvertor orderExcelConvertor;
    private final LogisticPayoffExcelConvertor payoffExcelConvertor;

    @ApiOperation("添加物流每日订单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/add")
    public Long addOrder(@Validated @RequestBody LogisticOrderVo order) {
        return orderService.addLogisticOrder(order);
    }

    @ApiOperation("更新物流每日订单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/update")
    public void updateOrder(@RequestBody LogisticOrderVo order) {
        if (!WrapperClassUtils.biggerThanLong(order.getProductOrderId(), 0L)) {
            throw new InvalidParameterException();
        }

        orderService.updateLogisticOrder(order);
    }

    @ApiOperation("导入物流每日订单明细，返回Excel中重复的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/import")
    public LogisticExcelCheckResult importOrder(@RequestPart("file") MultipartFile file, @RequestParam(required = false) Boolean overwrite) throws IOException {
        checkExcelFile(file);

        // 物流订单
        List<LogisticOrderExcelVo> importList = loadLogisticOrderFromExcel(file.getInputStream());

        Map<Integer, LogisticOrderExcelVo> errorData = checkLogisticExcelData(importList);
        if (!CollectionUtils.isEmpty(errorData)) {
            return new LogisticExcelCheckResult(errorData);
        }


        importList = importList.stream()
                .filter(x -> !StringUtils.isBlank(x.getProduct()) && !DecimalUtils.equals(x.getProductCount(), BigDecimal.ZERO))
                .collect(Collectors.toList());
        List<LogisticOrderExcelVo> duplicatedList = importList.stream()
                .collect(Collectors.toMap(Function.identity(), e -> 1, Integer::sum))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        log.info("duplicateList: {}", JsonUtils.object2Json(duplicatedList));
        if (!CollectionUtils.isEmpty(duplicatedList)) {
            return new LogisticExcelCheckResult(duplicatedList);
        }

        List<LogisticOrderVo> list = orderExcelConvertor.excelToVo(importList);
        orderService.saveLogisticOrderBatch(list, overwrite);
        return new LogisticExcelCheckResult();
    }

    @ApiOperation("批量更新物流单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/editBatch")
    public void editOrderBatch(@RequestBody LogisticOrderEditBatchVo editBatch) {
        if (CollectionUtils.isEmpty(editBatch.getProductOrderIds())) {
            throw new InvalidParameterException();
        }

        orderService.editLogisticOrderBatch(editBatch);
    }

    @ApiOperation("删除物流每日订单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/delete")
    public void deleteLogisticOrder(@RequestBody List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new InvalidParameterException();
        }

        orderService.deleteLogisticOrder(orderIds);
    }

    @ApiOperation("物流每日订单明细折扣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/ship/discount/get")
    public BigDecimal calShipOrderDiscount(@Validated @RequestBody ShipOrderDiscountSearchVo search) {
        return orderService.calShipOrderDiscount(search);
    }

    @ApiOperation("物流每日订单明细详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/get/{id}")
    public LogisticOrderVo findOrderById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return orderService.findLogisticOrderById(id);
    }

    @ApiOperation("物流每日订单明细页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/page")
    public LogisticOrderPage<LogisticOrderVo> listOrderPage(@RequestBody LogisticOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return LogisticOrderPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<LogisticOrderVo> page = orderService.listLogisticOrderPage(search);
        LogisticOrderPage<LogisticOrderVo> resultPage = new LogisticOrderPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        LogisticOrderVo sum = orderService.sumLogisticOrder(search);
        if (sum != null) {
            resultPage.setProductCount(sum.getProductCount());
            resultPage.setProductWeight(DecimalUtils.nullToZero(sum.getProductWeight()));
            resultPage.setProductSize(DecimalUtils.nullToZero(sum.getProductSize()));
            resultPage.setProductAmount(DecimalUtils.nullToZero(sum.getProductAmount()));
            resultPage.setProductDeduction(DecimalUtils.nullToZero(sum.getProductDeduction()));
            resultPage.setProductFullReduction(DecimalUtils.nullToZero(sum.getProductFullReduction()));
            resultPage.setProductTotalAmount(DecimalUtils.nullToZero(sum.getProductTotalAmount()));

            resultPage.setShipCount(sum.getShipCount());
            resultPage.setShipWeight(DecimalUtils.nullToZero(sum.getShipWeight()));
            resultPage.setShipSize(DecimalUtils.nullToZero(sum.getShipSize()));
            resultPage.setShipAmount(DecimalUtils.nullToZero(sum.getShipAmount()));
            resultPage.setSortAmount(DecimalUtils.nullToZero(sum.getSortAmount()));
            resultPage.setShipTotalAmount(DecimalUtils.nullToZero(sum.getShipTotalAmount()));

            resultPage.setProductPaid(DecimalUtils.nullToZero(sum.getProductPaid()));
            resultPage.setProductOwe(DecimalUtils.nullToZero(sum.getProductOwe()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sum.getTotalAmount()));
        }

        return resultPage;
    }

    @ApiOperation("物流每日订单明细导出")
    @GetMapping("/order/export")
    public void exportOrder(HttpServletResponse response, LogisticOrderSearchVo search) throws IOException {
        List<LogisticOrderVo> list = orderService.listLogisticOrder(search);
        List<LogisticOrderExportExcelVo> resultList = new ArrayList<>(list.size());

        int serial = 1;
        for (LogisticOrderVo item : list) {
            LogisticOrderExportExcelVo exportItem = orderExcelConvertor.orderVoToExportExcelVo(item);
            exportItem.setSerialNo(serial++);
            resultList.add(exportItem);
        }

        // 合计
        LogisticOrderVo sum = orderService.sumLogisticOrder(search);
        resultList.add(sumLogisticOrder(sum));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_LOGISTIC_DAILY,
                DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticOrderExportExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_LOGISTIC_DAILY).doWrite(resultList);
    }


    @ApiOperation("添加物流每日订单应付客户款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/add")
    public Long addLogisticPayoff(@Validated @RequestBody LogisticPayoffVo payoff) {
        return payoffService.addPayoff(payoff);
    }

    @ApiOperation("更新物流每日订单应付客户款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/update")
    public void updateLogisticPayoff(@Validated @RequestBody LogisticPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.updatePayoff(payoff);
    }

    @ApiOperation("删除物流每日订单应付客户款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/delete")
    public void deleteLogisticPayoff(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        payoffService.deletePayoff(ids);
    }

    @ApiOperation("生成物流每日订单应付客户款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/generate")
    public void generateLogisticPayoff(@Validated @RequestBody LogisticPayoffGenerateVo generate) {
        if (generate.getBillDate() == null) {
            throw new InvalidParameterException();
        }
        payoffService.generatePayoff(generate);
    }


    @ApiOperation("物流每日订单应付客户款明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/get/{id}")
    public LogisticPayoffVo findPayoffById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return payoffService.findById(id);
    }

    @ApiOperation("物流每日订单应付客户款列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/page")
    public LogisticOrderPage<LogisticPayoffVo> listPayoffPage(@RequestBody LogisticPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return LogisticOrderPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<LogisticPayoffVo> page = payoffService.listPayoffPage(search);
        LogisticOrderPage<LogisticPayoffVo> resultPage = new LogisticOrderPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        LogisticPayoffVo sum = payoffService.sumPayoff(search);
        if (sum != null) {
            resultPage.setProductCount(sum.getProductCount());
            resultPage.setProductAmount(DecimalUtils.nullToZero(sum.getProductAmount()));
            resultPage.setProductDeduction(DecimalUtils.nullToZero(sum.getProductDeduction()));
            resultPage.setProductFullReduction(DecimalUtils.nullToZero(sum.getProductFullReduction()));
            resultPage.setProductPaid(DecimalUtils.nullToZero(sum.getProductPaid()));
            resultPage.setProductOwe(DecimalUtils.nullToZero(sum.getProductOwe()));
            resultPage.setProductTotalAmount(DecimalUtils.nullToZero(sum.getProductToPaid()));
            resultPage.setShipTotalAmount(DecimalUtils.nullToZero(sum.getShipAmount()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sum.getTotalAmount()));
        }

        return resultPage;
    }

    @ApiOperation("物流每日订单应付客户款导出")
    @GetMapping("/payoff/export")
    public void exportPayoff(HttpServletResponse response, LogisticPayoffSearchVo search) throws IOException {
        List<LogisticPayoffVo> list = payoffService.listPayoff(search);
        List<LogisticPayoffExcelVo> resultList = new ArrayList<>(list.size());

        int serial = 1;
        for (LogisticPayoffVo item : list) {
            LogisticPayoffExcelVo exportItem = payoffExcelConvertor.toExcelVo(item);
            exportItem.setSerialNo(serial++);
            resultList.add(exportItem);
        }

        // 合计
        LogisticPayoffVo sum = payoffService.sumPayoff(search);
        resultList.add(sumPayoff(sum));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_LOGISTIC_PAYOFF,
                DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticPayoffExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_LOGISTIC_PAYOFF).doWrite(resultList);
    }



    /**
     * 加载物流订单
     * @param in
     * @return
     */
    private List<LogisticOrderExcelVo> loadLogisticOrderFromExcel(InputStream in) {
        List<LogisticOrderExcelVo> resultList = new LinkedList<>();
        // 默认一行行的读取 excel，创建 excel 一行行的回调监听器，PageReadListener 会分批处理数据，每次100条
        PageReadListener<LogisticOrderExcelVo> readListener = new PageReadListener<>(resultList::addAll);
        EasyExcelFactory.read(in).registerReadListener(readListener).head(LogisticOrderExcelVo.class).headRowNumber(2).sheet(0).doRead();
        return resultList;
    }

    private Map<Integer, LogisticOrderExcelVo> checkLogisticExcelData(List<LogisticOrderExcelVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<Integer, LogisticOrderExcelVo> resultMap = null;
        for (int index = 0; index < list.size(); index++) {
            LogisticOrderExcelVo item = list.get(index);
            if (item != null && !StringUtils.isBlank(item.getMerchant()) && item.getMerchant().contains("大库")) {
                resultMap = resultMap == null ? new HashMap<>(list.size()) : resultMap;
                resultMap.put(index + 3, list.get(index));
            }
        }
        return resultMap;
    }

    private LogisticOrderExportExcelVo sumLogisticOrder(LogisticOrderVo order) {
        return LogisticOrderExportExcelVo.builder()
                .merchant(SenoxConst.Export.COLUMN_SUM)
                .productCount(DecimalUtils.nullToZero(order == null ? null : order.getProductCount()))
                .productWeight(DecimalUtils.nullToZero(order == null ? null : order.getProductWeight()))
                .productSize(DecimalUtils.nullToZero(order == null ? null : order.getProductSize()))
                .productAmount(DecimalUtils.nullToZero(order == null ? null : order.getProductAmount()))
                .productDeduction(DecimalUtils.nullToZero(order == null ? null : order.getProductDeduction()))
                .productFullReduction(DecimalUtils.nullToZero(order == null ? null : order.getProductFullReduction()))
                .productTotalAmount(DecimalUtils.nullToZero(order == null ? null : order.getProductTotalAmount()))
                .shipCount(DecimalUtils.nullToZero(order == null ? null : order.getShipCount()))
                .shipCount2(DecimalUtils.nullToZero(order == null ? null : order.getShipCount()))
                .shipWeight(DecimalUtils.nullToZero(order == null ? null : order.getShipWeight()))
                .shipSize(DecimalUtils.nullToZero(order == null ? null : order.getShipSize()))
                .shipAmount(DecimalUtils.nullToZero(order == null ? null : order.getShipAmount()))
                .sortAmount(DecimalUtils.nullToZero(order == null ? null : order.getSortAmount()))
                .shipTotalAmount(DecimalUtils.nullToZero(order == null ? null : order.getShipTotalAmount()))
                .productPaid(DecimalUtils.nullToZero(order == null ? null : order.getProductPaid()))
                .productOwe(DecimalUtils.nullToZero(order == null ? null : order.getProductOwe()))
                .totalAmount(DecimalUtils.nullToZero(order == null ? null : order.getTotalAmount()))
                .build();
    }

    public LogisticPayoffExcelVo sumPayoff(LogisticPayoffVo payoff) {
        return LogisticPayoffExcelVo.builder()
                .merchant(SenoxConst.Export.COLUMN_SUM)
                .productCount(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductCount()))
                .productAmount(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductAmount()))
                .productDeduction(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductDeduction()))
                .productFullReduction(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductFullReduction()))
                .productPaid(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductPaid()))
                .productOwe(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductOwe()))
                .productToPaid(DecimalUtils.nullToZero(payoff == null ? null : payoff.getProductToPaid()))
                .shipAmount(DecimalUtils.nullToZero(payoff == null ? null : payoff.getShipAmount()))
                .totalAmount(DecimalUtils.nullToZero(payoff == null ? null : payoff.getTotalAmount()))
                .build();
    }
}
