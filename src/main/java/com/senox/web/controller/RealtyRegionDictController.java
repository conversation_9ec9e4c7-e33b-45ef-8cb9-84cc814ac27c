package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import com.senox.realty.vo.RealtyRegionDictVo;
import com.senox.web.service.RealtyRegionDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "物业区域字典")
@AllArgsConstructor
@RestController
@RequestMapping("/web/realty/region/dict")
public class RealtyRegionDictController extends BaseController {
    private final RealtyRegionDictService realtyRegionDictService;

    @ApiOperation("添加字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestBody RealtyRegionDictVo regionDict) {
        realtyRegionDictService.add(regionDict);
    }

    @ApiOperation("删除字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id) {
        realtyRegionDictService.deleteById(id);
    }

    @ApiOperation("修改字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/updateById")
    public void updateById(@RequestBody RealtyRegionDictVo regionDict) {
        realtyRegionDictService.updateById(regionDict);
    }

    @ApiOperation("查询字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<RealtyRegionDictVo> pageList(@RequestBody RealtyRegionDictSearchVo search) {
        return realtyRegionDictService.pageList(search);
    }
}
