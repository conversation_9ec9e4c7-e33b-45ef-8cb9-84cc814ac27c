package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import com.senox.realty.vo.FirefightingUtilityVo;
import com.senox.web.service.FirefightingUtilityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/13 15:34
 */
@Api(tags = "公共消防设施")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/firefighting/utility")
public class FirefightingUtilityController extends BaseController {

    private final FirefightingUtilityService utilityService;

    @ApiOperation("添加公共消防设施")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addUtility(@Validated @RequestBody FirefightingUtilityVo utility) {
        return utilityService.addUtility(utility);
    }

    @ApiOperation("更新公共消防设施")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateUtility(@Validated @RequestBody FirefightingUtilityVo utility) {
        if (!WrapperClassUtils.biggerThanLong(utility.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        utilityService.updateUtility(utility);
    }

    @ApiOperation("删除公共消防设施")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteUtility(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        utilityService.deleteUtility(id);
    }

    @ApiOperation("获取公共消防设施")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public FirefightingUtilityVo findUtilityById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return utilityService.findUtilityById(id);
    }

    @ApiOperation("公共消防设施页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<FirefightingUtilityVo> listUtilityPage(@RequestBody FirefightingUtilitySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return utilityService.listUtilityPage(search);
    }
}
