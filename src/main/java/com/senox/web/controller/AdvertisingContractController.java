package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.AdvertisingContractEditVo;
import com.senox.realty.vo.AdvertisingContractListVo;
import com.senox.realty.vo.AdvertisingContractSearchVo;
import com.senox.realty.vo.AdvertisingContractVo;
import com.senox.realty.vo.AdvertisingCostVo;
import com.senox.realty.vo.AdvertisingIncomeVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import com.senox.realty.vo.ContractSuspendDto;
import com.senox.web.config.ExcelFillCellMergeStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.AdvertisingIncomeConvertor;
import com.senox.web.service.AdvertisingContractService;
import com.senox.web.vo.AdvertisingIncomeExcelVo;
import com.senox.web.vo.IncomePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.senox.web.constant.SenoxConst.Export.SHEET_ADVERTISING_INCOME;

/**
 * <AUTHOR>
 * @date 2023/7/28 14:58
 */
@Api(tags = "广告合同")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/advertising/contract")
public class AdvertisingContractController extends BaseController {

    private static final int INCOME_TOTAL_COLUMNS = 12;

    private final AdvertisingContractService contractService;
    private final AdvertisingIncomeConvertor incomeConvertor;

    @ApiOperation("添加广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addContract(@Validated @RequestBody AdvertisingContractEditVo contract) {
        return contractService.addContract(contract);
    }

    @ApiOperation("修改广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateContract(@Validated @RequestBody AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        contractService.updateContract(contract);
    }

    @ApiOperation("修改已缴费广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/paid/update")
    public void updatePaidContract(@RequestBody AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        contractService.updatePaidContract(contract);
    }

    @ApiOperation("修改广告合同成本金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cost/update")
    public void updateContractCost(@Validated @RequestBody AdvertisingCostVo cost) {
        contractService.updateContractCost(cost);
    }

    @ApiOperation("停用广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/suspend")
    public void suspendContract(@Validated @RequestBody ContractSuspendDto suspendDto) {
        if (StringUtils.isBlank(suspendDto.getContractNo())) {
            throw new InvalidParameterException();
        }

        contractService.suspendContract(suspendDto);
    }

    @ApiOperation("删除广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteContract(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        contractService.deleteContract(id);
    }

    @ApiOperation("获取广告合同详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public AdvertisingContractVo findContractById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return contractService.findContractById(id);
    }

    @ApiOperation("统计广告合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public int countContract(@RequestBody AdvertisingContractSearchVo search) {
        return contractService.countContract(search);
    }

    @ApiOperation("广告合同列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<AdvertisingContractListVo> listContractPage(@RequestBody AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return contractService.listContractPage(search);
    }

    @ApiOperation("广告收益列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/income/page")
    public IncomePage<AdvertisingIncomeVo> listIncomePage(@RequestBody AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return IncomePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<AdvertisingIncomeVo> page = contractService.listAdvertisingIncomePage(search);
        IncomePage<AdvertisingIncomeVo> resultPage = new IncomePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        AdvertisingIncomeVo sumBill = contractService.sumAdvertisingIncome(search);
        if (sumBill != null) {
            resultPage.setAmount(DecimalUtils.nullToZero(sumBill.getAmount()));
            resultPage.setCost(DecimalUtils.nullToZero(sumBill.getCost()));
            resultPage.setShareAmount(DecimalUtils.nullToZero(sumBill.getShareAmount()));
            resultPage.setPaidShareAmount(DecimalUtils.nullToZero(sumBill.getPaidShareAmount()));
        }
        return resultPage;

    }

    @ApiOperation("广告收益导出")
    @GetMapping("/income/export")
    public void exportIncome(HttpServletResponse response, AdvertisingContractSearchVo search) throws IOException {
        List<AdvertisingIncomeVo> list = contractService.listAdvertisingIncome(search);
        List<AdvertisingIncomeExcelVo> resultList = new ArrayList<>(list.size());
        int serial = 1;
        for (AdvertisingIncomeVo income : list) {
            List<AdvertisingIncomeExcelVo> excelList = incomeToExcelVo(income);
            for (AdvertisingIncomeExcelVo item : excelList) {
                item.setSerialNo(serial);
            }
            resultList.addAll(excelList);

            serial++;
        }

        // 合计
        AdvertisingIncomeVo sumBill = contractService.sumAdvertisingIncome(search);
        resultList.add(incomeSumToExcelVo(sumBill));

        // 文件名
        String fileName = String.format(SenoxConst.Export.FILE_ADVERTISING_INCOME,
                DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        excelWriter.write(resultList, buildIncomeSheet(list));

        // 关闭流
        excelWriter.finish();
    }

    /**
     * 收益转导出视图
     * @param income
     * @return
     */
    private List<AdvertisingIncomeExcelVo> incomeToExcelVo(AdvertisingIncomeVo income) {
        List<AdvertisingPayoffVo> shares = income.getShares();

        List<AdvertisingIncomeExcelVo> resultList = null;
        if (!CollectionUtils.isEmpty(shares)) {
            resultList = new ArrayList<>(shares.size());
            for (AdvertisingPayoffVo payoff : shares) {
                AdvertisingIncomeExcelVo item = incomeConvertor.toExcelVo(income);
                item.setBenefit(DecimalUtils.subtract(item.getAmount(), item.getCost()));
                item.setRealtySerial(payoff.getRealtySerial());
                item.setRealtyName(payoff.getRealtyName());
                item.setRealtyOwner(payoff.getCustomerName());
                item.setShareAmount(payoff.getAmount());
                item.setSharePaidAmount(BillStatus.fromStatus(payoff.getStatus()) == BillStatus.PAID ? payoff.getAmount() : BigDecimal.ZERO);
                item.setShareTime(payoff.getPaidTime());
                resultList.add(item);
            }

        } else {
            AdvertisingIncomeExcelVo item = incomeConvertor.toExcelVo(income);
            item.setBenefit(DecimalUtils.subtract(item.getAmount(), item.getCost()));
            resultList = Collections.singletonList(item);
        }

        return resultList;
    }

    /**
     * 收益合计转导出视图
     * @param income
     * @return
     */
    public AdvertisingIncomeExcelVo incomeSumToExcelVo(AdvertisingIncomeVo income) {
        AdvertisingIncomeExcelVo result = new AdvertisingIncomeExcelVo();
        result.setSpaceSerial(SenoxConst.Export.COLUMN_SUM);
        result.setAmount(DecimalUtils.nullToZero(income.getAmount()));
        result.setCost(DecimalUtils.nullToZero(income.getCost()));
        result.setShareAmount(DecimalUtils.nullToZero(income.getShareAmount()));
        result.setSharePaidAmount(DecimalUtils.nullToZero(income.getPaidShareAmount()));
        return result;
    }

    /**
     * 收益sheet
     * @param list
     * @return
     */
    private WriteSheet buildIncomeSheet(List<AdvertisingIncomeVo> list) {
        ExcelWriterSheetBuilder builder = EasyExcelFactory.writerSheet(0, SHEET_ADVERTISING_INCOME).head(AdvertisingIncomeExcelVo.class);
        int rowIndex = 0;
        for (AdvertisingIncomeVo income : list) {
            // 当前行
            rowIndex += 1;
            if (!CollectionUtils.isEmpty(income.getShares()) && income.getShares().size() > 1) {
                int lastIndex = rowIndex + income.getShares().size() - 1;
                for (int columnIndex = 0; columnIndex < INCOME_TOTAL_COLUMNS; columnIndex++) {
                    builder.registerWriteHandler(new ExcelFillCellMergeStrategy(rowIndex, lastIndex, columnIndex, columnIndex));
                }

                rowIndex = lastIndex;
            }
        }
        return builder.build();
    }
}
