package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.context.AdminContext;
import com.senox.web.service.MaterialFlowService;
import com.senox.wms.constant.MaterialRequisitionStatus;
import com.senox.wms.vo.MaterialFlowInfoVo;
import com.senox.wms.vo.requisition.FlowRequest;
import com.senox.wms.vo.requisition.MaterialRequisitionSubmitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-09-13
 **/
@Api(tags = "仓储物料申购")
@RequiredArgsConstructor
@RequestMapping("/web/wms/material/flow")
@RestController
public class MaterialFlowController extends BaseController {
    private final MaterialFlowService materialFlowService;

    @ApiOperation("提交流程")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/submit")
    public MaterialRequisitionStatus submitFlow(@Validated({Add.class}) @RequestBody MaterialRequisitionSubmitVo submit) {
        return materialFlowService.submitFlow(submit);
    }

    @ApiOperation("获取流程")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/get")
    public MaterialFlowInfoVo getFlow(@RequestBody FlowRequest flowRequest){
        return materialFlowService.getFlow(flowRequest);
    }

}
