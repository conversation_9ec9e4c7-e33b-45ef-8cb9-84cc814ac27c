package com.senox.web.controller;

import com.senox.car.vo.ParkingRegulatorySearchVo;
import com.senox.car.vo.ParkingRegulatoryVo;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.ParkingRegulatoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:58
 */
@Api(tags = "监管名单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/parking/regulatory")
public class ParkingRegulatoryController {

    private final ParkingRegulatoryService parkingRegulatoryService;

    @ApiOperation("添加监管名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addParkingRegulatory(@Validated(Add.class) @RequestBody ParkingRegulatoryVo parkingRegulatoryVo) {
        return parkingRegulatoryService.addParkingRegulatory(parkingRegulatoryVo);
    }

    @ApiOperation("更新监管名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateParkingRegulatory(@Validated(Update.class) @RequestBody ParkingRegulatoryVo parkingRegulatoryVo) {
        parkingRegulatoryService.updateParkingRegulatory(parkingRegulatoryVo);
    }

    @ApiOperation("获取监管名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ParkingRegulatoryVo findById(@PathVariable Long id) {
        return parkingRegulatoryService.findById(id);
    }

    @ApiOperation("监管名单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<ParkingRegulatoryVo> page(@RequestBody ParkingRegulatorySearchVo searchVo) {
        return parkingRegulatoryService.page(searchVo);
    }
}
