package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.JadeBirdFireDeviceEventSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceEventVo;
import com.senox.web.service.JadeBirdFireDeviceEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Api(tags = "青鸟消防设备")
@RestController
@RequestMapping("/web/fire/jade/bird/device/event")
@RequiredArgsConstructor
public class JadeBirdFireDeviceEventController {
    private final JadeBirdFireDeviceEventService fireDeviceEventService;

    @ApiOperation("事件列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public List<JadeBirdFireDeviceEventVo> list(@RequestBody JadeBirdFireDeviceEventSearchVo search) {
        return fireDeviceEventService.list(search);
    }

    @ApiOperation("事件分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list/page")
    public PageResult<JadeBirdFireDeviceEventVo> pageList(@RequestBody JadeBirdFireDeviceEventSearchVo search) {
        return fireDeviceEventService.pageList(search);
    }
}
