package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicycleSettingVo;
import com.senox.web.service.BicycleSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 11:01
 */
@Api(tags = "三轮车配置信息")
@RestController
@RequestMapping("/web/bicycle/setting")
@RequiredArgsConstructor
public class BicycleSettingController {

    private final BicycleSettingService bicycleSettingService;

    @ApiOperation("添加三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleSetting(@Validated(Add.class) @RequestBody BicycleSettingVo settingVo) {
        return bicycleSettingService.addBicycleSetting(settingVo);
    }

    @ApiOperation("修改三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicycleSetting(@Validated(Update.class) @RequestBody BicycleSettingVo settingVo) {
        bicycleSettingService.updateBicycleSetting(settingVo);
    }

    @ApiOperation("根据id获取三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleSettingVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return bicycleSettingService.findById(id);
    }

    @ApiOperation("根据id删除三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        bicycleSettingService.deleteById(id);
    }

    @ApiOperation("三轮车配置信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicycleSettingVo> listBicycleSetting() {
        return bicycleSettingService.listBicycleSetting();
    }
}
