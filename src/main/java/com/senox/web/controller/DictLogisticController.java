package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import com.senox.web.service.DictLogisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "物流字典")
@RestController
@RequestMapping("/web/logistic/dict")
@RequiredArgsConstructor
public class DictLogisticController {
    private final DictLogisticService dictLogisticService;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/{category}/add")
    public void add(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticVo dictLogisticVo) {
        dictLogisticService.addDictLogistic(category,dictLogisticVo);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody DictLogisticVo dictLogisticVo) {
        dictLogisticService.updateDictLogistic(dictLogisticVo);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{dictLogisticId}")
    public void deleteById(@PathVariable Long dictLogisticId) {
        dictLogisticService.deleteDictLogisticById(dictLogisticId);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/{category}/list/page")
    public PageResult<DictLogisticVo> listPage(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticSearchVo searchVo) {
        return dictLogisticService.listDictLogistic(category,searchVo);
    }

}
