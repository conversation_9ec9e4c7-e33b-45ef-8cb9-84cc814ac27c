package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import com.senox.realty.vo.RealtyStatisticsVo;
import com.senox.realty.vo.StatisticsSearchVo;
import com.senox.web.service.StatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:29
 */
@Api(tags = "统计")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/statistic")
public class StatisticsController {

    private final StatisticsService statisticsService;

    @ApiOperation("物业统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/page")
    public PageResult<RealtyStatisticsVo> realtyStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo) {
        return statisticsService.realtyStatisticsPageResult(searchVo);
    }

    @ApiOperation("根据统计日期获取物业统计记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/findByDate")
    public RealtyStatisticsVo findRealtyStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate) {
        return statisticsService.findRealtyStatisticsByDate(statisticsDate);
    }

    @ApiOperation("广告位统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advertising/page")
    public PageResult<AdvertisingStatisticsVo> advertisingStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo) {
        return statisticsService.advertisingStatisticsPageResult(searchVo);
    }

    @ApiOperation("根据统计日期获取广告位统计记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advertising/findByDate")
    public AdvertisingStatisticsVo findAdvertisingStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate) {
        return statisticsService.findAdvertisingStatisticsByDate(statisticsDate);
    }
}
