package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.LogisticsDailyOrderDeliveryService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.LogisticsDailyOrderDeliveryExcelVo;
import com.senox.web.vo.LogisticsDailyOrderDeliveryExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.ResolverStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@Api(tags = "物流配送")
@RequiredArgsConstructor
@RequestMapping("/web/logistics/delivery/daily/order/")
@RestController
public class LogisticsDailyOrderDeliveryController extends BaseController {
    private final LogisticsDailyOrderDeliveryService dailyOrderDeliveryService;

    @ApiOperation("添加日订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addDailyOrder(@RequestBody LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo) {
        dailyOrderDeliveryService.addBatch(Collections.singletonList(dailyOrderDeliveryVo));
    }

    @ApiOperation("根据id查找配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticsDailyOrderDeliveryVo findById(@PathVariable Long id) {
        return dailyOrderDeliveryService.findById(id);
    }

    @ApiOperation("修改日订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateDailyOrder(@RequestBody LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo) {
        dailyOrderDeliveryService.update(dailyOrderDeliveryVo);
    }

    @ApiOperation("根据id删除日订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteById/{id}")
    public void deleteDailyOrderById(@PathVariable Long id) {
        dailyOrderDeliveryService.deleteById(id);
    }

    @ApiOperation("日订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageStatisticsResult<LogisticsDailyOrderDeliveryVo, LogisticsDailyOrderDeliveryTotalAmountVo> listDailyOrder(@RequestBody LogisticsDailyOrderDeliverySearchVo searchVo) {
        return dailyOrderDeliveryService.listPage(searchVo);
    }

    @ApiOperation("导入日订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void importDailyOrder(@RequestPart("file") MultipartFile file) throws IOException {
        checkExcelFile(file);
        List<LogisticsDailyOrderDeliveryExcelVo> excels = new ArrayList<>();
        PageReadListener<LogisticsDailyOrderDeliveryExcelVo> readListener = new PageReadListener<>(excels::addAll);
        EasyExcelFactory.read(file.getInputStream(), LogisticsDailyOrderDeliveryExcelVo.class, readListener).sheet().doRead();
        List<LogisticsDailyOrderDeliveryVo> list = toVo(excels.stream()
                //过滤下单时间为空的
                .filter(l -> !StringUtils.isBlank(l.getOrderTime()))
                .collect(Collectors.toList()));
        dailyOrderDeliveryService.addBatch(list);
    }

    @ApiOperation("导出日订单")
    @GetMapping("/export")
    public void export(HttpServletResponse response, LogisticsDailyOrderDeliverySearchVo searchVo) throws IOException {
        List<LogisticsDailyOrderDeliveryVo> list = dailyOrderDeliveryService.list(searchVo);
        List<LogisticsDailyOrderDeliveryExportVo> exports = toExport(list);
        String fileName = SenoxConst.Export.TMS_LOGISTIC_DAILY_ORDER_DELIVERY_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticsDailyOrderDeliveryExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_LOGISTIC_DAILY_ORDER_DELIVERY_SHEET)
                .doWrite(exports);
    }

    private List<LogisticsDailyOrderDeliveryExportVo> toExport(List<LogisticsDailyOrderDeliveryVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LogisticsDailyOrderDeliveryExportVo> exports = new ArrayList<>();
        AtomicInteger totalPieces = new AtomicInteger(0);
        AtomicReference<BigDecimal> totalReceivableFreightCharge = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalReceivedFreightCharge = new AtomicReference<>(BigDecimal.ZERO);
        list.forEach(l -> {
            LogisticsDailyOrderDeliveryExportVo export = new LogisticsDailyOrderDeliveryExportVo();
            export.setOrderTime(DateUtils.formatYearMonth(l.getOrderTime(), DateUtils.PATTERN_FULL_DATE));
            export.setSendTime(DateUtils.formatYearMonth(l.getSendTime(), DateUtils.PATTERN_FULL_DATE));
            export.setOrderPerson(l.getOrderPerson());
            export.setType(l.getType().equals(1) ? "干货线" : "冻品线");
            export.setOrderDeliveryNo(l.getOrderDeliveryNo());
            export.setOrderPieces(l.getOrderPieces());
            export.setOrderTotalKilograms(l.getOrderTotalKilograms());
            export.setOrderDeliveryCarNo(l.getOrderDeliveryCarNo());
            export.setPieces(l.getPieces());
            export.setKilograms(l.getKilograms());
            export.setFreightUnitPrice(l.getFreightUnitPrice());
            export.setReceivableFreightCharge(l.getReceivableFreightCharge());
            export.setReceivedFreightCharge(l.getReceivedFreightCharge());
            export.setDiscrepancyAmount(l.getDiscrepancyAmount());
            export.setCreateTime(DateUtils.formatDateTime(l.getCreateTime(), DateUtils.PATTERN_FULL_DATE_TIME));
            export.setCreatorName(l.getCreatorName());
            export.setRemark(l.getRemark());
            totalPieces.updateAndGet(p -> p + l.getPieces());
            totalReceivableFreightCharge.updateAndGet(r -> r.add(l.getReceivableFreightCharge()));
            totalReceivedFreightCharge.updateAndGet(r -> r.add(l.getReceivedFreightCharge()));
            exports.add(export);
        });
        LogisticsDailyOrderDeliveryExportVo totalExport = new LogisticsDailyOrderDeliveryExportVo();
        totalExport.setType("合计");
        totalExport.setPieces(totalPieces.get());
        totalExport.setReceivableFreightCharge(totalReceivableFreightCharge.get());
        totalExport.setReceivedFreightCharge(totalReceivedFreightCharge.get());
        exports.add(totalExport);
        return exports;
    }

    private List<LogisticsDailyOrderDeliveryVo> toVo(List<LogisticsDailyOrderDeliveryExcelVo> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return Collections.emptyList();
        }
        List<LogisticsDailyOrderDeliveryVo> list = new ArrayList<>();
        for (LogisticsDailyOrderDeliveryExcelVo excelData : excels) {
            LogisticsDailyOrderDeliveryVo data = new LogisticsDailyOrderDeliveryVo();
            data.setOrderDeliveryNo(excelData.getOrderDeliveryNo());
            data.setOrderDeliveryCarNo(excelData.getOrderDeliveryCarNo());
            data.setOrderPieces(excelData.getOrderPieces());
            data.setOrderTotalKilograms(excelData.getOrderTotalKilograms());
            final String pattern = "yyyy/M/d";
            data.setOrderTime(DateUtils.parseDate(excelData.getOrderTime(), pattern,ResolverStyle.LENIENT));
            data.setOrderPerson(excelData.getOrderPerson());
            data.setSendTime(DateUtils.parseDate(excelData.getSendTime(), pattern,ResolverStyle.LENIENT));
            data.setType(excelData.getType().equals("干货线") ? 1 : 2);
            data.setReceivableFreightCharge(excelData.getReceivableFreightCharge());
            list.add(data);
        }
        return list;
    }
}
