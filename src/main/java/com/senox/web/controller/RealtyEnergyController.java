package com.senox.web.controller;


import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.*;
import com.senox.web.service.RealtyEnergyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Api(tags = "物业集抄")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/realty/energy")
public class RealtyEnergyController extends BaseController {
    private final RealtyEnergyService realtyEnergyService;

    @ApiOperation("计量设备绑定物业")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/bind")
    public void meteringPointBindRealty(@Validated({Add.class}) @RequestBody RealtyBindEnergyMeteringPointVo realtyBindPoint) {
        realtyEnergyService.meteringPointBindRealty(realtyBindPoint);
    }

    @ApiOperation("物业计量点统计")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/list/count")
    public Integer meteringPointCountList(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyService.meteringPointCountList(search);
    }

    @ApiOperation("物业计量点列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/list")
    public PageResult<RealtyToEnergyMeteringPointVo> meteringPoint(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyService.meteringPoint(search);
    }

    @ApiOperation("物业计量点表码列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/readings/list")
    public PageResult<RealtyToEnergyMeteringPointReadingsVo> pointBmList(@RequestBody RealtyToEnergyMeteringPointReadingsSearchVo search) {
        return realtyEnergyService.meteringPointReadingsList(search);
    }

    @ApiOperation("同步计量点读数")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/readings/sync")
    public void syncMeteringPoint(@RequestBody RealtyWeBatchVo realtyWeBatch) {
        realtyEnergyService.syncMeteringPoint(realtyWeBatch);
    }

    @ApiOperation("自动绑定计量设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/automatic/bind")
    public PointBindRealtyResult automaticMeteringPointBindRealty(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyService.automaticMeteringPointBindRealty(search);
    }

    @ApiOperation("计量点作废")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/metering/point/cancel/{meteringPointCode}")
    public void meteringPointCancel(@PathVariable String meteringPointCode) {
        realtyEnergyService.meteringPointCancel(meteringPointCode);
    }

    @ApiOperation("计量点刷新")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/metering/point/refresh")
    public EnergyPointRefreshResult meteringPointRefresh(@RequestParam(required = false) String meteringPointCode) {
        return realtyEnergyService.meteringPointRefresh(meteringPointCode);
    }
}
