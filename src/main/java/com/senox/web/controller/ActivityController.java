package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import com.senox.web.service.ActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:52
 */
@Api(tags = "活动")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/activity")
public class ActivityController {

    private final ActivityService activityService;

    @ApiOperation("添加活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addActivity(@RequestBody ActivityVo activityVo) {
        return activityService.addActivity(activityVo);
    }

    @ApiOperation("更新活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateActivity(@RequestBody ActivityVo activityVo) {
        activityService.updateActivity(activityVo);
    }

    @ApiOperation("生成活动链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/generate/{id}")
    public String generateActivityUrl(@PathVariable Long id) {
        return activityService.generateActivityUrl(id);
    }

    @ApiOperation("根据id获取活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ActivityVo findActivityById(@PathVariable Long id) {
        return activityService.findActivityById(id);
    }

    @ApiOperation("根据id删除活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteActivityById(@PathVariable Long id) {
        activityService.deleteActivityById(id);
    }

    @ApiOperation("活动分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<ActivityVo> pageActivityResult(@RequestBody ActivitySearchVo searchVo) {
        return activityService.pageActivityResult(searchVo);
    }
}
