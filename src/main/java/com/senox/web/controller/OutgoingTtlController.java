package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.OutgoingTtlConvert;
import com.senox.web.service.OutgoingTtlService;
import com.senox.web.vo.OutgoingTtlExcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/8 14:11
 */
@Api(tags = "太太乐外发")
@RestController
@RequestMapping("/web/logistic/outgoing/ttl")
@RequiredArgsConstructor
public class OutgoingTtlController extends BaseController{

    private final OutgoingTtlService outgoingTtlService;
    private final OutgoingTtlConvert outgoingTtlConvert;

    @ApiOperation("添加太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo) {
        return outgoingTtlService.addOutgoingTtl(outgoingTtlVo);
    }

    @ApiOperation("更新太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo) {
        outgoingTtlService.updateOutgoingTtl(outgoingTtlVo);
    }

    @ApiOperation("根据ID获取太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public OutgoingTtlVo findById(@PathVariable Long id) {
        return outgoingTtlService.findById(id);
    }

    @ApiOperation("根据ID删除太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        outgoingTtlService.deleteById(id);
    }

    @ApiOperation("太太乐外发分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult(@RequestBody OutgoingTtlSearchVo searchVo) {
        return outgoingTtlService.pageResult(searchVo);
    }

    @ApiOperation("导入太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void importOutgoingTtl(@RequestPart("file") MultipartFile file) throws IOException {
        checkExcelFile(file);
        List<OutgoingTtlExcelVo> excelVoList = new ArrayList<>();
        PageReadListener<OutgoingTtlExcelVo> readListener =  new PageReadListener<>(excelVoList::addAll);
        EasyExcelFactory.read(file.getInputStream(), OutgoingTtlExcelVo.class, readListener).sheet().doRead();
        excelVoList = excelVoList.stream().filter(x -> !StringUtils.isBlank(x.getLogisticsNo())).collect(Collectors.toList());
        List<OutgoingTtlVo> voList = outgoingTtlConvert.toVo(excelVoList);
        outgoingTtlService.batchAddOutgoingTtl(voList);
    }

    @ApiOperation("太太乐外发导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportOutgoingTtl(HttpServletResponse response, OutgoingTtlSearchVo searchVo) throws IOException {
        searchVo.setPage(false);
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult = outgoingTtlService.pageResult(searchVo);
        List<OutgoingTtlExcelVo> excelVoList = new ArrayList<>(pageResult.getDataList().size() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (OutgoingTtlVo outgoingTtlVo : pageResult.getDataList()) {
                OutgoingTtlExcelVo excelVo = toExcelVo(outgoingTtlVo);
                excelVo.setSerialNo(String.valueOf(serial));
                excelVoList.add(excelVo);
                serial++;
            }
        }
        excelVoList.add(toSumVo(pageResult.getStatistics()));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_LOGISTIC_OUTGOING_TTL_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), OutgoingTtlExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_LOGISTIC_OUTGOING_TTL_REPORT).doWrite(excelVoList);
    }

    private OutgoingTtlExcelVo toSumVo(OutgoingTtlVo ttlVo) {
        OutgoingTtlExcelVo sumVo = new OutgoingTtlExcelVo();
        sumVo.setSerialNo(SenoxConst.Export.COLUMN_SUM);
        sumVo.setShipperPieces(ttlVo.getShipperPieces());
        sumVo.setActualFreightAmount(ttlVo.getActualFreightAmount());
        sumVo.setActualShippingAmount(ttlVo.getActualShippingAmount());
        sumVo.setProfitAmount(ttlVo.getProfitAmount());
        return sumVo;
    }

    private OutgoingTtlExcelVo toExcelVo(OutgoingTtlVo ttlVo) {
        OutgoingTtlExcelVo excelVo = new OutgoingTtlExcelVo();
        excelVo.setReceivingDate(ttlVo.getReceivingDate());
        excelVo.setShipperName(ttlVo.getShipperName());
        excelVo.setRecipientName(ttlVo.getRecipientName());
        excelVo.setRecipientAddress(ttlVo.getRecipientAddress());
        excelVo.setRecipientContact(ttlVo.getRecipientContact());
        excelVo.setLogisticsCompany(ttlVo.getLogisticsCompany());
        excelVo.setLogisticsNo(ttlVo.getLogisticsNo());
        excelVo.setShipperPieces(ttlVo.getShipperPieces());
        excelVo.setActualFreightAmount(ttlVo.getActualFreightAmount());
        excelVo.setActualShippingAmount(ttlVo.getActualShippingAmount());
        excelVo.setProfitAmount(ttlVo.getProfitAmount());
        excelVo.setSettlementType(ttlVo.getSettlementType().equals(1) ? "到付" : "现付");
        return excelVo;
    }
}
