package com.senox.web.controller;

import com.senox.context.AdminContext;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import com.senox.web.service.BicyclePayoffChargesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-4-2
 */
@Api(tags = "三轮车应付费用")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/bicycle/payoff/charges")
public class BicyclePayoffChargesController {
    private final BicyclePayoffChargesService payoffChargesService;

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody BicyclePayoffChargesVo payoffChargesVo) {
        payoffChargesService.update(payoffChargesVo);
    }

    @ApiOperation("查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/find")
    public BicyclePayoffChargesVo find() {
        return payoffChargesService.find();
    }


}
