package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageRequest;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.GateRoleDeviceEditVo;
import com.senox.dm.vo.GateRoleSearchVo;
import com.senox.dm.vo.GateRoleUserEditVo;
import com.senox.dm.vo.GateRoleUserVo;
import com.senox.dm.vo.GateRoleVo;
import com.senox.web.service.GateRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/13 16:52
 */
@Api(tags = "海康门禁设备角色")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/control/role")
public class GateRoleController extends BaseController {

    private final GateRoleService gateRoleService;

    @ApiOperation("添加门禁角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addRole(@Validated(Add.class) @RequestBody GateRoleVo gateRole) {
        return gateRoleService.addGateRole(gateRole);
    }

    @ApiOperation("更新门禁角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateRole(@Validated(Update.class) @RequestBody GateRoleVo gateRole) {
        gateRoleService.updateGateRole(gateRole );
    }

    @ApiOperation("删除门禁角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteRole(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        gateRoleService.deleteGateRole(id);
    }

    @ApiOperation("门禁角色列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<GateRoleVo> listRolePage(@RequestBody PageRequest search) {
        return gateRoleService.listGateRolePage(search);
    }

    @ApiOperation("添加门禁角色用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/user/add")
    public void addRoleUsers(@Validated @RequestBody GateRoleUserEditVo roleUsers) {
        if (CollectionUtils.isEmpty(roleUsers.getUsers())) {
            throw new InvalidParameterException();
        }

        gateRoleService.addGateRoleUsers(roleUsers);
    }

    @ApiOperation("删除门禁角色用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/user/delete")
    public void deleteRoleUsers(@Validated @RequestBody GateRoleUserEditVo roleUsers) {
        if (CollectionUtils.isEmpty(roleUsers.getUsers())) {
            throw new InvalidParameterException();
        }

        gateRoleService.deleteGateRoleUsers(roleUsers);
    }

    @ApiOperation("门禁角色用户列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/user/page")
    public PageResult<GateRoleUserVo> listRoleUserPage(@RequestBody GateRoleSearchVo search) {
        return gateRoleService.listRoleUserPage(search);
    }


    @ApiOperation("添加门禁角色设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/device/add")
    public void addRoleDevices(@Validated @RequestBody GateRoleDeviceEditVo roleDevices) {
        if (CollectionUtils.isEmpty(roleDevices.getDevices())) {
            throw new InvalidParameterException();
        }

        gateRoleService.addGateRoleDevices(roleDevices);
    }

    @ApiOperation("删除门禁角色设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/device/delete")
    public void deleteRoleDevices(@Validated @RequestBody GateRoleDeviceEditVo roleDevices) {
        if (CollectionUtils.isEmpty(roleDevices.getDevices())) {
            throw new InvalidParameterException();
        }

        gateRoleService.deleteGateRoleDevices(roleDevices);
    }

    @ApiOperation("门禁角色设备列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/device/page")
    public PageResult<AccessControlVo> listRoleDevicePage(@RequestBody GateRoleSearchVo search) {
        return gateRoleService.listGateRoleDevicePage(search);
    }
}
