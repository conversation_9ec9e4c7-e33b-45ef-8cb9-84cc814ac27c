package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.BicycleOrderReportService;
import com.senox.web.vo.BicycleOrderReportExportVo;
import com.senox.web.vo.BicyclePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/8 11:42
 */
@Api(tags = "三轮车报表")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/bicycle/report")
public class BicycleOrderReportController extends BaseController{

    private final BicycleOrderReportService bicycleOrderReportService;

    @ApiOperation("生成日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/generate")
    public void generateDayReport(@RequestBody BicycleReportDateVo dateVo) {
        bicycleOrderReportService.generateDayReport(dateVo);
    }


    @ApiOperation("更新日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/update")
    public void updateDayReport(@Validated(Update.class) @RequestBody BicycleOrderDayReportVo dayReportVo) {
        bicycleOrderReportService.updateDayReport(dayReportVo);
    }

    @ApiOperation("根据id集合查询日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/list/byIds")
    public List<BicycleOrderDayReportVo> listDayReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return bicycleOrderReportService.listDayReport(ids);
    }

    @ApiOperation("删除日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/delete")
    public void deleteDayReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        bicycleOrderReportService.deleteDayReport(ids);
    }

    @ApiOperation("获取日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/day/get/{id}")
    public BicycleOrderDayReportVo findDayReportById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return bicycleOrderReportService.findDayReportById(id);
    }

    @ApiOperation("日报表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/list")
    public BicyclePage<BicycleOrderDayReportVo> listDayReport(@RequestBody BicycleOrderDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicyclePage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        // list
        PageResult<BicycleOrderDayReportVo> page = bicycleOrderReportService.listDayReport(searchVo);
        BicyclePage<BicycleOrderDayReportVo> resultPage = new BicyclePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        BicycleOrderDayReportVo sumReport = bicycleOrderReportService.sumDayReport(searchVo);
        if (sumReport != null) {
            resultPage.setTotalCount(sumReport.getTotalCount());
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumReport.getDeliveryCharge()));
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumReport.getTotalPieces()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumReport.getOtherCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumReport.getTotalCharge()));
        }
        return resultPage;
    }

    @ApiOperation("导出日报")
    @GetMapping("/dayReport/export")
    public void exportBicycleDayReport(HttpServletResponse response, BicycleOrderDayReportSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setOrderStr("report_date");
        searchVo.setPage(false);
        PageResult<BicycleOrderDayReportVo> page = bicycleOrderReportService.listDayReport(searchVo);
        BicycleOrderDayReportVo sumDayReport = bicycleOrderReportService.sumDayReport(searchVo);
        List<BicycleOrderReportExportVo> exportList = new ArrayList<>(page.getTotalSize());

        if (!CollectionUtils.isEmpty(page.getDataList())) {
            for (int index = 0; index < page.getDataList().size(); index++) {
                BicycleOrderDayReportVo item = page.getDataList().get(index);
                BicycleOrderReportExportVo exportItem = newBicycleOrderDayReportExportVo(item);
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
            exportList.add(sumBicycleOrderDayReportExportVo(sumDayReport));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_BICYCLE_ORDER_DAY_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleOrderReportExportVo.class)
                .sheet(SenoxConst.Export.SHEET_BICYCLE_ORDER_DAY_REPORT)
                .doWrite(exportList);
    }

    @ApiOperation("生成月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/generate")
    public void generateMonthReport(@RequestBody BicycleReportDateVo dateVo) {
        bicycleOrderReportService.generateMonthReport(dateVo);
    }

    @ApiOperation("更新月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/update")
    public void updateMonthReport(@Validated(Update.class) @RequestBody BicycleOrderMonthReportVo monthReportVo) {
        bicycleOrderReportService.updateMonthReport(monthReportVo);
    }

    @ApiOperation("根据id集合查询月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/list/byIds")
    public List<BicycleOrderMonthReportVo> listMonthReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return bicycleOrderReportService.listMonthReport(ids);
    }

    @ApiOperation("删除月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/delete")
    public void deleteMonthReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        bicycleOrderReportService.deleteMonthReport(ids);
    }

    @ApiOperation("获取月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/get/{id}")
    public BicycleOrderMonthReportVo findMonthReportById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return bicycleOrderReportService.findMonthReportById(id);
    }

    @ApiOperation("月报表明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/dayList/{id}")
    public List<BicycleOrderDayReportVo> listMonthDayReport(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return bicycleOrderReportService.listMonthDayReport(id);
    }

    @ApiOperation("月报表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/list")
    public BicyclePage<BicycleOrderMonthReportVo> listMonthReport(@RequestBody BicycleOrderMonthReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicyclePage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        // list
        PageResult<BicycleOrderMonthReportVo> page = bicycleOrderReportService.listMonthReport(searchVo);
        BicyclePage<BicycleOrderMonthReportVo> resultPage = new BicyclePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        BicycleOrderMonthReportVo sumReport = bicycleOrderReportService.sumMonthReport(searchVo);
        if (sumReport != null) {
            resultPage.setTotalCount(sumReport.getTotalCount());
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumReport.getDeliveryCharge()));
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumReport.getTotalPieces()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumReport.getOtherCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumReport.getTotalCharge()));
        }
        return resultPage;
    }

    @ApiOperation("导出月报")
    @GetMapping("/monthReport/export")
    public void exportBicycleMonthReport(HttpServletResponse response, BicycleOrderMonthReportSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setOrderStr("report_year, report_month");
        searchVo.setPage(false);
        PageResult<BicycleOrderMonthReportVo> page = bicycleOrderReportService.listMonthReport(searchVo);
        BicycleOrderMonthReportVo sumDayReport = bicycleOrderReportService.sumMonthReport(searchVo);
        List<BicycleOrderReportExportVo> exportList = new ArrayList<>(page.getTotalSize());

        if (!CollectionUtils.isEmpty(page.getDataList())) {
            for (int index = 0; index < page.getDataList().size(); index++) {
                BicycleOrderMonthReportVo item = page.getDataList().get(index);
                BicycleOrderReportExportVo exportItem = newBicycleOrderMonthReportExportVo(item);
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
            exportList.add(sumBicycleOrderMonthReportExportVo(sumDayReport));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_BICYCLE_ORDER_MONTH_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleOrderReportExportVo.class)
                .sheet(SenoxConst.Export.SHEET_BICYCLE_ORDER_MONTH_REPORT)
                .doWrite(exportList);
    }

    private BicycleOrderReportExportVo newBicycleOrderDayReportExportVo(BicycleOrderDayReportVo reportVo) {
        BicycleOrderReportExportVo result = new BicycleOrderReportExportVo();
        result.setReportDate(reportVo.getReportDate().toString());
        return getBicycleOrderReportExportVo(result
                , reportVo.getMerchantName()
                , reportVo.getTotalPieces()
                , reportVo.getTotalCount()
                , reportVo.getDeliveryCharge(),
                reportVo.getOtherCharge()
                , reportVo.getTotalCharge());
    }

    private BicycleOrderReportExportVo sumBicycleOrderDayReportExportVo(BicycleOrderDayReportVo reportVo) {
        return getBicycleOrderReportExportVo(reportVo.getTotalPieces()
                , reportVo.getTotalCount()
                , reportVo.getDeliveryCharge()
                , reportVo.getOtherCharge()
                , reportVo.getTotalCharge());
    }

    private BicycleOrderReportExportVo newBicycleOrderMonthReportExportVo(BicycleOrderMonthReportVo reportVo) {
        BicycleOrderReportExportVo result = new BicycleOrderReportExportVo();
        result.setReportDate(reportVo.getReportYearMonth());
        return getBicycleOrderReportExportVo(result
                , reportVo.getMerchantName()
                , reportVo.getTotalPieces()
                , reportVo.getTotalCount()
                , reportVo.getDeliveryCharge()
                , reportVo.getOtherCharge()
                , reportVo.getTotalCharge());
    }


    private BicycleOrderReportExportVo sumBicycleOrderMonthReportExportVo(BicycleOrderMonthReportVo reportVo) {
        return getBicycleOrderReportExportVo(reportVo.getTotalPieces()
                , reportVo.getTotalCount()
                , reportVo.getDeliveryCharge()
                , reportVo.getOtherCharge()
                , reportVo.getTotalCharge());
    }

    @NotNull
    private BicycleOrderReportExportVo getBicycleOrderReportExportVo(BicycleOrderReportExportVo result, String merchantName, BigDecimal totalPieces, Integer totalCount, BigDecimal deliveryCharge, BigDecimal otherCharge, BigDecimal totalCharge) {
        result.setMerchantName(merchantName);
        return getBicycleOrderReportExportVo(result, totalPieces, totalCount, deliveryCharge, otherCharge, totalCharge);
    }

    @NotNull
    private BicycleOrderReportExportVo getBicycleOrderReportExportVo(BicycleOrderReportExportVo result, BigDecimal totalPieces, Integer totalCount, BigDecimal deliveryCharge, BigDecimal otherCharge, BigDecimal totalCharge) {
        result.setTotalPieces(totalPieces == null ? BigDecimal.ZERO : totalPieces);
        result.setTotalCount(totalCount == null ? 0 : totalCount);
        result.setDeliveryCharge(deliveryCharge == null ? BigDecimal.ZERO : deliveryCharge);
        result.setOtherCharge(otherCharge == null ? BigDecimal.ZERO : otherCharge);
        result.setTotalCharge(totalCharge == null ? BigDecimal.ZERO : totalCharge);
        return result;
    }

    @NotNull
    private BicycleOrderReportExportVo getBicycleOrderReportExportVo(BigDecimal totalPieces, Integer totalCount, BigDecimal deliveryCharge, BigDecimal otherCharge, BigDecimal totalCharge) {
        BicycleOrderReportExportVo result = new BicycleOrderReportExportVo();
        result.setReportDate(SenoxConst.Export.COLUMN_SUM);
        return getBicycleOrderReportExportVo(result, totalPieces, totalCount, deliveryCharge, otherCharge, totalCharge);
    }
}
