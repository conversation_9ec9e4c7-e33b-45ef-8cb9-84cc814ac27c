package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.user.vo.DepartmentNode;
import com.senox.user.vo.DepartmentVo;
import com.senox.web.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 14:29
 */
@Api(tags = "部门")
@RestController
@RequestMapping("/web/department")
public class DepartmentController extends BaseController {

    @Autowired
    private DepartmentService departmentService;

    @ApiOperation("添加部门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addDepartment(@Validated({Add.class}) @RequestBody DepartmentVo department) {
        return departmentService.addDepartment(department);
    }

    @ApiOperation("更新部门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateDepartment(@Validated(Update.class) @RequestBody DepartmentVo department) {
        if (department.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        departmentService.updateDepartment(department);
    }

    @ApiOperation("删除部门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteDepartment(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        departmentService.deleteDepartment(id);
    }

    @ApiOperation("获取部门详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public DepartmentVo getDepartment(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return departmentService.findById(id);
    }

    @ApiOperation("部门列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/list/{parentId}")
    public List<DepartmentVo> listDepartment(@PathVariable Long parentId) {
        if (parentId < 0L) {
            throw new InvalidParameterException("无效的id");
        }
        return departmentService.listDepartment(parentId);
    }

    @ApiOperation("部门树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/treeList")
    public List<DepartmentNode> listDepartmentTree() {
        return departmentService.listDepartmentTree();
    }
}
