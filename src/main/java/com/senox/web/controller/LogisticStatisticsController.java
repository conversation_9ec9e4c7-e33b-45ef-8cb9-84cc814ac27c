package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.constant.LogisticStatisticsIncomeType;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.LogisticStatisticsDayReportConvertor;
import com.senox.web.service.LogisticStatisticsService;
import com.senox.web.vo.LogisticStatisticsDayReportExcelVo;
import com.senox.web.vo.LogisticStatisticsDayReportExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:31
 */
@Api(tags = "物流货物统计")
@RestController
@RequestMapping("/web/logistic")
@RequiredArgsConstructor
public class LogisticStatisticsController extends BaseController{

    private final LogisticStatisticsService logisticStatisticsService;
    private final LogisticStatisticsDayReportConvertor convertor;


    @ApiOperation("添加货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/add")
    public Long addLogisticStatisticsDayReport(@RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        return logisticStatisticsService.addLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
    }

    @ApiOperation("更新货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/update")
    public void updateLogisticStatisticsDayReport(@Validated({Update.class}) @RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        logisticStatisticsService.updateLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
    }

    @ApiOperation("根据Id获取货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/statistics/get/{id}")
    public LogisticStatisticsDayReportVo findLogisticStatisticsDayReportById(@PathVariable Long id) {
        return logisticStatisticsService.findLogisticStatisticsDayReportById(id);
    }

    @ApiOperation("根据Id删除货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/delete/{id}")
    public void deleteLogisticStatisticsDayReportById(@PathVariable Long id) {
        logisticStatisticsService.deleteLogisticStatisticsDayReportById(id);
    }

    @ApiOperation("货物统计报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/page")
    public LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(@RequestBody LogisticStatisticsDayReportSearchVo searchVo) {
        return logisticStatisticsService.page(searchVo);
    }

    @ApiOperation("货物统计批量收款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/batch/update")
    public void batchUpdate(@RequestBody LogisticStatisticsDayReportBatchUpdateVo batchUpdateVo) {
        logisticStatisticsService.batchUpdate(batchUpdateVo);
    }

    @ApiOperation("货物统计报表导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/statistics/export")
    public void exportLogisticStatisticsDayReport(HttpServletResponse response, LogisticStatisticsDayReportSearchVo searchVo) throws IOException {
        searchVo.setPage(false);
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> resultPage = logisticStatisticsService.page(searchVo);
        List<LogisticStatisticsDayReportExportVo> exportList = new ArrayList<>(resultPage.getDataList().size() + 1);
        if (!CollectionUtils.isEmpty(resultPage.getDataList())) {
            int serial = 1;
            for (LogisticStatisticsDayReportVo reportVo : resultPage.getDataList()) {
                LogisticStatisticsDayReportExportVo exportVo = logisticStatisticsDayReportToExportVo(reportVo);
                exportVo.setSerialNo(String.valueOf(serial));
                exportList.add(exportVo);
                serial++;
            }
        }
        exportList.add(sumLogisticStatisticsDayReportExport(resultPage));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_LOGISTIC_STATISTICS_DAY_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticStatisticsDayReportExportVo.class)
                .sheet(SenoxConst.Export.SHEET_LOGISTIC_STATISTICS_DAY_REPORT).doWrite(exportList);
    }

    @ApiOperation("导入货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics/import")
    public void importLogisticStatisticsDayReport(@RequestPart("file") MultipartFile file) throws IOException {
        checkExcelFile(file);
        List<LogisticStatisticsDayReportExcelVo> excelVoList = new ArrayList<>();
        PageReadListener<LogisticStatisticsDayReportExcelVo> readListener = new PageReadListener<>(excelVoList::addAll);
        EasyExcelFactory.read(file.getInputStream(), LogisticStatisticsDayReportExcelVo.class, readListener).sheet().doRead();
        excelVoList = excelVoList.stream().filter(x -> !StringUtils.isBlank(x.getIncomeType())).collect(Collectors.toList());
        List<LogisticStatisticsDayReportVo> reportVoList = convertor.toVo(excelVoList);
        //插入
        logisticStatisticsService.batchAddLogisticStatisticsDayReport(calculate(reportVoList));
    }


    private List<LogisticStatisticsDayReportVo> calculate(List<LogisticStatisticsDayReportVo> vo) {
        if (CollectionUtils.isEmpty(vo)) {
            return Collections.emptyList();
        }
        vo.forEach(x -> {
            if (LogisticStatisticsIncomeType.FROZEN_PRODUCT_WIRE.getNumber() == x.getIncomeType()) {
                x.setFrozenGoodsDiscounts(DecimalUtils.multiple(x.getStorageWeight(), BigDecimal.valueOf(30)));
            }
        });
        return vo;
    }

    private LogisticStatisticsDayReportExportVo sumLogisticStatisticsDayReportExport(LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> resultPage) {
        LogisticStatisticsDayReportExportVo exportVo = new LogisticStatisticsDayReportExportVo();
        exportVo.setSerialNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setPieces(resultPage.getPieces());
        exportVo.setLoadingWeight(resultPage.getLoadingWeight());
        exportVo.setStorageWeight(resultPage.getStorageWeight());
        exportVo.setUnStockedWeight(resultPage.getUnStockedWeight());
        exportVo.setVolume(resultPage.getVolume());
        exportVo.setFreightIncomeAmount(resultPage.getFreightIncomeAmount());
        exportVo.setActualFreightAmount(resultPage.getActualFreightAmount());
        exportVo.setFrozenGoodsDiscounts(resultPage.getFrozenGoodsDiscounts());
        exportVo.setUnpaidAmount(resultPage.getUnpaidAmount());
        return exportVo;
    }

    private LogisticStatisticsDayReportExportVo logisticStatisticsDayReportToExportVo(LogisticStatisticsDayReportVo reportVo) {
        LogisticStatisticsDayReportExportVo exportVo = new LogisticStatisticsDayReportExportVo();
        exportVo.setReportDate(reportVo.getReportDate());
        exportVo.setOperationsDepartment(reportVo.getOperationsDepartment());
        exportVo.setShipper(reportVo.getShipper());
        exportVo.setLogisticsNo(reportVo.getLogisticsNo());
        exportVo.setIncomeType(LogisticStatisticsIncomeType.fromStatus(reportVo.getIncomeType()).getName());
        exportVo.setDriverName(reportVo.getDriverName());
        exportVo.setCarNo(reportVo.getCarNo());
        exportVo.setCharteredBus(BooleanUtils.isTrue(reportVo.getCharteredBus()) ? "是" : "否");
        exportVo.setDepartureStation(reportVo.getDepartureStation());
        exportVo.setPieces(reportVo.getPieces());
        exportVo.setDestinationStation(reportVo.getDestinationStation());
        exportVo.setLoadingWeight(reportVo.getLoadingWeight());
        exportVo.setStorageWeight(reportVo.getStorageWeight());
        exportVo.setUnStockedWeight(reportVo.getUnStockedWeight());
        exportVo.setVolume(reportVo.getVolume());
        exportVo.setPaymentTime(reportVo.getPaymentTime() == null ? null : reportVo.getPaymentTime().toLocalDate());
        exportVo.setFreightIncomeAmount(reportVo.getFreightIncomeAmount());
        exportVo.setActualFreightAmount(reportVo.getActualFreightAmount());
        exportVo.setWarehousingNo(reportVo.getWarehousingNo());
        exportVo.setFrozenGoodsDiscounts(reportVo.getFrozenGoodsDiscounts());
        exportVo.setUnpaidAmount(reportVo.getUnpaidAmount());
        exportVo.setCreateTime(reportVo.getCreateTime());
        exportVo.setCreateName(reportVo.getCreateName());
        exportVo.setRemark(reportVo.getRemark());
        return exportVo;
    }
}
