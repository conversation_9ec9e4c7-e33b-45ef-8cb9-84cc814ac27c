package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.WxReplyMessageTemplateSearchVo;
import com.senox.user.vo.WxReplyMessageTemplateVo;
import com.senox.web.service.WechatMpReplyMessageTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-04-21
 **/
@Api(tags = "微信回复消息模板")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/wechat/message/reply/template")
public class WechatMpReplyMessageTemplateController extends BaseController {
    private final WechatMpReplyMessageTemplateService templateService;

    @ApiOperation("添加")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestBody @Validated({Add.class}) WxReplyMessageTemplateVo template) {
        templateService.add(template);
    }

    @ApiOperation("根据id更新")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@RequestBody @Validated({Update.class}) WxReplyMessageTemplateVo template) {
        templateService.update(template);
    }

    @ApiOperation("更新状态")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update/status")
    public void updateStatus(@RequestBody @Validated({Update.class}) WxReplyMessageTemplateVo template) {
        templateService.updateStatus(template);
    }

    @ApiOperation("根据id删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id) {
        templateService.deleteById(id);
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findById/{id}")
    public WxReplyMessageTemplateVo findById(@PathVariable Long id) {
        return templateService.findById(id);
    }


    @ApiOperation("列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<WxReplyMessageTemplateVo> pageList(@RequestBody WxReplyMessageTemplateSearchVo search) {
        return templateService.pageList(search);
    }
}
