package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.constant.Gender;
import com.senox.user.constant.ResidentType;
import com.senox.user.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.ResidentService;
import com.senox.web.vo.ResidentExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 15:32
 */
@Api(tags = "住户管理")
@RestController
@RequestMapping("/web/resident")
public class ResidentController extends BaseController{

    @Autowired
    private ResidentService residentService;

    @ApiOperation("添加住户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public String addResident(@Validated(Add.class) @RequestBody ResidentVo residentVo){
        return residentService.addResident(residentVo);
    }

    @ApiOperation("修改住户，不修改人脸")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateResident(@Validated(Update.class) @RequestBody ResidentVo residentVo){
        residentService.updateResident(residentVo);
    }

    @ApiOperation("只修改人脸")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/face")
    public void updateFaceUrl(@Validated(Update.class) @RequestBody ResidentFaceUrlVo residentFaceUrlVo){
        residentService.updateFaceUrl(residentFaceUrlVo);
    }

    @ApiOperation("删除住户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteResident(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        residentService.deleteResident(id);
    }

    @ApiOperation("获取住户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ResidentVo findResidentById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return residentService.findResidentById(id);
    }

    @ApiOperation("根据住户编号查找住户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByNo")
    public ResidentVo findResidentByResidentNo(@RequestParam String residentNo) {
        return residentService.findResidentByResidentNo(residentNo);
    }

    @ApiOperation("根据身份证查找住户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByIdNum")
    public ResidentVo findResidentByIdNum(@RequestParam String idNum) {
        return residentService.findResidentByIdNum(idNum);
    }

    @ApiOperation("住户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ResidentVo> residentList(@RequestBody ResidentSearchVo search) {
        return residentService.residentList(search);
    }

    @ApiOperation("导出住户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportResident(HttpServletResponse response, ResidentSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        PageResult<ResidentVo> result = residentService.residentList(searchVo);

        List<ResidentExportVo> exportVoList = new ArrayList<>(result.getDataList().size());
        if (!CollectionUtils.isEmpty(result.getDataList())) {
            int serial = 1;
            for (ResidentVo residentVo : result.getDataList()) {
                ResidentExportVo exportVo = residentToExportVo(residentVo);
                exportVo.setSerialNo(serial++);
                exportVoList.add(exportVo);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_RESIDENT, LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_RESIDENT);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ResidentExportVo.class)
                .sheet(sheetName).doWrite(exportVoList);
    }

    private ResidentExportVo residentToExportVo(ResidentVo residentVo) {
        ResidentExportVo exportVo = new ResidentExportVo();
        exportVo.setName(residentVo.getName());
        exportVo.setBornDate(residentVo.getBornDate());
        exportVo.setNature(residentVo.getNature());
        Gender gender = Gender.fromValue(residentVo.getGender());
        exportVo.setGender(gender  == null ? StringUtils.EMPTY : gender.getName());
        exportVo.setAddress(residentVo.getAddress());
        exportVo.setTelephone(residentVo.getTelephone());
        ResidentType residentType = ResidentType.fromNumber(residentVo.getResidentType());
        exportVo.setResidentType(residentType == null ? StringUtils.EMPTY : residentType.getName());
        exportVo.setLastScanTime(residentVo.getLastScanTime());
        return exportVo;
    }

    @ApiOperation("添加住户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/add")
    public void addResidentAccess(@Validated(Add.class) @RequestBody ResidentAccessVo residentAccessVo){
        residentService.addResidentAccess(residentAccessVo);
    }

    @ApiOperation("删除住户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/delete")
    public void deleteResidentAccess(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        residentService.deleteResidentAccess(ids);
    }

    @ApiOperation("根据住户编号查询住户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/byNo")
    public ResidentAccessResultVo residentAccessResultByNo(@RequestParam String residentNo) {
        return residentService.residentAccessResultByNo(residentNo);
    }

    @ApiOperation("门禁住户权限同步")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/access/sync/{deviceId}")
    public void residentAccessSync(@PathVariable Long deviceId, @RequestParam Long targetDeviceId) {
        residentService.residentAccessSync(deviceId, targetDeviceId);
    }
}
