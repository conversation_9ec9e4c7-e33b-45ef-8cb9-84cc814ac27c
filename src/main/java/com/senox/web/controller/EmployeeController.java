package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.*;
import com.senox.web.config.ExcelFillCellMergeStrategy;
import com.senox.web.service.CompanyService;
import com.senox.web.service.EmployeeService;
import com.senox.web.service.HolidayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;

import static com.senox.web.constant.SenoxConst.Export;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:12
 */
@Api(tags = "员工")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/employee")
public class EmployeeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeController.class);


    private final CompanyService companyService;
    private final EmployeeService employeeService;
    private final HolidayService holidayService;

    @ApiOperation("添加员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addEmployee(@Validated({Add.class}) @RequestBody EmployeeVo employee) {
        return employeeService.addEmployee(employee);
    }

    @ApiOperation("更新员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateEmployee(@Validated({Update.class}) @RequestBody EmployeeVo employee) {
        if (employee.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        employeeService.updateEmployee(employee);
    }

    @ApiOperation("删除员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteEmployee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        employeeService.deleteEmployee(id);
    }

    @ApiOperation("根据id获取员工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public EmployeeVo getEmployee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return employeeService.findById(id);
    }

    @ApiOperation("员工列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<EmployeeVo> listEmployeePage(@RequestBody EmployeeSearchVo searchVo) {
        return employeeService.listEmployeePage(searchVo);
    }

    @ApiOperation("报餐列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bookingMeal/list")
    public PageResult<BookingMealVo> listBookingPage(@RequestBody BookingMealSearchVo searchVo) {
        return employeeService.listBookingPage(searchVo);
    }

    @ApiOperation("报餐日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bookingMeal/dayReport")
    public PageResult<BookingMealCompanyDayReportVo> listBookingCompanyDayReportPage(@RequestBody BookingMealDayReportSearchVo searchVo) {
        return employeeService.listBookingCompanyDayReportPage(searchVo);
    }

    @ApiOperation("报餐日报表详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bookingMeal/dayReportDetail")
    public List<BookingMealCompanyDayReportVo> listBookingCompanyDayReportDetail(@RequestParam String mealDate) {
        if (StringUtils.isBlank(mealDate)) {
            throw new InvalidParameterException("无效的参数");
        }
        LocalDate date = null;
        try {
            date = LocalDate.parse(mealDate);
        } catch (Exception e) {
            logger.warn("Invalid mealDate {}", mealDate);
        }
        if (date == null) {
            throw new InvalidParameterException("无效的参数");
        }
        return employeeService.listBookingCompanyDayReportDetail(mealDate);
    }

    @ApiOperation("导出报餐名单")
    @GetMapping("/bookingMeal/exportMonthDetail")
    public void exportBookingMealMonthDetail(HttpServletResponse response, int year, int month) throws IOException {
        // 工作日
        List<LocalDate> weekdays = prepareWeekdays(LocalDate.of(year, month, 1));
        // 公司列表
        List<CompanyVo> companyList = companyService.listCompany();

        // export
        String yearMonth = weekdays.get(0).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String fileName = String.format(Export.FILE_BOOKING_MEAL, yearMonth);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));


        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        for (int index = 0; index < companyList.size(); index++) {
            // export content
            List<List<Object>> contents = buildContent(companyList.get(index), weekdays);
            // export sheet
            WriteSheet sheet = buildSheet(index, companyList.get(index), weekdays, contents.size());
            excelWriter.write(contents, sheet);
        }
        // 关闭流
        excelWriter.finish();
    }

    /**
     * 工作日处理
     * @param monthStartDate
     * @return
     */
    private List<LocalDate> prepareWeekdays(LocalDate monthStartDate) {
        LocalDate monthEndDate = monthStartDate.plusMonths(1L).minusDays(1L);

        // holiday
        HolidaySearchVo holidaySearch = new HolidaySearchVo();
        holidaySearch.setStartDate(monthStartDate);
        holidaySearch.setEndDate(monthEndDate);
        List<HolidayVo> holidays = holidayService.listHoliday(holidaySearch);

        // result
        List<LocalDate> resultList = new ArrayList<>((int)DAYS.between(monthStartDate, monthEndDate));
        for (LocalDate date = monthStartDate; !date.isAfter(monthEndDate); date = date.plusDays(1L)) {
            final LocalDate dateItem = date;
            if (holidays != null && holidays.stream().anyMatch(x -> Objects.equals(x.getHoliday(), dateItem))) {
                continue;
            }
            resultList.add(date);
        }
        return resultList;
    }

    /**
     * 构建表单
     * @param sheetIndex
     * @param company
     * @param weekdays
     * @return
     */
    private WriteSheet buildSheet(int sheetIndex, CompanyVo company, List<LocalDate> weekdays, int contentRows) {
        List<List<String>> headTitles = buildTitle(company, weekdays);
        int lastRowIndex = headTitles.get(0).size() + contentRows;
        return EasyExcel.writerSheet(sheetIndex, company.getCompanyName())
                .head(headTitles)
                .registerWriteHandler(new ExcelFillCellMergeStrategy(lastRowIndex - 1, lastRowIndex - 1, 0, 2))
                .build();

    }

    /**
     * excel 标题
     * @param company
     * @param weekdays
     * @return
     */
    private List<List<String>> buildTitle(CompanyVo company, List<LocalDate> weekdays) {
        List<List<String>> result = new ArrayList<>(weekdays.size() + 4);
        // 标题
        String title = String.format(Export.TITLE_BOOKING_MEAL, weekdays.get(0).getYear(), company.getCompanyName(), weekdays.get(0).getMonthValue());

        // data header
        result.add(Arrays.asList(title, Export.COLUMN_BOOKING_MEAL_SERIAL, Export.COLUMN_BOOKING_MEAL_SERIAL));
        result.add(Arrays.asList(title, Export.COLUMN_BOOKING_MEAL_NAME, Export.COLUMN_BOOKING_MEAL_NAME));
        result.add(Arrays.asList(title, Export.COLUMN_BOOKING_MEAL_DEPT, Export.COLUMN_BOOKING_MEAL_DEPT));
        for (LocalDate date : weekdays) {
            result.add(Arrays.asList(title, String.valueOf(date.getDayOfMonth()), date.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.CHINA)));
        }
        result.add(Arrays.asList(title, Export.COLUMN_SUM, Export.COLUMN_SUM));
        return result;
    }

    /**
     * 导出内容
     * @param company
     * @param weekdays
     * @return
     */
    private List<List<Object>> buildContent(CompanyVo company, List<LocalDate> weekdays) {
        // 员工列表
        EmployeeSearchVo employeeSearch = new EmployeeSearchVo();
        employeeSearch.setCompanyName(company.getCompanyName());
        employeeSearch.setPageNo(1);
        employeeSearch.setPageSize(500);
        List<EmployeeVo> employees = employeeService.listEmployeePage(employeeSearch).getDataList();

        // 报餐信息
        BookingMealSearchVo bookingSearch = new BookingMealSearchVo();
        bookingSearch.setPageNo(1);
        bookingSearch.setPageSize(5000);
        bookingSearch.setCompany(company.getCompanyName());
        bookingSearch.setBookingDateStart(weekdays.get(0));
        bookingSearch.setBookingDateEnd(weekdays.get(weekdays.size() - 1));
        List<BookingMealVo> bookings = employeeService.listBookingPage(bookingSearch).getDataList();

        // 表格套娃
        List<List<Object>> resultList = new ArrayList<>(employees.size() + 1);
        for (int i = 0; i < employees.size(); i++) {
            // user data
            EmployeeVo employee = employees.get(i);
            List<Object> content = new ArrayList<>(weekdays.size() + 4);
            content.add(i + 1);
            content.add(employee.getUsername());
            content.add(employee.getCompanyName());

            for (LocalDate date : weekdays) {
                final LocalDate dateItem = date;
                Optional<BookingMealVo> bookingOp = bookings.stream()
                        .filter(x -> Objects.equals(x.getEmployee(), employee.getUsername()) && Objects.equals(x.getMealDate(), dateItem))
                        .findFirst();
                if (bookingOp.isPresent() && WrapperClassUtils.biggerThanInt(bookingOp.get().getMealBook(), 0)) {
                    content.add(bookingOp.get().getMealBook());
                } else {
                    content.add(StringUtils.EMPTY);
                }
            }
            content.add(bookings.stream().filter(x -> Objects.equals(x.getEmployee(), employee.getUsername())).map(BookingMealVo::getMealBook).reduce(0, Integer::sum));

            resultList.add(content);
        }

        // 合计栏
        List<Object> summaryRow = new ArrayList<>(weekdays.size() + 4);
        summaryRow.add(Export.COLUMN_SUM);
        summaryRow.add(Export.COLUMN_SUM);
        summaryRow.add(Export.COLUMN_SUM);
        for (LocalDate date : weekdays) {
            final LocalDate dateItem = date;
            summaryRow.add(bookings.stream().filter(x -> Objects.equals(x.getMealDate(), dateItem)).map(BookingMealVo::getMealBook).reduce(0, Integer::sum));
        }
        summaryRow.add(bookings.stream().map(BookingMealVo::getMealBook).reduce(0, Integer::sum));

        resultList.add(summaryRow);
        return resultList;
    }

}
