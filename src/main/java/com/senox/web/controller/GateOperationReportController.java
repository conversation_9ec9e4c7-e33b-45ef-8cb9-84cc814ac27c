package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.GateOperationDayReportVo;
import com.senox.dm.vo.GateOperationReportGenerateVo;
import com.senox.dm.vo.GateOperationReportSearchVo;
import com.senox.dm.vo.GateOperationReportSumVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.GateOperationConvertor;
import com.senox.web.service.GateOperationReportService;
import com.senox.web.vo.GateOperationDayReportExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:07
 */
@Api(tags = "海康门禁设备操作日志")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/control/operation/report")
public class GateOperationReportController extends BaseController {

    private final GateOperationReportService reportService;
    private final GateOperationConvertor operationConvertor;


    @ApiOperation("重新生成日报")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/generate")
    public void generateDayReport(@RequestBody GateOperationReportGenerateVo generate) {
        if (StringUtils.isBlank(generate.getDeviceIp()) || generate.getOperateDate() == null) {
            throw new InvalidParameterException();
        }

        reportService.generateDayReport(generate);
    }

    @ApiOperation("日报列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/page")
    public PageStatisticsResult<GateOperationDayReportVo, GateOperationReportSumVo> listDayReportPage(@RequestBody GateOperationReportSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>(PageResult.emptyPage(), GateOperationReportSumVo.empty());
        }

        // page
        PageResult<GateOperationDayReportVo> page = reportService.listDayReportPage(search);
        // sum
        GateOperationReportSumVo sum = reportService.sumDayReport(search);
        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出日报列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/day/export")
    public void exportDayReport(HttpServletResponse response, GateOperationReportSearchVo search) throws IOException {
        // list
        List<GateOperationDayReportVo> reportList = reportService.listDayReport(search);

        int index = 1;
        List<GateOperationDayReportExportVo> exportList = new ArrayList<>(reportList.size());
        for (GateOperationDayReportVo item : reportList) {
            GateOperationDayReportExportVo exportItem = operationConvertor.toReportExportVo(item);
            exportItem.setSerial(index++);
            exportList.add(exportItem);
        }

        // sum
        GateOperationReportSumVo sum = reportService.sumDayReport(search);
        GateOperationDayReportExportVo exportSum = new GateOperationDayReportExportVo();
        exportSum.setDeviceName(SenoxConst.Export.COLUMN_SUM);
        exportSum.setOpenCount(sum.getOpenCount());
        exportSum.setAlarmCount(sum.getAlarmCount());
        exportSum.setFalseAlarmCount(sum.getFalseAlarmCount());
        exportList.add(exportSum);

        // export
        String fileName = String.format(SenoxConst.Export.FILE_GATE_OPERATION_REPORT_DAY, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), GateOperationDayReportExportVo.class)
                .sheet(SenoxConst.Export.SHEET_GATE_OPERATION_REPORT_DAY)
                .doWrite(exportList);
    }
}
