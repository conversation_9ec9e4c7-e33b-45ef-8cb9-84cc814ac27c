package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.*;
import com.senox.realty.constant.MaintainType;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.AdvertisingTollConvertor;
import com.senox.web.convert.DepositTollConvertor;
import com.senox.web.convert.OrderTollExportConvertor;
import com.senox.web.convert.RefrigerationTollConvertor;
import com.senox.web.service.OrderService;
import com.senox.web.vo.*;
import com.senox.web.vo.RefundOrderRequestVo;
import com.senox.web.vo.AdvertisingTollExcelVo;
import com.senox.web.vo.BillPage;
import com.senox.web.vo.DepositTollExportVo;
import com.senox.web.vo.MaintainChargePage;
import com.senox.web.vo.MaintainChargeTollExcelVo;
import com.senox.web.vo.OrderExportVo;
import com.senox.web.vo.OrderTollExportVo;
import com.senox.web.vo.RefrigerationBillPage;
import com.senox.web.vo.RefrigerationTollExcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/18 11:25
 */
@Api(tags = "收费订单列表")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/order")
public class OrderController extends BaseController {

    private final OrderService orderService;
    private final OrderTollExportConvertor tollExportConvertor;
    private final RefrigerationTollConvertor refrigerationTollConvertor;
    private final AdvertisingTollConvertor advertisingTollConvertor;
    private final DepositTollConvertor depositTollConvertor;

    @ApiOperation("账单状态查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/status/query")
    public OrderResultVo queryOrderStatus(@RequestParam String tradeNo, @RequestParam(required = false, defaultValue = "false") Boolean realTime) {
        if (StringUtils.isBlank(tradeNo)) {
            throw new InvalidParameterException();
        }
        return orderService.queryOrderStatus(tradeNo, BooleanUtils.isTrue(realTime));
    }

    @ApiOperation("订单退费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund")
    public OrderResultVo refundOrder(HttpServletRequest request, @RequestBody RefundOrderRequestVo refundOrder) {
        refundOrder.setRequestIp(RequestUtils.getIpAddr(request));
        return orderService.refundOrder(refundOrder);
    }

    @ApiOperation("账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/{id}")
    public OrderVo findWithDetail(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return orderService.findOrderWithDetail(id);
    }

    @ApiOperation("获取订单项目明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/product")
    public OrderProductDetailVo getOrderProduct(@RequestParam("orderId") Long orderId,
                                                @RequestParam("productId") Long productId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)
                || !WrapperClassUtils.biggerThanLong(productId, 0L)) {
            throw new InvalidParameterException();
        }
        return orderService.getOrderProduct(orderId, productId);
    }

    @ApiOperation("账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public OrderPageResult<OrderListVo> listOrderPage(@Validated @RequestBody OrderSearchVo search) {
        return orderService.listOrderPage(search);
    }

    @ApiOperation("结算员收款明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/tollList")
    public OrderPageResult<OrderTollVo> listOrderTollPage(@Validated @RequestBody OrderTollSearchVo search) {
        return orderService.listOrderTollPage(search);
    }

    @ApiOperation("收款明细费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/tollDetailList")
    public PageResult<OrderTollDetailVo> listOrderTollDetail(@Validated @RequestBody OrderTollSearchVo search) {
        return orderService.listOrderTollDetail(search);
    }

    @ApiOperation("导出收费订单列表")
    @GetMapping("/export")
    public void exportOrder(HttpServletResponse response, OrderSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        OrderPageResult<OrderListVo> pageResult = orderService.listOrderPage(search);
        List<OrderExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (OrderListVo item : pageResult.getDataList()) {
                OrderExportVo exportItem = orderListVo2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumOrder(pageResult.getTotal()));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_ORDER, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), OrderExportVo.class)
                .sheet(SenoxConst.Export.SHEET_ORDER)
                .doWrite(exportList);
    }

    @ApiOperation("导出结算员收款明细列表")
    @GetMapping("/exportToll")
    public void exportOrderToll(HttpServletResponse response, OrderTollSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        OrderPageResult<OrderTollVo> pageResult = orderService.listOrderTollPage(search);
        List<OrderTollExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (OrderTollVo item : pageResult.getDataList()) {
                OrderTollExportVo exportItem = tollExportConvertor.toV(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumOrderToll(pageResult));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_ORDER_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), OrderTollExportVo.class)
                .sheet(SenoxConst.Export.SHEET_ORDER_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("冷藏结算收费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refrigeration/tollList")
    public RefrigerationBillPage<RefrigerationTollVo> listRefrigerationToll(@RequestBody RefrigerationTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefrigerationBillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<RefrigerationTollVo> page = orderService.listRefrigerationToll(search);
        RefrigerationBillPage<RefrigerationTollVo> resultPage = new RefrigerationBillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        RefrigerationTollVo sumToll = orderService.sumRefrigerationToll(search);
        if (sumToll != null) {
            resultPage.setStorageCharge(DecimalUtils.nullToZero(sumToll.getStorageCharge()));
            resultPage.setDisposalCharge(DecimalUtils.nullToZero(sumToll.getDisposalCharge()));
            resultPage.setHandlingCharge(DecimalUtils.nullToZero(sumToll.getHandlingCharge()));
            resultPage.setPassingCharge(DecimalUtils.nullToZero(sumToll.getPassingCharge()));
            resultPage.setMembraneCharge(DecimalUtils.nullToZero(sumToll.getMembraneCharge()));
            resultPage.setSortingCharge(DecimalUtils.nullToZero(sumToll.getSortingCharge()));
            resultPage.setOvertimeCharge(DecimalUtils.nullToZero(sumToll.getOvertimeCharge()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumToll.getOtherCharge()));
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumToll.getDeliveryCharge()));
            resultPage.setPenaltyCharge(DecimalUtils.nullToZero(sumToll.getPenaltyChargeToPaid()));
            resultPage.setPenaltyPaid(DecimalUtils.nullToZero(sumToll.getPenaltyCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumToll.getTotalCharge()));
        }
        return resultPage;
    }

    @ApiOperation("导出冷藏结算收费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/refrigeration/exportToll")
    public void exportRefrigerationToll(HttpServletResponse response, RefrigerationTollSearchVo search) throws IOException {
        // list
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<RefrigerationTollVo> page = orderService.listRefrigerationToll(search);
        List<RefrigerationTollExcelVo> exportList = Collections.emptyList();

        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int index = 1;
            exportList = new ArrayList<>(page.getTotalSize() + 1);
            for (RefrigerationTollVo item : page.getDataList()) {
                RefrigerationTollExcelVo exportItem = refrigerationTollConvertor.toll2ExcelVo(item);
                exportItem.setSerialNo(index++);
                exportList.add(exportItem);
            }

            // sum
            RefrigerationTollVo sumVo = orderService.sumRefrigerationToll(search);
            RefrigerationTollExcelVo sumItem = refrigerationTollConvertor.toll2ExcelVo(sumVo);
            sumItem.setCustomerSerial(SenoxConst.Export.COLUMN_SUM);
            exportList.add(sumItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REFRIGERATION_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RefrigerationTollExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_REFRIGERATION_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("物维结算收费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/maintain/charge/tollList")
    public MaintainChargePage<MaintainChargeTollVo> listMaintainChargeToll(@RequestBody MaintainChargeTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<MaintainChargeTollVo> page = orderService.listMaintainChargeToll(search);
        MaintainChargePage<MaintainChargeTollVo> resultPage = new MaintainChargePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        MaintainChargeTollVo sumToll = orderService.sumMaintainChargeToll(search);
        if (sumToll != null) {
            resultPage.setLaborTotalAmount(DecimalUtils.nullToZero(sumToll.getLaborAmount()));
            resultPage.setMaterialTotalAmount(DecimalUtils.nullToZero(sumToll.getMaterialAmount()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumToll.getTotalAmount()));
        }
        return resultPage;
    }

    @ApiOperation("导出物维结算收费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/maintain/charge/exportToll")
    public void exportMaintainChargeToll(HttpServletResponse response, MaintainChargeTollSearchVo search) throws IOException {
        // list
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<MaintainChargeTollVo> page = orderService.listMaintainChargeToll(search);
        List<MaintainChargeTollExcelVo> exportList = Collections.emptyList();

        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int index = 1;
            exportList = new ArrayList<>(page.getTotalSize() + 1);
            for (MaintainChargeTollVo item : page.getDataList()) {
                MaintainChargeTollExcelVo exportItem = maintainCharge2ExportVo(item);
                exportItem.setSerialNo(index++);
                exportList.add(exportItem);
            }

            // sum
            MaintainChargeTollVo sumVo = orderService.sumMaintainChargeToll(search);
            MaintainChargeTollExcelVo sumItem = sumMaintainChargeExport(sumVo);
            exportList.add(sumItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_MAINTAIN_CHARGE_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MaintainChargeTollExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_MAINTAIN_CHARGE_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("押金交易流水列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/deposit/tollList")
    public PageStatisticsResult<DepositTollVo, DepositTollVo> listDepositTollPage(@RequestBody DepositTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<DepositTollVo> page = orderService.listDepositTollPage(search);
        DepositTollVo sum = orderService.sumDepositToll(search);
        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出押金交易流水")
    @GetMapping("/deposit/exportToll")
    public void exportDepositToll(HttpServletResponse response, DepositTollSearchVo search) throws IOException {
        // list
        List<DepositTollVo> tollList = orderService.listDepositToll(search);

        int index = 1;
        List<DepositTollExportVo> exportList = new ArrayList<>(tollList.size());
        for (DepositTollVo item : tollList) {
            DepositTollExportVo exportItem = depositTollConvertor.toll2ExcelVo(item);
            exportItem.setSerialNo(index++);
            exportList.add(exportItem);
        }

        // sum
        DepositTollVo sum = orderService.sumDepositToll(search);
        DepositTollExportVo exportSum = depositTollConvertor.toll2ExcelVo(sum);
        exportSum.setRealtySerial(SenoxConst.Export.COLUMN_SUM);

        // export
        String fileName = String.format(SenoxConst.Export.FILE_DEPOSIT_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), DepositTollExportVo.class)
                .sheet(SenoxConst.Export.SHEET_DEPOSIT_TOLL)
                .doWrite(exportList);
    }

    @ApiOperation("广告结算收费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advertising/tollList")
    public BillPage<AdvertisingTollVo> listAdvertisingToll(@RequestBody AdvertisingTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return BillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<AdvertisingTollVo> page = orderService.listAdvertisingToll(search);
        BillPage<AdvertisingTollVo> resultPage = new BillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        AdvertisingTollVo sumToll = orderService.sumAdvertisingToll(search);
        if (sumToll != null) {
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumToll.getAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumToll.getPaidAmount()));
        }
        return resultPage;
    }

    @ApiOperation("导出广告结算收费明细")
    @GetMapping("/advertising/exportToll")
    public void exportAdvertisingToll(HttpServletResponse response, AdvertisingTollSearchVo search) throws IOException {
        // list
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<AdvertisingTollVo> page = orderService.listAdvertisingToll(search);
        List<AdvertisingTollExcelVo> exportList = Collections.emptyList();

        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int index = 1;
            exportList = new ArrayList<>(page.getTotalSize() + 1);
            for (AdvertisingTollVo item : page.getDataList()) {
                AdvertisingTollExcelVo exportItem = advertisingTollConvertor.toll2ExcelVo(item);
                exportItem.setSerialNo(index++);
                exportList.add(exportItem);
            }

            // sum
            AdvertisingTollVo sumVo = orderService.sumAdvertisingToll(search);
            AdvertisingTollExcelVo sumItem = advertisingTollConvertor.toll2ExcelVo(sumVo);
            sumItem.setTollSerial(SenoxConst.Export.COLUMN_SUM);
            exportList.add(sumItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_ADVERTISING_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), AdvertisingTollExcelVo.class)
                .sheet(SenoxConst.Export.SHEET_ADVERTISING_TOLL)
                .doWrite(exportList);
    }

    /**
     * 物维合计
     * @param sumVo
     * @return
     */
    private MaintainChargeTollExcelVo sumMaintainChargeExport(MaintainChargeTollVo sumVo) {
        MaintainChargeTollExcelVo result = new MaintainChargeTollExcelVo();
        result.setTollSerial(SenoxConst.Export.COLUMN_SUM);
        result.setLaborAmount(sumVo.getLaborAmount());
        result.setMaterialAmount(sumVo.getMaterialAmount());
        result.setTotalAmount(sumVo.getTotalAmount());
        return result;
    }

    /**
     * 物维视图转导出视图
     * @param item
     * @return
     */
    private MaintainChargeTollExcelVo maintainCharge2ExportVo(MaintainChargeTollVo item) {
        MaintainChargeTollExcelVo exportVo = new MaintainChargeTollExcelVo();
        exportVo.setPaidTime(item.getPaidTime());
        exportVo.setTollSerial(item.getTollSerial());
        exportVo.setTollMan(item.getTollMan());
        exportVo.setChargeYear(item.getChargeYear());
        exportVo.setChargeMonth(item.getChargeMonth());
        exportVo.setCustomerName(item.getCustomerName());
        exportVo.setAddress(item.getAddress());
        exportVo.setLaborAmount(item.getLaborAmount());
        exportVo.setMaterialAmount(item.getMaterialAmount());
        exportVo.setTotalAmount(item.getTotalAmount());

        MaintainType maintainType = MaintainType.fromValue(item.getMaintainType());
        exportVo.setMaintainType(maintainType == null ? StringUtils.EMPTY : maintainType.getDescription());
        PayWay way = PayWay.fromValue(item.getPayWay());
        exportVo.setPayWay(way == null ? StringUtils.EMPTY : way.getDescription());
        return exportVo;
    }

    /**
     * 订单视图转导出视图
     * @param order
     * @return
     */
    private OrderExportVo orderListVo2ExportVo(OrderListVo order) {
        OrderExportVo result = new OrderExportVo();
        result.setTradeNo(order.getTradeNo());
        result.setTitle(order.getTitle());
        result.setRealtySerial(order.getProductSerial());
        result.setTotalAmount(order.getTotalAmount());
        result.setBillStatus(OrderStatus.fromStatus(order.getStatus()).getDescription());
        result.setPayWay(PayWay.fromValue(order.getPayWay()).getDescription());
        result.setOrderTime(order.getRemoteOrderTime());
        return result;
    }

    /**
     * 订单合计
     * @param total
     * @return
     */
    private OrderExportVo sumOrder(BigDecimal total) {
        OrderExportVo result = new OrderExportVo();
        result.setTradeNo("合计");
        result.setTotalAmount(total == null ? BigDecimal.ZERO : total);
        return result;
    }

    /**
     * 收款明细合计
     * @param page
     * @return
     */
    private OrderTollExportVo sumOrderToll(OrderPageResult<OrderTollVo> page) {
        OrderTollExportVo result = new OrderTollExportVo();
        result.setTollMan(SenoxConst.Export.COLUMN_SUM);
        result.setManageAmount(page.getManageAmount());
        result.setRentAmount(page.getRentAmount());
        result.setWaterAmount(page.getWaterAmount());
        result.setElectricAmount(page.getElectricAmount());
        result.setPenaltyAmount(page.getPenaltyAmount());
        result.setTotalAmount(page.getTotal());
        return result;
    }
}
