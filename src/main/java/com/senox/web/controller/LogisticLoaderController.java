package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.utils.DateUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.LogisticLoaderService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.LogisticLoaderIncomeExportVo;
import com.senox.web.vo.LogisticLoaderSettlementExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "物流搬运工")
@RestController
@RequestMapping("/web/logistic/loader")
@RequiredArgsConstructor
public class LogisticLoaderController extends BaseController{
    private final LogisticLoaderService loaderService;

    @ApiOperation("添加搬运工结算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/settlement/add")
    public void addLoaderSettlement(@Validated({Add.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo) {
        loaderService.addLoaderSettlement(settlementFormVo);
    }

    @ApiOperation("更新搬运工结算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/settlement/update")
    public void updateLoaderSettlement(@Validated({Update.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo) {
        loaderService.updateLoaderSettlement(settlementFormVo);
    }

    @ApiOperation("根据id获取结算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/settlement/findById/{loaderSettlementId}")
    public LogisticLoaderSettlementVo findLoaderSettlementById(@PathVariable Long loaderSettlementId) {
        return loaderService.findLoaderSettlementById(loaderSettlementId);
    }

    @ApiOperation("删除搬运工结算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/settlement/delete/{id}")
    public void deleteLoaderSettlementById(@PathVariable Long id) {
        loaderService.deleteLoaderSettlementById(id);
    }

    @ApiOperation("搬运工结算列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/settlement/list")
    public BicycleTotalPageResult<LogisticLoaderSettlementVo> listLoaderSettlement(@RequestBody LogisticLoaderSettlementSearchVo searchVo) {
        return loaderService.listPageLoaderSettlement(searchVo);
    }

    @ApiOperation("生成搬运工收益日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/income/report/day/generate")
    public void generateLoaderIncomeDayReport(@RequestBody BicycleDateVo dateVo) {
        loaderService.generateLoaderIncomeDayReport(dateVo);
    }

    @ApiOperation("搬运工收益报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/income/statistics/list")
    public BicycleTotalPageResult<LogisticLoaderIncomeVo> listLoaderIncomeStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo) {
        return loaderService.listLoaderIncomeStatistics(searchVo);
    }

    @ApiOperation("搬运工收益统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/income/report/statistics")
    public List<LogisticLoaderIncomeReportVo> listLoaderIncomeReportStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo) {
        return loaderService.listLoaderIncomeReportStatistics(searchVo);
    }

    @ApiOperation("导出搬运工结算")
    @GetMapping("/settlement/export")
    public void exportLoaderSettlement(HttpServletResponse response,LogisticLoaderSettlementSearchVo searchVo) throws IOException {
        List<LogisticLoaderSettlementVo> loaderSettlements = loaderService.listLoaderSettlement(searchVo);
        List<LogisticLoaderSettlementExportVo> loaderSettlementExports = settlementToExportToExport(loaderSettlements);
        String fileName = SenoxConst.Export.TMS_LOGISTIC_LOADER_SETTLEMENT_INFO;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LogisticLoaderSettlementExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.TMS_LOGISTIC_LOADER_SETTLEMENT_SHEET)
                .doWrite(loaderSettlementExports);

    }

    @ApiOperation("导出搬运工收益报表")
    @GetMapping("/income/export")
    public void loaderIncomeExport(HttpServletResponse response, LogisticLoaderIncomeSearchVo searchVo) throws IOException {
        List<LogisticLoaderIncomeReportVo> list = loaderService.listLoaderIncomeReportStatistics(searchVo);
        List<LogisticLoaderIncomeExportVo> exports = toExport(list);
        prepareExcelResponse(response, URLEncoder.encode(SenoxConst.Export.TMS_LOGISTIC_LOADER_INCOME_INFO, StandardCharsets.UTF_8.name()));
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .build();
        List<List<String>> heads = new ArrayList<>();
        List<LogisticLoaderIncomeExportVo> loaderIncomes = exports.stream()
                .collect(Collectors.toMap(e -> Arrays.asList(e.getLoaderNumber(), e.getLoaderNumber()), Function.identity(), (e1, e2) -> e1))
                .values().stream().sorted(Comparator.comparing(LogisticLoaderIncomeExportVo::getLoaderNumber)).collect(Collectors.toList());
        buildHead(heads, loaderIncomes);
        List<List<Object>> tableData = new ArrayList<>();
        List<BigDecimal> rowTotalAmounts = new ArrayList<>();
        for (LogisticLoaderIncomeReportVo incomeReport : list) {
            List<Object> rowData = new ArrayList<>();
            rowData.add(DateUtils.formatYearMonth(incomeReport.getDate(), DateUtils.PATTERN_FULL_DATE));
            Map<String,LogisticLoaderIncomeVo> sortItemMap = incomeReport.getItems().stream()
                    .collect(Collectors.toMap(LogisticLoaderIncomeVo::getLoaderNumber,Function.identity()));
            for (LogisticLoaderIncomeExportVo incomeExport : loaderIncomes) {
                LogisticLoaderIncomeVo income = sortItemMap.get(incomeExport.getLoaderNumber());
                if (null != income && incomeExport.getLoaderNumber().equals(income.getLoaderNumber())) {
                    rowData.add(income.getAmount());
                    continue;
                }
                rowData.add(BigDecimal.ZERO);
            }
            //行合计
            BigDecimal rowTotalAmount = rowData.stream()
                    .filter(BigDecimal.class::isInstance)
                    .map(r -> new BigDecimal(String.valueOf(r)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            rowData.add(rowTotalAmount);
            rowTotalAmounts.add(rowTotalAmount);
            tableData.add(rowData);
        }
        //列合计
        List<Object> columnsData = new ArrayList<>();
        columnsData.add("合计");
        LinkedHashMap<String, List<LogisticLoaderIncomeExportVo>> loaderNumberMap = exports.stream()
                .sorted(Comparator.comparing(LogisticLoaderIncomeExportVo::getLoaderNumber))
                .collect(Collectors.groupingBy(LogisticLoaderIncomeExportVo::getLoaderNumber, LinkedHashMap::new, Collectors.toList()));
        loaderNumberMap.forEach((number, incomeExports) -> columnsData.add(incomeExports.stream()
                .map(LogisticLoaderIncomeExportVo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)));
        columnsData.add(rowTotalAmounts.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        tableData.add(columnsData);
        excelWriter.write(tableData, EasyExcel.writerSheet(0, SenoxConst.Export.TMS_LOGISTIC_LOADER_INCOME_SHEET).head(heads).build());
        excelWriter.finish();
    }

    /**
     * 结算转报表
     *
     * @param settlements 结算列表
     * @return 返回报表
     */
    private List<LogisticLoaderSettlementExportVo> settlementToExportToExport(List<LogisticLoaderSettlementVo> settlements) {
        if (CollectionUtils.isEmpty(settlements)) {
            return Collections.emptyList();
        }
        return settlements.stream().map(s -> {
            LogisticLoaderSettlementExportVo se = new LogisticLoaderSettlementExportVo();
            se.setDate(DateUtils.formatYearMonth(s.getDate(), DateUtils.PATTERN_FULL_DATE));
            se.setFreightType(s.getFreightType().getName());
            se.setCustomerName(s.getCustomerName());
            se.setGoodsType(s.getGoodsType().getName());
            se.setGoodsUnitPrice(s.getGoodsUnitPrice());
            se.setLoaders(s.getLoaders().stream().map(l -> l.getKey().concat(l.getName())).collect(Collectors.joining(",")));
            se.setTransportTotal(s.getTransportTotal());
            se.setParticipationNumber(s.getParticipationNumber());
            se.setTransportAvg(s.getTransportAvg());
            se.setFreightUnitPrice(s.getFreightUnitPrice());
            se.setFreightTotalAmount(s.getFreightTotalAmount());
            se.setPieces(s.getPieces());
            se.setSortingFee(s.getSortingFee());
            se.setCarNo(s.getCarNo());
            se.setWorkingHours(s.getWorkingHours());
            se.setMealAllowanceAmount(s.getMealAllowanceAmount());
            se.setOtherCharge(s.getOtherCharge());
            se.setSubtotalAmount(s.getSubtotalAmount());
            se.setTotalAmount(s.getTotalAmount());
            se.setRemark(s.getRemark());
            se.setCreateTime(s.getCreateTime());
            se.setCreatorName(s.getCreatorName());
            return se;
        }).collect(Collectors.toList());
    }

    private void buildHead(List<List<String>> heads, List<LogisticLoaderIncomeExportVo> loaderIncomes) {
        final String title = "搬运工工资表";
        List<String> heads1 = new ArrayList<>();
        heads1.add(title);
        heads1.add(title);
        heads1.add("日期");
        heads1.add("日期");
        heads.add(heads1);
        loaderIncomes.forEach(l -> {
            List<String> list = new ArrayList<>();
            list.add(title);
            list.add(title);
            list.add("工号/姓名");
            list.add(l.getLoaderNumber());
            list.add(l.getLoaderName());
            heads.add(list);
        });
        List<String> heads2 = new ArrayList<>();
        heads2.add(title);
        heads2.add(title);
        heads2.add("合计");
        heads2.add("合计");
        heads.add(heads2);
    }

    private List<LogisticLoaderIncomeExportVo> toExport(List<LogisticLoaderIncomeReportVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LogisticLoaderIncomeExportVo> exportList = new ArrayList<>();
        list.forEach(income -> {
            for (LogisticLoaderIncomeVo item : income.getItems()) {
                LogisticLoaderIncomeExportVo exportItem = new LogisticLoaderIncomeExportVo();
                exportItem.setDate(item.getDate());
                exportItem.setLoaderNumber(item.getLoaderNumber());
                exportItem.setLoaderName(item.getLoaderName());
                exportItem.setAmount(item.getAmount());
                exportList.add(exportItem);
            }
        });
        return exportList;
    }


}
