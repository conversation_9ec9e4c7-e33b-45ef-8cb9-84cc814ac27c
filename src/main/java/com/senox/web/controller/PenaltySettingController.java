package com.senox.web.controller;

import com.senox.common.domain.PenaltySetting;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.service.PenaltySettingService;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PenaltySettingSearch;
import com.senox.context.AdminContext;
import com.senox.web.convert.PenaltySettingConvertor;
import com.senox.web.vo.PenaltySettingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2022/2/8 14:23
 */
@Api(tags = "滞纳金配置")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/penaltySetting")
public class PenaltySettingController extends BaseController {

    private final PenaltySettingService settingService;
    private final PenaltySettingConvertor settingConvertor;

    @ApiOperation("保存滞纳金配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addSetting(@Validated @RequestBody PenaltySettingVo setting) {
        PenaltySetting item = settingConvertor.toDo(setting);

        item.setCreatorId(getAdminUserId());
        item.setCreatorName(getAdminUser().getUsername());
        item.setCreateTime(LocalDateTime.now());
        item.setModifierId(item.getCreatorId());
        item.setModifierName(item.getCreatorName());
        item.setModifiedTime(item.getCreateTime());
        settingService.addSetting(item);
    }

    @ApiOperation("更新滞纳金配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateSetting(@Validated @RequestBody PenaltySettingVo setting) {
        if (!WrapperClassUtils.biggerThanLong(setting.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        PenaltySetting item = settingConvertor.toDo(setting);
        item.setModifierId(getAdminUserId());
        item.setModifierName(getAdminUser().getUsername());
        item.setModifiedTime(LocalDateTime.now());
        settingService.updateSetting(item);
    }

    @ApiOperation("获取滞纳金配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public PenaltySettingVo findSettingById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        PenaltySetting setting = settingService.getById(id);
        return setting == null ? null : settingConvertor.toV(setting);
    }


    @ApiOperation("删除滞纳金配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteSetting(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        settingService.removeById(id);
    }


    @ApiOperation("滞纳金配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<PenaltySettingVo> listSetting(@RequestBody PenaltySettingSearch search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        PageResult<PenaltySetting> page = settingService.listSetting(search);
        return PageResult.convertPage(page, settingConvertor::toV);
    }
}
