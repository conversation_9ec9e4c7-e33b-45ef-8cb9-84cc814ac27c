package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.RealtyBillWithholdService;
import com.senox.web.vo.RealtyBillWithholdApplyExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17 16:49
 */
@Api(tags = "银行托收")
@RestController
@RequestMapping("/web/realtyBill/withhold")
public class RealtyBillWithholdController extends BaseController {

    @Autowired
    private RealtyBillWithholdService withholdService;

    @ApiOperation("银行托收报盘")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply")
    public void applyBillWithhold(@Validated @RequestBody BankWithholdVo withhold) {
        withholdService.applyBillWithhold(withhold);
    }

    @ApiOperation("取消银行托收报盘")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel")
    public void cancelBillWithhold(@Validated @RequestBody BankWithholdVo withhold) {
        withholdService.cancelBillWithhold(withhold);
    }

    @ApiOperation("银行托收回盘")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/back")
    public void backBillWithhold(@Validated @RequestBody BankWithholdVo withhold) {
        withholdService.backBillWithhold(withhold);
    }

    @ApiOperation("银行托收支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public void payBillWithhold(HttpServletRequest request, @Validated @RequestBody WithholdBackVo withholdBack) {
        withholdBack.setRequestIp(RequestUtils.getIpAddr(request));
        withholdBack.setTollMan(getAdminUserId());
        withholdService.payBillWithhold(withholdBack);
    }

    @ApiOperation("获取银行托收报盘记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get")
    public BankWithholdVo getBankWithhold(@RequestParam Integer year, @RequestParam Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0)
                || !WrapperClassUtils.biggerThanInt(month, 0)) {
            throw new InvalidParameterException("无效的年月");
        }
        return withholdService.getBankWithHold(year, month);
    }

    @ApiOperation("银行托收报盘列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/apply/page")
    public WithholdPage<BankOfferRealtyBillVo> listPage(@RequestBody RealtyBillSearchVo search) {
        if (!WrapperClassUtils.biggerThanInt(search.getBillYear(), 0)
                || !WrapperClassUtils.biggerThanInt(search.getBillMonth(), 0)) {
            throw new InvalidParameterException("无效的年月");
        }
        return withholdService.listApplyPage(search);
    }

    @ApiOperation("银行托收报盘列表下载")
    @GetMapping("/apply/export")
    public void export(HttpServletResponse response, RealtyBillSearchVo search) throws IOException {
        if (!WrapperClassUtils.biggerThanInt(search.getBillYear(), 0)
                || !WrapperClassUtils.biggerThanInt(search.getBillMonth(), 0)) {
            throw new InvalidParameterException("无效的年月");
        }
        List<BankOfferRealtyBillVo> list = withholdService.listApply(search);
        List<RealtyBillWithholdApplyExportVo> exportList = new ArrayList<>(list.size());
        if (!CollectionUtils.isEmpty(list)) {
            int serial = 1;
            String applyDate = DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE);
            for (BankOfferRealtyBillVo item : list) {
                RealtyBillWithholdApplyExportVo exportItem = bankOfferRealtyBill2applyExportVo(item);
                exportItem.setSerialNo(serial++);
                exportItem.setApplyDate(applyDate);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_BILL_WITHHOLD, search.getBillYear(), search.getBillMonth(), LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_REALTY_BILL_WITHHOLD, search.getBillYear(), search.getBillMonth());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcel.write(response.getOutputStream(), RealtyBillWithholdApplyExportVo.class)
                .needHead(false).sheet(sheetName).doWrite(exportList);
    }

    private RealtyBillWithholdApplyExportVo bankOfferRealtyBill2applyExportVo(BankOfferRealtyBillVo bill) {
        RealtyBillWithholdApplyExportVo result = new RealtyBillWithholdApplyExportVo();
        result.setRealtySerial(bill.getRealtySerial());
        result.setAccountNo(bill.getBankAccountNo());
        result.setAccountName(bill.getBankAccountName());
        result.setAmount(bill.getAmount());
        result.setStatus(BillStatus.INIT.getStatus());
        return result;
    }
}
