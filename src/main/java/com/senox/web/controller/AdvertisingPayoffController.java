package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.realty.vo.AdvertisingPayoffDetailVo;
import com.senox.realty.vo.AdvertisingPayoffSearchVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import com.senox.web.service.AdvertisingPayoffService;
import com.senox.web.vo.BillPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/4 17:30
 */
@Api(tags = "广告应付账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/advertising/payoff")
public class AdvertisingPayoffController extends BaseController {

    private final AdvertisingPayoffService payoffService;

    @ApiOperation("生成广告应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generatePayoff(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        payoffService.generatePayoff(contractNo);
    }

    @ApiOperation("更新广告应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updatePayoff(@RequestBody AdvertisingPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.updatePayoff(payoff);
    }

    @ApiOperation("删除广告应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deletePayoff(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.deletePayoff(id);
    }

    @ApiOperation("支付广告应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public void payPayoff(@RequestBody List<Long> ids, @RequestParam Integer payway) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        PayWay pw = PayWay.fromValue(payway);
        if (pw != PayWay.CASH && pw != PayWay.TRANSFER) {
            throw new InvalidParameterException("应付账单仅支持现金及转账");
        }

        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(ids);
        billPaid.setPaid(Boolean.TRUE);
        billPaid.setTollMan(getAdminUserId());
        billPaid.setPaidTime(LocalDateTime.now());
        // 无用参数
        billPaid.setOrderId(1L);

        payoffService.updatePayoffStatus(billPaid, pw);
    }

    @ApiOperation("广告合同应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/listByContract")
    public List<AdvertisingPayoffVo> listPayoffByContract(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        return payoffService.listPayoffByContract(contractNo);
    }

    @ApiOperation("广告应付账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public BillPage<AdvertisingPayoffDetailVo> listPayoffPage(@RequestBody AdvertisingPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return BillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<AdvertisingPayoffDetailVo> page = payoffService.listPayoffPage(search);
        BillPage<AdvertisingPayoffDetailVo> resultPage = new BillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        AdvertisingPayoffDetailVo sumPayoff = payoffService.sumPayoff(search);
        if (sumPayoff != null) {
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumPayoff.getAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumPayoff.getShareAmount()));
        }
        return resultPage;
    }
}
