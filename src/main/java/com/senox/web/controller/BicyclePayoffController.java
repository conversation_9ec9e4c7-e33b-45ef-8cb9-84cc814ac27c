package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.cold.vo.CloudWarehousingBillSearchVo;
import com.senox.cold.vo.CloudWarehousingBillVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.realty.vo.MaintainOrderVo;
import com.senox.tms.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.BicyclePayoffService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.BicyclePayoffExportVo;
import com.senox.web.vo.CloudWarehousingBilLExportVo;
import com.senox.web.vo.MaintainOrderExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:57
 */
@Api(tags = "三轮车应付账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/bicycle/payoff")
public class BicyclePayoffController extends BaseController{

    private final BicyclePayoffService bicyclePayoffService;

    @ApiOperation("支付三轮车应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public void payPayoff(@RequestBody List<Long> ids, @RequestParam Integer payway) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        PayWay pw = PayWay.fromValue(payway);
        if (pw != PayWay.CASH && pw != PayWay.TRANSFER) {
            throw new InvalidParameterException("应付账单仅支持现金及转账");
        }

        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(ids);
        billPaid.setPaid(Boolean.TRUE);
        billPaid.setTollMan(getAdminUserId());
        billPaid.setPaidTime(LocalDateTime.now());
        // 无用参数
        billPaid.setOrderId(1L);

        bicyclePayoffService.updatePayoffStatus(billPaid, pw);
    }

    @ApiOperation("应付金额更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/report/amount/update/{id}/{amount}")
    public void updateAmountFromReport(@PathVariable Long id, @PathVariable BigDecimal amount) {
        bicyclePayoffService.updateAmountFromReport(id, amount);
    }

    @ApiOperation("三轮车应付账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageStatisticsResult<BicyclePayoffVo, BicyclePayoffVo> listPayoffPage(@RequestBody BicyclePayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        // list
        PageResult<BicyclePayoffVo> page = bicyclePayoffService.listPayoff(search);
        // sum
        BicyclePayoffVo sumPayoff = bicyclePayoffService.sumPayoff(search);
        return new PageStatisticsResult<>(page, sumPayoff);
    }

    @ApiOperation("应付账单导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response, BicyclePayoffSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        //list
        List<BicyclePayoffVo> resultList = bicyclePayoffService.listPayoff(search).getDataList();
        List<BicyclePayoffExportVo> exportList = new ArrayList<>(resultList.size() + 1);
        // sum
        BicyclePayoffVo sumPayoff = bicyclePayoffService.sumPayoff(search);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (int index = 0; index < resultList.size(); index++) {
                BicyclePayoffExportVo exportItem = toExport(resultList.get(index));
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
            exportList.add(sumExport(sumPayoff));
        }
        String fileName = SenoxConst.Export.FILE_BICYCLE_PAYOFF;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicyclePayoffExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.BICYCLE_PAYOFF_SHEET)
                .doWrite(exportList);
    }

    private BicyclePayoffExportVo toExport(BicyclePayoffVo item) {
        BicyclePayoffExportVo exportVo = new BicyclePayoffExportVo();
        exportVo.setCreateTime(item.getCreateTime());
        exportVo.setOrderSerialNo(item.getOrderSerialNo());
        exportVo.setMerchantName(item.getMerchantName());
        exportVo.setPayeeName(item.getPayeeName());
        exportVo.setReferral(BooleanUtils.isTrue(item.getReferral()) ? "是" : "否");
        exportVo.setPieces(item.getPieces());
        exportVo.setReferralAmount(item.getReferralAmount());
        exportVo.setShareAmount(item.getShareAmount());
        exportVo.setOrderTotalPieces(item.getOrderTotalPieces());
        exportVo.setOrderTotalCharge(item.getOrderTotalCharge());
        return exportVo;
    }

    private BicyclePayoffExportVo sumExport(BicyclePayoffVo sumVo) {
        BicyclePayoffExportVo exportVo = new BicyclePayoffExportVo();
        exportVo.setOrderSerialNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setShareAmount(sumVo.getShareAmount());
        exportVo.setReferralAmount(sumVo.getReferralAmount());
        exportVo.setPieces(sumVo.getPieces());
        return exportVo;
    }

    @ApiOperation("生成日报表")
    @PostMapping("/report/generate/day")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public void generateReportByDay(@RequestBody BicycleDateVo dateVo) {
        bicyclePayoffService.generateReportByDay(dateVo);
    }

    @ApiOperation("生成月报表")
    @PostMapping("/report/generate/month")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public void generateReportByMonth(@RequestBody BicycleDateVo dateVo) {
        bicyclePayoffService.generateReportByMonth(dateVo);
    }

    @ApiOperation("日报表列表")
    @PostMapping("/report/day/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportDayList(@RequestBody BicyclePayoffReportSearchVo searchVo) {
        return bicyclePayoffService.reportDayList(searchVo);
    }

    @ApiOperation("月报表列表")
    @PostMapping("/report/month/list")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportMonthList(@RequestBody BicyclePayoffReportSearchVo searchVo) {
        return bicyclePayoffService.reportMonthList(searchVo);
    }

}
