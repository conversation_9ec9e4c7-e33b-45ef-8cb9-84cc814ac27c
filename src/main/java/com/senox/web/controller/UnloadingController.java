package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.constant.UnloadingCategory;
import com.senox.tms.vo.*;
import com.senox.web.config.UnloadingPayoffMergeStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.UnloadingService;
import com.senox.web.utils.BorderStyleUtil;
import com.senox.web.vo.UnloadingOrderBillExportVo;
import com.senox.web.vo.UnloadingOrderExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/11 9:14
 */
@Api(tags = "鹏翔装卸")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/unloading")
public class UnloadingController extends BaseController{

    private final UnloadingService unloadingService;

    @ApiOperation("批量添加字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/batchAdd")
    public void batchAddDict(@RequestBody List<UnloadingDictVo> dictVos) {
        unloadingService.batchAddDict(dictVos);
    }

    @ApiOperation("添加字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/add")
    public void addDict(@RequestBody UnloadingDictVo dictVo) {
        unloadingService.addDict(dictVo);
    }

    @ApiOperation("更新字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/update")
    public void updateDict(@RequestBody UnloadingDictVo dictVo) {
        unloadingService.updateDict(dictVo);
    }

    @ApiOperation("根据id查询字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/dict/get/{id}")
    public UnloadingDictVo findDictById(@PathVariable Long id) {
        return unloadingService.findDictById(id);
    }

    @ApiOperation("根据id删除字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/delete/{id}")
    public void deleteDict(@PathVariable Long id) {
        unloadingService.deleteDictById(id);
    }

    @ApiOperation("字典分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dict/page")
    public PageResult<UnloadingDictVo> dictPageResult(@RequestBody UnloadingDictSearchVo searchVo) {
        return unloadingService.dictPageResult(searchVo);
    }

    @ApiOperation("保存搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/save")
    public void saveWorker(@RequestBody UnloadingWorkerVo workerVo) {
        unloadingService.saveWorker(workerVo);
    }

    @ApiOperation("更新搬运工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/updateStatus/{id}")
    public void updateWorkerStatus(@PathVariable Long id, @RequestParam Integer status) {
        unloadingService.updateWorkerStatus(id, status);
    }

    @ApiOperation("根据搬运工编号更新搬运工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/updateStatusByWorkerNo")
    public void updateWorkerStatusByWorkerNo(@RequestParam String workerNo, @RequestParam Integer status) {
        unloadingService.updateWorkerStatusByWorkerNo(workerNo, status);
    }

    @ApiOperation("只更新人脸")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/updateFaceUrl")
    public void updateFaceUrl(@RequestBody UnloadingWorkerFaceUrlVo faceUrlVo) {
        unloadingService.updateFaceUrl(faceUrlVo);
    }

    @ApiOperation("指定位置排序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/appoint/resetOrderNum/{id}")
    public void appointResetOrderNum(@PathVariable Long id, @RequestParam Integer num) {
        unloadingService.appointResetOrderNum(id, num);
    }

    @ApiOperation("搬运工排序置底")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/appoint/bottomUp/{id}")
    public void bottomUp(@PathVariable Long id) {
        unloadingService.bottomUp(id);
    }

    @ApiOperation("根据id查询搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/get/{id}")
    public UnloadingWorkerVo findById(@PathVariable Long id) {
        return unloadingService.findWorkById(id);
    }

    @ApiOperation("搬运工请假")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/leave")
    public void workerLeave(@RequestBody UnloadingWorkerVo workerVo) {
        unloadingService.workerLeave(workerVo);
    }

    @ApiOperation("根据id删除搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/delete/{id}")
    public void deleteWorker(@PathVariable Long id) {
        unloadingService.deleteWorker(id);
    }

    @ApiOperation("搬运工列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/list")
    public List<UnloadingWorkerVo> listWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return unloadingService.listWorker(searchVo);
    }

    @ApiOperation("搬运工分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/page")
    public PageStatisticsResult<UnloadingWorkerVo, Integer> pageWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        PageResult<UnloadingWorkerVo> pageResult = unloadingService.pageWorker(searchVo);
        UnloadingWorkerSearchVo newSearchVo = new UnloadingWorkerSearchVo();
        newSearchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
        newSearchVo.setPage(false);
        int countWorker = unloadingService.countWorker(newSearchVo);
        return new PageStatisticsResult<>(pageResult, countWorker);
    }

    @ApiOperation("搬运工考勤记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/attendance/page")
    public PageResult<UnloadingAttendanceVo> pageAttendance(@RequestBody UnloadingAttendanceSearchVo searchVo) {
        return unloadingService.pageAttendance(searchVo);
    }

    @ApiOperation("更新考勤记录备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/attendance/updateRemark/{id}")
    public void updateRemark(@PathVariable Long id, @RequestParam String remark) {
        unloadingService.updateRemark(id, remark);
    }

    @ApiOperation("添加搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/access/save")
    public void addWorkerAccess(@RequestBody List<UnloadingWorkerAccessVo> accessVoList) {
        unloadingService.addWorkerAccess(accessVoList);
    }

    @ApiOperation("根据id删除搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/access/delete/{id}")
    public void deleteAccessById(@PathVariable Long id) {
        unloadingService.deleteAccessById(id);
    }

    @ApiOperation("根据Id获取搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/access/get/{workerId}")
    public List<UnloadingWorkerAccessVo> listAccessByWorkerId(@PathVariable Long workerId) {
        return unloadingService.listAccessByWorkerId(workerId);
    }

    @ApiOperation("搬运工顺序列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/sequence/list")
    public List<UnloadingWorkerVo> listSequenceWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return unloadingService.listSequenceWorker(searchVo);
    }

    @ApiOperation("根据id查询搬运工异常日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/log/get/{id}")
    public UnloadingWorkerLogVo findWorkerLogById(@PathVariable Long id) {
        return unloadingService.findWorkerLogById(id);
    }

    @ApiOperation("搬运工异常日志分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/log/page")
    public PageResult<UnloadingWorkerLogVo> pageWorkerLog(@RequestBody UnloadingWorkerLogSearchVo searchVo) {
        return unloadingService.pageWorkerLog(searchVo);
    }

    @ApiOperation("批量添加排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/schedule/batchAdd")
    public void batchAddSchedule(@RequestBody UnloadingNightScheduleBatchVo batchVo) {
        unloadingService.batchAddSchedule(batchVo);
    }

    @ApiOperation("添加排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/schedule/add")
    public void addSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo) {
        unloadingService.addSchedule(scheduleVo);
    }

    @ApiOperation("更新排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/schedule/update")
    public void updateSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo) {
        unloadingService.updateSchedule(scheduleVo);
    }

    @ApiOperation("根据id查询排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/schedule/get/{id}")
    public UnloadingNightScheduleVo findScheduleById(@PathVariable Long id) {
        return unloadingService.findScheduleById(id);
    }

    @ApiOperation("根据日期获取排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/schedule/findByScheduleDate")
    public List<UnloadingNightScheduleVo> findByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate) {
        return unloadingService.findByScheduleDate(scheduleDate);
    }

    @ApiOperation("根据id删除排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/schedule/delete/{id}")
    public void deleteScheduleById(@PathVariable Long id) {
        unloadingService.deleteScheduleById(id);
    }

    @ApiOperation("根据日期删除排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/worker/schedule/deleteByScheduleDate")
    public void deleteByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate) {
        unloadingService.deleteByScheduleDate(scheduleDate);
    }

    @ApiOperation("搬运工排期计划分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/schedule/page")
    public PageResult<UnloadingNightScheduleVo> pageSchedule(@RequestBody UnloadingNightScheduleSearchVo searchVo) {
        return unloadingService.pageSchedule(searchVo);
    }

    @ApiOperation("添加搬运订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/add")
    public void saveOrder(@RequestBody UnloadingOrderVo orderVo) {
        unloadingService.saveOrder(orderVo);
    }

    @ApiOperation("删除搬运订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/delete/{id}")
    public void deleteOrder(@PathVariable Long id) {
        unloadingService.deleteOrder(id);
    }

    @ApiOperation("根据id搬运订单及详细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/get/{id}")
    public UnloadingOrderVo findDetailById(@PathVariable Long id) {
        return unloadingService.findDetailById(id);
    }

    @ApiOperation("搬运订单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/page")
    public PageStatisticsResult<UnloadingOrderVo, UnloadingOrderVo> pageResult(@RequestBody UnloadOrderSearchVo searchVo) {
        PageResult<UnloadingOrderVo> result = unloadingService.pageResult(searchVo);
        UnloadingOrderVo sumOrder = unloadingService.sumOrder(searchVo);
        return  new PageStatisticsResult<>(result, sumOrder);
    }

    @ApiOperation("搬运订单详细分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/detail/page")
    public PageStatisticsResult<UnloadingOrderVo, UnloadingOrderVo> pageDetailResult(@RequestBody UnloadOrderSearchVo searchVo) {
        PageResult<UnloadingOrderVo> result = unloadingService.pageDetailResult(searchVo);
        UnloadingOrderVo sumOrder = unloadingService.sumOrder(searchVo);
        return  new PageStatisticsResult<>(result, sumOrder);
    }

    @ApiOperation("搬运订单导出")
    @GetMapping("/exportDetailOrder")
    public void exportOrder(HttpServletResponse response, UnloadOrderSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setPage(false);
        List<UnloadingOrderVo> orderVos = unloadingService.pageDetailResult(searchVo).getDataList();

        List<UnloadingOrderExportVo> exportList = new ArrayList<>(orderVos.size());
        if (!CollectionUtils.isEmpty(orderVos)) {
            for (int index = 0; index < orderVos.size(); index++) {
                UnloadingOrderVo orderVo = orderVos.get(index);
                UnloadingOrderExportVo exportVo = newUnloadingOrderExportVo(orderVo);
                exportVo.setSerial(index + 1);
                exportList.add(exportVo);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_UNLOADING_ORDER_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), UnloadingOrderExportVo.class)
                .sheet()
                .doWrite(exportList);
    }

    private UnloadingOrderExportVo newUnloadingOrderExportVo(UnloadingOrderVo orderVo) {
        UnloadingOrderExportVo exportVo = new UnloadingOrderExportVo();
        exportVo.setAddress(orderVo.getAddress());
        exportVo.setGoodsName(Optional.ofNullable(orderVo.getGoodsVoList()).filter(list -> !list.isEmpty()).map(list -> list.stream().map(x -> x.getGoodsName() + "（" + UnloadingCategory.fromStatus(x.getCategory()).getName() + "）").collect(Collectors.joining("，"))).orElse(StringUtils.EMPTY));
        exportVo.setGoodsNum(Optional.ofNullable(orderVo.getGoodsVoList()).filter(list -> !list.isEmpty()).map(list -> list.stream().map(x -> x.getQuantity() + "（" + x.getUnit() + "）").collect(Collectors.joining("，"))).orElse(StringUtils.EMPTY));
        exportVo.setWorkers(Optional.ofNullable(orderVo.getWorkersVoList()).filter(list -> !list.isEmpty()).map(list -> list.stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.joining("，"))).orElse(StringUtils.EMPTY));
        exportVo.setAmount(orderVo.getAmount());
        exportVo.setBillPaidDate(orderVo.getBillPaidTime() == null ? StringUtils.EMPTY : orderVo.getBillPaidTime().toLocalDate().toString());
        exportVo.setPaidMan(StringUtils.EMPTY);
        exportVo.setTollMan(orderVo.getTollMan());
        exportVo.setRemark(orderVo.getRemark());
        return exportVo;
    }

    @ApiOperation("指定搬运工人数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/appointWorkerNum/{orderId}")
    public void appointWorkerNum(@PathVariable Long orderId, @RequestParam Integer workerNum) {
        unloadingService.appointWorkerNum(orderId, workerNum);
    }

    @ApiOperation("分派搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/assignWorkers")
    public void assignWorkers(@RequestBody UnloadingOrderVo orderVo) {
        unloadingService.assignWorkers(orderVo);
    }

    @ApiOperation("完成订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/finishOrder/{orderId}")
    public void finishOrder(@PathVariable Long orderId, @RequestParam BigDecimal amount) {
        unloadingService.finishOrder(orderId, amount);
    }

    @ApiOperation("新增加急费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/addUrgentAmount/{orderId}")
    public void addUrgentAmount(@PathVariable Long orderId, @RequestParam BigDecimal urgentAmount) {
        unloadingService.addUrgentAmount(orderId, urgentAmount);
    }

    @ApiOperation("取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/cancelOrder/{orderId}")
    public void cancelOrder(@PathVariable Long orderId) {
        unloadingService.cancelOrder(orderId);
    }

    @ApiOperation("补录搬运订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/supplementOrder")
    public String supplementOrder(@RequestBody UnloadingOrderVo orderVo) {
        return unloadingService.supplementOrder(orderVo);
    }

    @ApiOperation("订单顺序分派")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/sequence/assignWorkers")
    public void sequenceAssignWorkers(@RequestBody UnloadingOrderWorkersAssignVo assignVo) {
        unloadingService.sequenceAssignWorkers(assignVo);
    }


    @ApiOperation("运营分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/operate/analysis/statistics")
    public UnloadingOperateAnalysisVo operateAnalysisStatistics(@RequestBody UnloadingStatisticSearchVo searchVo) {
        return unloadingService.operateAnalysisStatistics(searchVo);
    }

    @ApiOperation("排行榜")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/ranking/statistics")
    public UnloadingCountRankingVo rankingStatistics(@RequestBody UnloadingStatisticSearchVo searchVo) {
        return unloadingService.rankingStatistics(searchVo);
    }

    @ApiOperation("订单统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/statistics")
    public List<UnloadingOrderCountVo> statistics(@RequestBody UnloadingStatisticSearchVo searchVo) {
        return unloadingService.statistics(searchVo);
    }

    @ApiOperation("添加分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/shares/save")
    public void saveShares(@RequestBody UnloadingSharesVo sharesVo) {
        unloadingService.saveShares(sharesVo);
    }

    @ApiOperation("根据id获取分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/shares/get/{id}")
    public UnloadingSharesVo findSharesById(@PathVariable Long id) {
        return unloadingService.findSharesById(id);
    }

    @ApiOperation("根据id删除分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/shares/delete/{id}")
    public void deleteSharesById(@PathVariable Long id) {
        unloadingService.deleteSharesById(id);
    }

    @ApiOperation("分佣分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/shares/page")
    public PageResult<UnloadingSharesVo> pageShares(@RequestBody UnloadingSharesSearchVo searchVo) {
        return unloadingService.pageShares(searchVo);
    }

    @ApiOperation("生成应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/generate")
    public void generateBill(@RequestBody UnloadingMonthVo monthVo) {
        unloadingService.generateBill(monthVo);
    }

    @ApiOperation("支付应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/batchPayByIds")
    public void batchPayByIds(@RequestBody List<Long> ids) {
        unloadingService.batchPayByIds(ids);
    }

    @ApiOperation("支付应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/batchPay")
    public void batchPay(@RequestBody UnloadingOrderBillSearchVo searchVo) {
        unloadingService.batchPay(searchVo);
    }

    @ApiOperation("根据id查询应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/get/{id}")
    public UnloadingOrderBillVo findBillById(@PathVariable Long id) {
        return unloadingService.findBillById(id);
    }

    @ApiOperation("根据id删除应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/delete/{id}")
    public void deleteBillById(@PathVariable Long id) {
        unloadingService.deleteBillById(id);
    }

    @ApiOperation("应收账单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/page")
    public PageStatisticsResult<UnloadingOrderBillVo, UnloadingOrderBillVo> pageBill(@RequestBody UnloadingOrderBillSearchVo searchVo) {
        PageResult<UnloadingOrderBillVo> pageResult = unloadingService.pageBill(searchVo);
        UnloadingOrderBillVo sumBill = unloadingService.sumBill(searchVo);
        return new PageStatisticsResult<>(pageResult, sumBill);
    }

    @ApiOperation("导出应收账单")
    @GetMapping("/bill/export")
    public void exportBill(HttpServletResponse response, UnloadingOrderBillSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<UnloadingOrderBillVo> orderBillVos = unloadingService.pageBill(searchVo).getDataList();
        UnloadingOrderBillVo sumBill = unloadingService.sumBill(searchVo);
        List<UnloadingOrderBillExportVo> exportList = new ArrayList<>(orderBillVos.size() + 1);

        if (!CollectionUtils.isEmpty(orderBillVos)) {
            for (int index = 0; index < orderBillVos.size(); index++) {
                UnloadingOrderBillVo billVo = orderBillVos.get(index);
                UnloadingOrderBillExportVo exportVo = newUnloadingOrderBillExportVo(billVo);
                exportVo.setSerial(index + 1);
                exportList.add(exportVo);
            }
            exportList.add(sumUnloadingOrderBillExportVo(sumBill));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_UNLOADING_ORDER_BILL_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), UnloadingOrderBillExportVo.class)
                .sheet(SenoxConst.Export.SHEET_UNLOADING_ORDER_BILL_REPORT)
                .doWrite(exportList);
    }

    private UnloadingOrderBillExportVo newUnloadingOrderBillExportVo(UnloadingOrderBillVo billVo) {
        UnloadingOrderBillExportVo exportVo = new UnloadingOrderBillExportVo();
        exportVo.setBillDate(billVo.getBillDate().toString());
        exportVo.setOrderNo(billVo.getOrderNo());
        exportVo.setCustomerName(billVo.getCustomerName());
        exportVo.setAddress(billVo.getAddress());
        exportVo.setLicensePlate(billVo.getLicensePlate());
        exportVo.setContact(billVo.getContact());
        exportVo.setAmount(billVo.getAmount());
        exportVo.setQuantity(Optional.ofNullable(billVo.getGoodsVoList()).filter(list -> !list.isEmpty()).map(list -> list.stream().map(x -> x.getQuantity() + "（" + x.getUnit() + "）").collect(Collectors.joining("，"))).orElse(StringUtils.EMPTY));
        exportVo.setStatus(UnloadingOrderBillPayoffStatus.fromStatus(billVo.getStatus()).getName());
        exportVo.setPaidTime(billVo.getPaidTime() == null ? StringUtils.EMPTY : DateUtils.formatDateTime(billVo.getPaidTime(), DateUtils.PATTERN_FULL_DATE_TIME));
        return exportVo;
    }

    private UnloadingOrderBillExportVo sumUnloadingOrderBillExportVo(UnloadingOrderBillVo sumBillVo) {
        UnloadingOrderBillExportVo exportVo = new UnloadingOrderBillExportVo();
        exportVo.setOrderNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setAmount(sumBillVo.getAmount());
        exportVo.setQuantity(sumBillVo.getQuantity().stripTrailingZeros().toPlainString());
        return exportVo;
    }

    @ApiOperation("更新订单应收金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/updateOrderBill/{orderNo}")
    public void updateOrderBill(@PathVariable String orderNo, @RequestParam BigDecimal amount) {
        unloadingService.updateOrderBill(orderNo, amount);
    }
    @ApiOperation("生成应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/generate")
    public void generateOrderPayoff(@RequestBody UnloadingMonthVo monthVo) {
        unloadingService.generateOrderPayoff(monthVo);
    }

    @ApiOperation("根据id查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/get/{id}")
    public UnloadingOrderPayoffVo findPayoffById(@PathVariable Long id) {
        return unloadingService.findPayoffById(id);
    }

    @ApiOperation("根据订单号删除应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/deleteByOrderNo")
    public void deletePayoffByOrderNo(@RequestParam String orderNo) {
        unloadingService.deletePayoffByOrderNo(orderNo);
    }

    @ApiOperation("更新应付金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/update/sharesAmount")
    public void updateSharesAmount(@RequestBody List<UnloadingOrderPayoffVo> payoffVoList) {
        unloadingService.updateSharesAmount(payoffVoList);
    }

    @ApiOperation("应付分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/page")
    public PageStatisticsResult<UnloadingOrderPayoffVo, UnloadingOrderPayoffVo> pageOrderPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        PageResult<UnloadingOrderPayoffVo> pageResult = unloadingService.pageOrderPayoff(searchVo);
        UnloadingOrderPayoffVo sumPayoff = unloadingService.sumPayoff(searchVo);
        return new PageStatisticsResult<>(pageResult, sumPayoff);
    }

    @ApiOperation("应付合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/sum")
    public UnloadingOrderPayoffVo sumPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        return unloadingService.sumPayoff(searchVo);
    }

    @ApiOperation("搬运工应付导出")
    @GetMapping("/exportPayoff")
    public void exportPayoff(HttpServletResponse response, UnloadingOrderPayoffSearchVo searchVo) throws IOException {
        checkPayoffDateValid(searchVo);
        searchVo.setPage(false);
        List<UnloadingOrderPayoffVo> payoffVos = unloadingService.pageOrderPayoff(searchVo).getDataList();
        List<List<String>> data = new ArrayList<>();

        Map<String, List<UnloadingOrderPayoffVo>> singleGroupMap = payoffVos.stream().filter(x -> x.getOrderNum() == 0)
                .collect(Collectors.groupingBy(UnloadingOrderPayoffVo::getWorkerNo
                        , () -> new TreeMap<>(Comparator.comparingInt(Integer::parseInt)), Collectors.toList()));
        Map<String, List<UnloadingOrderPayoffVo>> doubleGroupMap = payoffVos.stream().filter(x -> x.getOrderNum() == 1)
                .collect(Collectors.groupingBy(UnloadingOrderPayoffVo::getWorkerNo
                        , () -> new TreeMap<>(Comparator.comparingInt(Integer::parseInt)), Collectors.toList()));

        for (Map.Entry<String, List<UnloadingOrderPayoffVo>> entry : singleGroupMap.entrySet()) {
            data.addAll(createRows(entry.getKey(), entry.getValue()));
        }
        for (Map.Entry<String, List<UnloadingOrderPayoffVo>> entry : doubleGroupMap.entrySet()) {
            data.addAll(createRows(entry.getKey(), entry.getValue()));
        }

        List<List<String>> headers = generatePayoffHeaders(searchVo);

        String fileName = String.format(SenoxConst.Export.FILE_UNLOADING_PAYOFF_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        exportPayoffExcel(response, headers, data);
    }

    @ApiOperation("根据订单编号查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/findByOrderNo")
    public List<UnloadingOrderPayoffVo> listPayoffByOrderNo(@RequestParam String orderNo) {
        return unloadingService.listPayoffByOrderNo(orderNo);
    }


    @ApiOperation("搬运工日应付信息导出")
    @GetMapping("/exportDailyList")
    public void exportDailyList(HttpServletResponse response, UnloadingOrderPayoffSearchVo searchVo) throws IOException {
        checkPayoffDateValid(searchVo);
        List<UnloadingOrderPayoffVo> payoffVos = unloadingService.payoffDailyList(searchVo);

        List<List<String>> headers = generateDailyListHeaders(searchVo.getPayoffDateStart(), searchVo.getPayoffDateEnd());

        List<Map<String, Object>> data = generateDailyListData(payoffVos, searchVo.getPayoffDateStart(), searchVo.getPayoffDateEnd());
        //处理完的数据
        List<List<String>> handleData = data.stream()
                .map(map -> map.values().stream()
                        .map(Object::toString)
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        String fileName = String.format(SenoxConst.Export.FILE_UNLOADING_DAILY_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        //导出
        exportDailyListExcel(response, headers, handleData);
    }

    /**
     * 校验应付时间有效
     * @param searchVo
     */
    private static void checkPayoffDateValid(UnloadingOrderPayoffSearchVo searchVo) {
        if (searchVo.getPayoffDateStart() == null || searchVo.getPayoffDateEnd() == null) {
            throw new BusinessException("请输入合适的时间范围!");
        }
        if (! YearMonth.from(searchVo.getPayoffDateStart()).equals(YearMonth.from(searchVo.getPayoffDateEnd()))) {
            throw new BusinessException("开始时间和结束时间应该在同一月！");
        }
    }

    @ApiOperation("根据应付月账单id查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/findByMonthPayoffId/{monthPayoffId}")
    public List<UnloadingOrderPayoffVo> findByMonthPayoffId(@PathVariable Long monthPayoffId) {
        return unloadingService.findByMonthPayoffId(monthPayoffId);
    }

    @ApiOperation("生成月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/month/generate")
    public void generateMonthPayoff(@RequestBody UnloadingMonthPayoffVo payoffVo) {
        unloadingService.generateMonthPayoff(payoffVo);
    }

    @ApiOperation("更新月应付账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/month/updateRemark")
    public void updateRemark(@RequestBody UnloadingMonthPayoffRemarkVo remarkVo) {
        unloadingService.updateRemark(remarkVo);
    }

    @ApiOperation("根据id查询月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/month/get/{id}")
    public UnloadingOrderMonthPayoffVo findMonthPayoffById(@PathVariable Long id) {
        return unloadingService.findMonthPayoffById(id);
    }

    @ApiOperation("根据id删除月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/payoff/month/delete/{id}")
    public void deleteMonthPayoff(@PathVariable Long id) {
        unloadingService.deleteMonthPayoff(id);
    }

    @ApiOperation("月应付账单批量支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/month/batch")
    public void batchMonthPayoffByIds(@RequestBody List<Long> ids) {
        unloadingService.batchMonthPayoffByIds(ids);
    }

    @ApiOperation("月应付账单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/month/page")
    public PageStatisticsResult<UnloadingOrderMonthPayoffVo, UnloadingOrderMonthPayoffVo> pageMonthPayoff(@RequestBody UnloadingOrderMonthPayoffSearchVo searchVo) {
        PageResult<UnloadingOrderMonthPayoffVo> pageResult = unloadingService.pageMonthPayoff(searchVo);
        UnloadingOrderMonthPayoffVo sumMonthPayoff = unloadingService.sumMonthPayoff(searchVo);
        return new PageStatisticsResult<>(pageResult, sumMonthPayoff);
    }

    @ApiOperation("查询搬运工情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payoff/worker/statistics")
    public UnloadingDayCountWorkerVo workerStatistics(@RequestBody UnloadingStatisticSearchVo searchVo) {
        return unloadingService.workerStatistics(searchVo);
    }

    /**
     * 生成动态表头
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 动态表头
     */
    private static List<List<String>> generateDailyListHeaders(LocalDate startDate, LocalDate endDate) {
        int year = startDate.getYear();
        int month = startDate.getMonthValue();
        List<List<String>> headers = new ArrayList<>();

        // 添加固定表头
        headers.add(Lists.newArrayList(year + "年" + month + "月份装卸部营业表", "工号"));
        headers.add(Lists.newArrayList(year + "年" + month + "月份装卸部营业表", "姓名"));

        // 添加动态日期表头
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d日");
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (long i = 0; i <= daysBetween; i++) {
            LocalDate date = startDate.plusDays(i);
            headers.add(Lists.newArrayList(Lists.newArrayList(year + "年" + month + "月份装卸部营业表", date.format(formatter))));
        }

        headers.add(Lists.newArrayList(year + "年" + month + "月份装卸部营业表", "合计"));
        return headers;
    }

    /**
     * 生成动态数据
     * @param payoffVos   原始数据
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 动态数据
     */
    private static List<Map<String, Object>> generateDailyListData(List<UnloadingOrderPayoffVo> payoffVos, LocalDate startDate, LocalDate endDate) {
        // 按用户分组
        Map<String, Map<String, Object>> userDataMap = new LinkedHashMap<>();
        List<Map<String, Object>> resultValues = new ArrayList<>();

        for (UnloadingOrderPayoffVo payoffVo : payoffVos) {
            // 初始化用户数据
            userDataMap.putIfAbsent(payoffVo.getWorkerNo(), new LinkedHashMap<String, Object>() {{
                put("工号", payoffVo.getWorkerNo());
                put("姓名", payoffVo.getWorkerName());
            }});

            // 将数据填入对应的日期列
            userDataMap.get(payoffVo.getWorkerNo()).put(payoffVo.getPayoffDate().toString()
                    , DecimalUtils.isPositive(payoffVo.getPayoffAmount()) ? payoffVo.getPayoffAmount().stripTrailingZeros().toPlainString() : StringUtils.EMPTY);
        }

        // 填充缺失的日期数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (Map<String, Object> userData : userDataMap.values()) {
            for (long i = 0; i <= daysBetween; i++) {
                LocalDate date = startDate.plusDays(i);
                String dateKey = date.format(formatter);
                userData.putIfAbsent(dateKey, StringUtils.EMPTY); // 填充空字符串
            }
            // 使用 Stream API 排序 Map 的键（非日期格式的字符串排前面，日期格式按日期升序排序）
            List<Map.Entry<String, Object>> sortedEntries = userData.entrySet()
                    .stream()
                    .sorted((entry1, entry2) -> {
                        boolean isDate1 = isDate(entry1.getKey());
                        boolean isDate2 = isDate(entry2.getKey());

                        // 非日期格式的 key 排前面
                        if (!isDate1 && isDate2) {
                            return -1; // 非日期排前
                        } else if (isDate1 && !isDate2) {
                            return 1; // 日期排后
                        }

                        // 如果两个都是日期格式，则按日期升序排序
                        if (isDate1) {
                            LocalDate date1 = LocalDate.parse(entry1.getKey());
                            LocalDate date2 = LocalDate.parse(entry2.getKey());
                            return date1.compareTo(date2); // 按日期升序排序
                        }

                        return 0; // 如果两个都是非日期格式，保持原顺序
                    })
                    .collect(Collectors.toList());

            // 将排序后的结果重新转换为 Map，保持顺序
            Map<String, Object> sortedMap = new LinkedHashMap<>();
            sortedEntries.forEach(entry -> sortedMap.put(entry.getKey(), entry.getValue()));
            BigDecimal sumValuesForDateKeys = sumValuesForDateKeys(sortedMap);
            sortedMap.put("合计", sumValuesForDateKeys.stripTrailingZeros().toPlainString());
            resultValues.add(sortedMap);
        }

        // 转换为列表
        return resultValues;
    }

    /**
     * 导出 Excel
     * @param response
     * @param headers  表头
     * @param handleData     数据
     */
    private static void exportDailyListExcel(HttpServletResponse response, List<List<String>> headers,  List<List<String>> handleData) throws IOException {
        HorizontalCellStyleStrategy styleStrategy = createHorizontalCellStyleStrategy();

        // 写入表头和数据
        EasyExcelFactory.write(response.getOutputStream())
                .head(headers)
                .registerWriteHandler(styleStrategy) // 注册样式策略
                .sheet()
                .doWrite(handleData);
    }

    /**
     * 创建样式策略
     * @return
     */
    private static HorizontalCellStyleStrategy createHorizontalCellStyleStrategy() {
        // 设置表头样式
        WriteCellStyle headWriteCellStyle = BorderStyleUtil.createBorderStyle(); // 表头边框
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignmentEnum.CENTER.getPoiHorizontalAlignment()); // 修正后的路径
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

        // 设置内容样式
        WriteCellStyle contentWriteCellStyle = BorderStyleUtil.createBorderStyle(); // 内容边框
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignmentEnum.CENTER.getPoiHorizontalAlignment()); // 修正后的路径
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

        // 创建样式策略
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 判断一个字符串是否是日期格式（"yyyy-MM-dd"）
     * @param key
     * @return
     */
    private static boolean isDate(String key) {
        try {
            LocalDate.parse(key);  // 尝试解析日期格式
            return true;
        } catch (Exception e) {
            return false;  // 如果解析失败，说明不是日期格式
        }
    }

    /**
     * 求和方法，考虑到只对日期格式的 key 进行加总
     * @param map
     * @return
     */
    public static BigDecimal sumValuesForDateKeys(Map<String, Object> map) {
        return map.entrySet().stream()
                .skip(2)
                .filter(entry -> isDate(entry.getKey())
                        && entry.getValue() instanceof String
                        && !StringUtils.isBlank(entry.getValue().toString()))
                .map(entry -> new BigDecimal(entry.getValue().toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 创建搬运工每行的数据
     * @param workerNo
     * @param payoffVos
     * @return
     */
    private static List<List<String>> createRows(String workerNo, List<UnloadingOrderPayoffVo> payoffVos) {
        List<List<String>> rows = new ArrayList<>();
        int chunkSize = 7; // 每行最多 7 条记录
        BigDecimal total = payoffVos.stream().map(UnloadingOrderPayoffVo::getPayoffAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (int i = 0; i < payoffVos.size(); i += chunkSize) {
            List<String> row = new ArrayList<>();
            row.add(workerNo);

            // 添加金额记录
            for (int j = 0; j < chunkSize; j++) {
                int index = i + j;
                if (index < payoffVos.size()) {
                    row.add(DecimalUtils.equals(payoffVos.get(index).getPayoffAmount(), BigDecimal.ZERO)
                            ? StringUtils.EMPTY : payoffVos.get(index).getPayoffAmount().stripTrailingZeros().toPlainString());
                } else {
                    row.add(StringUtils.EMPTY); // 不足 7 条时填充空值
                }
            }

            // 添加合计总数
            row.add(total.stripTrailingZeros().toPlainString());
            rows.add(row);
        }

        return rows;
    }

    /**
     * 生成动态表头
     * @param searchVo
     * @return
     */
    private static List<List<String>> generatePayoffHeaders(UnloadingOrderPayoffSearchVo searchVo) {
        int year = searchVo.getPayoffDateStart().getYear();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日", Locale.CHINA);
        String startDate = searchVo.getPayoffDateStart().format(formatter);
        String endDate = searchVo.getPayoffDateEnd().format(formatter);
        List<List<String>> headers = new ArrayList<>();

        // 固定表头
        headers.add(Lists.newArrayList(year + "年出单表（"+ startDate +"-" + endDate + "）", "工号"));

        // 动态金额表头（最多 7 条）
        for (int i = 1; i <= 7; i++) {
            headers.add(Lists.newArrayList(year + "年出单表（"+ startDate +"-" + endDate + "）", StringUtils.EMPTY));
        }

        // 合计总数表头
        headers.add(Lists.newArrayList(year + "年出单表（"+ startDate +"-" + endDate + "）", "总数"));

        return headers;
    }

    /**
     * 导出 Excel
     * @param response
     * @param headers  表头
     * @param data     数据
     */
    private static void exportPayoffExcel(HttpServletResponse response, List<List<String>> headers, List<List<String>> data) throws IOException {
        // 创建样式策略
        HorizontalCellStyleStrategy styleStrategy = createHorizontalCellStyleStrategy();

        // 写入表头和数据
        EasyExcelFactory.write(response.getOutputStream())
                .head(headers)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new UnloadingPayoffMergeStrategy()) // 注册合并单元格策略
                .sheet()
                .doWrite(data);
    }
}
