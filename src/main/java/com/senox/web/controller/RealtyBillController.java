package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.*;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.RealtyBillReceiptExportConvertor;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.OrderService;
import com.senox.web.service.RealtyBillService;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.senox.web.constant.SenoxConst.Export.COLUMN_SUM;
import static com.senox.web.constant.SenoxConst.PRINT_BILL_PAY_ONLINE;
import static com.senox.web.constant.SenoxConst.PRINT_BILL_PAY_WITHHOLD;

/**
 * <AUTHOR>
 * @date 2021/6/11 15:33
 */
@Api(tags = "应收账单")
@AllArgsConstructor
@RestController
@RequestMapping("/web/realtyBill")
public class RealtyBillController extends BaseController {

    private final RealtyBillService billService;
    private final OrderService orderService;
    private final AdminUserService adminUserService;
    private final RealtyBillReceiptExportConvertor receiptConvertor;

    @ApiOperation("生成应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generateBill(@Validated @RequestBody BillMonthVo month) {
        billService.generateBill(month);
    }

    @ApiOperation("重新生成应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/regenerate")
    public void regenerateBill(@Validated @RequestBody BillMonthVo month) {
        billService.regenerateBill(month);
    }

    @ApiOperation("同步应收账单水电")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/sync")
    public void syncBillWeData(@Validated @RequestBody BillMonthVo month) {
        billService.syncBillWeData(month);
    }

    @ApiOperation("更新应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBill(@Validated @RequestBody RealtyBillVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        billService.updateBill(bill);
    }

    @ApiOperation("发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/receipt")
    public void receiptBill(@RequestBody RealtyBillBatchVo batchBills) {
        if (WrapperClassUtils.biggerThanLong(batchBills.getId(), 0L)) {
            batchBills.setIds(Collections.singletonList(batchBills.getId()));
        }
        if (CollectionUtils.isEmpty(batchBills.getIds())) {
            throw new InvalidParameterException();
        }
        if (batchBills.getReceipt() == null) {
            throw new InvalidParameterException();
        }

        billService.receiptBill(batchBills);
    }

    @ApiOperation("更新应收账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/remark/save")
    public void saveBillRemark(@Validated @RequestBody RealtyBillRemarkVo remark) {
        if (!WrapperClassUtils.biggerThanLong(remark.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        billService.saveBillRemark(remark);
    }

    @ApiOperation("下发应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send")
    public void sendBill(@RequestBody RealtyBillSendVo sendVo) {
        if ((sendVo.getBillYear() == null || sendVo.getBillMonth() == null)
                && CollectionUtils.isEmpty(sendVo.getBillIds())) {
            throw new InvalidParameterException("未明下发的物业账单");
        }
        billService.sendBill(sendVo);
    }

    @ApiOperation("删除应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        billService.deleteBill(id);
    }

    @ApiOperation("支付应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return billService.payBill(payRequest);
    }

    @ApiOperation("混合支付应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/payMix")
    public List<OrderResultVo> mixPayBill(HttpServletRequest request, @Validated @RequestBody MixPayRequestVo payRequest) {
        // 校验支付参数
        validateMixPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return billService.mixPayBill(payRequest);
    }

    @ApiOperation("退费应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund")
    public OrderResultVo refundOrder(HttpServletRequest request, @Validated @RequestBody RefundOrderVo refundOrder) {
        refundOrder.setRequestIp(RequestUtils.getIpAddr(request));
        refundOrder.setTollMan(getAdminUserId());
        return billService.refundOrder(refundOrder);
    }

    @ApiOperation("部分退费应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refundPart")
    public OrderResultVo refundOrderPart(HttpServletRequest request, @Validated @RequestBody RefundOrderFeeVo refundOrderFee) {
        refundOrderFee.setRequestIp(RequestUtils.getIpAddr(request));
        refundOrderFee.setTollMan(getAdminUserId());
        return billService.refundBillOrderPart(refundOrderFee);
    }

    @ApiOperation("撤销应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel")
    public void cancelBill(@RequestBody BillCancelVo cancel) {
        if (!WrapperClassUtils.biggerThanLong(cancel.getBillId(), 0L)) {
            throw new InvalidParameterException();
        }

        billService.cancelBill(cancel);
    }

    @ApiOperation("应收账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RealtyBillVo findBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return billService.findBillById(id);
    }

    @ApiOperation("打印应收账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        RealtyBillVo bill = billService.findBillById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (bill.getStatus() != BillStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = realtyBill2TollPrintVo(bill);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            RealtyBillSerialVo billSerial = new RealtyBillSerialVo();
            billSerial.setBillId(id);
            billSerial.setBillSerial(result.getBillSerial());
            billSerial.setTollManId(getAdminUserId());
            billService.saveBillSerial(billSerial);
        }
        return result;
    }

    @ApiOperation("打印应收账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/printOrder/{id}/{productId}")
    public TollPrintVo printOrder(@PathVariable Long id, @PathVariable Long productId, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        OrderProductDetailVo product = orderService.getOrderProduct(id, productId);
        if (product == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (product.getStatus() != OrderStatus.PAID && product.getStatusValue() != OrderStatus.REFUND.getStatus()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = order2TollPrintVo(product);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            OrderSerialVo orderSerial = new OrderSerialVo();
            orderSerial.setOrderId(id);
            orderSerial.setProductId(productId);
            orderSerial.setBillSerial(result.getBillSerial());
            orderService.updateOrderSerial(orderSerial);
        }
        return result;
    }

    @ApiOperation("应收账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public RealtyBillPageResult<RealtyBillVo> listBillPage(@Validated @RequestBody RealtyBillSearchVo search) {
        return billService.listBillPage(search);
    }

    @ApiOperation("查找物业月账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/monthlyBillList")
    public List<RealtyBillVo> monthlyBillList(@RequestBody BillMonthVo monthVo) {
        return billService.monthlyBillList(monthVo);
    }

    @ApiOperation("新增物业账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/saveBill")
    public void saveBill(@RequestBody BillMonthVo monthVo) {
        billService.saveBill(monthVo);
    }

    @ApiOperation("物业发票账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/receiptList")
    public RealtyBillPageResult<RealtyBillVo> listReceiptBill(@Validated @RequestBody RealtyBillSearchVo search) {
        return billService.listReceiptBill(search);
    }

    @ApiOperation("应收账单详细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detailList")
    public RealtyBillPageResult<RealtyBillVo> listBillPageWithDetail(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillVo> billPage = billService.listBillDetailPage(search);

        // result
        RealtyBillPageResult<RealtyBillVo> resultPage = new RealtyBillPageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(billPage.getTotalSize());
        resultPage.setDataList(billPage.getDataList());
        resultPage.initTotalPage();

        // sum
        RealtyBillVo sumBill = resultPage.getTotalSize() < 1 ? new RealtyBillVo() : billService.sumBillDetail(search);
        if (sumBill != null) {
            resultPage.setManageAmount(DecimalUtils.nullToZero(sumBill.getManageAmount()));
            resultPage.setRentAmount(DecimalUtils.nullToZero(sumBill.getRentAmount()));
            resultPage.setElectricAmount(DecimalUtils.nullToZero(sumBill.getElectricAmount()));
            resultPage.setWaterAmount(DecimalUtils.nullToZero(sumBill.getWaterAmount()));
            resultPage.setPenaltyAmount(DecimalUtils.nullToZero(sumBill.getPenaltyAmount()));
            resultPage.setPenaltyPaidAmount(DecimalUtils.nullToZero(sumBill.getPenaltyPaidAmount()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumBill.getTotalAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumBill.getPaidAmount()));
            resultPage.setPaidStillAmount(DecimalUtils.nullToZero(sumBill.getPaidStillAmount()));
            resultPage.setRefundAmount(DecimalUtils.nullToZero(sumBill.getRefundAmount()));
            resultPage.setReceivableAmount(DecimalUtils.nullToZero(sumBill.getReceivableAmount()));
            resultPage.setTotalAmountIgnorePenalty(DecimalUtils.nullToZero(sumBill.getTotalAmountIgnorePenalty()));
        }
        return resultPage;
    }


    @ApiOperation("导出账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportBill(HttpServletResponse response, RealtyBillSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setContainContractType(Boolean.TRUE);

        // list
        RealtyBillPageResult<RealtyBillVo> pageResult = billService.listBillPageWithDetail(searchVo);
        List<RealtyBillExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (RealtyBillVo item : pageResult.getDataList()) {
                RealtyBillExportVo exportItem = realtyBill2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumRealtyBillExport(pageResult));
        Set<String> excludeColumnNames = new HashSet<>();
        if (BooleanUtils.isFalse(searchVo.getContainCustomerDetail())) {
            //不显示列
            setPropertyExclude(excludeColumnNames);
        }
        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_BILL, LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_REALTY_BILL);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyBillExportVo.class).excludeColumnFiledNames(excludeColumnNames)
                .sheet(sheetName).doWrite(exportList);
    }

    private static void setPropertyExclude(Set<String> excludeColumnNames) {
        excludeColumnNames.add("customerName");
        excludeColumnNames.add("customerContact");
        excludeColumnNames.add("customerAddress");
        excludeColumnNames.add("customerEnterpriseCode");
        excludeColumnNames.add("customerLegalPerson");
        excludeColumnNames.add("customerIdCard");
        excludeColumnNames.add("ownerName");
        excludeColumnNames.add("ownerContact");
        excludeColumnNames.add("ownerIdCard");
        excludeColumnNames.add("guaranteeRealty");
    }

    @ApiOperation("导出发票账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/exportReceipt")
    public void exportReceiptBill(HttpServletResponse response, RealtyBillSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RealtyBillPageResult<RealtyBillVo> pageResult = billService.listReceiptBill(searchVo);
        List<RealtyBillReceiptExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (RealtyBillVo item : pageResult.getDataList()) {
                RealtyBillReceiptExportVo exportItem = receiptConvertor.toV(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumRealtyBillReceiptExport(pageResult));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_BILL_RECEIPT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyBillReceiptExportVo.class)
                .sheet(SenoxConst.Export.SHEET_REALTY_BILL_RECEIPT).doWrite(exportList);
    }

    @ApiOperation("导出账单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/exportDetail")
    public void exportBillWithDetail(HttpServletResponse response, RealtyBillSearchVo searchVo, String title) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        title = StringUtils.isBlank(title) ? SenoxConst.Export.FILE_PAYING_BILL : title;
        boolean isPayingExport = Objects.equals(title, SenoxConst.Export.FILE_PAYING_BILL);

        // list
        RealtyBillPageResult<RealtyBillVo> pageResult = billService.listBillPageWithDetail(searchVo);
        List<RealtyBillDetailExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (RealtyBillVo item : pageResult.getDataList()) {
                RealtyBillDetailExportVo exportItem = realtyBill2DetailExportVo(item, isPayingExport);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // sum
        exportList.add(newRealtyBillDetailSumExportVo(pageResult, isPayingExport));

        // export
        String fileName = title.concat("_").concat(LocalDate.now().toString()).concat(".xlsx");
        String sheetName = String.format(title, searchVo.getBillYear(), searchVo.getBillMonth());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyBillDetailExportVo.class)
                .sheet(sheetName).doWrite(exportList);
    }

    @ApiOperation("月度账单微信通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/wechatNotify")
    public void notifyWechatMonthly(@RequestParam Integer year, @RequestParam Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            throw new InvalidParameterException("无效的参数");
        }
        billService.notifyWechatUserMonthlyBill(year, month);
    }

    @ApiOperation("应收账单水电明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/we/{billId}")
    public List<RealtyBillWeVo> listRealtyBillWeDetail(@PathVariable Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new InvalidParameterException("无效得账单id");
        }
        return billService.listRealtyBillWeDetail(billId);
    }

    @ApiOperation("物业账单水电列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/list")
    public PageResult<RealtyBillWeVo> listRealtyWeBill(@RequestBody RealtyBillWeSearchVo search) {
        return billService.listRealtyWeBill(search);
    }

    /**
     * 物业账单赚打印信息对象
     * @param bill
     * @return
     */
    private TollPrintVo realtyBill2TollPrintVo(RealtyBillVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getBillSerial());
        result.setPayer(bill.getRealtyOwnerName());
        result.setPayerDesc(bill.getRealtyName());
        result.setTollMan(bill.getTollMan());
        result.setTollTime(bill.getPaidTime());
        result.setTotalAmount(bill.getPaidAmount());

        // 费项明细
        List<TollPrintItemVo> list = Arrays.asList(
                newTollPrintItem(bill, RealtyFee.MANAGE), newTollPrintItem(bill, RealtyFee.RENT),
                newTollPrintItem(bill, RealtyFee.WATER), newTollPrintItem(bill, RealtyFee.ELECTRIC),
                newTollPrintItem(bill, RealtyFee.PENALTY)
        );
        result.setDetails(list.stream().filter(x -> !Objects.isNull(x)).collect(Collectors.toList()));
        return result;
    }

    private TollPrintVo order2TollPrintVo(OrderProductDetailVo orderProduct) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(orderProduct.getTollSerial());
        result.setPayer(orderProduct.getProductRealtyOwner());
        result.setPayerDesc(orderProduct.getProductRealtyName());
        result.setTollTime(orderProduct.getPaidTime());
        result.setTotalAmount(orderProduct.getAmount());

        result.setDetails(newTollPrintItem(orderProduct.getDetailList(), orderProduct.getProductSerial(), orderProduct.getRemark()));
        return result;
    }

    /**
     * 打印明细项
     * @param bill
     * @param fee
     * @return
     */
    private TollPrintItemVo newTollPrintItem(RealtyBillVo bill, RealtyFee fee) {
        BigDecimal amount = bill.getFeeAmount(fee);
        if (fee == RealtyFee.PENALTY) {
            amount = DecimalUtils.subtract(bill.getPenaltyAmount(), bill.getPenaltyIgnoreAmount());
        }
        if (DecimalUtils.equals(BigDecimal.ZERO, amount)) {
            return null;
        }

        TollPrintItemVo result = new TollPrintItemVo();
        result.setSerial(bill.getRealtySerial());
        result.setFee(fee.getName());
        result.setPrice(amount);
        result.setAmount(amount);

        // 支付方式
        PayWay payWay = PayWay.fromValue(bill.getPayWay());
        result.setRemark(bill.getContractNo());
        if (payWay == PayWay.DRC || payWay == PayWay.BOD) {
            result.setRemark(PRINT_BILL_PAY_ONLINE);
        } else if (payWay == PayWay.WITHHOLD) {
            result.setRemark(PRINT_BILL_PAY_WITHHOLD);
        }
        // 时间端
        if (fee == RealtyFee.ELECTRIC) {
            if (bill.getElectricStartDate() != null && bill.getElectricEndDate() != null) {
                result.setTime(bill.getElectricStartDate().concat("--").concat(bill.getElectricEndDate()));
            }
        } else if (fee == RealtyFee.WATER) {
            if (bill.getWaterStartDate() != null && bill.getWaterEndDate() != null) {
                result.setTime(bill.getWaterStartDate().concat("--").concat(bill.getWaterEndDate()));
            }
        } else {
            result.setTime(bill.getBillYear() + "-" + StringUtils.fixLength(String.valueOf(bill.getBillMonth()), 2, '0'));
        }
        return result;
    }

    private List<TollPrintItemVo> newTollPrintItem(List<OrderItemDetailVo> list, String productSerial, String remark) {
        List<TollPrintItemVo> resultList = new ArrayList<>(list.size());

        OrderItemDetailVo mItem = list.stream().filter(x -> x.getFeeId() == RealtyFee.MANAGE.getFeeId()).findFirst().orElse(null);
        OrderItemDetailVo rItem = list.stream().filter(x -> x.getFeeId() == RealtyFee.RENT.getFeeId()).findFirst().orElse(null);
        OrderItemDetailVo wItem = list.stream().filter(x -> x.getFeeId() == RealtyFee.WATER.getFeeId()).findFirst().orElse(null);
        OrderItemDetailVo eItem = list.stream().filter(x -> x.getFeeId() == RealtyFee.ELECTRIC.getFeeId()).findFirst().orElse(null);
        OrderItemDetailVo pItem = list.stream().filter(x -> x.getFeeId() ==  RealtyFee.PENALTY.getFeeId()).findFirst().orElse(null);

        addTollPrintItem(resultList, mItem, productSerial, RealtyFee.MANAGE, remark);
        addTollPrintItem(resultList, rItem, productSerial, RealtyFee.RENT, remark);
        addTollPrintItem(resultList, wItem, productSerial, RealtyFee.WATER, remark);
        addTollPrintItem(resultList, eItem, productSerial, RealtyFee.ELECTRIC, remark);
        addTollPrintItem(resultList, pItem, productSerial, RealtyFee.PENALTY, remark);
        return resultList;
    }

    private void addTollPrintItem(List<TollPrintItemVo> list, OrderItemDetailVo orderItem, String serial, RealtyFee fee, String remark) {
        if (orderItem == null) {
            return;
        }

        TollPrintItemVo printItem = newTollPrintItem(orderItem, serial, fee);
        if (printItem != null) {
            printItem.setRemark(remark);
            list.add(printItem);
        }
    }

    private TollPrintItemVo newTollPrintItem(OrderItemDetailVo item, String productSerial, RealtyFee fee) {
        if (DecimalUtils.equals(BigDecimal.ZERO, item.getTotalAmount())) {
            return null;
        }

        TollPrintItemVo result = new TollPrintItemVo();
        result.setSerial(productSerial);
        result.setFee(fee.getName());
        result.setPrice(item.getPrice());
        result.setAmount(item.getTotalAmount());
        if (fee == RealtyFee.ELECTRIC || fee == RealtyFee.WATER) {
            result.setTime(item.getAttr1().concat("--").concat(item.getAttr2()));
        }

        if (DecimalUtils.isNegative(item.getTotalAmount())) {
            result.setRemark(SenoxConst.PRINT_BILL_REFUND);
        }
        return result;
    }

    /**
     * 物业账单转导出对象
     * @param bill
     * @return
     */
    private RealtyBillExportVo realtyBill2ExportVo(RealtyBillVo bill) {
        RealtyBillExportVo result = new RealtyBillExportVo();
        result.setContractNo(bill.getContractNo());
        result.setRealtySerial(bill.getRealtySerial());
        result.setRealtyName(bill.getRealtyName());
        result.setRealtyRegion1(bill.getRealtyRegion1());
        result.setRealtyRegion2(bill.getRealtyRegion2());
        result.setRealtyOwnerName(bill.getRealtyOwnerName());
        result.setBillYear(bill.getBillYear());
        result.setBillMonth(bill.getBillMonth());
        result.setTotalAmount(bill.getReceivableAmount());
        result.setPaidAmount(bill.getPaidAmount());
        result.setPaidStillAmount(bill.getPaidStillAmount());
        result.setManageAmount(bill.getManageAmount());
        result.setRentAmount(bill.getRentAmount());
        result.setWaterAmount(bill.getWaterAmount());
        result.setElectricAmount(bill.getElectricAmount());
        result.setPenaltyAmount(bill.getPenaltyAmount());
        result.setPenaltyPaidAmount(bill.getPenaltyPaidAmount());
        result.setRefundAmount(bill.getRefundAmount());
        result.setPaidTime(bill.getPaidTime());
        result.setBackLease(bill.getBackLease());
        result.setReplaceLease(bill.getReplaceLease());
        result.setCollectionLease(bill.getReplaceLease());
        result.setCustomerName(bill.getCustomerName());
        result.setCustomerContact(bill.getCustomerContact());
        result.setCustomerAddress(bill.getCustomerAddress());
        result.setCustomerEnterpriseCode(bill.getCustomerEnterpriseCode());
        result.setCustomerLegalPerson(bill.getCustomerLegalPerson());
        result.setCustomerIdCard(bill.getCustomerIdCard());
        result.setOwnerName(bill.getOwnerName());
        result.setOwnerContact(bill.getOwnerContact());
        result.setOwnerIdCard(bill.getOwnerIdCard());
        result.setGuaranteeRealty(bill.getGuaranteeRealty());

        BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
        result.setPaidStatus(billStatus == null ? StringUtils.EMPTY : billStatus.getValue());
        return result;
    }

    /**
     * 账单合计
     * @param page
     * @return
     */
    private RealtyBillExportVo sumRealtyBillExport(RealtyBillPageResult<RealtyBillVo> page) {
        RealtyBillExportVo result = new RealtyBillExportVo();
        result.setContractNo(COLUMN_SUM);
        result.setPaidStillAmount(page.getPaidStillAmount());
        result.setPaidAmount(page.getPaidAmount());
        result.setRefundAmount(page.getRefundAmount());
        result.setTotalAmount(page.getReceivableAmount());
        return result;
    }

    /**
     * 账单合计
     * @param page
     * @return
     */
    private RealtyBillReceiptExportVo sumRealtyBillReceiptExport(RealtyBillPageResult<RealtyBillVo> page) {
        RealtyBillReceiptExportVo result = new RealtyBillReceiptExportVo();
        result.setRealtySerial(COLUMN_SUM);
        result.setManageAmount(page.getManageAmount());
        result.setRentAmount(page.getRentAmount());
        result.setWaterAmount(page.getWaterAmount());
        result.setElectricAmount(page.getElectricAmount());
        result.setPenaltyPaidAmount(page.getPenaltyPaidAmount());
        result.setPaidAmount(page.getPaidAmount());
        return result;
    }


    /**
     * 物业账单转导出明细对象
     * @param bill
     * @return
     */
    private RealtyBillDetailExportVo realtyBill2DetailExportVo(RealtyBillVo bill, boolean isPayingExport) {
        RealtyBillDetailExportVo result = new RealtyBillDetailExportVo();
        result.setContractNo(bill.getContractNo());
        result.setRealtySerial(bill.getRealtySerial());
        result.setBillYearMonth(bill.getBillYear() + StringUtils.fixLength(String.valueOf(bill.getBillMonth()), 2, '0'));
        result.setRealtyRegion(bill.getRealtyRegion());
        result.setRealtyName(bill.getRealtyName());
        result.setRealtyOwner(bill.getRealtyOwnerName());
        result.setManageAmount(bill.getManageAmount() == null ? BigDecimal.ZERO : bill.getManageAmount());
        result.setRentAmount(bill.getRentAmount() == null ? BigDecimal.ZERO : bill.getRentAmount());
        result.setWaterAmount(bill.getWaterAmount() == null ? BigDecimal.ZERO : bill.getWaterAmount());
        result.setElectricAmount(bill.getElectricAmount() == null ? BigDecimal.ZERO : bill.getElectricAmount());
        result.setPenaltyAmount(bill.getPenaltyAmount() == null ? BigDecimal.ZERO : bill.getPenaltyAmount());
        if (isPayingExport) {
            result.setTotalAmountIgnorePenalty(bill.getPaidStillAmount() == null ? BigDecimal.ZERO : DecimalUtils.subtract(bill.getPaidStillAmount(), bill.getPenaltyAmount()));
            result.setTotalAmount(bill.getPaidStillAmount() == null ? BigDecimal.ZERO : bill.getPaidStillAmount());
        } else {
            result.setTotalAmountIgnorePenalty(bill.getTotalAmountIgnorePenalty() == null ? BigDecimal.ZERO : bill.getTotalAmountIgnorePenalty());
            result.setTotalAmount(bill.getTotalAmount() == null ? BigDecimal.ZERO : bill.getTotalAmount());
        }
        return result;
    }

    /**
     * 明细合计
     * @param page
     * @return
     */
    private RealtyBillDetailExportVo newRealtyBillDetailSumExportVo(RealtyBillPageResult<RealtyBillVo> page, boolean isPayingExport) {
        RealtyBillDetailExportVo result = new RealtyBillDetailExportVo();
        result.setContractNo(COLUMN_SUM);
        result.setManageAmount(page.getManageAmount());
        result.setRentAmount(page.getRentAmount());
        result.setWaterAmount(page.getWaterAmount());
        result.setElectricAmount(page.getElectricAmount());
        result.setPenaltyAmount(page.getPenaltyAmount());
        if (isPayingExport) {
            result.setTotalAmountIgnorePenalty(page.getPaidStillAmount() == null ? BigDecimal.ZERO : page.getPenaltyAmount());
            result.setTotalAmount(page.getPaidStillAmount());
        } else {
            result.setTotalAmountIgnorePenalty(page.getTotalAmountIgnorePenalty() == null ? BigDecimal.ZERO : page.getTotalAmountIgnorePenalty());
            result.setTotalAmount(page.getTotalAmount());
        }
        return result;
    }
}
