package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.RefundWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.RefundOrderVo;
import com.senox.realty.vo.BillTollVo;
import com.senox.realty.vo.OneTimeFeeBillSearchVo;
import com.senox.realty.vo.OneTimeFeeBillTradeSearchVo;
import com.senox.realty.vo.OneTimeFeeBillTradeVo;
import com.senox.realty.vo.OneTimeFeeBillVo;
import com.senox.realty.vo.OneTimeFeeSearchVo;
import com.senox.realty.vo.OneTimeFeeVo;
import com.senox.realty.vo.RefundBillPageResult;
import com.senox.realty.vo.TollSerialVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.OneTimeFeeBillExportConverter;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.OneTimeFeeService;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.OneTimeFeeBillExportVo;
import com.senox.web.vo.OneTimeFeeBillTradeExportVo;
import com.senox.web.vo.TollPrintItemVo;
import com.senox.web.vo.TollPrintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 15:36
 */
@Api(tags = "一次性收入费项")
@RestController
@RequestMapping("/web/oneTimeFee")
public class OneTimeFeeController extends BaseController {

    @Autowired
    private OneTimeFeeService oneTimeFeeService;
    @Autowired
    private AdminUserService adminUserService;

    @ApiOperation("添加一次性收入费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addFee(@Validated({Add.class}) @RequestBody OneTimeFeeVo fee) {
        return oneTimeFeeService.addFee(fee);
    }

    @ApiOperation("更新一次性收入费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateFee(@Validated({Update.class}) @RequestBody OneTimeFeeVo fee) {
        if (fee.getId() < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.updateFee(fee);
    }

    @ApiOperation("删除一次性收入费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteFee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.deleteFee(id);
    }

    @ApiOperation("获取一次性收入费项详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public OneTimeFeeVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return oneTimeFeeService.findFeeById(id);
    }

    @ApiOperation("一次性收入费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<OneTimeFeeVo> listOneTimeFee(@RequestBody OneTimeFeeSearchVo search) {
        return oneTimeFeeService.listFee(search);
    }

    @ApiOperation("授权的一次性收入费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/authorizedList")
    public List<OneTimeFeeVo> listAuthorizedOneTimeFee() {
        return oneTimeFeeService.listUserAuthorizedFee(getAdminUser());
    }

    @ApiOperation("添加一次性收入账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/add")
    public Long addFeeBill(@Validated({Add.class}) @RequestBody OneTimeFeeBillVo bill) {
        return oneTimeFeeService.addFeeBill(bill);
    }

    @ApiOperation("更新一次性收入账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/update")
    public void updateFeeBill(@Validated({Update.class}) @RequestBody OneTimeFeeBillVo bill) {
        if (bill.getId() < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.updateFeeBill(bill);
    }

    @ApiOperation("删除一次性收入账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/delete/{id}")
    public void deleteFeeBill(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.deleteFeeBill(id);
    }

    @ApiOperation("一次性收入账单支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/pay/{id}")
    public void payFeeBill(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        toll.setOperator(getAdminUserId());
        oneTimeFeeService.payFeeBill(id, toll);
    }

    @ApiOperation("一次性收入账单支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/V2/bill/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return oneTimeFeeService.payBill(payRequest);
    }


    @ApiOperation("撤销一次性收入账单支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/payRevoke/{id}")
    public void revokeFeeBillPayment(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.revokeFeeBillPayment(id);
    }

    @ApiOperation("一次性收入账单退费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/refund/{id}")
    public void refundFeeBill(HttpServletRequest request, @PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        RefundOrderVo refundOrder = new RefundOrderVo();
        refundOrder.setTollMan(getAdminUserId());
        refundOrder.setRequestIp(RequestUtils.getIpAddr(request));
        refundOrder.setRefundWay(RefundWay.CASH);
        oneTimeFeeService.refundFeeBill(id, refundOrder);
    }

    @ApiOperation("撤销一次性收入账单退费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/refundRevoke/{id}")
    public void revokeFeeBillRefund(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        oneTimeFeeService.revokeFeeBillRefund(id);
    }

    @ApiOperation("获取一次性收入账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/get/{id}")
    public OneTimeFeeBillVo findFeeBillById(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return oneTimeFeeService.findById(id);
    }

    @ApiOperation("打印一次性收入票据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/bill/print/{id}")
    public TollPrintVo printFeeBill(@PathVariable Long id,
                                    @RequestParam(required = false) Boolean refund,
                                    @RequestParam(required = false) Boolean refreshSerial) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }

        // 账单信息
        OneTimeFeeBillTradeVo bill = oneTimeFeeService.findDetailById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        BillStatus billStatus = BillStatus.fromValue(bill.getStatus());
        if (BooleanUtils.isTrue(refund)) {
            if (billStatus != BillStatus.REFUND) {
                throw new BusinessException("账单未退费");
            }
        } else {
            if (billStatus == null || billStatus == BillStatus.INIT) {
                throw  new BusinessException("账单未缴费");
            }
        }

        // 票据号
        boolean newSerial = false;
        String serial = BooleanUtils.isTrue(refund) ? bill.getRefundSerial() : bill.getTollSerial();
        if (StringUtils.isBlank(serial) || BooleanUtils.isTrue(refreshSerial)) {
            newSerial = true;
            serial = adminUserService.getAndIncAdminTollSerial();
        }

        // 打印信息
        TollPrintVo result = bill2TollPrintVo(bill, serial, BooleanUtils.isTrue(refund));
        if (newSerial) {
            TollSerialVo billSerial = new TollSerialVo();
            billSerial.setId(id);
            billSerial.setSerial(serial);
            billSerial.setRefund(BooleanUtils.isTrue(refund));
            oneTimeFeeService.updateFeeBillSerial(billSerial);
        }
        return result;
    }

    @ApiOperation("一次性收入账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/list")
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listFeeBill(@RequestBody OneTimeFeeBillSearchVo search) {
        return oneTimeFeeService.listFeeBill(search);
    }

    @ApiOperation("导出一次性收入账单列表")
    @GetMapping("/bill/export")
    public void exportFeeBill(HttpServletResponse response, OneTimeFeeBillSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RefundBillPageResult<OneTimeFeeBillTradeVo> pageResult = oneTimeFeeService.listFeeBill(search);
        List<OneTimeFeeBillExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (OneTimeFeeBillTradeVo item : pageResult.getDataList()) {
                OneTimeFeeBillExportVo exportItem = OneTimeFeeBillExportConverter.INSTANCE.toV(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
            exportList.add(sumFeeBill(pageResult));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_ONE_TIME_FEE, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), OneTimeFeeBillExportVo.class)
                .sheet(SenoxConst.Export.SHEET_ONE_TIME_FEE)
                .doWrite(exportList);

    }

    @ApiOperation("一次性收入账单交易列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bill/tradeList")
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listFeeBillTrade(@RequestBody OneTimeFeeBillTradeSearchVo search) {
        return oneTimeFeeService.listFeeBillTrade(search);
    }

    @ApiOperation("导出一次性收入账单交易列表")
    @GetMapping("/bill/exportTrade")
    public void exportFeeBillTrade(HttpServletResponse response, OneTimeFeeBillTradeSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        RefundBillPageResult<OneTimeFeeBillTradeVo> pageResult = oneTimeFeeService.listFeeBillTrade(search);
        List<OneTimeFeeBillTradeExportVo> exportList = new ArrayList<>(pageResult.getTotalSize());
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (OneTimeFeeBillTradeVo item : pageResult.getDataList()) {
                OneTimeFeeBillTradeExportVo exportItem = oneTimeFeeBillTrade2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
            exportList.add(sumFeeBillTrade(pageResult));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_ONE_TIME_FEE_TOLL, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), OneTimeFeeBillTradeExportVo.class)
                .sheet(SenoxConst.Export.SHEET_ONE_TIME_FEE_TOLL)
                .doWrite(exportList);
    }

    /**
     * 账单转打印票据信息
     * @param bill
     * @param serial
     * @param refund
     * @return
     */
    private TollPrintVo bill2TollPrintVo(OneTimeFeeBillTradeVo bill, String serial, boolean refund) {
        TollPrintVo result = new TollPrintVo();
        result.setNotifySerial(bill.getBillNo());
        result.setPayer(bill.getCustomer());
        result.setPayer2(bill.getRealtyName());
        result.setBillSerial(serial);
        result.setTollUnit(bill.getDepartment());

        if (BooleanUtils.isTrue(refund)) {
            result.setTotalAmount(bill.getRefundAmount());
            result.setTollTime(bill.getRefundTime());
        } else {
            result.setTotalAmount(bill.getAmount());
            result.setTollTime(bill.getTollTime());
        }

        // 账单明细
        TollPrintItemVo printItem = new TollPrintItemVo();
        printItem.setFee(bill.getFee());
        printItem.setTime(DateUtils.formatYearMonth(bill.getBillYear(), bill.getBillMonth(), DateUtils.PATTERN_YEAR_MONTH));
        printItem.setPrice(refund ? bill.getRefundAmount() : bill.getAmount());
        printItem.setAmount(refund ? bill.getRefundAmount() : bill.getAmount());
        printItem.setRemark(bill.getRemark());
        result.setDetails(Collections.singletonList(printItem));

        return result;
    }

    /**
     * 账单交易转导出对象
     * @param trade
     * @return
     */
    private OneTimeFeeBillTradeExportVo oneTimeFeeBillTrade2ExportVo(OneTimeFeeBillTradeVo trade) {
        OneTimeFeeBillTradeExportVo result = new OneTimeFeeBillTradeExportVo();
        result.setTollDate(trade.getTollTime() == null ? null : trade.getTollTime().toLocalDate());
        result.setPayWay(trade.getPayWay());
        result.setCustomer(trade.getCustomer());
        result.setTollSerial(trade.getTollSerial());
        result.setBillNo(trade.getBillNo());
        result.setAmount(trade.getAmount());
        result.setFee(trade.getFee());
        result.setCreator(trade.getCreator());
        result.setTollMan(trade.getTollMan());
        result.setDepartment(trade.getDepartment());
        result.setRealtySerial(trade.getRealtySerial());
        result.setRealtyName(trade.getRealtyName());
        result.setRefundAmount(trade.getRefundAmount());
        result.setTotalAmount(trade.getTotalAmount());
        result.setRefundMan(trade.getRefundMan());
        result.setRefundTime(trade.getRefundTime());
        result.setRefundSerial(trade.getRefundSerial());
        result.setRemark(trade.getRemark());
        return result;
    }

    /**
     * 账单合计
     * @param page
     * @return
     */
    private OneTimeFeeBillExportVo sumFeeBill(RefundBillPageResult<OneTimeFeeBillTradeVo> page) {
        OneTimeFeeBillExportVo result = new OneTimeFeeBillExportVo();
        result.setCustomer(SenoxConst.Export.COLUMN_SUM);
        result.setAmount(page.getAmount());
        result.setRefundAmount(page.getRefundAmount());
        return result;
    }

    /**
     * 账单合计
     * @param page
     * @return
     */
    private OneTimeFeeBillTradeExportVo sumFeeBillTrade(RefundBillPageResult<OneTimeFeeBillTradeVo> page) {
        OneTimeFeeBillTradeExportVo result = new OneTimeFeeBillTradeExportVo();
        result.setCustomer(SenoxConst.Export.COLUMN_SUM);
        result.setAmount(page.getAmount());
        result.setRefundAmount(page.getRefundAmount());
        result.setTotalAmount(page.getTotalAmount());
        return result;
    }
}
