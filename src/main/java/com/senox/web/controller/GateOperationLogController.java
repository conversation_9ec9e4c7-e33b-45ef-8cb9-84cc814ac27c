package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.GateOperationLogSearchVo;
import com.senox.dm.vo.GateOperationLogVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.GateOperationConvertor;
import com.senox.web.service.GateOperationLogService;
import com.senox.web.vo.GateOperationLogExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:07
 */
@Api(tags = "海康门禁设备操作日志")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/control/operation/log")
public class GateOperationLogController extends BaseController {

    private final GateOperationLogService operationLogService;
    private final GateOperationConvertor operationConvertor;


    @ApiOperation("设置日志误报")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/markFalse")
    public void markFalseAlarm(@RequestParam Long id, @RequestParam Boolean marked) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        operationLogService.markOperationFalse(id, marked);
    }

    @ApiOperation("门禁操作日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<GateOperationLogVo> listOperationLogPage(@RequestBody GateOperationLogSearchVo search) {
        return operationLogService.listOperationLogPage(search);
    }


    @ApiOperation("导出门禁操作日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportOperationLog(HttpServletResponse response, GateOperationLogSearchVo search) throws IOException {
        // list
        List<GateOperationLogVo> list = operationLogService.listOperationLog(search);

        int index = 1;
        List<GateOperationLogExportVo> exportList = new ArrayList<>(list.size());
        for (GateOperationLogVo item : list) {
            GateOperationLogExportVo exportItem = operationConvertor.toLogExportVo(item);
            exportItem.setSerial(index++);
            exportList.add(exportItem);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_GATE_OPERATION_LOGS, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), GateOperationLogExportVo.class)
                .sheet(SenoxConst.Export.SHEET_GATE_OPERATION_LOGS)
                .doWrite(exportList);
    }
}
