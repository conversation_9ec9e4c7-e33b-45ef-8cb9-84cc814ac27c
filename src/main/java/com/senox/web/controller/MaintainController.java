package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.MaintainChargeTollVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.realty.constant.MaintainChargeStatus;
import com.senox.realty.constant.MaintainFee;
import com.senox.realty.constant.MaintainOrderStatus;
import com.senox.realty.constant.MaintainType;
import com.senox.realty.vo.*;
import com.senox.web.config.ExcelFillCellMergeStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.MaintainService;
import com.senox.web.service.OrderService;
import com.senox.web.utils.ReportExcelStyle;
import com.senox.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.senox.web.constant.SenoxConst.Export.COLUMN_SUM;

/**
 * <AUTHOR>
 * @date 2023/3/30 16:44
 */
@Api(tags = "维修单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/maintain")
public class MaintainController extends BaseController {


    private final MaintainService maintainService;

    private final OrderService orderService;

    private final AdminUserService adminUserService;

    private static final int MATERIAL_TOTAL_COLUMNS = 10;


    @ApiOperation("添加维修单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/save")
    public void addMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo) {
        maintainService.addMaintainOrder(maintainOrderVo);
    }

    @ApiOperation("修改维修单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/update")
    public void updateMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo) {
        maintainService.updateMaintainOrder(maintainOrderVo);
    }

    @ApiOperation("查询维修单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/get/{id}")
    public MaintainOrderVo findMaintainOrder(@PathVariable Long id, @RequestParam Boolean media) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return maintainService.findMaintainOrder(id, media);
    }

    @ApiOperation("维修单列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/list")
    public PageResult<MaintainOrderVo> listMaintainOrder(@RequestBody MaintainOrderSearchVo search) {
        return maintainService.listMaintainOrder(search);
    }

    @ApiOperation("导出维修单")
    @GetMapping("/order/export")
    public void exportMaintainOrder(HttpServletResponse response, MaintainOrderSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<MaintainOrderVo> orderVoList = maintainService.exportListMaintainOrder(searchVo);
        List<MaintainOrderExportVo> exportVoList = new ArrayList<>(orderVoList.size());

        if (!CollectionUtils.isEmpty(orderVoList)) {
            for (int index = 0; index < orderVoList.size(); index++) {
                MaintainOrderVo orderVo = orderVoList.get(index);
                MaintainOrderExportVo exportVo = newMaintainOrderExportVo(orderVo);
                exportVo.setSerial(index + 1);
                exportVoList.add(exportVo);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_MAINTAIN_ORDER_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MaintainOrderExportVo.class)
                .sheet(SenoxConst.Export.SHEET_MAINTAIN_ORDER_REPORT)
                .doWrite(exportVoList);
    }

    private MaintainOrderExportVo newMaintainOrderExportVo(MaintainOrderVo orderVo) {
        MaintainOrderExportVo exportVo = new MaintainOrderExportVo();
        exportVo.setCustomerName(orderVo.getCustomerName());
        exportVo.setCreateTime(orderVo.getCreateTime());
        exportVo.setMaintainType(MaintainType.fromValue(orderVo.getMaintainType()).getDescription());
        exportVo.setModifiedTime(orderVo.getModifiedTime());
        exportVo.setRegionName(orderVo.getRegionName());
        exportVo.setStreetName(orderVo.getStreetName());
        exportVo.setAddress(orderVo.getAddress());
        exportVo.setContact(orderVo.getContact());
        exportVo.setProblem(orderVo.getProblem());
        exportVo.setHandlerName(orderVo.getHandlerName());
        exportVo.setStatus(MaintainOrderStatus.fromValue(orderVo.getStatus()).getDescription());
        return exportVo;
    }

    @ApiOperation("删除维修单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/delete/{id}")
    public void deleteMaintainOrder(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        maintainService.deleteMaintainOrder(id);
    }

    @ApiOperation("维修单费用统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/statistic/page")
    public PageStatisticsResult<MaintainOrderStatisticVo, MaintainOrderStatisticVo> pageOrderStatistic(@RequestBody MaintainOrderSearchVo searchVo) {
        PageResult<MaintainOrderStatisticVo> result = maintainService.pageOrderStatistic(searchVo);
        MaintainOrderStatisticVo statisticVo = maintainService.sumOrderStatistic(searchVo);
        return new PageStatisticsResult<>(result, statisticVo);
    }

    @ApiOperation("导出维修单费用")
    @GetMapping("/order/statistic/export")
    public void exportOrderStatistic(HttpServletResponse response, MaintainOrderSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<MaintainOrderStatisticVo> dataList = maintainService.pageOrderStatistic(searchVo).getDataList();
        MaintainOrderStatisticVo sumOrderStatistic = maintainService.sumOrderStatistic(searchVo);
        List<MaintainOrderStatisticExportVo> exportVos = new ArrayList<>(dataList.size() + 1);

        if (!CollectionUtils.isEmpty(dataList)) {
            for (int index = 0; index < dataList.size(); index++) {
                MaintainOrderStatisticVo statisticVo = dataList.get(index);
                MaintainOrderStatisticExportVo exportVo = newMaintainOrderStatisticExportVo(statisticVo);
                exportVo.setSerial(index + 1);
                exportVos.add(exportVo);
            }
            exportVos.add(sumMaintainOrderStatisticExportVo(sumOrderStatistic));
        }


        String fileName = String.format(SenoxConst.Export.MAINTAIN_ORDER_STATISTIC_INFO, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MaintainOrderStatisticExportVo.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(12))
                .registerWriteHandler(ReportExcelStyle.cellBorder())
                .sheet(SenoxConst.Export.MAINTAIN_ORDER_STATISTIC_SHEET)
                .doWrite(exportVos);
    }

    private MaintainOrderStatisticExportVo sumMaintainOrderStatisticExportVo(MaintainOrderStatisticVo statisticVo) {
        MaintainOrderStatisticExportVo exportVo = new MaintainOrderStatisticExportVo();
        exportVo.setOrderNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setCostPrice(statisticVo.getCostPrice());
        exportVo.setLaborAmount(statisticVo.getLaborAmount());
        exportVo.setIncomeAmount(statisticVo.getIncomeAmount());
        exportVo.setTotalAmount(statisticVo.getTotalAmount());
        return exportVo;
    }

    private MaintainOrderStatisticExportVo newMaintainOrderStatisticExportVo(MaintainOrderStatisticVo statisticVo) {
        MaintainOrderStatisticExportVo exportVo = new MaintainOrderStatisticExportVo();
        exportVo.setOrderNo(statisticVo.getOrderNo());
        exportVo.setMaintainType(MaintainType.fromValue(statisticVo.getMaintainType()).getDescription());
        exportVo.setStatus(MaintainOrderStatus.fromValue(statisticVo.getStatus()).getDescription());
        exportVo.setCustomerName(statisticVo.getCustomerName());
        exportVo.setContact(statisticVo.getContact());
        exportVo.setCreateTime(statisticVo.getCreateTime());
        exportVo.setProblem(statisticVo.getProblem());
        exportVo.setPayStatus(statisticVo.getPayStatus() == 1 ? "已支付" : "未支付");
        exportVo.setCostPrice(statisticVo.getCostPrice());
        exportVo.setLaborAmount(statisticVo.getLaborAmount());
        exportVo.setIncomeAmount(statisticVo.getIncomeAmount());
        exportVo.setTotalAmount(statisticVo.getTotalAmount());
        return exportVo;
    }

    @ApiOperation("维修单评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/evaluate")
    public void evaluateOrder(@RequestBody MaintainOrderEvaluateVo evaluateVo) {
        maintainService.evaluateOrder(evaluateVo);
    }

    @ApiOperation("重置维修单评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/order/evaluate/reset/{orderId}")
    public void resetEvaluate(@PathVariable Long orderId) {
        maintainService.resetEvaluate(orderId);
    }

    @ApiOperation("维修单评价分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/order/evaluate/page")
    public PageResult<MaintainOrderVo> evaluateOrderPage(@RequestBody MaintainOrderEvaluateSearchVo searchVo) {
        return maintainService.evaluateOrderPage(searchVo);
    }

    @ApiOperation("添加派工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/job/save")
    public void addMaintainJob(@RequestBody MaintainJobVo maintainJobVo) {
        maintainService.addMaintainJob(maintainJobVo);
    }

    @ApiOperation("修改派工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/job/update")
    public void updateMaintainJob(@RequestBody MaintainJobVo maintainJobVo) {
        maintainService.updateMaintainJob(maintainJobVo);
    }

    @ApiOperation("修改派工人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/job/item/update")
    public void updateMaintainJobItem(@RequestBody MaintainJobItemVo maintainJobItemVo) {
        maintainService.updateMaintainJobItem(maintainJobItemVo);
    }

    @ApiOperation("获取派工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/job/get/{id}")
    public MaintainJobVo findMaintainJob(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return maintainService.findMaintainJob(id);
    }

    @ApiOperation("查询派工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/job/dispatch/{orderId}")
    public List<MaintainJobVo> listDispatchJobByOrderId(@PathVariable Long orderId) {
        if (orderId < 1L) {
            throw new InvalidParameterException("无效的orderId");
        }
        return maintainService.listDispatchJobByOrderId(orderId);
    }

    @ApiOperation("查询派工单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/job/dispatch/list")
    public PageResult<MaintainDispatchJobVo> listDispatchJob(@RequestBody MaintainJobSearchVo searchVo) {
        return maintainService.listDispatchJob(searchVo);
    }

    @ApiOperation("根据派工子单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/job/getByItem/{itemId}")
    public MaintainDispatchJobVo findDispatchByJobItemId(@PathVariable Long itemId) {
        if (itemId < 1L) {
            throw new InvalidParameterException("无效的itemId");
        }
        return maintainService.findDispatchByJobItemId(itemId);
    }

    @ApiOperation("删除派工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/job/delete/{jobId}")
    public void deleteMaintainJob(@PathVariable Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new InvalidParameterException();
        }
        maintainService.deleteMaintainJob(jobId);
    }

    @ApiOperation("添加维修所需物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/save")
    public void saveMaintainMaterial(@RequestBody MaintainMaterialVo materialVo) {
        maintainService.saveMaintainMaterial(materialVo);
    }

    @ApiOperation("删除维修物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/material/delete/{id}")
    public void deleteMaintainMaterial(@PathVariable Long id) {
        maintainService.deleteMaintainMaterial(id);
    }

    @ApiOperation("批量删除物料及明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/batch/delete")
    public void batchDeleteMaterial(@RequestBody List<Long> ids) {
        maintainService.batchDeleteMaterial(ids);
    }

    @ApiOperation("物料列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/list")
    public PageResult<MaintainMaterialDataVo> listMaintainMaterial(@RequestBody MaintainMaterialSearchVo search) {
        return maintainService.listMaintainMaterial(search);
    }

    @ApiOperation("物料列表导出")
    @GetMapping("/material/export")
    public void exportMaintainMaterial(HttpServletResponse response, MaintainMaterialSearchVo searchVo) throws IOException {
        searchVo.setPage(false);
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<MaintainMaterialDataVo> dataList = maintainService.listMaintainMaterial(searchVo).getDataList();
        List<MaintainMaterialExportVo> resultList = new ArrayList<>(dataList.size());
        int serialNo = 1;
        for (MaintainMaterialDataVo materialVo : dataList) {
            List<MaintainMaterialExportVo> exportVoList = materialToExportVo(materialVo);
            for (MaintainMaterialExportVo exportVo : exportVoList) {
                exportVo.setSerialNo(serialNo);
            }
            resultList.addAll(exportVoList);
            serialNo++;
        }

        // 文件名
        String fileName = String.format(SenoxConst.Export.FILE_MAINTAIN_MATERIAL,
                DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
        excelWriter.write(resultList, buildMaterialSheet(dataList));

        // 关闭流
        excelWriter.finish();
    }


    /**
     * 物料转导出对象
     * @param materialVo
     * @return
     */
    private List<MaintainMaterialExportVo> materialToExportVo(MaintainMaterialDataVo materialVo) {
        List<MaintainMaterialItemVo> materialItemVos = materialVo.getMaterialItemVos();
        List<MaintainMaterialExportVo> exportVoList = null;
        if (!CollectionUtils.isEmpty(materialItemVos)) {
            exportVoList = new ArrayList<>();
            for (MaintainMaterialItemVo itemVo : materialItemVos) {
                MaintainMaterialExportVo exportVo = toExportVo(materialVo);
                exportVo.setMaterialCode(itemVo.getMaterialCode());
                exportVo.setMaterialName(itemVo.getMaterialName());
                exportVo.setPrice(itemVo.getPrice());
                exportVo.setQuantity(itemVo.getQuantity());
                exportVo.setAmount(itemVo.getAmount());
                exportVoList.add(exportVo);
            }
        } else {
            MaintainMaterialExportVo exportVo = toExportVo(materialVo);
            exportVoList = Collections.singletonList(exportVo);
        }

        return exportVoList;
    }

    private MaintainMaterialExportVo toExportVo(MaintainMaterialDataVo materialVo) {
        MaintainMaterialExportVo materialExportVo = new MaintainMaterialExportVo();
        if (materialVo.getChargeYear() != null && materialVo.getChargeMonth() != null) {
            materialExportVo.setChargeDate(materialVo.getChargeYear().toString().concat("-").concat(materialVo.getChargeMonth().toString()));
        }
        materialExportVo.setJobNo(materialVo.getJobNo());
        materialExportVo.setMaintainType(MaintainType.fromValue(materialVo.getMaintainType()).getDescription());
        materialExportVo.setOutNo(materialVo.getOutNo());
        materialExportVo.setHandlerDeptName(materialVo.getHandlerDeptName());
        materialExportVo.setPaidStatus(materialVo.getStatus() == 0 ? "未支付" : "已支付");
        materialExportVo.setPickingStatus(materialVo.getOutNo() != null ? "已领料" : "未领料");
        materialExportVo.setTotalAmount(materialVo.getTotalAmount());
        materialExportVo.setRemark(materialVo.getRemark());
        materialExportVo.setReceivePerson(materialVo.getReceivePerson());
        return materialExportVo;
    }

    /**
     * 物料sheet合并
     * @param dataList
     * @return
     */
    private WriteSheet buildMaterialSheet(List<MaintainMaterialDataVo> dataList) {
        ExcelWriterSheetBuilder builder = EasyExcelFactory.writerSheet(0, SenoxConst.Export.SHEET_MAINTAIN_MATERIAL).head(MaintainMaterialExportVo.class);
        int rowIndex = 0;
        for (MaintainMaterialDataVo materialDataVo : dataList) {
            // 当前行
            rowIndex += 1;
            if (!CollectionUtils.isEmpty(materialDataVo.getMaterialItemVos()) && materialDataVo.getMaterialItemVos().size() > 1) {
                int lastIndex = rowIndex + materialDataVo.getMaterialItemVos().size() - 1;
                for (int columnIndex = 0; columnIndex < MATERIAL_TOTAL_COLUMNS; columnIndex++) {
                    builder.registerWriteHandler(new ExcelFillCellMergeStrategy(rowIndex, lastIndex, columnIndex, columnIndex));
                }

                rowIndex = lastIndex;
            }
        }
        return builder.build();
    }

    @ApiOperation("更新出库单号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/outNo/save")
    public void saveOutNo(@Validated @RequestBody MaintainMaterialOutNoVo outNoVo) {
        maintainService.saveOutNo(outNoVo);
    }

    @ApiOperation("撤销出库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/material/revoke/{outNo}")
    public void cancelOutBound(@PathVariable String outNo) {
        maintainService.cancelOutBound(outNo);
    }

    @ApiOperation("查询订单维修所需要物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/material/list/{orderId}")
    public List<MaintainMaterialItemVo> listMaterialByOrderIdAndJobId(@PathVariable Long orderId, @RequestParam(required = false) Long jobId) {
        if (orderId < 1L) {
            throw new InvalidParameterException("无效的orderId");
        }
        return maintainService.listMaterialByOrderIdAndJobId(orderId, jobId);
    }

    @ApiOperation("更新维修物料单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/update")
    public void updateMaintainMaterial(@RequestBody MaintainMaterialVo materialVo) {
        if (!WrapperClassUtils.biggerThanLong(materialVo.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        maintainService.updateMaintainMaterial(materialVo);
    }

    @ApiOperation("批量添加维修所需物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/material/batch/add")
    public void batchSaveMaterial(@RequestBody List<MaintainMaterialItemVo> materialItemVos) {
        maintainService.batchSaveMaterial(materialItemVos);
    }

    @ApiOperation("添加维修收费帐单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/add")
    public void addMaintainCharge(@Validated({Add.class}) @RequestBody MaintainChargeVo chargeVo) {
        maintainService.addMaintainCharge(chargeVo);
    }

    @ApiOperation("维修收费账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/list")
    public MaintainChargePageResult<MaintainChargeDataVo> listMaintainCharge(@RequestBody MaintainChargeSearchVo searchVo) {
        return maintainService.listMaintainCharge(searchVo);
    }

    @ApiOperation("删除维修账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/delete/{id}")
    public void deleteMaintainCharge(@PathVariable Long id) {
        maintainService.deleteMaintainCharge(id);
    }

    @ApiOperation("删除维修账单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/item/delete/{id}")
    public void deleteMaintainChargeItem(@PathVariable Long id) {
        maintainService.deleteMaintainChargeItem(id);
    }

    @ApiOperation("维修收费账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/get/{id}")
    public MaintainChargeDataVo chargeDataVoById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainService.chargeDataVoById(id);
    }

    @ApiOperation("根据订单号查询维修收费账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/getByOrderId/{orderId}")
    public List<MaintainChargeDataVo> listChargeDataVoByOrderId(@PathVariable Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainService.listChargeDataVoByOrderId(orderId);
    }

    @ApiOperation("根据收费单id查询物维收费单费项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/get/chargeId/{chargeId}")
    public List<MaintainChargeItemVo> chargeItemList(@PathVariable Long chargeId) {
        if (chargeId < 1L) {
            throw new InvalidParameterException("无效的收费单id");
        }
        return maintainService.chargeItemList(chargeId);
    }

    @ApiOperation("根据派工id查询物维收费单集合")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/get/jobId/{jobId}")
    public List<MaintainChargeItemVo> listChargeItemByJobId(@PathVariable Long jobId) {
        if (jobId < 1L) {
            throw new InvalidParameterException("无效的派工单id");
        }
        return maintainService.listChargeItemByJobId(jobId);
    }

    @ApiOperation("根据订单id查询物维收费单集合")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/get/orderId/{orderId}")
    public List<MaintainChargeItemVo> listChargeItemByOrderId(@PathVariable Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainService.listChargeItemByOrderId(orderId);
    }

    @ApiOperation("根据id物维收费单集合")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/listByIds")
    public List<MaintainChargeVo> listMaintainChargeByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        return maintainService.listMaintainChargeByIds(ids);
    }

    @ApiOperation("批量添加维修所需账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/batch/add")
    public void batchSaveCharge(@RequestBody List<MaintainChargeItemVo> chargeItemVos) {
        maintainService.batchSaveCharge(chargeItemVos);
    }

    @ApiOperation("支付物维收费账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/charge/pay")
    public OrderResultVo payCharge(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return maintainService.payCharge(payRequest);
    }

    @ApiOperation("导出账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/export")
    public void exportCharge(HttpServletResponse response, MaintainChargeSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        MaintainChargePageResult<MaintainChargeDataVo> pageResult = maintainService.listMaintainCharge(searchVo);
        List<MaintainChargeExportVo> exportList = new ArrayList<>(pageResult.getTotalSize() + 1);
        if (!CollectionUtils.isEmpty(pageResult.getDataList())) {
            int serial = 1;
            for (MaintainChargeDataVo item : pageResult.getDataList()) {
                MaintainChargeExportVo exportItem = maintainCharge2ExportVo(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }
        exportList.add(sumMaintainChargeExport(pageResult));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_MAINTAIN_CHARGE, LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_MAINTAIN_CHARGE);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), MaintainChargeExportVo.class)
                .sheet(sheetName).doWrite(exportList);
    }

    @ApiOperation("打印物维收费单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/charge/print/{id}")
    public MaintainChargeTollPrintVo printCharge(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        MaintainChargeTollVo maintainChargeTollVo = orderService.maintainChargeTollById(id);
        MaintainChargeDataVo chargeDataVo = maintainService.chargeDataVoById(id);
        if (chargeDataVo == null || maintainChargeTollVo == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (chargeDataVo.getStatus() != MaintainChargeStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }
        MaintainChargeTollPrintVo printVo = maintainCharge2TollPrintVo(maintainChargeTollVo, chargeDataVo);
        if (BooleanUtils.isTrue(refreshSerial)) {
            printVo.setChargeSerial(adminUserService.getAndIncAdminTollSerial());

            // 更新账单票据号
            MaintainChargeSerialVo tollSerial = new MaintainChargeSerialVo();
            tollSerial.setChargeId(id);
            tollSerial.setChargeSerial(printVo.getChargeSerial());
            tollSerial.setOperatorId(getAdminUserId());
            maintainService.saveChargeSerial(tollSerial);
        }
        return printVo;
    }

    private MaintainChargeTollPrintVo maintainCharge2TollPrintVo(MaintainChargeTollVo maintainChargeTollVo, MaintainChargeDataVo chargeDataVo) {
        MaintainChargeTollPrintVo printVo = new MaintainChargeTollPrintVo();
        printVo.setChargeSerial(maintainChargeTollVo.getTollSerial());
        printVo.setChargeNo(chargeDataVo.getChargeNo());
        printVo.setCustomerName(chargeDataVo.getCustomerName());
        printVo.setTollMan(maintainChargeTollVo.getTollMan());
        printVo.setTollTime(maintainChargeTollVo.getPaidTime());
        printVo.setTotalAmount(maintainChargeTollVo.getTotalAmount());

        List<MaintainChargeItemVo> chargeItemVos = maintainService.chargeItemList(maintainChargeTollVo.getChargeId());
        // 费项明细
        List<MaintainChargeTollPrintItemVo> list = new ArrayList<>(chargeItemVos.size());
        for (MaintainChargeItemVo itemVo : chargeItemVos) {
            list.add(newTollPrintItem(maintainChargeTollVo, chargeDataVo, itemVo, MaintainFee.fromFeeId(itemVo.getFeeId())));
        }
        printVo.setDetails(list.stream().filter(x -> !Objects.isNull(x) && !Objects.isNull(x.getAmount())).collect(Collectors.toList()));
        return printVo;
    }

    private MaintainChargeTollPrintItemVo newTollPrintItem(MaintainChargeTollVo maintainChargeTollVo, MaintainChargeDataVo chargeDataVo, MaintainChargeItemVo itemVo, MaintainFee fee) {
        MaintainChargeTollPrintItemVo printItemVo = new MaintainChargeTollPrintItemVo();
        printItemVo.setFee(fee.getName());
        printItemVo.setRegionName(chargeDataVo.getRegionName());
        printItemVo.setStreetName(chargeDataVo.getStreetName());
        printItemVo.setAddress(chargeDataVo.getAddress());
        printItemVo.setPrice(itemVo.getPrice());
        printItemVo.setQuantity(itemVo.getQuantity());
        printItemVo.setAmount(itemVo.getAmount());
        printItemVo.setFeeItemName(itemVo.getFeeItemName());
        printItemVo.setFeeItemCode(itemVo.getFeeItemCode());
        printItemVo.setTime(StringUtils.buildYearMonthStr(maintainChargeTollVo.getChargeYear(), maintainChargeTollVo.getChargeMonth()));
        printItemVo.setRemark(chargeDataVo.getRemark());
        MaintainType maintainType = MaintainType.fromValue(chargeDataVo.getMaintainType());
        printItemVo.setMaintainType(maintainType == null ? StringUtils.EMPTY : maintainType.getDescription());
        return printItemVo;
    }

    /**
     * 物维收费账单合计
     *
     * @param page
     * @return
     */
    private MaintainChargeExportVo sumMaintainChargeExport(MaintainChargePageResult<MaintainChargeDataVo> page) {
        MaintainChargeExportVo result = new MaintainChargeExportVo();
        result.setJobNo(COLUMN_SUM);
        result.setReceivableAmount(page.getReceivableTotalAmount());
        result.setPaidAmount(page.getPaidAmount());
        result.setLaborAmount(page.getLaborTotalAmount());
        result.setMaterialAmount(page.getMaterialTotalAmount());
        return result;
    }

    /**
     * 物维收费账单转导出对象
     *
     * @param item
     * @return
     */
    private MaintainChargeExportVo maintainCharge2ExportVo(MaintainChargeDataVo item) {
        MaintainChargeExportVo exportVo = new MaintainChargeExportVo();
        exportVo.setChargeNo(item.getChargeNo());
        exportVo.setJobNo(item.getJobNo());
        exportVo.setChargeYear(item.getChargeYear());
        exportVo.setChargeMonth(item.getChargeMonth());
        exportVo.setCustomerName(item.getCustomerName());
        exportVo.setContact(item.getContact());
        exportVo.setRemark(item.getRemark());
        exportVo.setReceivableAmount(item.getReceivableAmount());
        exportVo.setPaidAmount(item.getPaidAmount());
        exportVo.setLaborAmount(item.getLaborAmount());
        exportVo.setMaterialAmount(item.getMaterialAmount());

        MaintainType maintainType = MaintainType.fromValue(item.getMaintainType());
        exportVo.setMaintainType(maintainType == null ? StringUtils.EMPTY : maintainType.getDescription());
        MaintainChargeStatus status = MaintainChargeStatus.fromStatus(item.getStatus());
        exportVo.setPaidStatus(status == null ? StringUtils.EMPTY : status.getValue());
        PayWay payWay = PayWay.fromValue(item.getPayWay());
        exportVo.setPayWay(payWay == null ? StringUtils.EMPTY : payWay.getDescription());
        return exportVo;
    }


    @ApiOperation("物维单日报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/page")
    public PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> dayListPage(@RequestBody MaintainOrderDayReportSearchVo search) {
        return maintainService.dayListPage(search);
    }

    @ApiOperation("物维单月报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/page")
    public PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> monthListPage(@RequestBody MaintainOrderMonthReportSearchVo search) {
        return maintainService.monthListPage(search);
    }
}
