package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.*;
import com.senox.web.service.VoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/17 11:27
 */
@Api(tags = "投票")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/vote")
public class VoteController {

    private final VoteService voteService;

    @ApiOperation("添加投票分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/category/save")
    public void saveVoteCategory(@RequestBody VoteCategoryVo categoryVo) {
        voteService.saveVoteCategory(categoryVo);
    }

    @ApiOperation("根据id获取投票分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/category/get/{id}")
    public VoteCategoryVo findCategoryById(@PathVariable Long id) {
        return voteService.findCategoryById(id);
    }

    @ApiOperation("根据id删除投票类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/category/delete/{id}")
    public void deleteVoteCategoryById(@PathVariable Long id) {
        voteService.deleteVoteCategoryById(id);
    }

    @ApiOperation("投票类别分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/category/page")
    public PageResult<VoteCategoryVo> pageCategoryResult(@RequestBody VoteCategorySearchVo searchVo) {
        return voteService.pageCategoryResult(searchVo);
    }

    @ApiOperation("新增投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/resources/save")
    public void saveVoteResources(@RequestBody VoteResourcesVo resourcesVo) {
        voteService.saveVoteResources(resourcesVo);
    }

    @ApiOperation("根据id获取投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/resources/get/{id}")
    public VoteResourcesVo findResourcesById(@PathVariable Long id) {
        return voteService.findResourcesById(id);
    }

    @ApiOperation("根据id删除投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/resources/delete/{id}")
    public void deleteVoteResourcesById(@PathVariable Long id) {
        voteService.deleteVoteResourcesById(id);
    }

    @ApiOperation("投票资源分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/resources/page")
    public PageResult<VoteResourcesVo> pageResourcesResult(@RequestBody VoteResourcesSearchVo searchVo) {
        return voteService.pageResourcesResult(searchVo);
    }

    @ApiOperation("新增投票记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/records/save")
    public void saveVoteRecords(@RequestBody VoteRecordsVo recordsVo) {
        voteService.saveVoteRecords(recordsVo);
    }
}
