package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.AdminUserVo;
import com.senox.web.service.WmsMaterialProcureOrderService;
import com.senox.wms.vo.EnterWarehouseCollectVo;
import com.senox.wms.vo.requisition.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-12
 **/
@Api(tags = "仓储物料采购")
@RequiredArgsConstructor
@RequestMapping("/web/wms/material/procure/order")
@RestController
public class WmsMaterialProcureOrderController {
    private final WmsMaterialProcureOrderService materialProcureOrderService;

    @ApiOperation("保存采购单商品")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/goods/save/{procureOrderId}")
    public void saveGoods(@PathVariable Long procureOrderId, @RequestBody List<MaterialProcureOrderGoodsVo> procureOrderGoodsList) {
        materialProcureOrderService.saveGoods(procureOrderId, procureOrderGoodsList);
    }

    @ApiOperation("根据采购单id查找采购单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/findById/{procureOrderId}")
    public MaterialProcureOrderVo findById(@PathVariable Long procureOrderId) {
        return materialProcureOrderService.findById(procureOrderId);
    }

    @ApiOperation("根据申购id查找采购单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findByFlowId/{flowId}")
    public MaterialProcureOrderVo findByFlowId(@PathVariable Long flowId) {
        return materialProcureOrderService.findByFlowId(flowId);
    }

    @ApiOperation("采购单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<MaterialProcureOrderVo> pageList(@RequestBody MaterialProcureOrderSearchVo search) {
        return materialProcureOrderService.pageList(search);
    }

    @ApiOperation("采购商品列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/goods/list")
    public PageResult<MaterialProcureOrderGoodsVo> goodsPageList(@RequestBody MaterialProcureOrderGoodsSearchVo search) {
        return materialProcureOrderService.goodsPageList(search);
    }

    @ApiOperation("采购人列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/purchaser/list")
    public List<AdminUserVo> purchaserList() {
        return materialProcureOrderService.purchaserList();
    }

    @ApiOperation("确认商品")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/goods/confirm")
    public EnterWarehouseCollectVo confirmGoodsMaterial(@RequestBody MaterialProcureOrderFormVo procureOrderForm) {
        return materialProcureOrderService.confirmGoodsAndSubmit(procureOrderForm);
    }
}
