package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import com.senox.web.config.SameCellMergedStrategy;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.EnterpriseConvertor;
import com.senox.web.service.EnterpriseService;
import com.senox.web.vo.EnterpriseRealtyExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:41
 */
@Api(tags = "经营户")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/enterprise")
public class EnterpriseController extends BaseController {

    private final EnterpriseService enterpriseService;
    private final EnterpriseConvertor enterpriseConvertor;

    @ApiOperation("保存经营户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public Long saveEnterprise(@Validated @RequestBody EnterpriseEditVo enterprise) {
        return enterpriseService.saveEnterprise(enterprise);
    }

    @ApiOperation("删除经营户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteEnterprise(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        enterpriseService.deleteEnterprise(id);
    }

    @ApiOperation("保存经营户重点消防场所")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/firefighting/emphasis/save")
    public void saveEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            throw new InvalidParameterException();
        }

        enterpriseService.saveEnterpriseFirefightingEmphasis(enterpriseIds);
    }

    @ApiOperation("取消经营户重点消防场所")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/firefighting/emphasis/cancel")
    public void cancelEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            throw new InvalidParameterException();
        }

        enterpriseService.cancelEnterpriseFirefightingEmphasis(enterpriseIds);
    }

    @ApiOperation("获取经营户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public EnterpriseViewVo findEnterpriseById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return enterpriseService.findEnterpriseById(id);
    }

    @ApiOperation("获取经营户信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<EnterpriseViewVo> listEnterprise(@RequestBody EnterpriseSearchVo search) {
        return enterpriseService.listEnterprise(search);
    }

    @ApiOperation("获取经营户信息页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<EnterpriseViewVo> listEnterprisePage(@RequestBody EnterpriseSearchVo search) {
        return enterpriseService.listEnterprisePage(search);
    }

    @ApiOperation("导出经营户信息")
    @GetMapping("/export")
    public void exportEnterprises(HttpServletResponse response, EnterpriseSearchVo search) throws IOException {
        List<EnterpriseViewVo> list = enterpriseService.listEnterprise(search);

        int serial = 1;
        List<EnterpriseRealtyExportVo> exportList = new ArrayList<>(list.size());
        if (!CollectionUtils.isEmpty(list)) {
            for (EnterpriseViewVo item : list) {
                if (!CollectionUtils.isEmpty(item.getRealtyList())) {
                    for (EnterpriseRealtyVo er : item.getRealtyList()) {
                        EnterpriseRealtyExportVo exportItem = enterpriseConvertor.toExportVo(item);
                        exportItem.setSerial(serial);
                        exportItem.setRealtySerial(StringUtils.isBlank(er.getRealtyAlias()) ? er.getRealtySerial() : er.getRealtyAlias());
                        exportItem.setRealtyName(er.getRealtyName());
                        if (!StringUtils.isBlank(item.getOtherCategory())) {
                            exportItem.setCategoryDesc(item.getOtherCategory());
                        }
                        exportList.add(exportItem);
                    }
                } else {
                    EnterpriseRealtyExportVo exportItem = enterpriseConvertor.toExportVo(item);
                    exportItem.setSerial(serial);
                    if (!StringUtils.isBlank(item.getOtherCategory())) {
                        exportItem.setCategoryDesc(item.getOtherCategory());
                    }
                    exportList.add(exportItem);
                }
                serial++;
            }
        }



        // export
        String fileName = String.format(SenoxConst.Export.FILE_ENTERPRISE, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        //创建 Cell 样式
        WriteCellStyle writeCellStyle = new WriteCellStyle();
        // 设置垂直对齐方式
        writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置水平对齐方式
        writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcelFactory.write(response.getOutputStream(), EnterpriseRealtyExportVo.class)
                .sheet(SenoxConst.Export.SHEET_ENTERPRISE)
                .registerWriteHandler(new SameCellMergedStrategy(true, 1, Lists.newArrayList(0, 1, 2, 3, 6, 7, 8, 9)))
                .registerWriteHandler(new HorizontalCellStyleStrategy(null, writeCellStyle))
                .doWrite(exportList);
    }

}
