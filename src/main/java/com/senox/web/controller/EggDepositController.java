package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.car.constant.EggDepositStatus;
import com.senox.car.vo.*;
import com.senox.common.utils.RequestUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.EggDepositService;
import com.senox.web.vo.EggDepositExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/9 10:49
 */
@Api(tags = "蛋品区押金")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/web/egg/deposit")
public class EggDepositController extends BaseController{

    private final EggDepositService eggDepositService;

    @ApiOperation("押金支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo eggDepositPay(HttpServletRequest request, @RequestBody EggDepositVo eggDepositVo) {
        eggDepositVo.setRequestIp(RequestUtils.getIpAddr(request));
        return eggDepositService.eggDepositPay(eggDepositVo);
    }

    @ApiOperation("根据id获取押金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public EggDepositVo findEggDepositById(@PathVariable Long id) {
        return eggDepositService.findEggDepositById(id);
    }

    @ApiOperation("押金分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageStatisticsResult<EggDepositVo, EggDepositVo> pageResult(@RequestBody EggDepositSearchVo searchVo) {
        return eggDepositService.pageResult(searchVo);
    }

    @ApiOperation("导出押金列表")
    @GetMapping("/export")
    public void exportEggDeposit(HttpServletResponse response, EggDepositSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageStatisticsResult<EggDepositVo, EggDepositVo> pageResult = eggDepositService.pageResult(searchVo);
        List<EggDepositVo> dataList = pageResult.getDataList();
        EggDepositVo sum = pageResult.getStatistics();
        List<EggDepositExportVo> exportList = new ArrayList<>(dataList.size());

        if (!CollectionUtils.isEmpty(dataList)) {
            for (int index = 0; index < dataList.size(); index++) {
                EggDepositVo eggDepositVo = dataList.get(index);
                EggDepositExportVo exportVo = newEggDepositExportVo(eggDepositVo);
                exportVo.setSerial(index + 1);
                exportList.add(exportVo);
            }
            exportList.add(sumEggDepositExportVo(sum));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_EGG_DEPOSIT_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), EggDepositExportVo.class)
                .sheet(SenoxConst.Export.SHEET_EGG_DEPOSIT_REPORT)
                .doWrite(exportList);
    }

    private EggDepositExportVo sumEggDepositExportVo(EggDepositVo sum) {
        EggDepositExportVo exportVo = new EggDepositExportVo();
        exportVo.setOccupiedNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setAmount(sum.getAmount());
        exportVo.setDeductionAmount(sum.getDeductionAmount());
        exportVo.setRefundAmount(sum.getRefundAmount());
        return exportVo;
    }

    private EggDepositExportVo newEggDepositExportVo(EggDepositVo eggDepositVo) {
        EggDepositExportVo exportVo = new EggDepositExportVo();
        exportVo.setOccupiedNo(eggDepositVo.getOccupiedNo());
        exportVo.setContact(eggDepositVo.getContact());
        exportVo.setAmount(eggDepositVo.getAmount());
        exportVo.setDeductionAmount(eggDepositVo.getDeductionAmount());
        exportVo.setRefundAmount(eggDepositVo.getRefundAmount());
        exportVo.setPaidTime(eggDepositVo.getPaidTime());
        exportVo.setRefundTime(eggDepositVo.getRefundTime());
        exportVo.setRefundMan(eggDepositVo.getRefundMan());
        exportVo.setAuditStatus(Objects.equals(eggDepositVo.getCurrentNode(), "EGG_DEPOSIT_END") ? "已完成" : "处理中");
        EggDepositStatus depositStatus = EggDepositStatus.fromValue(eggDepositVo.getStatus());
        exportVo.setStatus(depositStatus == null ? "" : depositStatus.getName());
        PayWay payWay = PayWay.fromValue(eggDepositVo.getPayWay());
        PayWay refundPayWay = PayWay.fromValue(eggDepositVo.getRefundPayWay());
        exportVo.setPayWay(payWay == null ? "" : payWay.getDescription());
        exportVo.setRefundPayWay(refundPayWay == null ? "" : refundPayWay.getDescription());
        return exportVo;
    }

    @ApiOperation("押金退费申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund/apply")
    public Long refundApply(@RequestBody EggDepositRefundApplyVo applyVo) {
        return eggDepositService.refundApply(applyVo);
    }

    @ApiOperation("押金退费预申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund/pre/apply")
    public Long refundPreApply(@RequestBody EggDepositRefundApplyVo applyVo) {
        return eggDepositService.refundPreApply(applyVo);
    }

    @ApiOperation("根据id获取押金申请记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/refund/get/{id}")
    public EggDepositRefundApplyVo findEggDepositRefundById(@PathVariable Long id) {
        return eggDepositService.findEggDepositRefundById(id);
    }

    @ApiOperation("退费申请分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund/page")
    public PageResult<EggDepositRefundApplyVo> applyPage(@RequestBody EggDepositRefundApplySearchVo searchVo) {
        return eggDepositService.applyPage(searchVo);
    }

    @ApiOperation("押金提交")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/refund/submit")
    public void submit(@RequestBody EggDepositRefundSubmitVo submitVo) {
        eggDepositService.submit(submitVo);
    }

    @ApiOperation("退费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/refund/{applyId}")
    public void refundByDepositApply(@PathVariable Long applyId, @RequestParam PayWay payWay) {
        eggDepositService.refundByDepositApply(applyId, payWay);
    }
}
