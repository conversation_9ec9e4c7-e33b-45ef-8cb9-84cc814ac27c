package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.RealtyNature;
import com.senox.realty.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.RealtyService;
import com.senox.web.vo.RealtyExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/2 15:47
 */
@Api(tags = "物业管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/realty")
public class RealtyController extends BaseController{

    private final RealtyService realtyService;

    @ApiOperation("添加物业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addRealty(@Validated({Add.class}) @RequestBody RealtyVo realty) {
        return realtyService.addRealty(realty);
    }

    @ApiOperation("更新物业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateRealty(@Validated({Update.class}) @RequestBody RealtyVo realty) {
        if (realty.getId() < 1L) {
            throw new InvalidParameterException();
        }
        realtyService.updateRealty(realty);
    }

    @ApiOperation("更新物业担保")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/guarantee/update")
    public void updateRealtyGuarantee(@RequestBody RealtyGuaranteeVo guarantee) {
        if (!WrapperClassUtils.biggerThanLong(guarantee.getRealtyId(), 0L)) {
            throw new InvalidParameterException();
        }

        realtyService.updateRealtyGuarantee(guarantee);
    }

    @ApiOperation("删除物业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteRealty(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        realtyService.deleteRealty(id);
    }

    @ApiOperation("获取物业信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RealtyVo getRealty(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return realtyService.findById(id, true);
    }

    @ApiOperation("获取水电最新读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/we/{id}")
    public List<RealtyReadingsVo> getLatestWeReadings(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return realtyService.getLatestWeReadings(id);
    }

    @ApiOperation("更新水电读数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/update")
    public void updateWeReadings(@Valid @RequestBody List<RealtyReadingsVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new InvalidParameterException();
        }

        realtyService.updateWeReadings(list);
    }

    @ApiOperation("物业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<RealtyVo> listRealtyPage(@RequestBody RealtySearchVo searchVo) {
        return realtyService.listRealtyPage(searchVo);
    }

    @ApiOperation("导出物业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportRealty(HttpServletResponse response, RealtySearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        PageResult<RealtyVo> result = realtyService.listRealtyPage(searchVo);

        List<RealtyExportVo> exportVoList = new ArrayList<>(result.getDataList().size());
        if (!CollectionUtils.isEmpty(result.getDataList())) {
            int serial = 1;
            for (RealtyVo realtyVo : result.getDataList()) {
                RealtyExportVo exportVo = realtyToExportVo(realtyVo);
                exportVo.setSerialNo(serial++);
                exportVoList.add(exportVo);
            }
        }
        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY, LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_REALTY);
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyExportVo.class)
                .sheet(sheetName).doWrite(exportVoList);
    }

    private RealtyExportVo realtyToExportVo(RealtyVo realtyVo) {
        RealtyExportVo exportVo = new RealtyExportVo();
        exportVo.setRealtySerialNo(realtyVo.getSerialNo());
        exportVo.setName(realtyVo.getName());
        RealtyNature nature = RealtyNature.fromValue(realtyVo.getNature());
        exportVo.setNature(nature == null ? StringUtils.EMPTY : nature.getName());
        exportVo.setRegionName(realtyVo.getRegionName());
        exportVo.setStreetName(realtyVo.getStreetName());
        exportVo.setAddress(realtyVo.getAddress());
        exportVo.setArea(realtyVo.getArea());
        exportVo.setRegion1(realtyVo.getRegion1());
        exportVo.setRegion2(realtyVo.getRegion2());
        return exportVo;
    }

    @ApiOperation("保存物业别名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/alias/save/{realtyId}")
    public void saveRealtyAlias(@PathVariable Long realtyId, @Validated @RequestBody List<RealtyAliasVo> aliasList) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            throw new InvalidParameterException();
        }

        realtyService.batchSaveRealtyAlias(realtyId, aliasList);
    }

    @ApiOperation("获取物业别名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/alias/list/{realtyId}")
    public List<RealtyAliasVo> getRealtyAlias(@PathVariable Long realtyId) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            throw new InvalidParameterException();
        }

        return realtyService.listRealtyAlias(realtyId);
    }

    @ApiOperation("添加物业税率")
    @PostMapping("/taxRate/save")
    public void saveRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate) {
        realtyService.saveRealtyTaxRate(realtyTaxRate);
    }

    @ApiOperation("取消物业税率")
    @PostMapping("/taxRate/cancel")
    public void cancelRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate) {
        realtyService.cancelRealtyTaxRate(realtyTaxRate);
    }

    @ApiOperation("物业税率列表")
    @PostMapping("/taxRate/list")
    public PageResult<RealtyVo> listRealtyTaxRate(@RequestBody RealtySearchVo search) {
        return realtyService.pageListRealtyTaxRate(search);
    }
}
