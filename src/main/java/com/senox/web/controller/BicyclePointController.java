package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.vo.BicyclePointVo;
import com.senox.web.service.BicyclePointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:35
 */
@Api(tags = "三轮车配送点")
@RestController
@RequestMapping("/web/bicycle/point")
@RequiredArgsConstructor
public class BicyclePointController {

    private final BicyclePointService bicyclePointService;

    @ApiOperation("添加三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicyclePoint(@Validated(Add.class) @RequestBody BicyclePointVo bicyclePointVo) {
        return bicyclePointService.addBicyclePoint(bicyclePointVo);
    }

    @ApiOperation("修改三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicyclePoint(@Validated(Update.class) @RequestBody BicyclePointVo bicyclePointVo) {
        bicyclePointService.updateBicyclePoint(bicyclePointVo);
    }

    @ApiOperation("删除三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBicyclePoint(@PathVariable Long id) {
        bicyclePointService.deleteBicyclePoint(id);
    }

    @ApiOperation("根据id获取三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicyclePointVo findById(@PathVariable Long id) {
        return bicyclePointService.findById(id);
    }

    @ApiOperation("三轮车配送地点列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicyclePointVo> listBicyclePoint() {
        return bicyclePointService.listBicyclePoint();
    }
}
