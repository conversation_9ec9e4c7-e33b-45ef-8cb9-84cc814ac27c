package com.senox.web.controller;

import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.service.SystemSettingService;
import com.senox.context.AdminContext;
import com.senox.realty.vo.BillMonthVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 14:16
 */
@Api(tags = "系统设置")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/setting")
public class SettingController {

    private final SystemSettingService settingService;

    @ApiOperation("物业账单时间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/realtyBillTime/get")
    public BillMonthVo getRealtyBillTime() {
        List<SystemSetting> settings = settingService.listByParams(new SystemParam[] {
                SystemParam.REALTY_BILL_YEAR, SystemParam.REALTY_BILL_MONTH
        });

        Integer year = Integer.parseInt(settingService.getParamValueFromSettingList(settings, SystemParam.REALTY_BILL_YEAR));
        Integer month = Integer.parseInt(settingService.getParamValueFromSettingList(settings, SystemParam.REALTY_BILL_MONTH));
        return new BillMonthVo(year, month);
    }


    @ApiOperation("添加系统设置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public boolean addSetting(@RequestBody SystemSetting setting) {
        return settingService.saveOrUpdate(setting);
    }

    @ApiOperation("获取设置是否启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/checkEnable")
    public boolean checkEnable(@RequestParam("name") String name) {
        return settingService.checkEnable(name);
    }


    @ApiOperation("获取参数值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findByParam")
    public SystemSetting findByParam(@RequestParam String param) {
        return settingService.findByParam(param);
    }
}
