package com.senox.web.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.service.WmsMaterialRequisitionService;
import com.senox.wms.vo.requisition.MaterialFlowResult;
import com.senox.wms.vo.requisition.MaterialRequisitionSearchVo;
import com.senox.wms.vo.requisition.MaterialRequisitionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-09-12
 **/
@Api(tags = "仓储物料申购")
@RequiredArgsConstructor
@RequestMapping("/web/wms/material/requisition")
@RestController
public class WmsMaterialRequisitionController extends BaseController {
    private final WmsMaterialRequisitionService materialRequisitionService;

    @ApiOperation("保存申购")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/save")
    public Long save(@RequestBody MaterialRequisitionVo requisition) {
        return materialRequisitionService.save(requisition);
    }

    @ApiOperation("保存申购并提交")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/save/submit")
    public void saveAndSubmit(@RequestBody MaterialRequisitionVo requisition) {
        materialRequisitionService.saveAndSubmit(requisition);
    }

    @ApiOperation("根据流程id查找申购")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/findById/{flowId}")
    public MaterialRequisitionVo findByFlowId(@PathVariable Long flowId) {
        return materialRequisitionService.findByFlowId(flowId);
    }

    @ApiOperation("申购列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public PageResult<MaterialRequisitionVo> pageList(@RequestBody MaterialRequisitionSearchVo search) {
        return materialRequisitionService.pageList(search);
    }

    @ApiOperation("申购历史")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/history/{instanceId}")
    public MaterialFlowResult history(@PathVariable Long instanceId) {
        return materialRequisitionService.history(instanceId);
    }
}
