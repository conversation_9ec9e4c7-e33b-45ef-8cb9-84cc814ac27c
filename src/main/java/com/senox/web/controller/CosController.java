package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.user.vo.CosVo;
import com.senox.web.service.RoleCosService;
import com.senox.web.vo.CosTreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/28 15:44
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("/web/cos")
public class CosController {

    @Autowired
    private RoleCosService roleCosService;

    @ApiOperation("添加权限")
    @ApiImplicitParams({
        @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addCos(@Validated({Add.class}) @RequestBody CosVo cos) {
        return roleCosService.addCos(cos);
    }

    @ApiOperation("更新权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateCos(@Validated({Update.class}) @RequestBody CosVo cos) {
        if (cos.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        roleCosService.updateCos(cos);
    }

    @ApiOperation("删除权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteCos(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        roleCosService.deleteCos(id);
    }

    @ApiOperation("根据id获取权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public CosVo getCos(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return roleCosService.findCosById(id);
    }

    @ApiOperation("权限树列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<CosTreeNode> listCos() {
        return roleCosService.listCosTree();
    }
}
