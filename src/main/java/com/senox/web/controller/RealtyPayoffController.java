package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyPayoffSearchVo;
import com.senox.realty.vo.RealtyPayoffVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.RealtyPayoffExcelConvertor;
import com.senox.web.service.RealtyPayoffService;
import com.senox.web.vo.RealtyPayoffExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static com.senox.web.constant.SenoxConst.Export.COLUMN_SUM;

/**
 * <AUTHOR>
 * @date 2022/11/25 11:45
 */
@Api(tags = "应付账单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/payoff")
public class RealtyPayoffController extends BaseController {

    private final RealtyPayoffService payoffService;
    private final RealtyPayoffExcelConvertor payoffConvertor;


    @ApiOperation("生成应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generatePayoff(@Validated @RequestBody BillMonthVo month) {
        payoffService.generatePayoff(month);
    }

    @ApiOperation("更新应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updatePayoff(@RequestBody RealtyPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.updatePayoff(payoff);
    }

    @ApiOperation("删除应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deletePayoff(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.deletePayoff(id);
    }

    @ApiOperation("应付账单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public RealtyPayoffVo getPayoff(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return payoffService.getPayoff(id);
    }

    @ApiOperation("应付账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageStatisticsResult<RealtyPayoffVo, RealtyPayoffVo> listPayoff(@Validated  @RequestBody RealtyPayoffSearchVo search) {
        // list
        PageResult<RealtyPayoffVo> page = payoffService.listPayoffPage(search);

        // sum
        RealtyPayoffVo sum = payoffService.sumPayoff(search);
        if (sum != null) {
            sum.setAmount(DecimalUtils.nullToZero(sum.getAmount()));
            sum.setRentAmount(DecimalUtils.nullToZero(sum.getRentAmount()));
            sum.setFirstCharge(DecimalUtils.nullToZero(sum.getFirstCharge()));
            sum.setOwnerCharge(DecimalUtils.nullToZero(sum.getOwnerCharge()));
            sum.setRentToPay(DecimalUtils.nullToZero(sum.getRentToPay()));
        }

        return new PageStatisticsResult<>(page, sum);
    }

    @ApiOperation("导出应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void export(HttpServletResponse response, RealtyPayoffSearchVo search) throws IOException {
        search.setPageNo(1);

        // list
        List<RealtyPayoffVo> list = payoffService.listPayoff(search);
        List<RealtyPayoffExportVo> exportList = new ArrayList<>(list.size() + 1);
        if (!CollectionUtils.isEmpty(list)) {
            int serial = 1;
            for (RealtyPayoffVo item : list) {
                RealtyPayoffExportVo exportItem = payoffConvertor.payoff2ExportVo(item);
                exportItem.setSerial(serial++);
                exportList.add(exportItem);
            }
        }

        // 合计
        RealtyPayoffExportVo sum = sumPayoffExport(search);
        if (sum != null) {
            exportList.add(sum);
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_REALTY_PAYOFF, search.getBillYear(), search.getBillMonth(), LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_REALTY_PAYOFF, search.getBillYear(), search.getBillMonth());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), RealtyPayoffExportVo.class)
                .sheet(sheetName).doWrite(exportList);
    }

    /**
     * 合计应付账单
     * @param search
     * @return
     */
    private RealtyPayoffExportVo sumPayoffExport(RealtyPayoffSearchVo search) {
        RealtyPayoffVo sum = payoffService.sumPayoff(search);
        if (sum == null) {
            return null;
        }

        RealtyPayoffExportVo result = new RealtyPayoffExportVo();
        result.setContractNo(COLUMN_SUM);
        result.setRentAmount(DecimalUtils.nullToZero(sum.getRentAmount()));
        result.setFirstCharge(DecimalUtils.nullToZero(sum.getFirstCharge()));
        result.setOwnerCharge(DecimalUtils.nullToZero(sum.getOwnerCharge()));
        result.setRentToPay(DecimalUtils.nullToZero(sum.getRentToPay()));
        return result;
    }
}
