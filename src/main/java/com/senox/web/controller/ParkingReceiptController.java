package com.senox.web.controller;


import com.alibaba.excel.EasyExcelFactory;
import com.senox.car.vo.ReceiptOrderSearchVo;
import com.senox.car.vo.ReceiptOrderVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.ReceiptOrderExportConvertor;
import com.senox.web.service.ParkingReceiptService;
import com.senox.web.vo.ReceiptAuditVo;
import com.senox.web.vo.ReceiptOrderExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "停车发票")
@RestController
@RequestMapping("/web/parking/receipt")
@RequiredArgsConstructor
public class ParkingReceiptController extends BaseController {
    private final ParkingReceiptService parkingReceiptService;
    private final ReceiptOrderExportConvertor exportConvertor;


    @ApiOperation("审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/audit")
    public void audit(@Validated @RequestBody ReceiptAuditVo audit) {
        parkingReceiptService.audit(audit);
    }

    @ApiOperation("重置申请结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/reset/{id}")
    public void resetAudit(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        parkingReceiptService.resetAudit(id);
    }

    @ApiOperation("根据id获取发票申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ReceiptOrderVo getReceiptOrder(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return parkingReceiptService.findById(id);
    }

    @ApiOperation("发票申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ReceiptOrderVo> listReceiptOrder(@RequestBody ReceiptOrderSearchVo search) {
        return parkingReceiptService.listOrder(search);
    }

    @ApiOperation("导出发票申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/export")
    public void exportReceiptOrder(HttpServletResponse response, ReceiptOrderSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);

        // list
        PageResult<ReceiptOrderVo> page = parkingReceiptService.listOrder(search);
        List<ReceiptOrderExportVo> exportList = new ArrayList<>(page.getTotalSize());
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int serial = 1;
            for (ReceiptOrderVo item : page.getDataList()) {
                ReceiptOrderExportVo exportItem = exportConvertor.vo2Export(item);
                exportItem.setSerialNo(serial++);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_RECEIPT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ReceiptOrderExportVo.class)
                .sheet(SenoxConst.Export.SHEET_RECEIPT).doWrite(exportList);
    }

}
