package com.senox.web.controller.openapi;

import com.senox.realty.vo.BusinessRegionVo;
import com.senox.realty.vo.StreetVo;
import com.senox.user.authcredentials.annotation.AuthCredentialsIndicate;
import com.senox.web.authcredentials.handler.DefaultAuthCredentialsHandler;
import com.senox.web.controller.BaseController;
import com.senox.web.service.BusinessRegionService;
import com.senox.web.service.StreetService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-8-31
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/api/realty")
public class ApiRealtyController extends BaseController {
    private final BusinessRegionService regionService;
    private final StreetService streetService;

    @AuthCredentialsIndicate(paramDecrypt = false, paramEncrypt = false, decryptHandler = DefaultAuthCredentialsHandler.class)
    @PostMapping("/region/list")
    public List<BusinessRegionVo> listRegion(HttpEntity<String> entity) {
        return regionService.listAll();
    }

    @AuthCredentialsIndicate(paramDecrypt = false, paramEncrypt = false, decryptHandler = DefaultAuthCredentialsHandler.class)
    @PostMapping("/region/street/list")
    public List<StreetVo> listRegionStreet(@RequestBody Long regionId) {
        return streetService.listRegionStreet(regionId);
    }
}
