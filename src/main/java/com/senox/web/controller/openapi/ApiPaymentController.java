package com.senox.web.controller.openapi;


import com.senox.pm.vo.BodTokenVo;
import com.senox.user.authcredentials.annotation.AuthCredentialsIndicate;
import com.senox.web.authcredentials.handler.DefaultAuthCredentialsHandler;
import com.senox.web.controller.BaseController;
import com.senox.web.service.PaymentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2023-10-10
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/api/payment")
public class ApiPaymentController extends BaseController {
    private final PaymentService paymentService;

    @AuthCredentialsIndicate(paramEncrypt = false, decryptHandler = DefaultAuthCredentialsHandler.class)
    @PostMapping("/bod/getToken")
    public BodTokenVo getBodToken(@RequestBody String appId) {
        return paymentService.getBodToken(appId);
    }


}
