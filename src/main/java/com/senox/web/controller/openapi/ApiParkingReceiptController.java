package com.senox.web.controller.openapi;

import com.senox.car.dto.ParkingReceiptManagerDto;
import com.senox.user.authcredentials.annotation.AuthCredentialsIndicate;
import com.senox.web.authcredentials.handler.DefaultAuthCredentialsHandler;
import com.senox.web.controller.BaseController;
import com.senox.web.service.ParkingReceiptService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-8-31
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/api/parking")
public class ApiParkingReceiptController extends BaseController {
    private final ParkingReceiptService parkingReceiptService;

    @AuthCredentialsIndicate(paramEncrypt = false, decryptHandler = DefaultAuthCredentialsHandler.class)
    @PostMapping("/receipt/apply")
    public String receiptApply(@RequestBody ParkingReceiptManagerDto receiptManagerDto) {
        return parkingReceiptService.receiptApply(receiptManagerDto);
    }

}
