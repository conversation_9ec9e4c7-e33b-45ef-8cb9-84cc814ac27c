package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.vo.*;
import com.senox.user.constant.MerchantBillSettlePeriod;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.ExcelRowReadListener;
import com.senox.web.service.BicycleOrderService;
import com.senox.web.vo.BicycleCustomerCountPage;
import com.senox.web.vo.BicycleOrderExportVo;
import com.senox.web.vo.BicycleOrderImportExcel;
import com.senox.web.vo.BicycleOrderImportRawData;
import com.senox.web.vo.BicyclePage;
import com.senox.web.vo.ExcelAnalysisResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * @date 2023/9/19 11:35
 */
@Slf4j
@Api(tags = "三轮车配送订单")
@RestController
@RequestMapping("/web/bicycle/order")
@RequiredArgsConstructor
public class BicycleOrderController extends BaseController {

    private final BicycleOrderService bicycleOrderService;

    @ApiOperation("添加三轮车配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleDeliveryOrder(@Validated(Add.class) @RequestBody BicycleOrderVo orderVo) {
        return bicycleOrderService.addBicycleOrder(orderVo);
    }

    @ApiOperation("根据id获取配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleOrderVo findOrderVoById(@PathVariable Long id, @RequestParam(required = false) Boolean containStatus) {
        return bicycleOrderService.findOrderVoById(id, containStatus);
    }

    @ApiOperation("配送订单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public BicyclePage<BicycleOrderVo> page(@RequestBody BicycleOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicyclePage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        //list
        PageResult<BicycleOrderVo> page = bicycleOrderService.page(searchVo);
        BicyclePage<BicycleOrderVo> resultPage = new BicyclePage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        searchVo.setDelivery(null);
        BicycleOrderVo sumOrder = bicycleOrderService.sumOrder(searchVo);
        if (sumOrder != null) {
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumOrder.getPieces()));
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumOrder.getDeliveryCharge()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumOrder.getOtherCharge()));
            resultPage.setHandlingCharge(DecimalUtils.nullToZero(sumOrder.getHandlingCharge()));
            resultPage.setUpstairsCharge(DecimalUtils.nullToZero(sumOrder.getUpstairsCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumOrder.getTotalCharge()));
        }
        return resultPage;
    }

    @ApiOperation("三轮车配送订单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delivery/page")
    public PageStatisticsResult<BicycleOrderVo, BicycleOrderVo> listDeliveryPage(@RequestBody BicycleOrderSearchVo search,
                                                                                 @RequestParam(required = false) Boolean sum) {
        PageResult<BicycleOrderVo> page = bicycleOrderService.listDeliveryPage(search);

        PageStatisticsResult<BicycleOrderVo, BicycleOrderVo> resultPage = new PageStatisticsResult<>(page);
        if (BooleanUtils.isTrue(sum)) {
            resultPage.setStatistics(bicycleOrderService.sumDeliveryOrder(search));
        }
        return resultPage;
    }

    @ApiOperation("导出三轮车订单")
    @GetMapping("/export")
    public void exportBicycleOrder(HttpServletResponse response, BicycleOrderV2SearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setDelivery(true);
        List<BicycleDeliveryOrderV2Vo> orderVoList = bicycleOrderService.pageOrderV2(searchVo).getDataList();
        BicycleOrderV2Vo sumOrder = bicycleOrderService.sumOrderV2(searchVo);
        List<BicycleOrderExportVo> exportList = new ArrayList<>(orderVoList.size());

        if (!CollectionUtils.isEmpty(orderVoList)) {
            for (int index = 0; index < orderVoList.size(); index++) {
                BicycleDeliveryOrderV2Vo orderVo = orderVoList.get(index);
                BicycleOrderExportVo exportVo = newBicycleOrderExportVo(orderVo);
                exportVo.setSerial(index + 1);
                exportList.add(exportVo);
            }
            exportList.add(sumBicycleOrderExportVo(sumOrder));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_BICYCLE_ORDER_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), BicycleOrderExportVo.class)
                .sheet(SenoxConst.Export.SHEET_BICYCLE_ORDER_REPORT)
                .doWrite(exportList);
    }

    @ApiOperation("导出客户每月下单统计")
    @GetMapping("/customer/month/info/export")
    public void exportCustomerInfo(HttpServletResponse response, BicycleCustomerMonthInfoSearchVo searchVo) throws IOException {
        String fileName = String.format(SenoxConst.Export.FILE_BICYCLE_CUSTOMER_MONTH_INFO, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));

        List<BicycleCustomerMonthInfoVo> customerInfoVos = bicycleOrderService.customerMonthInfoList(searchVo);
        LocalDateTime startTime = searchVo.getStartTime();
        LocalDateTime endTime = searchVo.getEndTime();
        long numOfDays = ChronoUnit.DAYS.between(startTime, endTime);
        List<LocalDate> dateList = LongStream.range(0, numOfDays)
                .mapToObj(startTime::plusDays).map(x -> x.toLocalDate().withDayOfMonth(1)).distinct()
                .collect(Collectors.toList());
        // 新建ExcelWriter
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).registerWriteHandler(setConfigure()).build();
        WriteSheet sheet = buildSheet(SenoxConst.Export.TITLE_BICYCLE_CUSTOMER_MONTH_INFO, dateList);
        List<List<Object>> contents = buildContent(customerInfoVos, dateList);
        excelWriter.write(contents, sheet);
        // 关闭流
        excelWriter.finish();
    }

    @ApiOperation("三轮车配送订单分页V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page/order/v2")
    public BicyclePage<BicycleDeliveryOrderV2Vo> pageOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        PageResult<BicycleDeliveryOrderV2Vo> pageResult = bicycleOrderService.pageOrderV2(searchVo);

        BicyclePage<BicycleDeliveryOrderV2Vo> resultPage = new BicyclePage<>(pageResult.getPageNo(), pageResult.getPageSize());
        resultPage.setTotalPages(pageResult.getTotalPages());
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setDataList(pageResult.getDataList());

        // sum
        BicycleOrderV2Vo sumOrder = null;
        if (BooleanUtils.isTrue(searchVo.getDelivery())) {
            sumOrder = bicycleOrderService.sumOrderV2(searchVo);
        }
        if (sumOrder != null) {
            resultPage.setHandlingCharge(DecimalUtils.nullToZero(sumOrder.getHandlingCharge()));
            resultPage.setDeliveryCharge(DecimalUtils.nullToZero(sumOrder.getDeliveryCharge()));
            resultPage.setOtherCharge(DecimalUtils.nullToZero(sumOrder.getOtherCharge()));
            resultPage.setUpstairsCharge(DecimalUtils.nullToZero(sumOrder.getUpstairsCharge()));
            resultPage.setTotalCharge(DecimalUtils.nullToZero(sumOrder.getTotalCharge()));
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumOrder.getPieces()));
        }
        return resultPage;
    }

    @ApiOperation("根据订单id查询货物信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/goodsDetail/{orderId}")
    public List<BicycleOrderGoodsDetailVo> goodsDetailByOrderId(@PathVariable Long orderId) {
        return bicycleOrderService.goodsDetailByOrderId(orderId);
    }

    @ApiOperation("查询订单统计数量情况V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count/v2")
    public BicycleOrderCountVo orderCountV2(@RequestBody BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderService.orderCountV2(searchVo);
    }

    /**
     * 构建内容
     *
     * @param customerInfoVos
     * @param dateList
     * @return
     */
    private List<List<Object>> buildContent(List<BicycleCustomerMonthInfoVo> customerInfoVos, List<LocalDate> dateList) {
        List<List<Object>> contentList = Lists.newArrayList();
        for (BicycleCustomerMonthInfoVo customerInfoVo : customerInfoVos) {
            List<Object> contentDetailList = new ArrayList<>();
            contentDetailList.add(customerInfoVo.getCustomerName());
            contentDetailList.add(customerInfoVo.getContact());
            contentDetailList.add(customerInfoVo.getChargesName());
            Map<LocalDate, BicycleCustomerMonthOrderCountVo> voMap = customerInfoVo.getCountVoList().stream().collect(Collectors.toMap(BicycleCustomerMonthOrderCountVo::getYearMonth, o -> o, (exist, replace) -> exist));
            for (LocalDate date : dateList) {
                BicycleCustomerMonthOrderCountVo countVo = voMap.get(date);
                LocalDate minusMonths = date.minusMonths(1);
                BicycleCustomerMonthOrderCountVo minusCountVo = voMap.get(minusMonths);
                if (countVo == null) {
                    contentDetailList.add(0);
                    contentDetailList.add(0);
                    contentDetailList.add("0%");
                } else {
                    contentDetailList.add(countVo.getCount());
                    contentDetailList.add(countVo.getPieces());
                    if (minusCountVo == null) {
                        contentDetailList.add("0%");
                    } else {
                        contentDetailList.add(String.format("%.2f%%", ((double) (countVo.getCount() - minusCountVo.getCount()) / minusCountVo.getCount()) * 100));
                    }
                }
            }
            int totalCount = customerInfoVo.getCountVoList().stream().mapToInt(BicycleCustomerMonthOrderCountVo::getCount).sum();
            BigDecimal totalPieces = customerInfoVo.getCountVoList().stream().map(BicycleCustomerMonthOrderCountVo::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add);
            contentDetailList.add(totalCount);
            contentDetailList.add(totalPieces);
            contentDetailList.add((double) totalCount / customerInfoVo.getCountVoList().size());
            contentDetailList.add(totalPieces.divide(BigDecimal.valueOf(customerInfoVo.getCountVoList().size()), 2, RoundingMode.HALF_UP));
            contentList.add(contentDetailList);
        }
        return contentList;
    }

    /**
     * 构建表头
     *
     * @param dateList
     * @return
     */
    private static List<List<String>> buildHead(List<LocalDate> dateList) {
        List<List<String>> headTitles = Lists.newArrayList();
        //第一列，1/2/3行
        headTitles.add(Lists.newArrayList("客户名"));
        headTitles.add(Lists.newArrayList("联系方式"));
        //第二列，1/2/3行
        headTitles.add(Lists.newArrayList("收费标准"));
        // 第四列及之后
        ArrayList<String> list = Lists.newArrayList("下单数量", "下单件数", "下单数量环比");
        // 根据实际需要，决定要渲染多少列
        dateList.forEach(date -> list.forEach(x -> headTitles.add(Lists.newArrayList(DateUtils.formatDateTime(date.atStartOfDay(), "yyyy-MM"), x))));
        headTitles.add(Lists.newArrayList("合计", "合计下单数量"));
        headTitles.add(Lists.newArrayList("合计", "合计下单件数"));
        headTitles.add(Lists.newArrayList("平均", "平均下单数量"));
        headTitles.add(Lists.newArrayList("平均", "平均下单件数"));
        return headTitles;
    }

    /**
     * 构建SHEET
     *
     * @param title
     * @param dateList
     * @return
     */
    private WriteSheet buildSheet(String title, List<LocalDate> dateList) {
        List<List<String>> head = buildHead(dateList);
        return EasyExcelFactory.writerSheet(title)
                .head(head).build();
    }

    /**
     * 配置字体，表头背景等
     *
     * @return
     */
    private static HorizontalCellStyleStrategy setConfigure() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.YELLOW1.getIndex()); // 黄色背景
        WriteFont headWriteFont = new WriteFont();
        // 加粗
        headWriteFont.setBold(true);
        headWriteFont.setFontHeightInPoints((short) 14); // 设置行高，不重要
        headWriteCellStyle.setWriteFont(headWriteFont);


        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //导出数据垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //导出数据水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //边框
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置
        //这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    @ApiOperation("计算配送费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/calculate")
    public BicycleOrderCalculateEstimateVo calculateCharges(@Validated(Update.class) @RequestBody BicycleOrderVo orderVo, @RequestParam Boolean useChargesId) {
        return bicycleOrderService.calculateCharges(orderVo, useChargesId);
    }

    @ApiOperation("运营分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/operate/analysis/statistics")
    public BicycleOperateAnalysisVo operateAnalysisStatistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderService.operateAnalysisStatistics(searchVo);
    }

    @ApiOperation("配送地点使用统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/point/count")
    public List<BicyclePointCountVo> listPointCount(@RequestParam Boolean isStart) {
        return bicycleOrderService.listPointCount(isStart);
    }

    @ApiOperation("排行榜")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/ranking/statistics")
    public BicycleCountRankingVo rankingStatistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderService.rankingStatistics(searchVo);
    }

    @ApiOperation("未处理的订单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/undoCount")
    public Integer undoOrderCount() {
        return bicycleOrderService.undoOrderCount();
    }

    @ApiOperation("历史运营分析记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/analysis/list")
    public PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(@RequestBody BicycleOperateAnalysisSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleCustomerCountPage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<BicycleOperateAnalysisVo> page = bicycleOrderService.listOperateAnalysis(searchVo);
        BicycleCustomerCountPage<BicycleOperateAnalysisVo> resultPage = new BicycleCustomerCountPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        //sum
        BicycleOperateAnalysisVo sumVo = bicycleOrderService.sumOperateAnalysis(searchVo);
        if (sumVo != null) {
            resultPage.setTotalCount(sumVo.getTotalCount());
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumVo.getTotalPieces()));
            resultPage.setTotalCost(DecimalUtils.nullToZero(sumVo.getTotalAmount()));
        }
        return resultPage;
    }

    @ApiOperation("今日下单客户统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/customer/count/list")
    public BicycleCustomerCountPage<BicycleCustomerCountVo> customerCountList(@RequestBody BicycleCustomerCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleCustomerCountPage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        // page
        PageResult<BicycleCustomerCountVo> page = bicycleOrderService.customerCountList(searchVo);
        BicycleCustomerCountPage<BicycleCustomerCountVo> resultPage = new BicycleCustomerCountPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        //sum
        BicycleCustomerCountVo sumVo = bicycleOrderService.sumCustomerCount(searchVo);
        if (sumVo != null) {
            resultPage.setTotalCount(sumVo.getTodayCount());
            resultPage.setTotalPieces(DecimalUtils.nullToZero(sumVo.getTodayPieces()));
            resultPage.setTotalCost(DecimalUtils.nullToZero(sumVo.getTodayCost()));
        }
        return resultPage;
    }

    @ApiOperation("查询订单统计数量情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public BicycleOrderCountVo orderCount(@RequestBody BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderService.orderCount(searchVo);
    }

    @ApiOperation("删除未生成结算单的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/cancel/{id}")
    public void cancelBicycleOrderById(@PathVariable Long id) {
        bicycleOrderService.cancelBicycleOrderById(id);
    }

    @ApiOperation("批量删除未生成结算单的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel")
    public void cancelBicycleOrderByIds(@RequestBody List<Long> ids) {
        bicycleOrderService.cancelBicycleOrderByIds(ids);
    }

    @ApiOperation("取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel/order")
    public void cancelBicycleOrder(@RequestBody BicycleOrderCancelVo cancelVo) {
        bicycleOrderService.cancelBicycleOrder(cancelVo);
    }

    @ApiOperation("根据id删除未配送的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/undelivered/{id}")
    public void deleteUndeliveredBicycleOrder(@PathVariable Long id) {
        bicycleOrderService.deleteUndeliveredBicycleOrder(id);
    }

    @ApiOperation("订单导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import/{merchantId}")
    public void orderImport(@PathVariable Long merchantId, @RequestPart("file") MultipartFile file) throws IOException {
        String lockKey = String.format(SenoxConst.Cache.KEY_BICYCLE_ORDER_IMPORT, merchantId);
        if (!RedisUtils.lock(lockKey, SenoxConst.Cache.TTL_1H)) {
            throw new BusinessException("导入频繁，请稍后再试");
        }
        try {
            ExcelAnalysisResult<BicycleOrderImportExcel> result = analysisOrderExcel(file.getInputStream(), excels -> excels
                    .stream().map(this::excelDataToOrderData).collect(Collectors.toList()));
            result.getExcelDataMap().forEach((sheetName, data) -> {
                String mark = "";
                for (BicycleOrderImportExcel excel : data) {
                    if (excel.getType().equals(0)) {
                        return;
                    }
                    if (!StringUtils.isBlank(excel.getParticipant())) {
                        excel.setMark(UUID.randomUUID().toString());
                        mark = excel.getMark();
                    } else {
                        excel.setMark(mark);
                    }
                }
            });
            bicycleOrderService.orderImport(merchantId, result.getExcelDataMap());
        } finally {
            RedisUtils.del(lockKey);
        }

    }

    /**
     * 解析excel
     *
     * @param in 输入流
     * @return excel数据集
     */
    public <T> ExcelAnalysisResult<T> analysisOrderExcel(InputStream in, Function<List<BicycleOrderImportRawData>, List<T>> function) {
        int headRowNumber = 3;
        MultiValueMap<String, T> excelDataMap = new LinkedMultiValueMap<>();
        MultiValueMap<String, CellExtra> extraMap = new LinkedMultiValueMap<>();
        EasyExcelFactory.read(in, BicycleOrderImportRawData.class, new ExcelRowReadListener<BicycleOrderImportRawData>(headRowNumber, result -> {
            List<BicycleOrderImportRawData> dataList = result.getDataList();
            excelDataMap.addAll(result.getSheetName(), function.apply(dataList));
            extraMap.addAll(result.getSheetName(), result.getCellExtraList());
        }, (data, context) -> {
            if (null == data || data.isEmpty()) {
                return null;
            }
            return data;
        })).extraRead(CellExtraTypeEnum.MERGE).headRowNumber(headRowNumber).doReadAll();
        return new ExcelAnalysisResult<>(headRowNumber, excelDataMap, extraMap);
    }

    private BicycleOrderExportVo newBicycleOrderExportVo(BicycleDeliveryOrderV2Vo orderVo) {
        BicycleOrderV2Vo orderV2Vo = orderVo.getOrderV2VoList().get(0);
        List<BicycleDeliveryOrderInfoV2Vo> infoV2Vos = orderV2Vo.getInfoV2Vos();
        BicycleOrderExportVo exportVo = new BicycleOrderExportVo();
        exportVo.setOrderSerialNo(orderV2Vo.getOrderSerialNo());
        exportVo.setOrderTime(orderV2Vo.getOrderTime());
        exportVo.setSender(orderV2Vo.getSender());
        MerchantBillSettlePeriod settlePeriod = MerchantBillSettlePeriod.fromNumber(orderV2Vo.getSettlePeriod());
        exportVo.setSettlePeriod(settlePeriod.getName());
        exportVo.setStartPointName(orderV2Vo.getStartPointName().concat(orderV2Vo.getStartPointDetailName()));
        exportVo.setEndPointName(orderV2Vo.getEndPointName().concat(orderV2Vo.getEndPointDetailName()));
        exportVo.setDeliveryOrderSerialNo(orderVo.getDeliveryOrderSerialNo());
        exportVo.setRiderName(infoV2Vos.stream().map(BicycleDeliveryOrderInfoV2Vo::getRiderName).collect(Collectors.joining(",")));
        exportVo.setPieces(orderV2Vo.getPieces());
        exportVo.setDeliveryCharge(orderV2Vo.getDeliveryCharge());
        exportVo.setOtherCharge(orderV2Vo.getOtherCharge());
        exportVo.setHandlingCharge(orderV2Vo.getHandlingCharge());
        exportVo.setUpstairsCharge(orderV2Vo.getUpstairsCharge());
        exportVo.setTotalCharge(orderV2Vo.getTotalCharge());
        BicycleDeliveryOrderStatus status = BicycleDeliveryOrderStatus.fromStatus(orderVo.getStatus());
        exportVo.setStatusDescribe(status.getName());
        return exportVo;
    }

    private BicycleOrderExportVo sumBicycleOrderExportVo(BicycleOrderV2Vo sumOrder) {
        BicycleOrderExportVo exportVo = new BicycleOrderExportVo();
        exportVo.setOrderSerialNo(SenoxConst.Export.COLUMN_SUM);
        exportVo.setPieces(sumOrder.getPieces());
        exportVo.setDeliveryCharge(sumOrder.getDeliveryCharge());
        exportVo.setOtherCharge(sumOrder.getOtherCharge());
        exportVo.setHandlingCharge(sumOrder.getHandlingCharge());
        exportVo.setUpstairsCharge(sumOrder.getUpstairsCharge());
        exportVo.setTotalCharge(sumOrder.getTotalCharge());
        return exportVo;
    }

    private BicycleOrderImportExcel excelDataToOrderData(BicycleOrderImportRawData excel) {
        BicycleOrderImportExcel orderExcel = new BicycleOrderImportExcel();
        excelDataToOrderData3(excel,orderExcel);

        return orderExcel;
    }


    private void excelDataToOrderData3(BicycleOrderImportRawData excel, BicycleOrderImportExcel orderExcel) {
        orderExcel.setParticipant(excel.getColumn1());
        if (!StringUtils.isBlank(excel.getColumn2())) {
            orderExcel.setDate(DateUtils.parseDate(excel.getColumn2()));
        }
        if (!StringUtils.isBlank(excel.getColumn3())) {
            orderExcel.setTime(LocalTime.parse(excel.getColumn3().replace(";", ":"), DateTimeFormatter.ofPattern("H:mm")));
        }
        orderExcel.setGoodsName(excel.getColumn4());
        orderExcel.setStartPoint(excel.getColumn5());
        orderExcel.setEndPoint(excel.getColumn6());
        if (!StringUtils.isBlank(excel.getColumn7())) {
            orderExcel.setPieces(new BigDecimal(excel.getColumn7()).setScale(2, RoundingMode.DOWN));
        }
        if (!StringUtils.isBlank(excel.getColumn8())) {
            orderExcel.setWeight(new BigDecimal(excel.getColumn8()).setScale(2, RoundingMode.DOWN));
        }
        if (!StringUtils.isBlank(excel.getColumn9())) {
            orderExcel.setConsumedTime(Integer.parseInt(excel.getColumn9()));
        }
        orderExcel.setType(2);
    }
}
