package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.vo.AdvertisingSpaceListVo;
import com.senox.realty.vo.AdvertisingSpaceSearchVo;
import com.senox.realty.vo.AdvertisingSpaceVo;
import com.senox.web.service.AdvertisingSpaceService;
import com.senox.web.vo.AdvertisingSpaceRentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/7/28 14:03
 */
@Api(tags = "广告位")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/advertising/space")
public class AdvertisingSpaceController extends BaseController {

    private final AdvertisingSpaceService spaceService;

    @ApiOperation("添加广告位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addSpace(@Validated(Add.class)  @RequestBody AdvertisingSpaceVo space) {
        return spaceService.addSpace(space);
    }

    @ApiOperation("添加广告位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateSpace(@Validated(Update.class) @RequestBody AdvertisingSpaceVo space) {
        spaceService.updateSpace(space);
    }

    @ApiOperation("删除广告位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteSpace(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        spaceService.deleteSpace(id);
    }

    @ApiOperation("获取广告位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public AdvertisingSpaceVo getSpace(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return spaceService.findById(id);
    }

    @ApiOperation("广告位租赁统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/summary/rent")
    public AdvertisingSpaceRentVo getSpaceRentSummary() {
        AdvertisingSpaceSearchVo search = new AdvertisingSpaceSearchVo();

        AdvertisingSpaceRentVo result = new AdvertisingSpaceRentVo();
        search.setRent(Boolean.TRUE);
        result.setRentCount(spaceService.countSpace(search));
        search.setRent(Boolean.FALSE);
        result.setIdleCount(spaceService.countSpace(search));
        return result;
    }

    @ApiOperation("广告位列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<AdvertisingSpaceListVo> listSpacePage(@RequestBody AdvertisingSpaceSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return spaceService.listPage(search);
    }
}
