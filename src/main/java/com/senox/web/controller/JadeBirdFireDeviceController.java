package com.senox.web.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.dm.vo.JadeBirdFireDeviceControlRequest;
import com.senox.dm.vo.JadeBirdFireDeviceSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceVo;
import com.senox.web.service.JadeBirdFireDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Api(tags = "青鸟消防设备")
@RestController
@RequestMapping("/web/fire/jade/bird/device")
@RequiredArgsConstructor
public class JadeBirdFireDeviceController {
    private final JadeBirdFireDeviceService fireDeviceService;

    @ApiOperation("添加设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@Validated(Add.class) @RequestBody JadeBirdFireDeviceVo fireDevice) {
        fireDeviceService.add(fireDevice);
    }

    @ApiOperation("修改设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@Validated(Update.class) @RequestBody JadeBirdFireDeviceVo fireDevice) {
        fireDeviceService.update(fireDevice);
    }

    @ApiOperation("根据id删除设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/deleteById/{fireDeviceId}")
    public void deleteById(@PathVariable Long fireDeviceId) {
        fireDeviceService.deleteById(fireDeviceId);
    }

    @ApiOperation("根据psn查找设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findByPsn/{psn}")
    public JadeBirdFireDeviceVo findByPsn(@PathVariable String psn) {
        return fireDeviceService.findByPsn(psn);
    }

    @ApiOperation("设备分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list/page")
    public PageResult<JadeBirdFireDeviceVo> pageList(@RequestBody JadeBirdFireDeviceSearchVo search) {
        return fireDeviceService.pageList(search);
    }

    @ApiOperation("设备复位")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/control/reset")
    public void reset(@Validated @RequestBody JadeBirdFireDeviceControlRequest fireDeviceControlRequest) {
        fireDeviceService.reset(fireDeviceControlRequest);
    }
}
