package com.senox.web.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.vo.AdminUserChangePwdVo;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import com.senox.user.vo.TollManSerialVo;
import com.senox.web.service.AdminUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/11 15:30
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/web/adminUser")
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    @ApiOperation("添加用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addAdminUser(@Validated({Add.class}) @RequestBody AdminUserVo user) {
        return adminUserService.addAdminUser(user);
    }

    @ApiOperation("更新用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateAdminUser(@Validated({Update.class}) @RequestBody AdminUserVo user) {
        if (user.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        adminUserService.updateAdminUser(user);
    }

    @ApiOperation("删除用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteAdminUser(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        adminUserService.deleteAdminUser(id);
    }

    @ApiOperation("修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/password/modify")
    public void modifyPassword(@Validated @RequestBody AdminUserChangePwdVo changePwd) {
        adminUserService.modifyPassword(changePwd);
    }

    @Deprecated
    @ApiOperation("修改票据编号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/billSerial/modify")
    public void modifyBillSerial(@Validated @RequestBody TollManSerialVo adminToll) {
        if (StringUtils.isBlank(adminToll.getBillSerial())) {
            throw new BusinessException("无效的票据编号");
        }
        adminUserService.modifyAdminToll(adminToll);
    }

    @ApiOperation("修改收费票据、设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/toll/modify")
    public void modifyAdminToll(@Validated @RequestBody TollManSerialVo adminToll) {
        adminUserService.modifyAdminToll(adminToll);
    }

    @ApiOperation("获取票据编号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/billSerial/get")
    public String findBillSerial() {
        TollManSerialVo result = adminUserService.findAdminToll();
        return result == null || result.getBillSerial() == null ? StringUtils.EMPTY : result.getBillSerial();
    }

    @ApiOperation("获取收费票据、设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/toll/get")
    public TollManSerialVo findAdminToll() {
        return adminUserService.findAdminToll();
    }

    @ApiOperation("获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public AdminUserVo getAdminUser(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return adminUserService.findById(id);
    }

    @ApiOperation("获取当前用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/info")
    public AdminUserDto getInfo() {
        AdminUserDto user = AdminContext.getUser();
        user.setCosList(adminUserService.listUserCos(user.getUserId()));
        user.setLoginable(null);
        user.setToken(user.getAccessToken());
        user.setAccessToken(null);
        return user;
    }

    @ApiOperation("用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<AdminUserVo> listAdminUserPage(@RequestBody AdminUserSearchVo searchVo) {
        return adminUserService.listAdminUserPage(searchVo);
    }

    @ApiOperation("用户部门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/department/list")
    public List<Long> listDepartment() {
        return adminUserService.listUserDepartment(AdminContext.getUser().getUserId());
    }

    @ApiOperation("用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cos/list")
    public List<String> listCos() {
        return adminUserService.listUserCos(AdminContext.getUser().getUserId());
    }

}
