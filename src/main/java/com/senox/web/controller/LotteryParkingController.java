package com.senox.web.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.senox.car.constant.LotteryParkingQueueStatus;
import com.senox.car.constant.LotteryParkingRecordType;
import com.senox.car.constant.LotteryParkingStatus;
import com.senox.car.vo.*;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.LotteryParkingEntryQueueService;
import com.senox.web.service.LotteryParkingExitService;
import com.senox.web.service.LotteryParkingService;
import com.senox.web.vo.LotteryParkingEntryQueueExportVo;
import com.senox.web.vo.ParkingDayReportExportVo;
import com.senox.web.vo.ParkingRecordExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/5 15:10
 */
@Api(tags = "摇号停车")
@RestController
@RequestMapping("/web/lottery/parking")
@RequiredArgsConstructor
public class LotteryParkingController extends BaseController {
    private final LotteryParkingExitService lotteryParkingExitService;
    private final LotteryParkingService parkingService;
    private final LotteryParkingEntryQueueService lotteryParkingEntryQueueService;

    @ApiOperation("保存摇号停车配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/setting/save")
    public void saveSetting(@Validated @RequestBody LotterySettingVo setting) {
        parkingService.saveLotterySetting(setting);
    }

    @ApiOperation("获取摇号停车配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/setting/get")
    public LotterySettingVo getSetting() {
        return parkingService.getLotterySetting();
    }

    @ApiOperation("添加摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addParking(@Validated(Add.class) @RequestBody LotteryParkingVo parking) {
        return parkingService.addParking(parking);
    }

    @ApiOperation("更新摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateParking(@Validated(Update.class) @RequestBody LotteryParkingVo parking) {
        if (parking.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        if (StringUtils.isBlank(parking.getName())) {
            throw new InvalidParameterException("无效的车位");
        }
        // 更新车位不更新车牌信息
        parking.setOccupiedNo(null);
        parking.setOccupiedTime(null);
        parkingService.updateParking(parking);
    }

    @ApiOperation("变更摇号车位车牌")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/changeCar")
    public void changeParkingCar(@RequestBody LotteryParkingVo parking) {
        if (parking.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        if (StringUtils.isBlank(parking.getOccupiedNo())) {
            throw new InvalidParameterException("无效的车牌");
        }

        LotteryParkingVo carParking = new LotteryParkingVo();
        carParking.setId(parking.getId());
        carParking.setOccupiedNo(parking.getOccupiedNo());
        carParking.setContact(parking.getContact());
        if (parking.getOccupiedTime() != null) {
            carParking.setOccupiedTime(parking.getOccupiedTime());
        }
        parkingService.updateParking(carParking);
    }


    @ApiOperation("启用摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/enable/{id}")
    public void enableParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        parkingService.changeParkingStatus(id, LotteryParkingStatus.READY);
    }

    @ApiOperation("禁用摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/disable/{id}")
    public void disableParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        LotteryParkingVo parking = parkingService.findParkingById(id);
        if (parking != null) {
            if (LotteryParkingStatus.fromStatus(parking.getStatus()) == LotteryParkingStatus.OCCUPIED) {
                throw new BusinessException("车位被占用");
            }
            parkingService.changeParkingStatus(id, LotteryParkingStatus.DISABLE);
        }

    }

    @ApiOperation("删除摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        LotteryParkingVo parking = parkingService.findParkingById(id);
        if (parking != null) {
            if (LotteryParkingStatus.fromStatus(parking.getStatus()) == LotteryParkingStatus.OCCUPIED) {
                throw new BusinessException("车位被占用");
            }
            parkingService.deleteParking(id);
        }
    }

    @ApiOperation("获取摇号车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public LotteryParkingVo getParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return parkingService.findParkingById(id);
    }

    @ApiOperation("摇号车位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<LotteryParkingVo> listLotteryParking() {
        return parkingService.listLotteryParking();
    }

    @ApiOperation("摇号进场")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/entryLottery")
    public LotteryParkingVo runAndOccupied(@RequestBody LotteryParkingRecordVo recordVo) {
        if (StringUtils.isBlank(recordVo.getCarNo())) {
            throw new InvalidParameterException("无效的车牌");
        }
        return parkingService.runAndOccupied(recordVo);
    }

    @ApiOperation("手工进场")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/fillEntry")
    public LotteryParkingVo fillParking(HttpServletRequest request, @RequestBody LotteryParkingVo parkingVo) {
        if (StringUtils.isBlank(parkingVo.getOccupiedNo())) {
            throw new InvalidParameterException("无效的车牌");
        }
        if (!WrapperClassUtils.biggerThanLong(parkingVo.getId(), 0L)) {
            throw new InvalidParameterException("无效的参数");
        }
        if (parkingVo.getPayWay() == null) {
            throw new InvalidParameterException("无效的支付方式");
        }
        parkingVo.setRequestIp(RequestUtils.getIpAddr(request));
        return parkingService.fill(parkingVo);
    }

    @ApiOperation("离场")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exit/{id}")
    public LotteryParkingRecordVo exitParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        LotteryParkingRecordVo result = lotteryParkingExitService.exitNotPay(id);
        if (result == null) {
            throw new BusinessException("离场失败");
        }
        return result;
    }

    @ApiOperation("撤销离场")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exit/revoke/{id}")
    public LotteryParkingVo revokeExitParking(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return parkingService.revokeExitParking(id);
    }

    @ApiOperation("离场补录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exit/supplement")
    public void exitSupplement(@RequestBody @Validated LotteryParkingExitSupplementVo lotteryParkingExitSupplementVo) {
        lotteryParkingExitService.exitSupplement(lotteryParkingExitSupplementVo);
    }

    @ApiOperation("离场支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exit/pay")
    public OrderResultVo exitPay(HttpServletRequest request, @RequestBody LotteryParkingRecordVo parkingRecordVo) {
        parkingRecordVo.setRequestIp(RequestUtils.getIpAddr(request));
        return lotteryParkingExitService.exitPay(parkingRecordVo);
    }

    @ApiOperation("离场并支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exitAndPay")
    public LotteryParkingRecordVo exitAndPay(HttpServletRequest request, @RequestBody LotteryParkingRecordVo parkingRecordVo) {
        parkingRecordVo.setRequestIp(RequestUtils.getIpAddr(request));
        return lotteryParkingExitService.exitAndPay(parkingRecordVo);
    }

    @ApiOperation("最近停车记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/record/top{size}")
    public List<LotteryParkingRecordVo> topParkingRecord(@PathVariable int size) {
        if (size < 1) {
            throw new InvalidParameterException("无效的停车记录");
        }
        return parkingService.topParkingRecord(size);
    }

    @ApiOperation("停车记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/record/list")
    public LotteryParkingPage<LotteryParkingRecordVo> listParkingRecord(@Validated @RequestBody LotteryParkingRecordSearchVo searchVo) {
        PageResult<LotteryParkingRecordVo> page = parkingService.listParkingRecord(searchVo);
        LotteryParkingPage<LotteryParkingRecordVo> resultPage = new LotteryParkingPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        LotteryParkingAmountVo sumAmountVo = parkingService.summaryParkingAmount(searchVo);
        if (sumAmountVo != null) {
            resultPage.setEntryAmount(sumAmountVo.getEntryAmount());
            resultPage.setExceedAmount(sumAmountVo.getExitAmount());
            resultPage.setTotalAmount(sumAmountVo.getTotalAmount());
        }
        return resultPage;
    }

    @ApiOperation("导出停车记录")
    @GetMapping("/record/export")
    public void exportParkingRecord(HttpServletResponse response, LotteryParkingRecordSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageResult<LotteryParkingRecordVo> recordPage = parkingService.listParkingRecord(searchVo);

        // convert
        List<ParkingRecordExportVo> exportList = recordPage == null ? Collections.emptyList()
                : new ArrayList<>(recordPage.getTotalSize());
        if (recordPage != null && !CollectionUtils.isEmpty(recordPage.getDataList())) {
            for (int index = 0; index < recordPage.getDataList().size(); index++) {
                LotteryParkingRecordVo recordVo = recordPage.getDataList().get(index);
                ParkingRecordExportVo exportItem = newParkingRecordExportVo(recordVo);
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_PARKING_RECORD, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcel.write(response.getOutputStream(), ParkingRecordExportVo.class)
                .sheet(SenoxConst.Export.SHEET_PARKING_RECORD)
                .doWrite(exportList);
    }

    @ApiOperation("最近摇号停车日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/log/top{size}")
    public List<LotteryParkingLogVo> topParkingLog(@PathVariable int size) {
        if (size < 1) {
            throw new InvalidParameterException("无效的停车记录");
        }
        return parkingService.topParkingLog(size);
    }

    @ApiOperation("摇号停车日志列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/log/list")
    public PageResult<LotteryParkingLogVo> listParkingLog(@Validated @RequestBody LotteryParkingLogSearchVo searchVo) {
        return parkingService.listParkingLog(searchVo);
    }

    @ApiOperation("摇号停车日报列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/dayReport/list")
    public LotteryParkingPage<LotteryParkingDayReportVo> listParkingDayReport(@Validated @RequestBody LotteryParkingDayReportSearchVo searchVo) {
        return parkingService.listParkingDayReport(searchVo);
    }

    @ApiOperation("导出停车日报")
    @GetMapping("/dayReport/export")
    public void exportParkingDayReport(HttpServletResponse response, LotteryParkingDayReportSearchVo searchVo) throws IOException {
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        searchVo.setOrderStr("report_date");
        LotteryParkingPage<LotteryParkingDayReportVo> resultPage = parkingService.listParkingDayReport(searchVo);

        // convert
        List<ParkingDayReportExportVo> exportList = resultPage == null ? Collections.emptyList() : new ArrayList<>(resultPage.getTotalSize());
        if (resultPage != null && !CollectionUtils.isEmpty(resultPage.getDataList())) {
            for (int index = 0; index < resultPage.getDataList().size(); index++) {
                LotteryParkingDayReportVo item = resultPage.getDataList().get(index);
                ParkingDayReportExportVo exportItem = newParkingDayReportExportVo(item);
                exportItem.setSerial(index + 1);
                exportList.add(exportItem);
            }
            exportList.add(sumParkingDayReportExportVo(resultPage));
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_PARKING_DAY_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ParkingDayReportExportVo.class)
                .sheet(SenoxConst.Export.SHEET_PARKING_DAY_REPORT)
                .doWrite(exportList);
    }

    @ApiOperation("摇号停车月报列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/monthReport/list")
    public LotteryParkingPage<LotteryParkingMonthReportVo> listParkingMonthReport(@RequestBody LotteryParkingMonthReportSearchVo searchVo) {
        return parkingService.listParkingMonthReport(searchVo);
    }


    @ApiOperation("查询车辆支付结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/paid/get")
    public LotteryParkingPaidVo findCarParkingPaid(@RequestParam String carNo) {
        if (StringUtils.isBlank(carNo)) {
            throw new InvalidParameterException("无效的车牌");
        }

        LotteryParkingPaidVo result = parkingService.findCarParkingPaid(carNo);
        if (result == null) {
            throw new BusinessException("找不到" + carNo + "的停车缴费记录");
        }
        return result;
    }

    @ApiOperation("入场支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/entry/pay")
    public OrderResultVo entryPay(HttpServletRequest request, @RequestBody LotteryParkingVo parkingVo) {
        parkingVo.setRequestIp(RequestUtils.getIpAddr(request));
        return parkingService.entryPay(parkingVo);
    }

    @ApiOperation("车牌号获取停车信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/info/{carNo}")
    public LotteryParkingInfoVo infoByCarNo(@PathVariable String carNo) {
        return parkingService.infoByCarNo(carNo);
    }


    @ApiOperation("同步车位数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/async/stock")
    public void asyncStock() {
        parkingService.asyncStock();
    }

    @ApiOperation("实时获取当天进出场数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/count")
    public LotteryParkingDayCount parkingDayCount() {
        return parkingService.parkingDayCount();
    }

    @ApiOperation("查询进出场次数和设备记录次数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/diff/count")
    public LotteryParkingRecordCountVo findEntryAndExitCount(@RequestBody  LotteryParkingRecordSearchVo searchVo) {
        return parkingService.findEntryAndExitCount(searchVo);
    }

    @ApiOperation("临期车位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advent/list")
    public List<LotteryParkingVo> adventParkingList() {
        return parkingService.adventParkingList();
    }

    @ApiOperation("最近时间离场记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/latest/record/list")
    public List<LotteryParkingRecordVo> latestExitRecord(@RequestParam(value = "minutes") Long minutes) {
        return parkingService.latestExitRecord(minutes);
    }

    @ApiOperation("发送离场缴费账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/send/exitBill/{id}")
    public void sendExitBill(@PathVariable Long id) {
        parkingService.sendExitBill(id);
    }

    @ApiOperation("监管临期列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/regulatory/advent/list")
    public List<LotteryParkingVo> regulatoryAdventParkingList() {
        return parkingService.regulatoryAdventParkingList();
    }

    @ApiOperation("删除设备的离场时间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/deviceExitTime/{id}")
    public void deleteDeviceExitTime(@PathVariable Long id) {
        parkingService.deleteDeviceExitTime(id);
    }

    @ApiOperation("更新离场支付状态为已支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/record/exit/paid/{exitOrderId}")
    public void updateExitPaid(@PathVariable Long exitOrderId) {
        parkingService.updateExitPaid(exitOrderId);
    }

    @ApiOperation("排队支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/entry/queue/pay")
    public OrderResultVo entryQueuePay(HttpServletRequest request, @Validated(Add.class) @RequestBody LotteryParkingEntryQueueVo entryQueueVo) {
        entryQueueVo.setRequestIp(RequestUtils.getIpAddr(request));
        entryQueueVo.setTollManId(getAdminUserId());
        return lotteryParkingEntryQueueService.entryQueuePay(entryQueueVo);
    }

    @ApiOperation("更新入场队列信息,返回车位id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/entry/queue/update/status/{id}")
    public Long updateEntryQueueStatus(@PathVariable Long id, @RequestParam Integer status) {
        return lotteryParkingEntryQueueService.updateEntryQueueStatus(id, status);
    }

    @ApiOperation("进入临时车位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/entry/queue/fill/entry/{id}")
    public void fillEntryQueue(@PathVariable Long id, @RequestParam Long parkingId) {
        lotteryParkingEntryQueueService.fillEntryQueue(id, parkingId);
    }

    @ApiOperation("入场队列列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/entry/queue/page")
    public PageStatisticsResult<LotteryParkingEntryQueueVo, LotteryParkingEntryQueueVo> entryQueuePage(@RequestBody LotteryParkingEntryQueueSearchVo searchVo) {
        PageResult<LotteryParkingEntryQueueVo> page = lotteryParkingEntryQueueService.entryQueuePage(searchVo);
        LotteryParkingEntryQueueVo sumVo = lotteryParkingEntryQueueService.sumEntryQueue(searchVo);
        return new PageStatisticsResult<>(page, sumVo);
    }

    @ApiOperation("入场队列列表导出")
    @GetMapping("/entry/queue/export")
    public void exportParkingEntryQueue(HttpServletResponse response, LotteryParkingEntryQueueSearchVo searchVo) throws IOException {
        searchVo.setPage(false);
        searchVo.setPageNo(1);
        searchVo.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        PageResult<LotteryParkingEntryQueueVo> page = lotteryParkingEntryQueueService.entryQueuePage(searchVo);
        List<LotteryParkingEntryQueueExportVo> exportList = new ArrayList<>(page.getDataList().size() + 1);
        if (!CollectionUtils.isEmpty(page.getDataList())) {
            int serial = 1;
            for (LotteryParkingEntryQueueVo entryQueueVo : page.getDataList()) {
                LotteryParkingEntryQueueExportVo exportVo = parkingEntryQueueToExportVo(entryQueueVo);
                exportVo.setSerialNo(String.valueOf(serial));
                exportList.add(exportVo);
                serial++;
            }
        }

        LotteryParkingEntryQueueVo sumVo = lotteryParkingEntryQueueService.sumEntryQueue(searchVo);
        exportList.add(sumParkingEntryQueueToExportVo(sumVo));

        // export
        String fileName = String.format(SenoxConst.Export.FILE_PARKING_ENTRY_QUEUE, LocalDate.now());
        String sheetName = SenoxConst.Export.SHEET_PARKING_ENTRY_QUEUE;
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LotteryParkingEntryQueueExportVo.class)
                .sheet(sheetName).doWrite(exportList);
    }

    private LotteryParkingEntryQueueExportVo sumParkingEntryQueueToExportVo(LotteryParkingEntryQueueVo sumVo) {
        LotteryParkingEntryQueueExportVo exportVo = new LotteryParkingEntryQueueExportVo();
        exportVo.setSerialNo("合计");
        exportVo.setEntryAmount(sumVo.getEntryAmount());
        return exportVo;
    }

    private LotteryParkingEntryQueueExportVo parkingEntryQueueToExportVo(LotteryParkingEntryQueueVo entryQueueVo) {
        LotteryParkingEntryQueueExportVo exportVo = new LotteryParkingEntryQueueExportVo();
        exportVo.setOccupiedNo(entryQueueVo.getOccupiedNo());
        exportVo.setSubCarNo(entryQueueVo.getSubCarNo());
        exportVo.setContact(entryQueueVo.getContact());
        exportVo.setPaidTime(entryQueueVo.getPaidTime());
        exportVo.setEntryAmount(entryQueueVo.getEntryAmount());
        exportVo.setStatus(LotteryParkingQueueStatus.fromValue(entryQueueVo.getStatus()).getName());
        exportVo.setPayWay(PayWay.fromValue(entryQueueVo.getEntryPayWay()).getDescription());
        return exportVo;
    }

    @ApiOperation("根据id重排")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/entry/queue/rearrangement/{id}")
    public void rearrangement(@PathVariable Long id) {
        lotteryParkingEntryQueueService.rearrangement(id);
    }

    private ParkingRecordExportVo newParkingRecordExportVo(LotteryParkingRecordVo recordVo) {
        ParkingRecordExportVo result = new ParkingRecordExportVo();
        result.setParkingName(recordVo.getParkingName());
        result.setCarNo(recordVo.getCarNo());
        result.setContact(recordVo.getContact());
        result.setEntryTime(recordVo.getEntryTime());
        result.setExitTime(recordVo.getExitTime());
        result.setDurationDescription(DateUtils.getDurationDescription(recordVo.getDuration()));
        result.setEntryAmount(recordVo.getEntryAmount());
        result.setEntryPayWay(recordVo.getEntryPayWay() == null ? StringUtils.EMPTY : PayWay.fromValue(recordVo.getEntryPayWay()).getDescription());
        result.setExceedAmount(recordVo.getExitAmount());
        result.setExceedPayWay(recordVo.getExitPayWay() == null ? StringUtils.EMPTY : PayWay.fromValue(recordVo.getExitPayWay()).getDescription());
        result.setEntryPaidTime(recordVo.getEntryPaidTime());
        result.setExitPaidTime(recordVo.getExitPaidTime());
        result.setAmount(recordVo.getEntryAmount().add(recordVo.getExitAmount()));
        result.setType(LotteryParkingRecordType.fromType(recordVo.getType()).getMessage());
        return result;
    }

    private ParkingDayReportExportVo newParkingDayReportExportVo(LotteryParkingDayReportVo reportVo) {
        ParkingDayReportExportVo result = new ParkingDayReportExportVo();
        result.setReportDate(reportVo.getReportDate().toString());
        result.setEntryCount(reportVo.getEntryCount() == null ? 0 : reportVo.getEntryCount());
        result.setExitCount(reportVo.getExitCount() == null ? 0 : reportVo.getExitCount());
        result.setQueueCount(reportVo.getQueueWaitCount() == null ? 0 : reportVo.getQueueWaitCount());
        result.setQueueCount(reportVo.getWaitExitCount() == null ? result.getQueueCount() : reportVo.getQueueWaitCount() +  result.getQueueCount());
        result.setTradeCount(reportVo.getTradeCount() == null ? 0 : reportVo.getTradeCount());
        result.setTradeAmount(reportVo.getTradeAmount() == null ? BigDecimal.ZERO : reportVo.getTradeAmount());
        result.setTradeAmount(reportVo.getQueueAmount() == null ? result.getTradeAmount() : DecimalUtils.add(result.getTradeAmount(), reportVo.getQueueAmount()));
        result.setExceedAmount(reportVo.getExceedAmount() == null ? BigDecimal.ZERO : reportVo.getExceedAmount());
        result.setSumAmount(reportVo.getSumAmount() == null ? BigDecimal.ZERO : reportVo.getSumAmount());
        return result;
    }

    private ParkingDayReportExportVo sumParkingDayReportExportVo(LotteryParkingPage<LotteryParkingDayReportVo> page) {
        ParkingDayReportExportVo result = new ParkingDayReportExportVo();
        result.setReportDate(SenoxConst.Export.COLUMN_SUM);
        result.setEntryCount(page.getEntryCount());
        result.setExitCount(page.getExitCount());
        result.setQueueCount(page.getQueueWaitCount() + page.getQueueWaitCount());
        result.setTradeCount(page.getTradeCount());
        result.setTradeAmount(DecimalUtils.add(page.getTradeAmount(), page.getQueueAmount()));
        result.setExceedAmount(page.getExceedAmount());
        result.setSumAmount(page.getSumAmount());
        return result;
    }
}
