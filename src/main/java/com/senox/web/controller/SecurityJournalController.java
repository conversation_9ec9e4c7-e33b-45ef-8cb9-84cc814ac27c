package com.senox.web.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.SecurityEvent;
import com.senox.realty.vo.SecurityJournalSearchVo;
import com.senox.realty.vo.SecurityJournalVo;
import com.senox.web.service.SecurityJournalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/27 14:54
 */
@Api(tags = "安保日志")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/security/journal")
public class SecurityJournalController extends BaseController {

    private final SecurityJournalService journalService;

    @ApiOperation("安保日志类别清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/types")
    public SecurityEvent[] listEventTypes() {
        return journalService.listEventTypes();
    }

    @ApiOperation("添加安保日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addJournal(@Validated(Add.class) @RequestBody SecurityJournalVo journal) {
        return journalService.addJournal(journal);
    }

    @ApiOperation("更新安保日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateJournal(@Validated(Update.class) @RequestBody SecurityJournalVo journal) {
        journalService.updateJournal(journal);
    }

    @ApiOperation("删除安保日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteJournal(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        journalService.deleteJournal(id);
    }

    @ApiOperation("获取安保日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/get/{id}")
    public SecurityJournalVo findJournalById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return journalService.findJournalById(id);
    }

    @ApiOperation("安保日志列表页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<SecurityJournalVo> listJournalPage(@RequestBody SecurityJournalSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return journalService.listJournalPage(search);
    }
}
