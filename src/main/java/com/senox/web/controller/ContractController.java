package com.senox.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.vo.*;
import com.senox.web.constant.SenoxConst;
import com.senox.web.service.ContractService;
import com.senox.web.vo.ContractExportVo;
import com.senox.web.vo.LeaseContractExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/2/20 9:54
 */
@Api(tags = "合同管理")
@RestController
@RequestMapping("/web/contract")
public class ContractController extends BaseController{

    @Autowired
    private ContractService contractService;

    @ApiOperation("添加合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addContract(@Validated({Add.class}) @RequestBody ContractVo contract) {
        checkContractFee(contract.getFees(), false);
        return contractService.addContract(contract);
    }

    @ApiOperation("更新合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateContract(@Validated({Update.class}) @RequestBody ContractVo contract) {
        if (contract.getId() < 1L) {
            throw new InvalidParameterException();
        }

        checkContractFee(contract.getFees(), false);
        contractService.updateContract(contract);
    }

    @ApiOperation("启用合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/enable")
    public void enableContract(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        contractService.enableContract(contractNo);
    }

    @ApiOperation("停用合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/suspend")
    public void suspendContract(@Validated @RequestBody RealtyContractSuspendRequestDto suspendReq) {
        contractService.suspendContract(suspendReq);
    }

    @ApiOperation("删除合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteContract(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        contractService.deleteContract(id);
    }

    @ApiOperation("获取合同信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ContractVo getContract(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        return contractService.findById(id);
    }

    @ApiOperation("根据合同号获取合同信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/getByContractNo")
    public ContractVo getByContractNo(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        return contractService.getByContractNo(contractNo);
    }

    @ApiOperation("获取合同源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/renew/from/{id}")
    public ContractVo getRenewFrom(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return contractService.findRenewFrom(id);
    }

    @ApiOperation("合同列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ContractVo> listContractPage(@RequestBody ContractSearchVo search) {
        return contractService.listContractPage(search);
    }

    @ApiOperation("合同列表导出")
    @GetMapping("/list/export")
    public void exportContract(HttpServletResponse response, ContractSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<ContractVo> dataList = contractService.listContractPage(search).getDataList();
        List<ContractExportVo> exportVoList = new ArrayList<>(dataList.size());

        if (!CollectionUtils.isEmpty(dataList)) {
            for (int index = 0; index < dataList.size(); index++) {
                ContractVo contractVo = dataList.get(index);
                ContractExportVo exportVo = newContractExportVo(contractVo);
                exportVo.setSerial(index + 1);
                exportVoList.add(exportVo);
            }
        }

        // export
        String fileName = String.format(SenoxConst.Export.FILE_CONTRACT_REPORT, ContractType.fromValue(search.getType()).getChName(), LocalDate.now());
        String sheetName = String.format(SenoxConst.Export.SHEET_CONTRACT_REPORT, ContractType.fromValue(search.getType()).getChName());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), ContractExportVo.class)
                .sheet(sheetName)
                .doWrite(exportVoList);
    }

    @ApiOperation("租赁合同列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/lease/list")
    public PageResult<LeaseContractListVo> listLeaseContractPage(@RequestBody ContractSearchVo search) {
        return contractService.listLeaseContractPage(search);
    }

    @ApiOperation("租赁合同列表导出")
    @GetMapping("/lease/list/export")
    public void exportLeaseContract(HttpServletResponse response, ContractSearchVo search) throws IOException {
        search.setPageNo(1);
        search.setPageSize(SenoxConst.EXPORT_PAGE_SIZE);
        List<LeaseContractListVo> dataList = contractService.listLeaseContractPage(search).getDataList();
        List<LeaseContractExportVo> exportVoList = new ArrayList<>(dataList.size());

        if (!CollectionUtils.isEmpty(dataList)) {
            for (int index = 0; index < dataList.size(); index++) {
                LeaseContractListVo contractVo = dataList.get(index);
                LeaseContractExportVo exportVo = newLeaseContractExportVo(contractVo);
                exportVo.setSerial(index + 1);
                exportVoList.add(exportVo);
            }
        }

        Set<String> excludeColumnNames = new HashSet<>();
        //不显示列
        setPropertyExclude(excludeColumnNames, search);

        // export
        String fileName = String.format(SenoxConst.Export.FILE_LEASE_CONTRACT_REPORT, LocalDate.now());
        prepareExcelResponse(response, URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()));
        EasyExcelFactory.write(response.getOutputStream(), LeaseContractExportVo.class).excludeColumnFiledNames(excludeColumnNames)
                .sheet(SenoxConst.Export.SHEET_LEASE_CONTRACT_REPORT)
                .doWrite(exportVoList);
    }

    private static void setPropertyExclude(Set<String> excludeColumnNames, ContractSearchVo search) {
        if (BooleanUtils.isFalse(search.getRentAmount())) {
            excludeColumnNames.add("rentAmount");
        }
        if (BooleanUtils.isFalse(search.getLeaseContractNo())) {
            excludeColumnNames.add("contractNo");
        }
        if (BooleanUtils.isFalse(search.getDepositStatus())) {
            excludeColumnNames.add("depositStatus");
        }
        if (BooleanUtils.isFalse(search.getArea())) {
            excludeColumnNames.add("area");
        }
        if (BooleanUtils.isFalse(search.getManageAmount())) {
            excludeColumnNames.add("manageAmount");
        }
        if (BooleanUtils.isFalse(search.getDepositAmount())) {
            excludeColumnNames.add("depositAmount");
        }
        if (BooleanUtils.isFalse(search.getRentProxyContractNo())) {
            excludeColumnNames.add("rentProxyContractNo");
        }
        if (BooleanUtils.isFalse(search.getOwnerName())) {
            excludeColumnNames.add("ownerName");
        }
        if (BooleanUtils.isFalse(search.getOwnerContact())) {
            excludeColumnNames.add("ownerContact");
        }
        if (BooleanUtils.isFalse(search.getContainCustomerSerial())) {
            excludeColumnNames.add("customerSerial");
        }
    }

    @ApiOperation("合同银行代扣信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/bankDelegate/list")
    public PageResult<ContractBankVo> listContractBank(@RequestBody ContractSearchVo search) {
        return contractService.listContractBank(search);
    }

    /**
     * 合同费项校验
     * @param feeNodes
     * @param checkDate
     */
    private void checkContractFee(List<ContractFeeNode> feeNodes, boolean checkDate) {
        if (feeNodes == null || feeNodes.isEmpty()) {
            return;
        }
        for (ContractFeeNode item : feeNodes) {
            if (!WrapperClassUtils.biggerThanLong(item.getFeeId(), 0L)
                || item.getAmount() == null || item.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new InvalidParameterException("无效的合同费项");
            }

            if (checkDate && (item.getStartDate() == null || item.getEndDate() == null
                        || item.getStartDate().isAfter(item.getEndDate()))) {
                    throw new InvalidParameterException("无效的合同费项");
            }

            if (item.getDetail() != null && !item.getDetail().isEmpty()) {
                checkContractFee(item.getDetail(), true);
            }
        }
    }

    private ContractExportVo newContractExportVo(ContractVo contractVo) {
        ContractExportVo exportVo = new ContractExportVo();
        exportVo.setContractNo(contractVo.getContractNo());
        exportVo.setRealtySerial(contractVo.getRealtySerial());
        exportVo.setRealtyName(contractVo.getRealtyName());
        exportVo.setCustomerSerial(contractVo.getCustomerSerial());
        exportVo.setCustomerName(contractVo.getCustomerName());
        exportVo.setCustomerContact(contractVo.getCustomerContact());
        exportVo.setStartDate(contractVo.getStartDate().toString());
        exportVo.setEndDate(contractVo.getEndDate().toString());
        ContractStatus contractStatus = ContractStatus.fromValue(contractVo.getStatus());
        exportVo.setStatus(contractStatus == null ? "" : contractStatus.getValue());
        return exportVo;
    }

    private LeaseContractExportVo newLeaseContractExportVo(LeaseContractListVo contractVo) {
        LeaseContractExportVo exportVo = new LeaseContractExportVo();
        exportVo.setContractNo(contractVo.getContractNo());
        exportVo.setRealtySerial(contractVo.getRealtySerial());
        exportVo.setRealtyName(contractVo.getRealtyName());
        exportVo.setCustomerSerial(contractVo.getCustomerSerial());
        exportVo.setCustomerName(contractVo.getCustomerName());
        exportVo.setCustomerContact(contractVo.getCustomerContact());
        exportVo.setStartDate(contractVo.getStartDate().toString());
        exportVo.setEndDate(contractVo.getEndDate().toString());
        ContractStatus contractStatus = ContractStatus.fromValue(contractVo.getStatus());
        exportVo.setStatus(contractStatus == null ? "" : contractStatus.getValue());
        BillStatus depositStatus = BillStatus.fromStatus(contractVo.getDepositStatus());
        exportVo.setDepositStatus(depositStatus == BillStatus.UNKNOWN ? "无押金" : depositStatus.getValue());
        exportVo.setRentProxyContractNo(contractVo.getRentProxyContractNo());
        exportVo.setRentAmount(contractVo.getRentAmount());
        exportVo.setArea(contractVo.getArea());
        exportVo.setManageAmount(contractVo.getManageAmount());
        exportVo.setDepositAmount(contractVo.getDepositAmount());
        exportVo.setOwnerName(contractVo.getOwnerName());
        exportVo.setOwnerContact(contractVo.getOwnerContact());
        return exportVo;
    }
}
