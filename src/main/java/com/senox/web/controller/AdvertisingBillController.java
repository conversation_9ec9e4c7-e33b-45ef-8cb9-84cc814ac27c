package com.senox.web.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.context.AdminContext;
import com.senox.pm.vo.OrderResultVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.AdvertisingBillSearchVo;
import com.senox.realty.vo.AdvertisingBillVo;
import com.senox.web.service.AdminUserService;
import com.senox.web.service.AdvertisingBillService;
import com.senox.web.vo.BillPage;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.TollPrintItemVo;
import com.senox.web.vo.TollPrintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

import static com.senox.web.constant.SenoxConst.FEE_ADVERTISING;

/**
 * <AUTHOR>
 * @date 2023/7/28 16:45
 */
@Api(tags = "广告应收账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/advertising/bill")
public class AdvertisingBillController extends BaseController {

    private final AdvertisingBillService billService;
    private final AdminUserService adminUserService;

    @ApiOperation("支付广告应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payBill(HttpServletRequest request, @Validated @RequestBody BillPayRequestVo payRequest) {
        // 校验支付参数
        validBillPayRequest(payRequest);

        payRequest.setRequestIp(RequestUtils.getIpAddr(request));
        payRequest.setTollMan(getAdminUserId());
        return billService.payBill(payRequest);
    }

    @ApiOperation("添加广告账单发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/receipt/add")
    public void addBillReceipt(@Validated @RequestBody BillReceiptBriefVo receipt) {
        if (StringUtils.isBlank(receipt.getTaxHeader())) {
            throw new InvalidParameterException();
        }
        billService.addBillReceipt(receipt);
    }

    @ApiOperation("取消广告账单发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/receipt/cancel")
    public void cancelBillReceipt(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        billService.cancelBillReceipt(ids);
    }

    @ApiOperation("获取广告应收账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public AdvertisingBillVo findBillById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billService.findBillById(id);
    }

    @ApiOperation("打印广告应收账单收据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/print/{id}")
    public TollPrintVo printBill(@PathVariable Long id, @RequestParam(required = false) Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingBillVo bill = billService.findBillById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        if (bill.getStatus() != BillStatus.PAID.ordinal()) {
            throw new BusinessException("账单未支付");
        }

        TollPrintVo result = advertisingBill2TollPrint(bill);
        if (StringUtils.isBlank(result.getBillSerial()) || BooleanUtils.isTrue(refreshSerial)) {
            result.setBillSerial(adminUserService.getAndIncAdminTollSerial());

            TollSerialVo tollSerial = new TollSerialVo();
            tollSerial.setBillId(id);
            tollSerial.setBillSerial(result.getBillSerial());
            billService.updateTollSerial(tollSerial);
        }
        return result;
    }

    @ApiOperation("广告应收账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public BillPage<AdvertisingBillVo> listBillPage(@RequestBody AdvertisingBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return BillPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // list
        PageResult<AdvertisingBillVo> page = billService.listBillPage(search);
        BillPage<AdvertisingBillVo> resultPage = new BillPage<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        // sum
        AdvertisingBillVo sumBill = billService.sumBill(search);
        if (sumBill != null) {
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumBill.getAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumBill.getPaidAmount()));
        }
        return resultPage;
    }

    /**
     * 广告费用收据
     * @param bill
     * @return
     */
    private TollPrintVo advertisingBill2TollPrint(AdvertisingBillVo bill) {
        TollPrintVo result = new TollPrintVo();
        result.setBillSerial(bill.getTollSerial());
        result.setPayer(bill.getCustomerName());
        result.setPayerDesc(bill.getSpaceName());
        result.setTollMan(bill.getTollMan());
        result.setTollTime(bill.getPaidTime());
        result.setTotalAmount(bill.getPaidAmount());
        result.setDetails(Collections.singletonList(newTollPrintItem(bill)));
        return result;
    }

    /**
     * 广告费用收据明细
     * @param bill
     * @return
     */
    private TollPrintItemVo newTollPrintItem(AdvertisingBillVo bill) {
        TollPrintItemVo result = new TollPrintItemVo();
        result.setFee(FEE_ADVERTISING);
        result.setPrice(bill.getAmount());
        result.setAmount(bill.getAmount());
        result.setTime(bill.getStartDate().toString().concat(" -- ").concat(bill.getEndDate().toString()));
        return result;
    }
}
