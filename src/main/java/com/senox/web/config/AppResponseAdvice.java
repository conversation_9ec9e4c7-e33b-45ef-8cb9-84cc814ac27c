package com.senox.web.config;

import com.senox.common.constant.ResultConst;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.Result;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Arrays;
import java.util.List;

/**
 * response 返回切面
 * <AUTHOR>
 * @Date 2020/12/21 11:44
 */
@ControllerAdvice
public class AppResponseAdvice implements ResponseBodyAdvice {

    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/error", "/swagger-resources", "/v2/api-docs", "/web/device/report", "/export", "/web/device/signalTone/event/receive"
    );

    @Override
    public boolean supports(MethodParameter methodParameter, Class clazz) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType,
                                  Class clazz, ServerHttpRequest request, ServerHttpResponse response) {
        for (String excludePath : EXCLUDE_PATHS) {
            if (request.getURI().getPath().contains(excludePath)) {
                return o;
            }
        }
        if (mediaType == MediaType.APPLICATION_OCTET_STREAM) {
            return o;
        }

        if (o instanceof Result) {
            return o;
        } else if (o instanceof ResultConst) {
            ResultConst result = (ResultConst) o;
            return Result.fail(result);
        } else if (o instanceof String) {
            return JsonUtils.object2Json(Result.success(o));
        } else {
            return Result.success(o);
        }
    }
}
