package com.senox.web.config;

import com.senox.user.component.AdminUserComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/1/11 16:01
 */
@Configuration
public class WebConfigure implements WebMvcConfigurer {

    @Value("${senox.user.multiLogin:true}")
    private Boolean userMultiLogin;
    @Value("#{'${senox.adminFilter.excludeUrls:}'.split(',')}")
    private String[] excludeUrls;
    @Autowired
    private AdminUserComponent adminUserComponent;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                //.allowedOrigins("*")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AccessInterceptor(adminUserComponent, userMultiLogin, Arrays.asList(excludeUrls)))
                .addPathPatterns("/web/**")
                .excludePathPatterns(excludeUrls);
    }
}
