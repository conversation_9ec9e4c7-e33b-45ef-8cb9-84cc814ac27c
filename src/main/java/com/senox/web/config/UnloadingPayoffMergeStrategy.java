package com.senox.web.config;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.HashMap;
import java.util.Map;

public class UnloadingPayoffMergeStrategy extends AbstractMergeStrategy {
    private final Map<Integer, Integer> mergeMap = new HashMap<>();

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        int currentRowIndex = cell.getRowIndex();
        int currentColumnIndex = cell.getColumnIndex();

        // 只处理工号（第 0 列）和合计总数（第 8 列）
        if (currentColumnIndex == 0 || currentColumnIndex == 8) {
            String currentValue = cell.getStringCellValue();
            int startRowIndex = mergeMap.getOrDefault(currentColumnIndex, currentRowIndex);

            // 如果当前值与上一行相同，则合并
            if (currentRowIndex > 0) {
                Cell prevCell = sheet.getRow(currentRowIndex - 1).getCell(currentColumnIndex);
                Cell firstCell = sheet.getRow(currentRowIndex).getCell(0);
                Cell prevFirstCell = sheet.getRow(currentRowIndex - 1).getCell(0);
                String prevValue = prevCell.getStringCellValue();
                if (currentValue.equals(prevValue) && firstCell.getStringCellValue().equals(prevFirstCell.getStringCellValue())) {
                    return;
                }
            }

            // 合并单元格
            if (startRowIndex < currentRowIndex - 1) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(
                        startRowIndex, currentRowIndex - 1, currentColumnIndex, currentColumnIndex
                ));
            }

            // 更新合并起始行
            mergeMap.put(currentColumnIndex, currentRowIndex);
        }
    }
}
