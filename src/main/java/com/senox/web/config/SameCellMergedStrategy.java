package com.senox.web.config;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.senox.common.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/19 16:21
 */
@Slf4j
@Getter
@Setter
public class SameCellMergedStrategy extends AbstractMergeStrategy {

    /**
     * 相同列合并
     */
    private boolean columnAlikeMerged;
    /**
     * 相同行合并
     */
    private boolean rowAlikeMerged;
    /**
     * 开始进行合并的行index
     */
    private int mergedRowStart;
    /**
     * 支持行合并的列
     */
    private List<Integer> mergedColumns;


    public SameCellMergedStrategy(boolean columnAlikeMerged, int mergedRowStart) {
        this(columnAlikeMerged, false, mergedRowStart, null);
    }

    public SameCellMergedStrategy(boolean rowAlikeMerged, int mergedRowStart, List<Integer> mergedColumns) {
        this(false, rowAlikeMerged, mergedRowStart, mergedColumns);
    }

    public SameCellMergedStrategy(boolean columnAlikeMerged, boolean rowAlikeMerged, int mergedRowStart, List<Integer> mergedColumns) {
        this.columnAlikeMerged = columnAlikeMerged;
        this.rowAlikeMerged = rowAlikeMerged;
        this.mergedRowStart = mergedRowStart;
        this.mergedColumns = mergedColumns == null ? Collections.emptyList() : mergedColumns;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {
        if (mergedRowStart > cell.getRowIndex()) {
            return;
        }

        // 等值单元格列合并
        mergeSameCellOnColumn(sheet, cell);
        // 等值单元格行合并
        mergeSameCellOnRow(sheet, cell);
    }

    /**
     * 等值单元格列合并
     * @param sheet
     * @param cell
     */
    private void mergeSameCellOnColumn(Sheet sheet, Cell cell) {
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();
        if (!columnAlikeMerged || columnIndex < 1) {
            return;
        }

        String cellVal = getCellValue(cell);
        // 当前行前一列
        Cell preCell = sheet.getRow(rowIndex).getCell(columnIndex - 1);
        String preCellVal = getCellValue(preCell);
        if (StringUtils.isBlank(preCellVal) || StringUtils.isBlank(cellVal) || !Objects.equals(preCellVal, cellVal)) {
            return;
        }

        // 当前党员个内容与上一行内容相等，进行合并
        CellRangeAddress rangeAddress = new CellRangeAddress(rowIndex, rowIndex, columnIndex - 1, columnIndex);
        rangeAddress = fixConflictRangeAddress(sheet, rangeAddress);
        if (rangeAddress != null) {
            sheet.addMergedRegion(rangeAddress);
        }
    }


    /**
     * 等值单元格行合并
     * @param sheet
     * @param cell
     */
    private void mergeSameCellOnRow(Sheet sheet, Cell cell) {
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();
        if (!rowAlikeMerged || !mergedColumns.contains(columnIndex)) {
            return;
        }

        String cellVal = getCellValue(cell);
        // 上一行该列单元格
        Cell preCell = sheet.getRow(rowIndex - 1).getCell(columnIndex);
        String preCellVal = getCellValue(preCell);
        if (StringUtils.isBlank(preCellVal) || StringUtils.isBlank(cellVal) || !Objects.equals(preCellVal, cellVal)) {
            return;
        }

        // 当前党员个内容与上一行内容相等，进行合并
        CellRangeAddress rangeAddress = new CellRangeAddress(rowIndex - 1, rowIndex, columnIndex, columnIndex);
        rangeAddress = fixConflictRangeAddress(sheet, rangeAddress);
        if (rangeAddress != null) {
            sheet.addMergedRegion(rangeAddress);
        }
    }

    /**
     * 冲突合并单元格扩容合并
     * @param sheet
     * @param rangeAddress
     * @return
     */
    private CellRangeAddress fixConflictRangeAddress(Sheet sheet, CellRangeAddress rangeAddress) {
        // 找区域冲突的合并单元格
        Map<Integer, CellRangeAddress> conflictRegions = findConflictRangeAddresses(sheet, rangeAddress);
        if (CollectionUtils.isEmpty(conflictRegions)) {
            return rangeAddress;
        }

        // 冲突的合并单元格下表列表
        List<Integer> conflictRegionIndexList = new ArrayList<>(conflictRegions.size());
        for (Map.Entry<Integer, CellRangeAddress> entry : conflictRegions.entrySet()) {
            CellRangeAddress conflictRegion = entry.getValue();

            // 冲突单元格合并处理
            int firstRow = rangeAddress.getFirstRow();
            int lastRow = rangeAddress.getLastRow();
            int lastColumn = rangeAddress.getLastColumn();

            int firstRowC = conflictRegion.getFirstRow();
            int lastRowC = conflictRegion.getLastRow();
            int lastColumnC = conflictRegion.getLastColumn();
            // 行合并，列相等，行不相等
            if (lastRow > lastRowC && lastColumn == lastColumnC) {
                conflictRegion.setLastRow(lastRow);
                rangeAddress = conflictRegion;
                conflictRegionIndexList.add(entry.getKey());
            }

            // 列合并 行相等，列不相等
            if (lastColumn > lastColumnC && firstRow == firstRowC) {
                conflictRegion.setLastColumn(lastColumn);
                rangeAddress = conflictRegion;
                conflictRegionIndexList.add(entry.getKey());
            }
        }

        // 移除已经存在且冲突的合并单元格
        if (!CollectionUtils.isEmpty(conflictRegionIndexList)) {
            sheet.removeMergedRegions(conflictRegionIndexList);
        } else {
            rangeAddress = null;
        }

        return rangeAddress;
    }

    /**
     * 找到和合并单元格冲突的单元格
     * @param sheet
     * @param rangeAddress
     * @return
     */
    private Map<Integer, CellRangeAddress> findConflictRangeAddresses(Sheet sheet, CellRangeAddress rangeAddress) {
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
        if (CollectionUtils.isEmpty(mergedRegions)) {
            return Collections.emptyMap();
        }

        int regionIndex = 0;
        Map<Integer, CellRangeAddress> resultList = new LinkedHashMap<>(mergedRegions.size());
        for (CellRangeAddress region : mergedRegions) {
            if (region.intersects(rangeAddress)) {
                resultList.put(regionIndex, region);
            }
            regionIndex++;
        }
        return resultList;
    }

    /**
     * 获取单元格内容
     * @param cell
     * @return
     */
    private String getCellValue(Cell cell) {
        String result = null;
        try {
            result = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : BigDecimal.valueOf(cell.getNumericCellValue()).toString();
        } catch (Exception e) {
            log.warn("读取单元格内容失败，行：{} 列：{}", cell.getRowIndex(), cell.getColumnIndex());
        }
        return StringUtils.trimToEmpty(result);
    }
}
