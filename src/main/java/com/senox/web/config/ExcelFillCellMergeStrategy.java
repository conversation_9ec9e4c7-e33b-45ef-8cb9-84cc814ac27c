package com.senox.web.config;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;
import java.util.Objects;

/**
 * 单元格合并
 *
 * <AUTHOR>
 * @date 2021/4/20 15:24
 */
public class ExcelFillCellMergeStrategy implements CellWriteHandler {

    /**
     * 合并项开始行
     */
    private int mergeFirstRow;
    /**
     * 合并项结束行
     */
    private int mergeLastRow;
    /**
     * 合并项开始列
     */
    private int mergeFirstColumn;
    /**
     * 合并项结束列
     */
    private int mergeLastColumn;

    public ExcelFillCellMergeStrategy() {
    }

    public ExcelFillCellMergeStrategy(int mergeFirstRow, int mergeLastRow, int mergeFirstColumn, int mergeLastColumn) {
        this.mergeFirstRow = mergeFirstRow;
        this.mergeLastRow = mergeLastRow;
        this.mergeFirstColumn = mergeFirstColumn;
        this.mergeLastColumn = mergeLastColumn;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Cell cell, Head head, Integer integer, Boolean aBoolean) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 当前行
        int curRowIndex = cell.getRowIndex();
        // 当前列
        int curColIndex = cell.getColumnIndex();

        if (curRowIndex >= mergeFirstRow && curRowIndex <= mergeLastRow
                && curColIndex >= mergeFirstColumn && curColIndex <= mergeLastColumn) {
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            CellStyle mergeStyle = workbook.createCellStyle();
            mergeStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            mergeStyle.setAlignment(HorizontalAlignment.CENTER);
            cell.setCellStyle(mergeStyle);
        }

        if (curRowIndex == mergeLastRow && curColIndex == mergeLastColumn) {
            mergeRowColumn(writeSheetHolder, cell, curRowIndex, curColIndex);
        }
    }

    /**
     * 合并单元格
     *
     * @param writeSheetHolder
     * @param cell
     * @param curRowIndex
     * @param curColIndex
     */
    private void mergeRowColumn(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        // 合并开始的第一个单元格忽略
        if (curRowIndex == mergeFirstRow && curColIndex == mergeFirstColumn) {
            return;
        }

        // 获取当前行当前列的数据
        Object curData = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();

        // 比较合并区域内的值是否一样
        boolean isMergeAble = true;
        for (int rowIndex = curRowIndex - 1; rowIndex >= mergeFirstRow; rowIndex--) {
            for (int colIndex = curColIndex - 1; colIndex >= mergeFirstColumn; colIndex--) {
                Cell itemCell = cell.getSheet().getRow(rowIndex).getCell(colIndex);
                Object itemData = itemCell.getCellType() == CellType.STRING ? itemCell.getStringCellValue() : itemCell.getNumericCellValue();

                if (!Objects.equals(curData, itemData)) {
                    isMergeAble = false;
                    break;
                }
            }
        }

        if (isMergeAble) {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(mergeFirstRow, mergeLastRow, mergeFirstColumn, mergeLastColumn);
            writeSheetHolder.getSheet().addMergedRegion(cellRangeAddress);
        }
    }
}
