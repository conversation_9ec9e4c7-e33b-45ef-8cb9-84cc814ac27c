package com.senox.web.config;

import com.senox.common.constant.ResultConst;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.Result;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.component.AdminUserComponent;
import org.apache.http.entity.ContentType;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

import static com.senox.web.constant.SenoxConst.CACHE_KEY_LOGIN_USER;
import static com.senox.web.constant.SenoxConst.CACHE_TTL_6H;

/**
 * <AUTHOR>
 * @Date 2021/1/11 15:45
 */
public class AccessInterceptor implements HandlerInterceptor {

    /**
     * token参数
     */
    private static final String PARAMETER_TOKEN = "stoken";
    /**
     * 导出路径
     */
    private static final String PATTERN_EXPORT = "/export";

    private static final String PATTERN_DOWNLOAD = "/download";
    private static final String AUTH_CREDENTIALS_PATH = "/web/api";

    /**
     * 排除url
     */
    private List<String> excludeUrls;
    /**
     * 用户同时登录
     */
    private boolean userMultiLogin;
    private AdminUserComponent adminUserComponent;

    public AccessInterceptor(AdminUserComponent adminUserComponent, Boolean userMultiLogin, List<String> excludeUrls) {
        this.adminUserComponent = adminUserComponent;
        this.userMultiLogin = WrapperClassUtils.isTrue(userMultiLogin);
        this.excludeUrls = excludeUrls;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 拦截器配置绕过部分url
        String requestUri = request.getRequestURI();
        for (String excludeUrl : excludeUrls) {
            if (requestUri.contains(excludeUrl)) {
                return true;
            }
        }

        //绕过openAPI相关的
        if (requestUri.contains(AUTH_CREDENTIALS_PATH)) {
            return true;
        }

        // 尝试从上下文获取用户信息
        AdminUserDto adminUser = AdminContext.getUser();
        // 尝试从请求头中获取用户信息
        if (adminUser == null || !adminUser.isValid()) {
            adminUser = getUserFromHeader(request);
            if (adminUser == null || !adminUser.isValid()){
                adminUser = getUserFromHeaderAccessToken(request);
            }
        }

        // 尝试从请求参数中获取
        if (adminUser == null || !adminUser.isValid()) {
            if (isExportUrl(requestUri) || isDownloadUrl(requestUri)) {
                adminUser = getUserFromParameter(request);
            }
        }

        // 获取用户失败
        if (adminUser == null || !adminUser.isValid()) {
            responseData(response, Result.fail(ResultConst.USER_NOT_AUTHENTICATED));
            return false;
        }

        // 判断用户是否可以同时登录
        if (!userMultiLogin && !StringUtils.isBlank(adminUser.getToken())) {
            String cacheKey = String.format(CACHE_KEY_LOGIN_USER, adminUser.getUserId());
            String cacheValue = RedisUtils.get(cacheKey);
            if (!StringUtils.isBlank(cacheValue)) {
                if (!Objects.equals(adminUser.getToken(), cacheValue)) {
                    responseData(response, Result.fail(ResultConst.USER_NOT_AUTHENTICATED, "用户已在别处登录"));
                    return false;
                }
            } else {
                RedisUtils.set(cacheKey, adminUser.getToken(), CACHE_TTL_6H);
            }
        }


        AdminContext.setUser(adminUser);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        AdminContext.clear();
    }

    /**
     * 从请求头获取用户
     *
     * @param request
     * @return
     */
    private AdminUserDto getUserFromHeader(HttpServletRequest request) {
        String token = request.getHeader(AdminContext.HEADER_AUTHORIZATION);
        return getUserFromToken(token);
    }

    /**
     * 从请求头获取用户
     * @param request
     * @return
     */
    private AdminUserDto getUserFromHeaderAccessToken(HttpServletRequest request) {
        String token = request.getHeader(AdminContext.HEADER_ACCESS_TOKEN);
        return getUserFromAccessToken(token);
    }

    /**
     * 从请求参数中获取用户
     * @param request
     * @return
     */
    private AdminUserDto getUserFromParameter(HttpServletRequest request) {
        String token = request.getParameter(PARAMETER_TOKEN);
        return getUserFromToken(token);
    }

    /**
     * 从token获取用户
     * @param token
     * @return
     */
    private AdminUserDto getUserFromToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }

        AdminUserDto result = adminUserComponent.getUserFromToken(token);
        if (result != null) {
            result.setToken(token);
        }
        return result;
    }

    /**
     * 从accessToken获取用户
     * @param accessToken
     * @return
     */
    private AdminUserDto getUserFromAccessToken(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }

        AdminUserDto result = adminUserComponent.getUserFromAuthAccessToken(accessToken);
        if (result != null) {
            result.setAccessToken(accessToken);
        }
        return result;
    }

    /**
     * 判断是否导出
     * @param url
     * @return
     */
    private static boolean isExportUrl(String url) {
        return url.contains(PATTERN_EXPORT);
    }

    /**
     * 下载地址
     * @param url
     * @return
     */
    private static boolean isDownloadUrl(String url) {
        return url.contains(PATTERN_DOWNLOAD);
    }

    /**
     * 通过response 返回数据
     * @param response
     * @param data
     * @throws IOException
     */
    private void responseData(HttpServletResponse response, Object data) throws IOException {
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType(ContentType.APPLICATION_JSON.toString());
        response.getWriter().print(JsonUtils.object2Json(data));
        response.getWriter().flush();
    }

}
