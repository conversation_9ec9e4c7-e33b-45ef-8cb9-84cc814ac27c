package com.senox.web.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.vo.RealtyBillVo;
import com.senox.web.vo.RealtyBillReceiptExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/10/28 10:45
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyBillReceiptExportConvertor extends BaseConvert<RealtyBillVo, RealtyBillReceiptExportVo> {
}
