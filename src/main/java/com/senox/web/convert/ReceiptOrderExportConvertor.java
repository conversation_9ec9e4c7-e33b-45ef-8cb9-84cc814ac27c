package com.senox.web.convert;

import com.senox.car.vo.ReceiptOrderVo;
import com.senox.web.vo.ReceiptOrderExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/12/2 14:46
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ReceiptOrderExportConvertor {

    /**
     * 开票申请转导出对象
     * @param vo
     * @return
     */
    ReceiptOrderExportVo vo2Export(ReceiptOrderVo vo);
}
