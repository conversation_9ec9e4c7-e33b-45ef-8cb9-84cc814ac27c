package com.senox.web.convert;

import com.senox.cold.vo.CloudWarehousingBillVo;
import com.senox.web.vo.CloudWarehousingBillExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 15:12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CloudWarehousingBillConvertor {

    /**
     * 导入excel对象转视图对象
     * @param excelVos
     * @return
     */
    List<CloudWarehousingBillVo> toVoList(List<CloudWarehousingBillExcelVo> excelVos);
}
