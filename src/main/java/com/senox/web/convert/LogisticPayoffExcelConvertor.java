package com.senox.web.convert;

import com.senox.tms.vo.LogisticPayoffVo;
import com.senox.web.vo.LogisticPayoffExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/1/16 8:52
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticPayoffExcelConvertor {

    /**
     * 应付客户货款转导出视图
     * @param vo
     * @return
     */
    LogisticPayoffExcelVo
    toExcelVo(LogisticPayoffVo vo);
}
