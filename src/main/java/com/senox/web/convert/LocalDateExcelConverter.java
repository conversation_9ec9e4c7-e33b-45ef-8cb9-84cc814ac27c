package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2021/1/28 8:59
 */
public class LocalDateExcelConverter implements Converter<LocalDate>  {

    @Override
    public Class supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (CellDataTypeEnum.NUMBER.equals(cellData.getType())) {
            LocalDate date = LocalDate.of(1900, 1, 1);
            // excel 有些奇怪的bug ，差2天
            return date.plusDays(cellData.getNumberValue().longValue() - 2);
        }

        String strValue = cellData.getStringValue();
        if (strValue == null) {
            return null;
        }

        strValue = strValue.trim();
        if (strValue.matches("^\\d{4}\\.\\d{1,2}\\.\\d{1,2}$")) {
            return LocalDate.parse(strValue, DateTimeFormatter.ofPattern("yyyy.M.d"));
        }
        return LocalDate.parse(strValue, DateTimeFormatter.ISO_LOCAL_DATE);
    }

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

}
