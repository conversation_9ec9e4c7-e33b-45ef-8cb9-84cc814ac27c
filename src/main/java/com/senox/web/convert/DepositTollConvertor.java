package com.senox.web.convert;

import com.senox.pm.vo.DepositTollVo;
import com.senox.web.vo.DepositTollExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/1/13 13:51
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface DepositTollConvertor {

    /**
     * 押金交易流水转excel对象
     * @param toll
     * @return
     */
    DepositTollExportVo toll2ExcelVo(DepositTollVo toll);
}
