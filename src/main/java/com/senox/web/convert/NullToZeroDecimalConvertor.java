package com.senox.web.convert;

import com.alibaba.excel.converters.NullableObjectConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/6 15:40
 */
public class NullToZeroDecimalConvertor implements NullableObjectConverter<BigDecimal> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return BigDecimal.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public BigDecimal convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (cellData.getNumberValue() != null) {
            return cellData.getNumberValue();
        }
        return cellData.getStringValue() != null && cellData.getStringValue().matches("^\\d+\\.?\\d*$")
                ? new BigDecimal(cellData.getStringValue()) : BigDecimal.ZERO;
    }

    @Override
    public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value == null ? StringUtils.EMPTY : value.toString());
    }
}
