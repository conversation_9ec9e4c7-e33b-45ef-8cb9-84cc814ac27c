package com.senox.web.convert;

import com.senox.cold.vo.SkuVo;
import com.senox.common.convert.BaseConvert;
import com.senox.web.vo.SkuExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/9 16:58
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface SkuConvertor extends BaseConvert<SkuVo, SkuExcelVo> {


}
