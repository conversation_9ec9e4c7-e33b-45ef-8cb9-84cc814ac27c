package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/1/28 8:59
 */
public class BooleanExcelConvertor implements Converter<Boolean> {

    private static final String YES = "是";
    private static final String NO = "否";

    @Override
    public Class supportJavaTypeKey() {
        return Boolean.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Boolean convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return StringUtils.isBlank(cellData.getStringValue()) ? null : "是".equals(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(Boolean value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String result = StringUtils.EMPTY;
        if (value != null) {
            result = value ? YES : NO;
        }
        return new WriteCellData<>(result);
    }
}
