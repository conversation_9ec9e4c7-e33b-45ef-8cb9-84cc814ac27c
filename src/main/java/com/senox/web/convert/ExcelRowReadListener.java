package com.senox.web.convert;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.senox.web.vo.ExcelRowResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.function.Consumer;


@Slf4j
public class ExcelRowReadListener<T> extends AnalysisEventListener<T> {
    public static final int BATCH_COUNT = 100;
    public final int headRowNumber;
    private ExcelRowResult<T> excelRowResult = new ExcelRowResult<>();
    private final Consumer<ExcelRowResult<T>> excelRowResultConsumer;
    private final CustomBiConsumer<T, AnalysisContext, T> dataCustomizedConsumer;

    public ExcelRowReadListener(int headRowNumber, Consumer<ExcelRowResult<T>> excelRowResultConsumer, CustomBiConsumer<T, AnalysisContext, T> dataCustomizedConsumer) {
        this.headRowNumber = headRowNumber;
        this.excelRowResultConsumer = excelRowResultConsumer;
        this.dataCustomizedConsumer = dataCustomizedConsumer;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        context.readWorkbookHolder().setIgnoreEmptyRow(false);
        T handlerData = dataCustomizedConsumer.acceptAndReturn(data, context);
        if (null != handlerData) {
            excelRowResult.getDataList().add(data);
            if (excelRowResult.getDataList().size() >= BATCH_COUNT) {
                excelRowResult.setSheetName(context.readSheetHolder().getSheetName());
                excelRowResultConsumer.accept(excelRowResult);
                excelRowResult.clear();
            }
        }
    }

   @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        excelRowResult.setSheetName(context.readSheetHolder().getSheetName());
        if (extra.getType() == CellExtraTypeEnum.MERGE
                && !context.readSheetHolder().getSheetName().contains("汇总")
                && extra.getRowIndex() >= headRowNumber
        ) {
            extra.setText(excelRowResult.getSheetName());
            excelRowResult.getCellExtraList().add(extra);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!CollectionUtils.isEmpty(excelRowResult.getDataList())) {
            excelRowResultConsumer.accept(excelRowResult);
            excelRowResult.clear();
        }
    }
}
