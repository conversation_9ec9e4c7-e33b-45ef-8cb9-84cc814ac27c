package com.senox.web.convert;

import com.senox.common.vo.BillBadDebtVo;
import com.senox.web.vo.BillDiscountVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:56
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BillDiscountConvertor {

    /**
     * 折扣信息转坏账核销信息
     * @param discount
     * @return
     */
    BillBadDebtVo discount2BadDebt(BillDiscountVo discount);

}
