package com.senox.web.convert;

import com.senox.cold.vo.RefrigerationMonthBillVo;
import com.senox.tms.vo.LogisticPayoffVo;
import com.senox.web.vo.RefrigerationBillReceiptExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/2/28 15:08
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RefrigerationBillReceiptExcelConvertor {

    /**
     * 冷藏月账单转导出视图
     * @param vo
     * @return
     */
    RefrigerationBillReceiptExportVo toExcelVo(RefrigerationMonthBillVo vo);
}
