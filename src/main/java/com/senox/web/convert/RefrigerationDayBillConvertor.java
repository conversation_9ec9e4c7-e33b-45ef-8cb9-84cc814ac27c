package com.senox.web.convert;

import com.senox.cold.vo.RefrigerationDayBillDto;
import com.senox.cold.vo.RefrigerationDayBillVo;
import com.senox.web.vo.RefrigerationDayBillExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16 11:57
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RefrigerationDayBillConvertor {


    /**
     * excel 视图对象转数据对象
     * @param list
     * @return
     */
    List<RefrigerationDayBillDto> excelVo2Dto(List<RefrigerationDayBillExcelVo> list);

    /**
     * 视图对象转excel视图对象
     * @param vo
     * @return
     */
    RefrigerationDayBillExcelVo vo2ExcelVo(RefrigerationDayBillVo vo);

    /**
     * 视图对象列表转excel视图对象列表
     * @param list
     * @return
     */
    List<RefrigerationDayBillExcelVo> vo2ExcelVo(List<RefrigerationDayBillVo> list);
}
