package com.senox.web.convert;

import com.senox.pm.vo.RefrigerationTollVo;
import com.senox.web.vo.RefrigerationTollExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/1/13 13:51
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RefrigerationTollConvertor {

    /**
     * 冷藏缴费账单转excel对象
     * @param toll
     * @return
     */
    RefrigerationTollExcelVo toll2ExcelVo(RefrigerationTollVo toll);
}
