package com.senox.web.convert;

import com.alibaba.excel.converters.NullableObjectConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 整型
 * <AUTHOR>
 */
public class NullToZeroIntConvertor implements NullableObjectConverter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.NUMBER;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty excelContentProperty,
                                     GlobalConfiguration globalConfiguration) throws Exception{
        String data = null;
        if (cellData.getNumberValue() != null) {
            data = cellData.getNumberValue().toPlainString();
        }
        if (!StringUtils.isBlank(cellData.getStringValue())) {
            data = cellData.getStringValue().trim();
        }
        return data == null ? 0 : new BigDecimal(data).setScale(0, RoundingMode.HALF_UP).intValue();
    }

}
