package com.senox.web.convert;

import com.senox.realty.vo.RealtyDepositVo;
import com.senox.web.vo.RealtyDepositExportVo;
import com.senox.web.vo.RealtyDepositRefundExportVo;
import com.senox.web.vo.RealtyDepositTollExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/4/20 10:33
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyDepositConvertor {

    /**
     * 视图转导出视图
     * @param vo
     * @return
     */
    RealtyDepositExportVo voToExcelVo(RealtyDepositVo vo);

    /**
     * 视图转收费导出视图
     * @param vo
     * @return
     */
    RealtyDepositTollExportVo voToTollExcelVo(RealtyDepositVo vo);

    /**
     * 视图转收退费导出视图
     * @param vo
     * @return
     */
    RealtyDepositRefundExportVo voToRefundExcelVo(RealtyDepositVo vo);
}
