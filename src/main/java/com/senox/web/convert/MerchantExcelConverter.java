package com.senox.web.convert;

import com.senox.user.vo.MerchantVo;
import com.senox.web.vo.MerchantExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/6 13:59
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MerchantExcelConverter {

    /**
     * 导入excel对象转视图对象
     * @param excelVos
     * @return
     */
    List<MerchantVo> toVoList(List<MerchantExcelVo> excelVos);
}
