package com.senox.web.convert;

import com.senox.tms.vo.OutgoingTtlVo;
import com.senox.web.vo.OutgoingTtlExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 14:40
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface OutgoingTtlConvert {

    @Mapping(target = "settlementType", expression = "java(getSettlementType(vo.getSettlementType()))")
    List<OutgoingTtlVo> toVo(List<OutgoingTtlExcelVo> excelVoList);

    default Integer getSettlementType(String settlementType) {
        return "到付".equals(settlementType) ? 1 : 2;
    }
}
