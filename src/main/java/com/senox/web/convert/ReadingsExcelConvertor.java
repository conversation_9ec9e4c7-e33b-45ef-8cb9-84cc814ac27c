package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/24 9:44
 */
@Slf4j
public class ReadingsExcelConvertor implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.NUMBER;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String data = null;
        if (cellData.getNumberValue() != null) {
            data = cellData.getNumberValue().toPlainString();
        }

        if (!StringUtils.isBlank(cellData.getStringValue())) {
           data = cellData.getStringValue().trim();
        }
        return data == null ? null : new BigDecimal(data).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
    }
}
