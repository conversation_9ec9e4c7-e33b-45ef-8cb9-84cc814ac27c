package com.senox.web.convert;

import com.senox.car.vo.ParkingPaymentStatisticVo;
import com.senox.pm.vo.TollManDailyStatisticVo;
import com.senox.web.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/1/13 13:51
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface TollStatisticConvertor {

    /**
     * 收费员日收费统计转excel对象
     * @param statistic
     * @return
     */
    TollManDailyStatisticExportVo tollManStatistic2ExcelVo(PaymentTollManDailyStatisticVo statistic);

    /**
     * 支付通道统计转excel对象
     * @param statistic
     * @return
     */
    PayWayDailyStatisticExportVo payWayStatistic2ExcelVo(PaymentDailyWayStatisticVo statistic);

    /**
     * 支付通道日明细转excel对象
     * @param statistic
     * @return
     */
    PaymentWayStatisticExportVo paymentWayStatistic2ExcelVo(PaymentWayStatisticVo statistic);

    /**
     * 停车缴费统计转excel对象
     * @param statistic
     * @return
     */
    ParkingPaymentStatisticExportVo parkingPaymentStatistic2ExcelVo(ParkingPaymentStatisticVo statistic);
}
