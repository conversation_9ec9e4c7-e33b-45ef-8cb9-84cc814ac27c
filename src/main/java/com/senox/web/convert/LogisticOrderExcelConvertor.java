package com.senox.web.convert;

import com.senox.tms.vo.LogisticOrderVo;
import com.senox.web.vo.LogisticOrderExcelVo;
import com.senox.web.vo.LogisticOrderExportExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:15
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticOrderExcelConvertor {

    /**
     * excel 对象转视图对象
     * @param excelVo
     * @return
     */
    List<LogisticOrderVo> excelToVo(List<LogisticOrderExcelVo> excelVo);

    /**
     * 视图对象转导出视图对象
     * @param order
     * @return
     */
    @Mapping(source = "shipCount", target = "shipCount2")
    LogisticOrderExportExcelVo orderVoToExportExcelVo(LogisticOrderVo order);
}
