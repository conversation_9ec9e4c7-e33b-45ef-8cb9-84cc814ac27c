package com.senox.web.convert;

import com.senox.realty.vo.AdvertisingIncomeVo;
import com.senox.web.vo.AdvertisingIncomeExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/8/25 9:30
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingIncomeConvertor {


    /**
     * 收益明细转导出视图
     * @param incomeVo
     * @return
     */
    AdvertisingIncomeExcelVo toExcelVo(AdvertisingIncomeVo incomeVo);
}
