package com.senox.web.convert;

import com.senox.realty.vo.RealtyWeVo;
import com.senox.web.vo.RealtyWeExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 11:15
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyWeExcelConvertor {

    /**
     * excel 对象转视图对象
     * @param excelVo
     * @return
     */
    RealtyWeVo excelToVo(RealtyWeExcelVo excelVo);

    /**
     * excel 对象转视图对象
     * @param excelVo
     * @return
     */
    List<RealtyWeVo> excelToVo(List<RealtyWeExcelVo> excelVo);
}
