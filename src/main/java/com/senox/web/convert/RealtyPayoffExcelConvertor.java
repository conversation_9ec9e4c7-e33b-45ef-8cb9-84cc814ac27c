package com.senox.web.convert;

import com.senox.common.utils.StringUtils;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.RealtyPayoffVo;
import com.senox.web.vo.RealtyPayoffExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/25 14:03
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyPayoffExcelConvertor {

    /**
     * 应付账单转导出视图对象
     * @param vo
     * @return
     */
    @Mapping(target = "paidStatus", expression = "java(status2PaidStatus(vo.getStatus()))")
    RealtyPayoffExportVo payoff2ExportVo(RealtyPayoffVo vo);

    /**
     * 订单状态描述
     * @param status
     * @return
     */
    default String status2PaidStatus(Integer status) {
        BillStatus billStatus = BillStatus.fromStatus(status);
        return billStatus == null ? StringUtils.EMPTY : billStatus.getValue();
    }
}
