package com.senox.web.convert;

import com.senox.user.vo.CustomerBusinessVo;
import com.senox.web.vo.CustomerBusinessExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/28 16:10
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CustomerBusinessExcelConvertor {

    /**
     * 导入excel对象转视图对象
     * @param excelVos
     * @return
     */
    List<CustomerBusinessVo> toVoList(List<CustomerBusinessExcelVo> excelVos);
}
