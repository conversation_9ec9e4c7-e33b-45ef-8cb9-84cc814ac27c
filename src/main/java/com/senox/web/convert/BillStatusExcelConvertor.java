package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.constant.BillStatus;
import com.senox.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/12/2 14:42
 */
public class BillStatusExcelConvertor implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) throws Exception {
        BillStatus status = BillStatus.fromValue(value);
        return new WriteCellData<>(status == null ? StringUtils.EMPTY : status.getDescription());
    }
}
