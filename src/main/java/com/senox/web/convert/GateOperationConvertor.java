package com.senox.web.convert;

import com.senox.dm.vo.GateOperationDayReportVo;
import com.senox.dm.vo.GateOperationLogVo;
import com.senox.web.vo.GateOperationDayReportExportVo;
import com.senox.web.vo.GateOperationLogExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/6/18 11:33
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface GateOperationConvertor {


    @Mapping(target = "overTime", expression = "java(checkOperationOvertime(log.getOpDuration(), log.getSafeDuration()))")
    GateOperationLogExportVo toLogExportVo(GateOperationLogVo log);

    /**
     * 门禁操作日报转excel对象
     * @param report
     * @return
     */
    GateOperationDayReportExportVo toReportExportVo(GateOperationDayReportVo report);


    default Boolean checkOperationOvertime(Integer opDuration, Integer safeDuration) {
        if (opDuration == null || safeDuration == null) {
            return Boolean.FALSE;
        }

        return opDuration > safeDuration;
    }
}
