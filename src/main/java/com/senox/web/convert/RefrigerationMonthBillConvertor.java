package com.senox.web.convert;

import com.senox.cold.vo.RefrigerationMonthBillVo;
import com.senox.web.vo.RefrigerationMonthBillExcelVo;
import com.senox.web.vo.RefrigerationMonthBillWithDepositVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/12/16 11:57
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RefrigerationMonthBillConvertor {


    /**
     * 视图对象 转 excel视图对象
     * @param vo
     * @return
     */
    RefrigerationMonthBillExcelVo vo2ExcelVo(RefrigerationMonthBillVo vo);

    /**
     * 带押金视图对象 转 excel 视图对象
     * @param vo
     * @return
     */
    RefrigerationMonthBillExcelVo depositVo2ExcelVo(RefrigerationMonthBillWithDepositVo vo);



}
