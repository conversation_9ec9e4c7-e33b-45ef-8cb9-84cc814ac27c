package com.senox.web.convert;


import com.senox.cold.constant.NormalTemperatureWarehousingFee;
import com.senox.cold.vo.NormalTemperatureWarehousingBillAddDto;
import com.senox.cold.vo.NormalTemperatureWarehousingBillItemVo;
import com.senox.common.exception.BusinessException;
import com.senox.user.vo.MerchantVo;
import com.senox.web.service.MerchantService;
import com.senox.web.vo.NormalTemperatureWarehousingBillImportExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-20
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface NormalTemperatureWarehousingBillConvert {

    default List<NormalTemperatureWarehousingBillAddDto> toVo(MerchantService merchantService, List<NormalTemperatureWarehousingBillImportExcelVo> vo) {
        if (CollectionUtils.isEmpty(vo)) {
            return Collections.emptyList();
        }
        List<NormalTemperatureWarehousingBillAddDto> dtoList = new ArrayList<>(vo.size());
        for (NormalTemperatureWarehousingBillImportExcelVo excelVo : vo) {
            MerchantVo merchant = merchantService.findByName(excelVo.getMerchantName());
            if (null == merchant) {
                throw new BusinessException(String.format("未找到商户[%s]", excelVo.getMerchantName()));
            }
            NormalTemperatureWarehousingBillAddDto dto = new NormalTemperatureWarehousingBillAddDto();
            List<NormalTemperatureWarehousingBillItemVo> items = new ArrayList<>(6);
            dto.setMerchantId(merchant.getId());
            dto.setRemark(excelVo.getRemark());
            items.add(toItem(NormalTemperatureWarehousingFee.STORAGE, excelVo.getStorageCharges()));
            items.add(toItem(NormalTemperatureWarehousingFee.HANDLING, excelVo.getHandlingCharges()));
            items.add(toItem(NormalTemperatureWarehousingFee.SORTING, excelVo.getSortingCharges()));
            items.add(toItem(NormalTemperatureWarehousingFee.LOADING, excelVo.getLoadingCharges()));
            items.add(toItem(NormalTemperatureWarehousingFee.FREIGHT, excelVo.getFreightCharges()));
            items.add(toItem(NormalTemperatureWarehousingFee.OTHER, excelVo.getOtherCharges()));
            dto.setItems(items);
            dtoList.add(dto);
        }
        return dtoList;
    }

    default NormalTemperatureWarehousingBillItemVo toItem(NormalTemperatureWarehousingFee fee, BigDecimal charges) {
        NormalTemperatureWarehousingBillItemVo item = new NormalTemperatureWarehousingBillItemVo();
        item.setFeeId(fee.getFeeId());
        item.setFeeName(fee.getName());
        item.setAmount(null == charges ? BigDecimal.ZERO : charges);
        return item;
    }
}
