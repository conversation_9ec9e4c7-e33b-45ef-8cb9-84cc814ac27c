package com.senox.web.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.common.utils.StringUtils;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.vo.OneTimeFeeBillTradeVo;
import com.senox.web.vo.OneTimeFeeBillExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/3/31 16:34
 */
@Mapper
public interface OneTimeFeeBillExportConverter extends BaseConvert<OneTimeFeeBillTradeVo, OneTimeFeeBillExportVo> {

    OneTimeFeeBillExportConverter INSTANCE = Mappers.getMapper(OneTimeFeeBillExportConverter.class);

    @Mapping(target = "status", expression = "java(billStatusToString(domain.getStatus()))")
    @Override
    OneTimeFeeBillExportVo toV(OneTimeFeeBillTradeVo domain);

    /**
     * 订单状态中文描述
     * @param status
     * @return
     */
    default String billStatusToString(Integer status) {
        BillStatus billStatus = BillStatus.fromStatus(status);
        return billStatus == null ? StringUtils.EMPTY : billStatus.getValue();
    }
}
