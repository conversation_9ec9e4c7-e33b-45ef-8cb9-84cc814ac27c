package com.senox.web.convert;

import com.senox.user.vo.EnterpriseViewVo;
import com.senox.web.vo.EnterpriseRealtyExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/9/20 14:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnterpriseConvertor {

    /**
     * 经营户视图转导出视图
     * @param view
     * @return
     */
    EnterpriseRealtyExportVo toExportVo(EnterpriseViewVo view);
}
