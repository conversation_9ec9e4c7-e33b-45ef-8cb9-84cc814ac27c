package com.senox.web.convert;

import com.senox.common.utils.DateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.tms.constant.LogisticStatisticsIncomeType;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import com.senox.web.vo.LogisticStatisticsDayReportExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.ResolverStyle;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/2 10:45
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticStatisticsDayReportConvertor {

     String PATTERN = "yyyy/M/d";

    /**
     * excelVo to Vo
     * @param excelVoList
     * @return
     */
    @Mapping(target = "reportDate", expression = "java(getReportDate(vo.getReportDate()))")
    @Mapping(target = "incomeType", expression = "java(getIncomeType(vo.getIncomeType()))")
    @Mapping(target = "charteredBus", expression = "java(getCharteredBus(vo.getCharteredBus()))")
    @Mapping(target = "paymentTime", expression = "java(getPaymentTime(vo.getPaymentTime()))")
    List<LogisticStatisticsDayReportVo> toVo(List<LogisticStatisticsDayReportExcelVo> excelVoList);


    default LocalDate getReportDate(String reportDate) {
        return StringUtils.isBlank(reportDate) ? null : DateUtils.parseDate(reportDate, PATTERN, ResolverStyle.LENIENT);
    }

    default Integer getIncomeType(String incomeType) {
        return StringUtils.isBlank(incomeType) ? null : LogisticStatisticsIncomeType.fromName(incomeType).getNumber();
    }

    default Boolean getCharteredBus(String charteredBus) {
        if (StringUtils.isBlank(charteredBus)) {
            return Boolean.FALSE;
        }
        return charteredBus.equals("是") ? Boolean.TRUE : Boolean.FALSE;
    }

    default LocalDateTime getPaymentTime(String paymentTime) {
        return StringUtils.isBlank(paymentTime) ? null : DateUtils.parseDate(paymentTime, PATTERN, ResolverStyle.LENIENT).atStartOfDay();
    }
}
