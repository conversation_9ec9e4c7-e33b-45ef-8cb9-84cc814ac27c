package com.senox.web.convert;


import com.senox.web.vo.MaterialOutRecordInfoExportVo;
import com.senox.wms.vo.MaterialOutRecordInfoVo;
import org.mapstruct.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-4-20
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaterialOutRecordInfoConvertor {

    /**
     * vo转export
     *
     * @param vo vo
     * @return export
     */
    @Mappings({
            @Mapping(target = "outType", expression = "java(vo.getOutType().equals(0) ? \"普通出仓\" : \"调拨出仓\")")
    })
    MaterialOutRecordInfoExportVo toExport(MaterialOutRecordInfoVo vo);


    /**
     * vo转export
     *
     * @param vo vo
     * @return export
     */
    List<MaterialOutRecordInfoExportVo> toExport(List<MaterialOutRecordInfoVo> vo);
}
