package com.senox.web.convert;

import com.senox.common.domain.PenaltySetting;
import com.senox.web.vo.PenaltySettingVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/2/1 11:59
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PenaltySettingConvertor {

    /**
     * 滞纳金配置实体对象转视图对象
     * @param domain
     * @return
     */
    PenaltySettingVo toV(PenaltySetting domain);

    /**
     * 滞纳金配置视图对象转实体对象
     * @param vo
     * @return
     */
    PenaltySetting toDo(PenaltySettingVo vo);
}
