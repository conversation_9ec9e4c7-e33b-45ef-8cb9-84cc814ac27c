package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;
import com.senox.realty.constant.ContractType;

/**
 * <AUTHOR>
 * @date 2022/10/28 10:29
 */
public class ContractTypeExcelConvertor implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        ContractType contractType = ContractType.fromChName(cellData.getStringValue());
        return contractType == null ? null : contractType.getValue();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        ContractType contractType = ContractType.fromValue(value);
        return new WriteCellData<>(contractType == null ? StringUtils.EMPTY : contractType.getChName());
    }
}
