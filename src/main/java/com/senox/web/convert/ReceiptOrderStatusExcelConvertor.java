package com.senox.web.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.StringUtils;
import com.senox.pm.constant.ReceiptOrderStatus;

/**
 * <AUTHOR>
 * @date 2022/12/2 14:42
 */
public class ReceiptOrderStatusExcelConvertor implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) throws Exception {
        ReceiptOrderStatus status = ReceiptOrderStatus.fromValue(value);
        return new WriteCellData<>(status == null ? StringUtils.EMPTY : status.getDescription());
    }
}
