package com.senox.web.convert;

import com.senox.user.vo.DiningInformationVo;
import com.senox.web.vo.DiningInformationExcelVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface DiningInformationConvertor {

    /**
     * 转导出视图
     * @param vo
     * @return
     */
    DiningInformationExcelVo voToExcelVo(DiningInformationVo vo);
}
