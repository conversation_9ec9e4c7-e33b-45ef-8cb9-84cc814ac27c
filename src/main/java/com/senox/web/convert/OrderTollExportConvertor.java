package com.senox.web.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.common.utils.StringUtils;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderTollVo;
import com.senox.web.vo.OrderTollExportVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/31 9:27
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface OrderTollExportConvertor extends BaseConvert<OrderTollVo, OrderTollExportVo> {

    @Mapping(target = "payWay", expression = "java(paywayStrToInt(vo.getPayWay()))")
    @Override
    OrderTollVo toDo(OrderTollExportVo vo);

    @Mapping(target = "payWay", expression = "java(paywayIntToStr(domain.getPayWay()))")
    @Mapping(target = "status", expression = "java(statusIntToStr(domain.getStatus()))")
    @Override
    OrderTollExportVo toV(OrderTollVo domain);

    @Mapping(target = "payWay", expression = "java(paywayStrToInt(vo.getPayWay()))")
    @Override
    List<OrderTollVo> toDo(List<OrderTollExportVo> vo);

    @Mapping(target = "payWay", expression = "java(paywayIntToStr(domain.getPayWay()))")
    @Override
    List<OrderTollExportVo> toV(List<OrderTollVo> domain);

    default String paywayIntToStr(Integer payway) {
        PayWay item = PayWay.fromValue(payway);
        return item == null ? StringUtils.EMPTY : item.getDescription();
    }

    default Integer paywayStrToInt(String value) {
        if (!StringUtils.isBlank(value)) {
            for (PayWay item : PayWay.values()) {
                if (Objects.equals(item.getDescription(), value)) {
                    return item.getValue();
                }
            }
        }
        return null;
    }

    default String statusIntToStr(Integer status) {
        OrderStatus item = OrderStatus.fromStatus(status);
        return item == null ? StringUtils.EMPTY : item.getDescription();
    }
}
