package com.senox.web;

import com.senox.common.spring.SenoxBaseConfigure;
import com.senox.common.spring.SenoxWebConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @Date 2021/1/6 9:23
 */
@SpringBootApplication
@Import({SenoxBaseConfigure.class, SenoxWebConfigure.class})
public class WebApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
