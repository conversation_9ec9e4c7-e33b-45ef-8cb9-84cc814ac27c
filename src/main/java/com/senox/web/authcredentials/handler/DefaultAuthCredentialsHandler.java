package com.senox.web.authcredentials.handler;

import com.senox.common.utils.AesUtils;
import com.senox.common.utils.Md5Utils;
import com.senox.common.utils.StringUtils;
import com.senox.user.authcredentials.constant.AuthCredentialsConst;
import com.senox.user.authcredentials.context.CredentialsManageContext;
import com.senox.user.authcredentials.dto.AuthCredentialsDto;
import com.senox.user.authcredentials.exception.CredentialsException;
import com.senox.user.authcredentials.handler.AuthCredentialsHandler;
import com.senox.user.vo.AuthCredentialsVo;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2024-1-2
 */
@Configuration
public class DefaultAuthCredentialsHandler implements AuthCredentialsHandler {
    protected int validityPeriod = 5;
    protected String appSecret;

    @Override
    public String verifyAndDecrypt(AuthCredentialsDto authCredentials, String content, Integer checksumHashIteration, Integer contentHashIteration) {
        //凭证校验
        authCredentials.validator();
        //获取上下文用户私钥
        AuthCredentialsVo authCredentialsVo = CredentialsManageContext.getAuthCredentials();
        if (null != authCredentialsVo && !StringUtils.isBlank(authCredentialsVo.getAppSecret())){
            appSecret = authCredentialsVo.getAppSecret();
        }
        String appKey = authCredentials.getAppKey();
        String sign = authCredentials.getSign();
        checksum(authCredentials, appSecret, checksumHashIteration);
        String decryptContent = AesUtils.decrypt(content, appKey, appSecret, contentHashIteration);
        checkSign(decryptContent, sign);
        return decryptContent;
    }

    @Override
    public void checksum(AuthCredentialsDto authCredentials, String appSecret, Integer checksumHashIteration) {
        String encrypt = AesUtils.encrypt(authCredentials.getAppKey().concat(authCredentials.getSign()), authCredentials.getAppKey(), appSecret, checksumHashIteration);
        if (!encrypt.equals(authCredentials.getChecksum())) {
            throw new CredentialsException(AuthCredentialsConst.SIGNATURE_FAILURE);
        }
    }

    @Override
    public void checkSign(String context, String sign) {
        if (!Md5Utils.encode(context).equals(sign)) {
            throw new CredentialsException(AuthCredentialsConst.SIGNATURE_FAILURE);
        }
    }

    /**
     * 校验时间
     *
     * @param currentTime 请求时间
     */
    protected void checkCurrentTime(Long currentTime) {
        if (LocalDateTime.ofEpochSecond(currentTime, 0, ZoneOffset.ofHours(8)).until(LocalDateTime.now(), ChronoUnit.MINUTES) > validityPeriod) {
            throw new CredentialsException(AuthCredentialsConst.REQUEST_EXPIRED);
        }
    }
}
