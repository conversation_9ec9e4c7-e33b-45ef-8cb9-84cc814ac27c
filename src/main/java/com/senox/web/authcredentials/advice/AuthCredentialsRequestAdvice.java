package com.senox.web.authcredentials.advice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RequestUtils;
import com.senox.common.utils.StringUtils;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.api.clients.AuthCredentialsClient;
import com.senox.user.authcredentials.advice.AuthCredentialsHeader;
import com.senox.user.authcredentials.annotation.AuthCredentialsIndicate;
import com.senox.user.authcredentials.context.CredentialsManage;
import com.senox.user.authcredentials.context.CredentialsManageContext;
import com.senox.user.authcredentials.dto.AuthCredentialsDto;
import com.senox.user.authcredentials.handler.AuthCredentialsHandler;
import com.senox.user.vo.AuthCredentialsVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import javax.servlet.http.HttpServletRequest;
import java.beans.Introspector;
import java.io.*;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-9
 */
@Slf4j
@RequiredArgsConstructor
@ControllerAdvice
public class AuthCredentialsRequestAdvice extends RequestBodyAdviceAdapter {
    private static final String CONTENT = "content";
    private final ApplicationContext applicationContext;
    private final AuthCredentialsClient authCredentialsClient;

    @SneakyThrows
    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        Method method = methodParameter.getMethod();
        if (null != method && method.isAnnotationPresent(AuthCredentialsIndicate.class)) {
            AuthCredentialsDto authCredentialsDto = getAuthHeader(RequestUtils.getRequest());
            CredentialsManageContext.setAuthHeader(authCredentialsDto);
            setContextUser(authCredentialsDto.getAppKey());
            log.debug("【Auth】Request appKey: {}", authCredentialsDto.getAppKey());
            log.debug("【Auth】Request sign: {}", authCredentialsDto.getSign());
            log.debug("【Auth】Request checksum: {}", authCredentialsDto.getChecksum());
            log.debug("【Auth】Request currentTime: {}", authCredentialsDto.getCurrentTime());
            return method.getAnnotation(AuthCredentialsIndicate.class).paramDecrypt();
        }

        return false;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        AuthCredentialsIndicate annotation = Objects.requireNonNull(parameter.getMethod()).getAnnotation(AuthCredentialsIndicate.class);
        Class<? extends AuthCredentialsHandler> handlerClass = annotation.decryptHandler();
        AuthCredentialsHandler decryptHandler = applicationContext.getBean(Introspector.decapitalize(handlerClass.getSimpleName()), handlerClass);
        String body = new BufferedReader(new InputStreamReader(inputMessage.getBody(), StandardCharsets.UTF_8)).lines().collect(Collectors.joining());
        log.debug("【Auth】Request body: {}", body);
        Map<String, String> bodyMap = JsonUtils.OBJECT_MAPPER.readValue(body, new TypeReference<Map<String, String>>() {
        });
        String rawContent = bodyMap.get(CONTENT);
        log.debug("【Auth】Request raw content: {}", rawContent);
        String decryptContent = decryptHandler.verifyAndDecrypt(CredentialsManageContext.getAuthHeader()
                , rawContent
                , CredentialsManage.CHECKSUM_HASH_ITERATION
                , CredentialsManage.CONTENT_HASH_ITERATION
        );
        log.debug("【Auth】Request decrypt content: {}", decryptContent);
        return new HttpInputMessage() {
            @Override
            public InputStream getBody() {
                return new ByteArrayInputStream(decryptContent.getBytes(StandardCharsets.UTF_8));
            }

            @Override
            public HttpHeaders getHeaders() {
                return inputMessage.getHeaders();
            }
        };
    }

    /**
     * 从响应头取认证凭证
     *
     * @param request request
     * @return 返回认证凭证
     */
    private AuthCredentialsDto getAuthHeader(HttpServletRequest request) {
        String appKey = request.getHeader(AuthCredentialsHeader.APP_KEY);
        String sing = request.getHeader(AuthCredentialsHeader.SING);
        String currentTime = request.getHeader(AuthCredentialsHeader.CURRENT_TIME);
        String checksum = request.getHeader(AuthCredentialsHeader.CHECKSUM);
        return new AuthCredentialsDto(appKey, sing, StringUtils.isBlank(currentTime) ? null : Long.parseLong(currentTime), checksum);
    }

    /**
     * 设置上下文用户
     *
     * @param appKey 公钥
     */
    protected void setContextUser(String appKey) {
        AdminUserDto adminUserDto = new AdminUserDto();
        adminUserDto.setAuthCredentials(appKey);
        AdminContext.setUser(adminUserDto);
        //查询凭证
        AuthCredentialsVo authCredentialsVo = authCredentialsClient.getByAppKey();
        if (null == authCredentialsVo) {
            throw new BusinessException(ResultConst.OAUTH_CREDENTIALS_INVALID);
        }
        //把凭证set到上下文
        CredentialsManageContext.setAuthCredentials(authCredentialsVo);
    }
}
