package com.senox.web.authcredentials.advice;

import com.senox.common.utils.AesUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.Md5Utils;
import com.senox.common.vo.Result;
import com.senox.user.authcredentials.advice.AuthCredentialsHeader;
import com.senox.user.authcredentials.annotation.AuthCredentialsIndicate;
import com.senox.user.authcredentials.context.CredentialsManage;
import com.senox.user.authcredentials.context.CredentialsManageContext;
import com.senox.user.authcredentials.dto.AuthCredentialsDto;
import com.senox.user.vo.AuthCredentialsVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @date 2023-10-10
 */
@Slf4j
@RequiredArgsConstructor
@ControllerAdvice
public class AuthCredentialsResponseAdvice implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        Method method = returnType.getMethod();
        return null != method
                && method.isAnnotationPresent(AuthCredentialsIndicate.class)
                && method.getAnnotation(AuthCredentialsIndicate.class).paramEncrypt()
                && !method.getReturnType().equals(void.class);
    }


    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        String content = body instanceof String ? (String) body : JsonUtils.object2Json(body);
        log.debug("【Auth】Response raw content: {}", content);
        AuthCredentialsDto authCredentials = encrypt(content);
        settingAuthHeader(response.getHeaders(), authCredentials);
        return body instanceof String ? authCredentials.getContent() : Result.success((Object) authCredentials.getContent());
    }

    /**
     * 加密
     *
     * @param rawContent 原始内容
     * @return 返回加密后的内容
     */
    private static AuthCredentialsDto encrypt(String rawContent) {
        AuthCredentialsVo authCredentials = CredentialsManageContext.getAuthCredentials();
        String appKey = authCredentials.getAppKey();
        String appSecret = authCredentials.getAppSecret();
        int checksumHashIteration = CredentialsManage.CHECKSUM_HASH_ITERATION;
        Integer contentHashIteration = CredentialsManage.CONTENT_HASH_ITERATION;
        //加密
        String sign = Md5Utils.encode(rawContent);
        Long currentTime = LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).getEpochSecond();
        String checksum = AesUtils.encrypt(appKey.concat(sign), appKey, appSecret, checksumHashIteration);
        String encryptContent = AesUtils.encrypt(rawContent, appKey, appSecret, contentHashIteration);
        log.debug("【Auth】Response encrypt content: {}", encryptContent);
        return new AuthCredentialsDto(appKey, sign, currentTime, checksum, encryptContent);
    }

    /**
     * 设置请求头认证凭证
     *
     * @param headers            请求头
     * @param authCredentialsDto 认证凭证
     */
    private void settingAuthHeader(HttpHeaders headers, AuthCredentialsDto authCredentialsDto) {
        headers.add(AuthCredentialsHeader.APP_KEY, authCredentialsDto.getAppKey());
        headers.add(AuthCredentialsHeader.SING, authCredentialsDto.getSign());
        headers.add(AuthCredentialsHeader.CURRENT_TIME, String.valueOf(authCredentialsDto.getCurrentTime()));
        headers.add(AuthCredentialsHeader.CHECKSUM, authCredentialsDto.getChecksum());
    }

}
