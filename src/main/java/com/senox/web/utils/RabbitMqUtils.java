package com.senox.web.utils;

import com.senox.common.utils.SystemUtils;
import com.senox.web.config.RabbitMqEntity;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;

/**
 * MQ工具类
 *
 * <AUTHOR>
 * @date 2023/5/30 10:09
 */
public class RabbitMqUtils {

    private RabbitMqUtils() {
    }

    /**
     * 创建mq连接对象
     * @param entity
     * @param vhost
     * @return
     */
    public static ConnectionFactory buildMQConnectionFactory(RabbitMqEntity entity, String vhost) {
        CachingConnectionFactory factory = new CachingConnectionFactory();
        factory.setAddresses(entity.getAddresses());
        factory.setUsername(entity.getUsername());
        factory.setPassword(entity.getPassword());
        factory.setConnectionTimeout(entity.getConnectionTimeout());
        factory.setVirtualHost(vhost);
        return factory;
    }

    /**
     * 构建队列监听器工厂
     */
    public static SimpleRabbitListenerContainerFactory buildListenerContainerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                                     MessageConverter messageConverter,
                                                                                     ConnectionFactory connectionFactory,
                                                                                     int consumerThreads) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setMessageConverter(messageConverter);
        factory.setConcurrentConsumers(consumerThreads);
        factory.setConsumerTagStrategy(x -> SystemUtils.getMachineName());
        configurer.configure(factory, connectionFactory);
        return factory;
    }

}
