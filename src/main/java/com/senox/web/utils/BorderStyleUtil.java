package com.senox.web.utils;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;

public class BorderStyleUtil {

    public static WriteCellStyle createBorderStyle() {
        WriteCellStyle borderStyle = new WriteCellStyle();

        // 设置边框样式
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderBottom(BorderStyle.THIN);

        // 设置边框颜色
        borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());  // 左边框颜色
        borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色
        borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());  // 上边框颜色
        borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色

        return borderStyle;
    }
}
