package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.LogisticStatisticsClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:52
 */
@Component
@RequiredArgsConstructor
public class LogisticStatisticsComponent {

    private final LogisticStatisticsClient logisticStatisticsClient;

    /**
     * 批量添加货物统计报表
     * @param reportVoList
     */
    public void batchAddLogisticStatisticsDayReport(List<LogisticStatisticsDayReportVo> reportVoList) {
        try {
            logisticStatisticsClient.batchAddLogisticStatisticsDayReport(reportVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加货物统计报表
     * @param logisticStatisticsDayReportVo
     * @return
     */
    public Long addLogisticStatisticsDayReport(LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        try {
            return logisticStatisticsClient.addLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新货物统计报表
     * @param logisticStatisticsDayReportVo
     */
    public void updateLogisticStatisticsDayReport(LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        try {
            logisticStatisticsClient.updateLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据Id获取货物统计报表
     * @param id
     * @return
     */
    public LogisticStatisticsDayReportVo findLogisticStatisticsDayReportById(Long id) {
        try {
            return logisticStatisticsClient.findLogisticStatisticsDayReportById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据Id删除货物统计报表
     * @param id
     */
    public void deleteLogisticStatisticsDayReportById(Long id) {
        try {
            logisticStatisticsClient.deleteLogisticStatisticsDayReportById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 货物统计报表分页
     * @param searchVo
     * @return
     */
    public LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(LogisticStatisticsDayReportSearchVo searchVo) {
        try {
            return logisticStatisticsClient.page(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return LogisticStatisticsDayReportPageResult.emptyPage();
    }

    /**
     * 货物统计批量收款
     * @param batchUpdateVo
     */
    public void batchUpdate(LogisticStatisticsDayReportBatchUpdateVo batchUpdateVo) {
        try {
            logisticStatisticsClient.batchUpdate(batchUpdateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
