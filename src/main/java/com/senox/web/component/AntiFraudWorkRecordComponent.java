package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.api.clients.AntiFraudWorkRecordClient;
import com.senox.realty.vo.AntiFraudWorkRecordSearchVo;
import com.senox.realty.vo.AntiFraudWorkRecordStatistics;
import com.senox.realty.vo.AntiFraudWorkRecordVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-02
 **/
@RequiredArgsConstructor
@Component
public class AntiFraudWorkRecordComponent {
    private final AntiFraudWorkRecordClient workRecordClient;

    /**
     * 添加
     * @param workRecord 工作记录参数集
     */
    public void add(AntiFraudWorkRecordVo workRecord) {
        try {
            workRecordClient.add(workRecord);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新
     * @param workRecord 工作记录参数
     */
    public void update(AntiFraudWorkRecordVo workRecord) {
        try {
            workRecordClient.update(workRecord);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的数据
     */
    public AntiFraudWorkRecordVo findById(Long id) {
        try {
            return workRecordClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id批量删除
     * @param ids id集
     */
    public void deleteByIds(List<Long> ids) {
        try {
            workRecordClient.deleteByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(AntiFraudWorkRecordSearchVo search) {
        try {
            return workRecordClient.countList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<AntiFraudWorkRecordVo> list(AntiFraudWorkRecordSearchVo search) {
        try {
            return workRecordClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<AntiFraudWorkRecordVo, AntiFraudWorkRecordStatistics> pageList(AntiFraudWorkRecordSearchVo search) {
        try {
            return workRecordClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

}
