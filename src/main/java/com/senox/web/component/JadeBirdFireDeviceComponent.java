package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.api.clients.JadeBirdFireDeviceClient;
import com.senox.dm.vo.JadeBirdFireDeviceControlRequest;
import com.senox.dm.vo.JadeBirdFireDeviceSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Component
public class JadeBirdFireDeviceComponent {
    private final JadeBirdFireDeviceClient fireDeviceClient;

    /**
     * 添加设备
     *
     * @param fireDevice 消防设备
     */
    public void add(JadeBirdFireDeviceVo fireDevice) {
        try {
            fireDeviceClient.add(fireDevice);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新设备
     *
     * @param fireDevice 消防设备
     */
    public void update(JadeBirdFireDeviceVo fireDevice) {
        try {
            fireDeviceClient.update(fireDevice);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id删除设备
     *
     * @param fireDeviceId 消防设备id
     */
    public void deleteById(Long fireDeviceId) {
        try {
            fireDeviceClient.deleteById(fireDeviceId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据psn查找设备
     *
     * @param psn psn
     * @return 设备信息
     */
    public JadeBirdFireDeviceVo findByPsn(String psn) {
        try {
            return fireDeviceClient.findByPsn(psn);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<JadeBirdFireDeviceVo> pageList(JadeBirdFireDeviceSearchVo search) {
        try {
            fireDeviceClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 设备复位
     *
     * @param fireDeviceControlRequest 消防设备控制请求参数
     */
    public void reset(JadeBirdFireDeviceControlRequest fireDeviceControlRequest) {
        try {
            fireDeviceClient.reset(fireDeviceControlRequest);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
