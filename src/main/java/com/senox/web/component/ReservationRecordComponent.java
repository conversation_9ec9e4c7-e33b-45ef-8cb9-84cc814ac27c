package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.ReservationRecordClient;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/27 18:32
 */
@Component
@RequiredArgsConstructor
public class ReservationRecordComponent {

    private final ReservationRecordClient reservationRecordClient;

    /**
     * 添加预约记录
     * @param recordVo
     * @return
     */
    public Long addReservationRecord(ReservationRecordVo recordVo) {
        try {
            return reservationRecordClient.addReservationRecord(recordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新预约记录
     * @param recordVo
     * @return
     */
    public void updateParkingRecord(ReservationRecordVo recordVo) {
        try {
            reservationRecordClient.updateParkingRecord(recordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取预约记录
     * @param id
     * @return
     */
    public ReservationRecordVo findById(Long id) {
        try {
            return reservationRecordClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 预约记录列表
     * @param searchVo
     * @return
     */
    public PageResult<ReservationRecordVo> page(ReservationRecordSearchVo searchVo) {
        try {
            return reservationRecordClient.page(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 预约记录同行人数合计
     * @param searchVo
     * @return
     */
    public ReservationRecordVo sumReservationRecord(ReservationRecordSearchVo searchVo) {
        try {
            return reservationRecordClient.sumReservationRecord(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
