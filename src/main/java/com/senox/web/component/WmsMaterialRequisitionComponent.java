package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.wms.api.clients.MaterialRequisitionClient;
import com.senox.wms.vo.requisition.MaterialFlowResult;
import com.senox.wms.vo.requisition.MaterialRequisitionSearchVo;
import com.senox.wms.vo.requisition.MaterialRequisitionVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-12
 **/
@RequiredArgsConstructor
@Component
public class WmsMaterialRequisitionComponent {
    private final MaterialRequisitionClient materialRequisitionClient;


    /**
     * 保存申购
     *
     * @param requisition 申购参数
     */
    public Long save(MaterialRequisitionVo requisition) {
        try {
            materialRequisitionClient.save(requisition);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 保存申购并提交
     * @param requisition 申购参数
     */
    public void saveAndSubmit(MaterialRequisitionVo requisition) {
        try {
            materialRequisitionClient.saveAndSubmit(requisition);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据流程id查找申购
     *
     * @param flowId 流程id
     * @return 返回查找到的数据
     */
    public MaterialRequisitionVo findByFlowId(Long flowId) {
        try {
            return materialRequisitionClient.findByFlowId(flowId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 申购列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */

    public List<MaterialRequisitionVo> list(MaterialRequisitionSearchVo search) {
        try {
            return materialRequisitionClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 申购列表分页
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialRequisitionVo> pageList(MaterialRequisitionSearchVo search) {
        try {
            return materialRequisitionClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 申购历史
     * @param instanceId 实例id
     * @return 返回申购历史
     */
    public MaterialFlowResult history(Long instanceId) {
        try {
            return materialRequisitionClient.history(instanceId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

}
