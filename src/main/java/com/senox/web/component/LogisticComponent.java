package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.LogisticOrderClient;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import com.senox.tms.vo.LogisticPayoffVo;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 10:36
 */
@Component
@RequiredArgsConstructor
public class LogisticComponent {

    private final LogisticOrderClient logisticOrderClient;

    /**
     * 添加物流单
     * @param order
     * @return
     */
    public Long addLogisticOrder(LogisticOrderVo order) {
        try {
            return logisticOrderClient.addLogisticOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新物流单
     * @param order
     */
    public void updateLogisticOrder(LogisticOrderVo order) {
        try {
            logisticOrderClient.updateLogisticOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量保存物流单
     * @param orderList
     * @param overwrite
     */
    public void saveLogisticOrderBatch(List<LogisticOrderVo> orderList, Boolean overwrite) {
        try {
            logisticOrderClient.saveLogisticOrderBatch(orderList, overwrite);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量更新物流单信息
     * @param editInfo
     */
    public void editLogisticOrderBatch(LogisticOrderEditBatchVo editInfo) {
        try {
            logisticOrderClient.editLogisticOrderBatch(editInfo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除物流订单
     * @param orderIds
     */
    public void deleteLogisticOrder(List<Long> orderIds) {
        try {
            logisticOrderClient.deleteLogisticOrder(orderIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 计算物流费折扣率
     * @param search
     * @return
     */
    public BigDecimal calShipDiscount(ShipOrderDiscountSearchVo search) {
        try {
            return logisticOrderClient.calShipDiscount(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return BigDecimal.ONE;
    }

    /**
     * 获取物流单详情
     * @param id
     * @return
     */
    public LogisticOrderVo findLogisticOrderById(Long id) {
        try {
            return logisticOrderClient.findLogisticOrderById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物流单合计
     * @param search
     * @return
     */
    public LogisticOrderVo sumLogisticOrder(LogisticOrderSearchVo search) {
        try {
            return logisticOrderClient.sumLogisticOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物流订单列表（无分页）
     * @param search
     * @return
     */
    public List<LogisticOrderVo> listLogisticOrder(LogisticOrderSearchVo search) {
        try {
            return logisticOrderClient.listLogisticOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物流单列表
     * @param search
     * @return
     */
    public PageResult<LogisticOrderVo> listLogisticOrderPage(LogisticOrderSearchVo search) {
        try {
            return logisticOrderClient.listLogisticOrderPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加物流客户应付账单
     * @param payoff
     * @return
     */
    public Long addLogisticPayoff(LogisticPayoffVo payoff) {
        try {
            return logisticOrderClient.addPayoff(payoff);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新物流客户应付账单
     * @param payoff
     */
    public void updateLogisticPayoff(LogisticPayoffVo payoff) {
        try {
            logisticOrderClient.updatePayoff(payoff);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除客户应付账单
     * @param ids
     */
    public void deleteLogisticPayoff(List<Long> ids) {
        try {
            logisticOrderClient.deletePayoff(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 生成物流客户应付账单
     * @param generate
     */
    public void generateLogisticPayoff(LogisticPayoffGenerateVo generate) {
        try {
            logisticOrderClient.generatePayoff(generate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取物流客户应付账单
     * @param id
     * @return
     */
    public LogisticPayoffVo findPayoffById(Long id) {
        try {
            return logisticOrderClient.findPayoffById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取物流客户应付账单合计
     * @param search
     * @return
     */
    public LogisticPayoffVo sumPayoff(LogisticPayoffSearchVo search) {
        try {
            return logisticOrderClient.sumPayoff(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物流客户应付账单列表
     * @param search
     * @return
     */
    public List<LogisticPayoffVo> listPayoff(LogisticPayoffSearchVo search) {
        try {
            return logisticOrderClient.listPayoff(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物流客户应付账单页
     * @param search
     * @return
     */
    public PageResult<LogisticPayoffVo> listPayoffPage(LogisticPayoffSearchVo search) {
        try {
            return logisticOrderClient.listPayoffPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


}
