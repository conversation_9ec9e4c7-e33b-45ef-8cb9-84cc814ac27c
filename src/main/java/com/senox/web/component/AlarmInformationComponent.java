package com.senox.web.component;

import com.senox.car.api.clients.AlarmInformationClient;
import com.senox.car.vo.AlarmInformationSearchVo;
import com.senox.car.vo.AlarmInformationVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/9 10:45
 */
@Component
public class AlarmInformationComponent {

    @Autowired
    private AlarmInformationClient alarmInformationClient;

    /**
     * 更新告警信息
     * @param alarmInformationVo
     */
    public void updateAlarmInformation(AlarmInformationVo alarmInformationVo) {
        try {
            alarmInformationClient.updateAlarmInformation(alarmInformationVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取告警信息
     * @param id
     * @return
     */
    public AlarmInformationVo findById(Long id) {
        try {
            return alarmInformationClient.getAlarmInformation(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 告警列表
     * @param searchVo
     * @return
     */
    public PageResult<AlarmInformationVo> list(AlarmInformationSearchVo searchVo) {
        try {
            return alarmInformationClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
