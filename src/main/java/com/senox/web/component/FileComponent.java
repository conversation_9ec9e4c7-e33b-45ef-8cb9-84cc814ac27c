package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.dm.api.clients.FileClient;
import com.senox.dm.vo.FileVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/12/10 15:17
 */
@Component
public class FileComponent {

    @Autowired
    private FileClient fileClient;


    /**
     * 上传文件
     * @param type
     * @param prefix
     * @param file
     * @return
     */
    public FileVo uploadFile(String type, String prefix, MultipartFile file) {
        try {
            return fileClient.uploadFile(type, prefix, file);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

}
