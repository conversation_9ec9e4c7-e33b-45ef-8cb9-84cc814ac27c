package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.flow.api.clients.WorkflowClient;
import com.senox.flow.vo.WorkflowNodeVo;
import com.senox.flow.vo.WorkflowSearchVo;
import com.senox.flow.vo.WorkflowVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-09
 **/
@Component
@RequiredArgsConstructor
public class WorkflowComponent {
    private final WorkflowClient workflowClient;

    /**
     * 添加
     *
     * @param workflow 流程
     */
    public void add(WorkflowVo workflow) {
        try {
            workflowClient.add(workflow);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除
     *
     * @param flowId 流程id
     */
    public void delete(Long flowId) {
        try {
            workflowClient.delete(flowId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新流程
     *
     * @param workflow 流程
     */
    public void update(WorkflowVo workflow) {
        try {
            workflowClient.update(workflow);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据编码查找流程
     *
     * @param flowCode 流程编码
     * @return 返回查找到的流程
     */
    public WorkflowVo findByCode(String flowCode) {
        try {
            return workflowClient.findByCode(flowCode);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<WorkflowVo> pageList(WorkflowSearchVo search) {
        try {
            return workflowClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
