package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.RealtyRegionDictClient;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import com.senox.realty.vo.RealtyRegionDictVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-04-28
 **/
@Component
@RequiredArgsConstructor
public class RealtyRegionDictComponent {
    private final RealtyRegionDictClient regionDictClient;

    /**
     * 添加字典
     * @param regionDict 字典信息
     */
    public void add(RealtyRegionDictVo regionDict) {
        try {
            regionDictClient.add(regionDict);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id删除字典
     * @param id 字典id
     */
    public void deleteById(Long id) {
        try {
            regionDictClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id更新字典
     * @param regionDict 字典信息
     */
    public void updateById(RealtyRegionDictVo regionDict) {
        try {
            regionDictClient.updateById(regionDict);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 分页查询字典
     * @param search 查询条件
     * @return 分页结果
     */
    public PageResult<RealtyRegionDictVo> pageList(RealtyRegionDictSearchVo search) {
        try {
            return regionDictClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


}
