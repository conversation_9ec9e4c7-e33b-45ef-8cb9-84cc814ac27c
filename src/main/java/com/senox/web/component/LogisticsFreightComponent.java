package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.clients.LogisticsFreightClient;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-27
 */
@RequiredArgsConstructor
@Component
public class LogisticsFreightComponent {
    private final LogisticsFreightClient freightClient;


    /**
     * 批量添加
     *
     * @param freightVos 货运列表
     */
    public void addBatch(List<LogisticsFreightVo> freightVos) {
        try {
            freightClient.add(freightVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找
     *
     * @param id id
     * @return 返回查找到的
     */
    public LogisticsFreightVo findById(Long id) {
        try {
            return freightClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新
     *
     * @param freightVo 货运
     */
    public void update(LogisticsFreightVo freightVo) {
        try {
            freightClient.update(freightVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除
     *
     * @param id 货运id
     */
    public void deleteById(Long id) {
        try {
            freightClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    public List<LogisticsFreightVo> list(LogisticsFreightSearchVo searchVo) {
        try {
            return freightClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页列表
     */
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(LogisticsFreightSearchVo searchVo) {
        try {
            return freightClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 根据id集合查询
     * @param ids
     * @return
     */
    public List<LogisticsFreightVo> listByIds(List<Long> ids) {
        try {
            return freightClient.listByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新账单状态
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        try {
            freightClient.updateBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单备注
     * @param remarkVo
     */
    public void updateBillRemark(LogisticFreightRemarkVo remarkVo) {
        try {
            freightClient.updateBillRemark(remarkVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
