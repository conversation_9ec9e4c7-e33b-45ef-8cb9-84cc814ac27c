package com.senox.web.component;

import com.senox.car.api.clients.MonthlyCarClient;
import com.senox.car.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/1 9:58
 */
@Component
public class MonthlyCarComponent {

    @Autowired
    private MonthlyCarClient monthlyCarClient;

    /**
     * 车型列表
     * @param search
     * @return
     */
    public PageResult<CarTypeVo> listCarType(CarTypeSearchVo search) {
        try {
            return monthlyCarClient.listCarType(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id查找车型
     * @param id
     * @return
     */
    public CarTypeVo findCarTypeById(Long id) {
        try {
            return monthlyCarClient.findCarTypeById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加车型
     * @param carType
     * @return
     */
    public Long addCarType(CarTypeVo carType) {
        try {
            return monthlyCarClient.addCarType(carType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新车型
     * @param carType
     */
    public void updateCarType(CarTypeVo carType) {
        try {
            monthlyCarClient.updateCarType(carType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 月卡车列表
     * @param search
     * @return
     */
    public PageResult<MonthlyCarVo> listMonthlyCar(MonthlyCarSearchVo search) {
        try {
            return monthlyCarClient.listMonthlyCar(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 月卡车账单列表
     * @param search
     * @return
     */
    public MonthlyCarPageResult<MonthlyCarVo> listMonthlyCarBill(MonthlyCarSearchVo search) {
        try {
            return monthlyCarClient.listMonthlyCarBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return MonthlyCarPageResult.emptyPage();
    }

    /**
     * 根据id列表查找月卡车列表
     * @param ids
     * @return
     */
    public List<MonthlyCarVo> listMonthlyCarById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        try {
            return monthlyCarClient.listMonthlyCarById(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id查找月卡车
     * @param id
     * @return
     */
    public MonthlyCarVo findMonthlyCarById(Long id) {
        try {
            return monthlyCarClient.findMonthlyCarById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id朝朝月卡车支付信息
     * @param id
     * @return
     */
    public MonthlyCarVo findMonthlyCarPayById(Long id) {
        try {
            return monthlyCarClient.findMonthlyCarPayById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据车牌查找最近的月卡车记录
     * @param carNo
     * @return
     */
    public MonthlyCarVo findRecentMonthlyCarByCarNo(String carNo) {
        try {
            return monthlyCarClient.findRecentMonthlyCarByCarNo(carNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加月卡车
     * @param car
     * @return
     */
    public Long addMonthlyCar(MonthlyCarEditVo car) {
        try {
            return monthlyCarClient.addMonthlyCar(car);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新月卡车
     * @param car
     */
    public void updateMonthlyCar(MonthlyCarEditVo car) {
        try {
            monthlyCarClient.updateMonthlyCar(car);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 作废月卡车
     * @param cancel
     */
    public void cancelMonthlyCar(MonthlyCarCancelVo cancel) {
        try {
            monthlyCarClient.cancelMonthlyCar(cancel);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新月卡车账单支付状态
     * @param carPaid
     */
    public void updateMonthlyCarPaid(MonthlyCarPaidVo carPaid) {
        try {
            monthlyCarClient.updateMonthlyCarPaid(carPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销月卡车支付
     * @param id
     */
    public void revokeMonthlyCarPaid(Long id) {
        try {
            monthlyCarClient.revokeMonthlyCarPaid(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销月卡车退费
     * @param id
     */
    public void revokeMonthlyCarRefund(Long id) {
        try {
            monthlyCarClient.revokeMonthlyCarRefund(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
