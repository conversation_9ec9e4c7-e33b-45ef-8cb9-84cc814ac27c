package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.BicyclePointClient;
import com.senox.tms.vo.BicyclePointVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:26
 */
@Component
@RequiredArgsConstructor
public class BicyclePointComponent {

    private final BicyclePointClient bicyclePointClient;

    /**
     * 添加三轮车配送地点
     * @param pointVo
     * @return
     */
    public Long addBicyclePoint(BicyclePointVo pointVo) {
        try {
            return bicyclePointClient.addBicyclePoint(pointVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改三轮车配送地点
     * @param pointVo
     */
    public void updateBicyclePoint(BicyclePointVo pointVo) {
        try {
            bicyclePointClient.updateBicyclePoint(pointVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除三轮车配送地点
     * @param id
     */
    public void deleteBicyclePoint(Long id) {
        try {
            bicyclePointClient.deleteBicyclePoint(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取三轮车配送地点
     * @param id
     * @return
     */
    public BicyclePointVo findById(Long id) {
        try {
            return bicyclePointClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 三轮车配送地点列表
     * @return
     */
    public List<BicyclePointVo> listBicyclePoint() {
        try {
            return bicyclePointClient.listBicyclePoint();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
