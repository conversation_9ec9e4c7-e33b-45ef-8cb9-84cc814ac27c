package com.senox.web.component;

import com.senox.car.api.clients.LotteryParkingExitClient;
import com.senox.car.vo.LotteryParkingExitSupplementVo;
import com.senox.car.vo.LotteryParkingRecordVo;
import com.senox.common.utils.FeignUtils;
import com.senox.pm.vo.OrderResultVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LotteryParkingExitComponent {
    private final LotteryParkingExitClient lotteryParkingExitClient;

    /**
     * 离场不支付
     * @param id
     * @return
     */
    public LotteryParkingRecordVo exitNotPay(Long id) {
        try {
            return lotteryParkingExitClient.exit(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 离场补录
     *
     * @param lotteryParkingExitSupplementVo 补录参数
     */
    public void exitSupplement(LotteryParkingExitSupplementVo lotteryParkingExitSupplementVo) {
        try {
            lotteryParkingExitClient.exitSupplement(lotteryParkingExitSupplementVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 离场支付
     *
     * @param parkingRecordVo 车位记录
     * @return 订单下单结果
     */
    public OrderResultVo exitPay(LotteryParkingRecordVo parkingRecordVo) {
        try {
            return lotteryParkingExitClient.exitPay(parkingRecordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 离场并支付
     * @param parkingRecordVo
     * @return
     */
    public LotteryParkingRecordVo exitAndPay(LotteryParkingRecordVo parkingRecordVo) {
        try {
            return lotteryParkingExitClient.exitAndPay(parkingRecordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
