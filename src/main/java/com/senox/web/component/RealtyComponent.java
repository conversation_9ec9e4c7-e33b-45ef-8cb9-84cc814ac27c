package com.senox.web.component;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.RealtyClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/2 15:27
 */
@Component
@RequiredArgsConstructor
public class RealtyComponent {

    private final RealtyClient realtyClient;

    /**
     * 物业列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<RealtyVo> listRealtyPage(RealtySearchVo searchVo) {
        try {
            return realtyClient.listRealtyPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id获取物业信息
     *
     * @param id
     * @return
     */
    public RealtyVo findById(Long id) {
        try {
            return realtyClient.getRealty(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取物业及业主信息
     *
     * @param id
     * @return
     */
    public RealtyVo findWithOwnerById(Long id) {
        try {
            return realtyClient.getRealtyWithOwner(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取最新物业水电读数
     * @param id
     * @return
     */
    public List<RealtyReadingsVo> getLatestWeReadings(Long id) {
        try {
            return realtyClient.getRealtyReadings(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新物业水电读数
     * @param list
     */
    public void updateWeReadings(List<RealtyReadingsVo> list) {
        try {
            realtyClient.updateRealtyReadings(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加物业
     *
     * @param realty
     * @return
     */
    public Long addRealty(RealtyVo realty) {
        try {
            return realtyClient.addRealty(realty);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新物业
     *
     * @param realty
     */
    public void updateRealty(RealtyVo realty) {
        try {
            realtyClient.updateRealty(realty);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物业担保
     * @param guarantee
     */
    public void updateRealtyGuarantee(RealtyGuaranteeVo guarantee) {
        try {
            realtyClient.updateRealtyGuarantee(guarantee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 生成账单
     * @param month
     */
    public void generateBill(BillMonthVo month) {
        try {
            realtyClient.generateBill(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 重新生成应收账单
     * @param month
     */
    public void regenerateBill(BillMonthVo month) {
        try {
            realtyClient.regenerateBill(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 同步账单水电数据
     * @param billMonth
     */
    public void syncBillWeData(BillMonthVo billMonth) {
        try {
            realtyClient.syncBillWeData(billMonth);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应收账单
     *
     * @param bill
     */
    public void updateRealtyBill(RealtyBillVo bill) {
        try {
            realtyClient.updateRealtyBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 免滞纳金
     *
     * @param penaltyIgnore
     */
    public void ignoreBillPenalty(BillPenaltyIgnoreVo penaltyIgnore) {
        try {
            realtyClient.ignoreRealtyBillPenalty(penaltyIgnore);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 发票
     * @param batchBills
     */
    public void receiptBill(RealtyBillBatchVo batchBills) {
        try {
            realtyClient.receiptBill(batchBills);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 保存应收账单备注
     *
     * @param remark
     */
    public void saveRealtyBillRemark(RealtyBillRemarkVo remark) {
        try {
            realtyClient.saveRealtyBillRemark(remark);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 保存无讹账单票据号
     *
     * @param serial
     */
    public void saveRealtyBillSerial(RealtyBillSerialVo serial) {
        try {
            realtyClient.saveRealtyBillSerial(serial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应收账单
     *
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            throw new BusinessException("未名的应收账单列表");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new BusinessException("未名的物业缴费订单");
        }
        if (billPaid.getPaid() == null) {
            throw new BusinessException("未名的物业缴费订单状态");
        }

        try {
            realtyClient.updateRealtyBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 应收账单下发
     *
     * @param sendVo
     */
    public void sendBill(RealtyBillSendVo sendVo) {
        try {
            realtyClient.sendRealtyBill(sendVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销支付
     * @param cancel
     */
    public void cancelBill(BillCancelVo cancel) {
        try {
            realtyClient.cancelRealtyBill(cancel);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除应收账单
     * @param billId
     */
    public void deleteBill(Long billId) {
        try {
            realtyClient.deleteRealtyBill(billId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取应收账单明细
     *
     * @param billId
     * @return
     */
    public RealtyBillVo findBillById(Long billId) {
        try {
            return realtyClient.getRealtyBill(billId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取应收账单列表
     *
     * @param billIds
     * @param isWithDetail
     * @return
     */
    public List<RealtyBillVo> listBillById(List<Long> billIds, boolean isWithDetail) {
        try {
            return realtyClient.listRealtyBillById(billIds, isWithDetail);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 查找物业月账单列表
     * @param monthVo
     * @return
     */
    public List<RealtyBillVo> monthlyBillList(BillMonthVo monthVo) {
        try {
            return realtyClient.monthlyBillList(monthVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 新增物业账单
     * @param monthVo
     */
    public void saveBill(BillMonthVo monthVo) {
        try {
            realtyClient.saveBill(monthVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 应收账单列表
     *
     * @param search
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listBillPage(RealtyBillSearchVo search) {
        try {
            return realtyClient.listRealtyBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RealtyBillPageResult.emptyPage();
    }

    /**
     * 应收账单合计
     * @param search
     * @return
     */
    public RealtyBillVo sumBillDetail(RealtyBillSearchVo search) {
        try {
            return realtyClient.sumBillDetail(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 应收账单列表
     * @param search
     * @return
     */
    public PageResult<RealtyBillVo> listBillDetailPage(RealtyBillSearchVo search) {
        try {
            return realtyClient.listBillDetailInfoPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 物业发票账单列表
     * @param search
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listReceiptBill(RealtyBillSearchVo search) {
        try {
            return realtyClient.listRealtyReceiptBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RealtyBillPageResult.emptyPage();
    }

    /**
     * 应收账单明细列表
     *
     * @param searchVo
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listBillPageWitDetail(RealtyBillSearchVo searchVo) {
        try {
            return realtyClient.listBillDetailInfo(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RealtyBillPageResult.emptyPage();
    }

    /**
     * 生成应付账单
     * @param month
     */
    public void generatePayoff(BillMonthVo month) {
        try {
            realtyClient.generatePayoff(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应付账单
     * @param payoff
     */
    public void updatePayoff(RealtyPayoffVo payoff) {
        try {
            realtyClient.updatePayoff(payoff);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除应付账单
     * @param id
     */
    public void deletePayoff(Long id) {
        try {
            realtyClient.deletePayoff(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 应付账单明细
     * @param id
     * @return
     */
    public RealtyPayoffVo getPayoff(Long id) {
        try {
            return realtyClient.getPayoff(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    public RealtyPayoffVo sumPayoff(RealtyPayoffSearchVo search) {
        try {
            return realtyClient.sumPayoff(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    public List<RealtyPayoffVo> listPayoff(RealtyPayoffSearchVo search) {
        try {
            return realtyClient.listPayoff(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 应付账单页
     * @param search
     * @return
     */
    public PageResult<RealtyPayoffVo> listPayoffPage(RealtyPayoffSearchVo search) {
        try {
            return realtyClient.listPayoffPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 申请银行托收报盘
     *
     * @param withhold
     */
    public void applyBillWithhold(BankWithholdVo withhold) {
        try {
            realtyClient.applyBillWithhold(withhold);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消银行报盘申请
     *
     * @param withhold
     */
    public void cancelBillWithhold(BankWithholdVo withhold) {
        try {
            realtyClient.cancelBillWithhold(withhold);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 银行托收回盘
     *
     * @param withhold
     */
    public void backBillWithhold(BankWithholdVo withhold) {
        try {
            realtyClient.backBillWithhold(withhold);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 银行托收支付
     *
     * @param withholdBack
     */
    public void withholdPay(WithholdBackVo withholdBack) {
        try {
            realtyClient.payBillWithhold(withholdBack);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取银行报盘信息
     *
     * @param year
     * @param month
     * @return
     */
    public BankWithholdVo getBankWithHold(Integer year, Integer month) {
        try {
            return realtyClient.getBankWithHold(year, month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 银行托收报盘记录
     *
     * @param search
     * @return
     */
    public WithholdPage<BankOfferRealtyBillVo> listBillWithholdPage(RealtyBillSearchVo search) {
        try {
            return realtyClient.listBillWithholdPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return WithholdPage.emptyPage();
    }

    /**
     * 银行托收报盘记录
     *
     * @param search
     * @return
     */
    public List<BankOfferRealtyBillVo> listBillWithhold(RealtyBillSearchVo search) {
        try {
            return realtyClient.listBillWithhold(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加物业押金
     *
     * @param deposit
     * @return
     */
    public Long addDeposit(RealtyDepositVo deposit) {
        try {
            return realtyClient.addRealtyDeposit(deposit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 批量添加物业押金
     * @param depositList
     */
    public void batchAddDeposit(List<RealtyDepositVo> depositList) {
        try {
            realtyClient.addRealtyDepositBatch(depositList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物业押金
     *
     * @param deposit
     */
    public void updateDeposit(RealtyDepositVo deposit) {
        try {
            realtyClient.updateRealtyDeposit(deposit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新状态结果
     * @param billPaid
     */
    public void updateDepositStatus(BillPaidVo billPaid) {
        try {
            realtyClient.updateRealtyDepositStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物业押金编号
     *
     * @param serial
     */
    public void updateDepositSerial(TollSerialVo serial) {
        try {
            realtyClient.updateRealtyDepositSerial(serial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 支付物业押金
     *
     * @param id
     * @param toll
     */
    public void payDeposit(Long id, BillTollVo toll) {
        try {
            realtyClient.payRealtyDeposit(id, toll);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销支付物业押金
     *
     * @param id
     */
    public void revokeDepositPayment(Long id) {
        try {
            realtyClient.revokeRealtyDepositPayment(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 退档物业押金
     *
     * @param id
     * @param toll
     */
    public void refundDeposit(Long id, BillTollVo toll) {
        try {
            realtyClient.refundRealtyDeposit(id, toll);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销退档物业押金
     *
     * @param id
     */
    public void revokeDepositRefund(Long id) {
        try {
            realtyClient.revokeRealtyDepositRefund(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找物业押金
     *
     * @param id
     * @return
     */
    public RealtyDepositVo findDepositById(Long id) {
        try {
            return realtyClient.findRealtyDepositById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id列表查找物业押金
     * @param ids
     * @return
     */
    public List<RealtyDepositVo> listDepositByIds(List<Long> ids) {
        try {
            return realtyClient.listRealtyDepositByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }


    /**
     * 根据合同id查找物业押金
     * @param contractId
     * @return
     */
    public List<RealtyDepositVo> listContractDeposit(Long contractId) {
        try {
            return realtyClient.listRealtyContractDeposit(contractId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物业押金列表
     *
     * @param search
     * @return
     */
    public RefundBillPageResult<RealtyDepositVo> listDepositPage(RealtyDepositSearchVo search) {
        try {
            return realtyClient.listDepositPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RefundBillPageResult.emptyPage();
    }

    /**
     * 应收账单水电明细列表
     *
     * @param billId
     * @return
     */
    public List<RealtyBillWeVo> listRealtyBillWeDetail(Long billId) {
        try {
            return realtyClient.listRealtyBillWeDetail(billId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 生成/更新物业水电账单
     * @param month
     */
    public void generateRealtyWeBill(BillMonthVo month) {
        try {
            realtyClient.generateRealtyWeBill(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物业水电账单
     * @param bill
     */
    public void updateRealtyWeBill(RealtyBillWeVo bill) {
        try {
            realtyClient.updateRealtyWeBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除物业水电账单
     * @param id
     */
    public void deleteRealtyWeBill(Long id) {
        try {
            realtyClient.deleteRealtyWeBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 同步水电账单至应收账单
     * @param id
     * @param billMonth
     */
    public void syncWeBill2RealtyBill(Long id, BillMonthVo billMonth) {
        try {
            realtyClient.syncWeBill2RealtyBill(id, billMonth);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取物业水电账单
     * @param id
     * @return
     */
    public RealtyBillWeVo findRealtyWeBillById(Long id) {
        try {
            return realtyClient.findRealtyWeBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物业水电账单合计
     * @param search
     * @return
     */
    public RealtyBillWeVo sumRealtyWeBill(RealtyBillWeSearchVo search) {
        try {
            return realtyClient.sumRealtyWeBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物业水电账单列表
     *
     * @return
     */
    public PageResult<RealtyBillWeVo> listRealtyWeBill(RealtyBillWeSearchVo search) {
        try {
            return realtyClient.listRealtyWeBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增物业副档
     *
     * @param realtyId
     * @param aliasList
     */
    public void batchSaveRealtyAlias(Long realtyId, List<RealtyAliasVo> aliasList) {
        try {
            realtyClient.saveRealtyAlias(realtyId, aliasList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取物业副档
     *
     * @param realtyId 主档id
     */
    public List<RealtyAliasVo> listRealtyAlias(Long realtyId) {
        try {
           return realtyClient.listRealtyAlias(realtyId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    public void saveRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        try {
            realtyClient.saveRealtyTaxRate(realtyTaxRate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    public void cancelRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        try {
            realtyClient.cancelRealtyTaxRate(realtyTaxRate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物业税率分页列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<RealtyVo> pageListRealtyTaxRate(RealtySearchVo search) {
        try {
            return realtyClient.pageListRealtyTaxRate(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
