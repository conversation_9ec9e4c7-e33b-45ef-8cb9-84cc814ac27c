package com.senox.web.component;

import com.senox.car.api.clients.EggDepositClient;
import com.senox.car.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/5 10:36
 */
@Component
@RequiredArgsConstructor
public class EggDepositComponent {

    private final EggDepositClient eggDepositClient;

    /**
     * 押金支付
     * @param eggDepositVo
     * @return
     */
    public OrderResultVo eggDepositPay(EggDepositVo eggDepositVo) {
        try {
            return eggDepositClient.eggDepositPay(eggDepositVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取押金
     * @param id
     * @return
     */
    public EggDepositVo findEggDepositById(Long id) {
        try {
            return eggDepositClient.findEggDepositById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 押金分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<EggDepositVo, EggDepositVo> pageResult(EggDepositSearchVo searchVo) {
        try {
            return eggDepositClient.pageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 押金退费申请
     * @param applyVo
     * @return
     */
    public Long refundApply(EggDepositRefundApplyVo applyVo) {
        try {
            return eggDepositClient.refundApply(applyVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 押金退费预申请
     * @param applyVo
     * @return
     */
    public Long refundPreApply(EggDepositRefundApplyVo applyVo) {
        try {
            return eggDepositClient.refundPreApply(applyVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 根据id获取押金申请记录
     * @param id
     * @return
     */
    public EggDepositRefundApplyVo findEggDepositRefundById(Long id) {
        try {
            return eggDepositClient.findEggDepositRefundById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 押金退费提交
     * @param submitVo
     */
    public void submit(EggDepositRefundSubmitVo submitVo) {
        try {
            eggDepositClient.submit(submitVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 退费申请分页
     * @param searchVo
     * @return
     */
    public PageResult<EggDepositRefundApplyVo> applyPage(EggDepositRefundApplySearchVo searchVo) {
        try {
            return eggDepositClient.applyPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 退费
     * @param applyId
     * @param payWay
     */
    public void refundByDepositApply(Long applyId, PayWay payWay) {
        try {
            eggDepositClient.refundByDepositApply(applyId, payWay);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
