package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageRequest;
import com.senox.common.vo.PageResult;
import com.senox.dm.api.clients.AccessControlClient;
import com.senox.dm.vo.*;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 13:48
 */
@Component
public class AccessControlComponent {

    @Autowired
    private AccessControlClient accessControlClient;

    /**
     * 批量新增海康门禁设备
     *
     * @param accessControlVoList
     */
    public void addBatchAccessControl(List<AccessControlVo> accessControlVoList) {
        try {
            accessControlClient.addBatchAccessControl(accessControlVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新海康门禁设备
     *
     * @param accessControlVo
     */
    public void updateAccessControl(AccessControlVo accessControlVo) {
        try {
            accessControlClient.updateAccessControl(accessControlVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 维修海康门禁设备
     * @param maintain
     */
    public void maintainAccessControl(AccessControlMaintainVo maintain) {
        try {
            accessControlClient.maintainAccessControl(maintain);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 延时报警时长保存
     * @param acTimeout
     */
    public void saveAlarmTimeout(AccessControlAlarmTimeoutVo acTimeout) {
        try {
            accessControlClient.saveAlarmTimeout(acTimeout);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 海康门禁设备布防
     * @param ip
     */
    public void deployAccessControl(String ip) {
        try {
            accessControlClient.deployAccessControl(ip);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 海康门禁设备取消布防
     * @param ip
     */
    public void undeployAccessControl(String ip) {
        try {
            accessControlClient.undeployAccessControl(ip);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除海康门禁设备
     *
     * @param id
     */
    public void deleteAccessControl(Long id) {
        try {
            accessControlClient.deleteAccessControl(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取海康门禁设备
     *
     * @param id
     * @return
     */
    public AccessControlVo findAccessControlById(Long id) {
        try {
            return accessControlClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 海康门禁设备列表
     *
     * @param search
     * @return
     */
    public PageResult<AccessControlVo> listAccessControl(AccessControlSearchVo search) {
        try {
            return accessControlClient.listAccessControl(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据物业编号获取设备
     *
     * @param realtySerial
     * @return
     */
    public List<AccessControlVo> getByRealtySerial(String realtySerial) {
        try {
            return accessControlClient.getByRealtySerial(realtySerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 授权门禁列表
     * @param search
     * @return
     */
    public List<AuthorizedGateVo> listAuthorizedGate(AuthorizedGateSearchVo search) {
        try {
            return accessControlClient.listAuthorizedGate(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 远程开门
     *
     * @param deviceIp
     */
    public void remoteControlGate(String deviceIp) {
        try {
            accessControlClient.remoteControlGate(deviceIp);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 执行命令
     *
     * @param id
     */
    public void executeCommand(Long id) {
        try {
            accessControlClient.executeAccessCommand(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 恢复命令
     *
     * @param id
     */
    public void enableCommand(Long id) {
        try {
            accessControlClient.enableAccessCommand(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 作废命令
     *
     * @param id
     */
    public void disableCommand(Long id) {
        try {
            accessControlClient.disableAccessCommand(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 命令列表
     *
     * @param search
     * @return
     */
    public PageResult<AccessControlCommandVo> listCommand(AccessControlCommandSearchVo search) {
        try {
            return accessControlClient.listAccessCommand(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加门禁角色
     * @param gateRole
     * @return
     */
    public Long addGateRole(GateRoleVo gateRole) {
        try {
            return accessControlClient.addGateRole(gateRole);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新门禁角色
     * @param gateRole
     */
    public void updateGateRole(GateRoleVo gateRole) {
        try {
            accessControlClient.updateGateRole(gateRole);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除门禁角色
     * @param id
     */
    public void deleteGateRole(Long id) {
        try {
            accessControlClient.deleteGateRole(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 门禁角色列表页
     * @param search
     * @return
     */
    public PageResult<GateRoleVo> listGateRolePage(PageRequest search) {
        try {
            return accessControlClient.listGateRolePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 添加门禁角色用户
     * @param roleUsers
     */
    public void addGateRoleUsers(GateRoleUserEditVo roleUsers) {
        try {
            accessControlClient.addGateRoleUsers(roleUsers);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除门禁角色用户
     * @param roleUsers
     */
    public void deleteGateRoleUsers(GateRoleUserEditVo roleUsers) {
        try {
            accessControlClient.deleteGateRoleUsers(roleUsers);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 门禁角色用户列表页
     * @param search
     * @return
     */
    public PageResult<GateRoleUserVo> listGateRoleUserPage(GateRoleSearchVo search) {
        try {
            return accessControlClient.listRoleUserPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 添加门禁角色设备
     * @param roleDevices
     */
    public void addGateRoleDevices(GateRoleDeviceEditVo roleDevices) {
        try {
            accessControlClient.addGateRoleDevices(roleDevices);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除门禁角色设备
     * @param roleDevices
     */
    public void deleteGateRoleDevices(GateRoleDeviceEditVo roleDevices) {
        try {
            accessControlClient.deleteGateRoleDevices(roleDevices);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 门禁角色设备列表页
     * @param search
     * @return
     */
    public PageResult<AccessControlVo> listRoleDevicePage(GateRoleSearchVo search) {
        try {
            return accessControlClient.listRoleDevicePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 设置日志误报
     * @param id
     * @param marked
     */
    public void markOperationFalse(Long id, Boolean marked) {
        try {
            accessControlClient.markOperationFalse(id, marked);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 操作日志列表
     * @param search
     * @return
     */
    public List<GateOperationLogVo> listOperationLog(GateOperationLogSearchVo search) {
        try {
            return accessControlClient.listOperationLog(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 门禁日志页
     * @param search
     * @return
     */
    public PageResult<GateOperationLogVo> listOperationLogPage(GateOperationLogSearchVo search) {
        try {
            return accessControlClient.listOperationLogPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 重新生成日报
     * @param generate
     */
    public void generateDayReport(GateOperationReportGenerateVo generate) {
        try {
            accessControlClient.generateDayReport(generate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 日报统计
     * @param search
     * @return
     */
    public GateOperationReportSumVo sumDayReport(GateOperationReportSearchVo search) {
        try {
            return accessControlClient.sumDayReport(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return GateOperationReportSumVo.empty();
    }

    /**
     * 日报列表页
     * @param search
     * @return
     */
    public PageResult<GateOperationDayReportVo> listDayReportPage(GateOperationReportSearchVo search) {
        try {
            return accessControlClient.listDayReportPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 日报列表
     * @param search
     * @return
     */
    public List<GateOperationDayReportVo> listDayReport(GateOperationReportSearchVo search) {
        try {
            return accessControlClient.listDayReport(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }
}
