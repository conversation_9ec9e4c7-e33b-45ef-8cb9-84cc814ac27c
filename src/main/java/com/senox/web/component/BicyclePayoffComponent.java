package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.tms.api.clients.BicyclePayoffClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:51
 */
@Component
@RequiredArgsConstructor
public class BicyclePayoffComponent {

    private final BicyclePayoffClient bicyclePayoffClient;

    /**
     * 更新三轮车应付账单状态
     * @param billPaid
     * @param payway
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway) {
        try {
            bicyclePayoffClient.updatePayoffStatus(billPaid, payway.getValue());
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应付金额
     *
     * @param id     应付id
     * @param amount 金额
     */
    public void updateAmountFromReport(Long id, BigDecimal amount) {
        try {
            bicyclePayoffClient.updateAmountFromReport(id, amount);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 三轮车应付账单合计
     * @param searchVo
     * @return
     */
    public BicyclePayoffVo sumPayoff(BicyclePayoffSearchVo searchVo) {
        try {
            return bicyclePayoffClient.sumPayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 三轮车应付账单列表页
     * @param searchVo
     * @return
     */
    public PageResult<BicyclePayoffVo> listPayoff(BicyclePayoffSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        try {
            return bicyclePayoffClient.listPayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 生成日报表
     *
     * @param dateVo 日期
     */
    public void generateReportByDay(BicycleDateVo dateVo) {
        try {
            bicyclePayoffClient.generateReportByDay(dateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 生成月报表
     * @param dateVo 日期
     */
    public void generateReportByMonth(BicycleDateVo dateVo){
        try {
            bicyclePayoffClient.generateReportByMonth(dateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 日报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportDayList(BicyclePayoffReportSearchVo searchVo){
        try {
           return bicyclePayoffClient.reportDayList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new BicycleTotalPageResult<>(PageResult.emptyPage());
    }

    /**
     * 月报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportMonthList(BicyclePayoffReportSearchVo searchVo) {
        try {
            return bicyclePayoffClient.reportMonthList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new BicycleTotalPageResult<>(PageResult.emptyPage());
    }
}
