package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.SettleFileClient;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.SettleFileSearchVo;
import com.senox.pm.vo.SettleFileVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/17 10:30
 */
@Component
public class SettleFileComponent {

    @Autowired
    private SettleFileClient settleFileClient;

    /**
     * 农商行对账文件列表
     * @param search
     * @return
     */
    public PageResult<SettleFileVo> listDrcSettleFile(SettleFileSearchVo search) {
        try {
            return settleFileClient.listDrcSettleFile(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 农商行对账文件路径
     * @param settleDate
     * @param settleType
     * @param payWay 
     * @return
     */
    public String getDrcSettleFilePath(String settleDate, String settleType, PayWay payWay) {
        try {
            return settleFileClient.getDrcSettleFilePath(settleDate, settleType, payWay);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return StringUtils.EMPTY;
    }
}
