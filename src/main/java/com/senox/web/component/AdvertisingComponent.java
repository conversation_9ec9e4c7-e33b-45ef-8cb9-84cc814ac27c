package com.senox.web.component;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.pm.constant.PayWay;
import com.senox.realty.api.clients.AdvertisingClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/28 11:19
 */
@Component
@RequiredArgsConstructor
public class AdvertisingComponent {

    private final AdvertisingClient advertisingClient;

    /**
     * 添加广告位
     * @param space
     * @return
     */
    public Long addSpace(AdvertisingSpaceVo space) {
        try {
            return advertisingClient.addSpace(space);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新广告位
     * @param space
     */
    public void updateSpace(AdvertisingSpaceVo space) {
        try {
            advertisingClient.updateSpace(space);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除广告位
     * @param id
     */
    public void deleteSpace(Long id) {
        try {
            advertisingClient.deleteSpace(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找广告位
     * @param id
     * @return
     */
    public AdvertisingSpaceVo findSpaceById(Long id) {
        try {
            return advertisingClient.getSpace(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 广告位统计
     * @param search
     * @return
     */
    public int countSpace(AdvertisingSpaceSearchVo search) {
        try {
            return advertisingClient.countSpace(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 广告位列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingSpaceListVo> listSpacePage(AdvertisingSpaceSearchVo search) {
        try {
            return advertisingClient.listSpacePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加广告合同
     * @param contract
     * @return
     */
    public Long addContract(AdvertisingContractEditVo contract) {
        try {
            return advertisingClient.addContract(contract);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 修改广告合同
     * @param contract
     */
    public void updateContract(AdvertisingContractEditVo contract) {
        try {
            advertisingClient.updateContract(contract);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新已缴费合同
     * @param contract
     */
    public void updatePaidContract(AdvertisingContractEditVo contract) {
        try {
            advertisingClient.updatePaidContract(contract);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新广告合同成本金额
     * @param cost
     */
    public void updateContractCost(AdvertisingCostVo cost) {
        try {
            advertisingClient.updateContractCost(cost);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 停用广告合同
     * @param suspendDto
     */
    public void suspendContract(ContractSuspendDto suspendDto) {
        try {
            advertisingClient.suspendContract(suspendDto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除广告合同
     * @param id
     */
    public void deleteContract(Long id) {
        try {
            advertisingClient.deleteContract(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查找广告合同详情
     * @param id
     * @return
     */
    public AdvertisingContractVo findContractById(Long id) {
        try {
            return advertisingClient.getContract(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 统计广告合同
     * @param search
     * @return
     */
    public int countContract(AdvertisingContractSearchVo search) {
        try {
            return advertisingClient.countContract(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 广告合同列表
     * @param search
     * @return
     */
    public PageResult<AdvertisingContractListVo> listContractPage(AdvertisingContractSearchVo search) {
        try {
            return advertisingClient.listContractPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 广告收益列表
     * @param search
     * @return
     */
    public List<AdvertisingIncomeVo> listAdvertisingIncome(AdvertisingContractSearchVo search) {
        try {
            return advertisingClient.listContractIncome(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 广告收益合计
     * @param search
     * @return
     */
    public AdvertisingIncomeVo sumAdvertisingIncome(AdvertisingContractSearchVo search) {
        try {
            return advertisingClient.sumContractIncome(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        AdvertisingIncomeVo result = new AdvertisingIncomeVo();
        result.setAmount(BigDecimal.ZERO);
        result.setCost(BigDecimal.ZERO);
        result.setShareAmount(BigDecimal.ZERO);
        return result;
    }

    /**
     * 广告收益列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingIncomeVo> listAdvertisingIncomePage(AdvertisingContractSearchVo search) {
        try {
            return advertisingClient.listContractIncomePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 更新广告应收账单状态
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        try {
            advertisingClient.updateBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新广告应收账单票据号
     * @param tollSerial
     */
    public void updateBillTollSerial(TollSerialVo tollSerial) {
        try {
            advertisingClient.updateBillTollSerial(tollSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加广告账单发票
     * @param receipt
     */
    public void addBillReceipt(BillReceiptBriefVo receipt) {
        try {
            advertisingClient.addBillReceipt(receipt);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消广告账单发票
     * @param ids
     */
    public void cancelBillReceipt(List<Long> ids) {
        try {
            advertisingClient.cancelBillReceipt(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取广告应收账单详情
     * @param id
     * @return
     */
    public AdvertisingBillVo findBillById(Long id) {
        try {
            return advertisingClient.getBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id列表获取广告应收账单详情
     * @param ids
     * @return
     */
    public List<AdvertisingBillVo> listBillByIds(List<Long> ids) {
        try {
            return advertisingClient.listBillByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 广告应收账单合计
     * @param search
     * @return
     */
    public AdvertisingBillVo sumBill(AdvertisingBillSearchVo search) {
        try {
            return advertisingClient.sumBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        AdvertisingBillVo result = new AdvertisingBillVo();
        result.setAmount(BigDecimal.ZERO);
        result.setPaidAmount(BigDecimal.ZERO);
        return result;
    }

    /**
     * 广告应收账单列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingBillVo> listBillPage(AdvertisingBillSearchVo search) {
        try {
            return advertisingClient.listBillPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 生成广告应付账单
     * @param contractNo
     */
    public void generatePayoff(String contractNo) {
        try {
            advertisingClient.generatePayoff(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新广告应付账单
     * @param payoff
     */
    public void updatePayoff(AdvertisingPayoffVo payoff) {
        try {
            advertisingClient.updatePayoff(payoff);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除广告应付账单
     * @param id
     */
    public void deletePayoff(Long id) {
        try {
            advertisingClient.deletePayoff(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应付账单状态
     * @param billPaid
     * @param payway
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway) {
        try {
            advertisingClient.updatePayoffStatus(billPaid, payway.getValue());
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 广告合同应付账单
     * @param contractNo
     * @return
     */
    public List<AdvertisingPayoffVo> listPayoffByContract(String contractNo) {
        try {
            return advertisingClient.listPayoffByContract(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 广告应付账单合计
     * @param search
     * @return
     */
    public AdvertisingPayoffDetailVo sumPayoff(AdvertisingPayoffSearchVo search) {
        AdvertisingPayoffDetailVo  result = null;
        try {
            result = advertisingClient.sumPayoff(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        result = result == null ? new AdvertisingPayoffDetailVo() : result;
        result.setAmount(DecimalUtils.nullToZero(result.getAmount()));
        result.setShareAmount(DecimalUtils.nullToZero(result.getShareAmount()));
        return result;
    }

    /**
     * 广告应付账单页
     * @param search
     * @return
     */
    public PageResult<AdvertisingPayoffDetailVo> listPayoffPage(AdvertisingPayoffSearchVo search) {
        try {
            return advertisingClient.listPayoffPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }
}
