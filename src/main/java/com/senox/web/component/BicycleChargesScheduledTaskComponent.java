package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleChargesScheduledTaskClient;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@RequiredArgsConstructor
@Component
public class BicycleChargesScheduledTaskComponent {
    private final BicycleChargesScheduledTaskClient taskClient;

    /**
     * 添加
     *
     * @param task 任务
     */
    public void add(BicycleChargesScheduledTaskVo task) {
        try {
            taskClient.add(task);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新
     *
     * @param task 任务
     */
    public void update(BicycleChargesScheduledTaskVo task) {
        try {
            taskClient.update(task);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除
     *
     * @param taskId 任务id
     */
    public void delete(Long taskId) {
        try {
            taskClient.delete(taskId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<BicycleChargesScheduledTaskVo> list(BicycleChargesScheduledTaskSearchVo searchVo) {
        try {
            taskClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<BicycleChargesScheduledTaskVo> listPage(BicycleChargesScheduledTaskSearchVo searchVo) {
        try {
            return taskClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id查找计划任务
     *
     * @param id id
     * @return 返回查找到的计划任务
     */
    public BicycleChargesScheduledTaskVo findById(Long id) {
        try {
            return taskClient.find(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
