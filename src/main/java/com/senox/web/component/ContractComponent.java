package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.ContractClient;
import com.senox.realty.vo.ContractBankVo;
import com.senox.realty.vo.ContractSearchVo;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.LeaseContractListVo;
import com.senox.realty.vo.RealtyContractSuspendRequestDto;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/2/20 9:17
 */
@Component
public class ContractComponent {

    @Autowired
    private ContractClient contractClient;

    /**
     * 合同列表
     * @param search
     * @return
     */
    public PageResult<ContractVo> listContractPage(ContractSearchVo search) {
        try {
            return contractClient.listContractPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 租赁合同列表
     * @param search
     * @return
     */
    public PageResult<LeaseContractListVo> listLeaseContractPage(ContractSearchVo search) {
        try {
            return contractClient.listLeaseContractPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 合同银行代扣信息
     * @param search
     * @return
     */
    public PageResult<ContractBankVo> listContractBank(ContractSearchVo search) {
        try {
            return contractClient.listContractBank(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id获取合同信息
     * @param id
     * @return
     */
    public ContractVo findById(Long id) {
        try {
            return contractClient.getContract(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**C
     *
     * @param id
     * @return
     */
    public ContractVo findRenewFrom(Long id) {
        try {
            return contractClient.getRenewFrom(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据合同号获取合同
     * @param contractNo
     */
    public ContractVo getByContractNo(String contractNo) {
        try {
            return contractClient.getByContractNo(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加合同
     * @param contract
     * @return
     */
    public Long addContract(ContractVo contract) {
        try {
            return contractClient.addContract(contract);
        }catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新合同
     * @param contract
     */
    public void updateContract(ContractVo contract) {
        try {
            contractClient.updateContract(contract);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 启用合同
     * @param contractNo
     */
    public void enableContract(String contractNo) {
        try {
            contractClient.enableContract(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 停用合同
     * @param suspendReq
     */
    public void suspendContract(RealtyContractSuspendRequestDto suspendReq) {
        try {
            contractClient.suspendContract(suspendReq);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

    }
}
