package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.AuditVo;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.MerchantClient;
import com.senox.user.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-2
 */
@RequiredArgsConstructor
@Component
public class MerchantComponent {
    private final MerchantClient merchantClient;


    /**
     * 添加商户
     *
     * @param merchantVo 商户
     */
    public Long addMerchant(MerchantVo merchantVo) {
        try {
            return merchantClient.add(merchantVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 批量新增商户，返回已存在的商户列表
     * @param list
     * @return
     */
    public List<MerchantVo> batchAddMerchant(List<MerchantVo> list) {
        try {
            return merchantClient.batchAddMerchant(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新商户
     *
     * @param merchantVo 商户
     */
    public void updateMerchant(MerchantVo merchantVo) {
        try {
            merchantClient.update(merchantVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据联系方式查询商户
     * @param contact
     * @return
     */
    public List<MerchantVo> findByContact(String contact) {
        try {
            return merchantClient.findByContact(contact);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 删除商户
     * @param id
     */
    public void deleteMerchant(Long id) {
        try {
            merchantClient.delete(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取商户详情
     * @param id
     * @return
     */
    public MerchantVo findMerchantById(Long id) {
        try {
            return merchantClient.findById(id);
        } catch (FeignException  e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 商户列表
     *
     * @param searchVo 查询参数
     */
    public PageResult<MerchantVo> listMerchant(MerchantSearchVo searchVo) {
        try {
            return merchantClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增权限申请
     * @param apply
     * @return
     */
    public Long addAuthApply(MerchantAuthApplyEditVo apply) {
        try {
            return merchantClient.addApply(apply);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新权限申请
     *
     * @param apply
     */
    public void updateAuthApply(MerchantAuthApplyEditVo apply) {
        try {
            merchantClient.updateApply(apply);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除权限申请
     * @param id
     */
    public void deleteAuthApply(Long id) {
        try {
            merchantClient.deleteApply(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 权限申请审批
     * @param audit
     * @return
     */
    public MerchantVo auditAuthApply(AuditVo audit) {
        try {
            return merchantClient.auditApply(audit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取申请详情
     * @param id
     * @return
     */
    public MerchantAuthApplyVo findApplyById(Long id) {
        try {
            return merchantClient.findApplyById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 申请列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MerchantAuthApplyListVo> listApplyPage(MerchantAuthApplySearchVo search) {
        try {
            return merchantClient.listApplyPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据冷藏客户编号查找商户
     *
     * @param rcSerial 冷藏编号
     * @return
     */
    public MerchantVo findByRc(String rcSerial) {
        try {
            return merchantClient.findByRc(rcSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据城际运输编号查找商户
     *
     * @param ltSerials 城际运输编号集
     * @return 返回查找到的商户列表
     */
    public List<MerchantVo> findByLtSerialList(List<String> ltSerials) {
        try {
            return merchantClient.findByLtSerialList(ltSerials);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据商户名查找商户
     *
     * @param merchantName 商户名
     * @return 返回查找到的商户
     */
    public MerchantVo findByName(String merchantName) {
        try {
            return merchantClient.findByName(merchantName);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 商户地址保存
     * @param addressVo
     */
    public void saveMerchantAddress(MerchantAddressVo addressVo) {
        try {
            merchantClient.saveMerchantAddress(addressVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 商户地址获取
     * @param id
     * @return
     */
    public MerchantAddressVo findMerchantAddress(Long id) {
        try {
            return merchantClient.findMerchantAddress(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 商户地址删除
     * @param id
     */
    public void deleteMerchantAddress(Long id) {
        try {
            merchantClient.deleteMerchantAddress(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据商户id获取地址
     * @param merchantId
     * @return
     */
    public List<MerchantAddressVo> listByMerchantId(Long merchantId) {
        try {
            return merchantClient.listByMerchantId(merchantId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 商户地址分页
     * @param searchVo
     * @return
     */
    public PageResult<MerchantAddressVo> pageMerchantAddress(MerchantAddressSearchVo searchVo) {
        try {
            return merchantClient.pageMerchantAddress(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
