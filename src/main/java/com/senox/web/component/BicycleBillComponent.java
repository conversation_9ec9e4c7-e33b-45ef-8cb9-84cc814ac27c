package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleBillClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 16:03
 */
@Component
@RequiredArgsConstructor
public class BicycleBillComponent {

    private final BicycleBillClient bicycleBillClient;


    /**
     * 获取三轮车应收账单详情
     * @param id
     * @return
     */
    public BicycleBillVo findBillById(Long id) {
        try {
            return bicycleBillClient.getBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id列表获取三轮车应收账单详情
     * @param ids
     * @return
     */
    public List<BicycleBillVo> listBillByIds(List<Long> ids) {
        try {
            return bicycleBillClient.listBillByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 三轮车应收账单合计
     * @param searchVo
     * @return
     */
    public BicycleBillVo sumBill(BicycleBillSearchVo searchVo) {
        try {
            return bicycleBillClient.sumBill(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        BicycleBillVo result = new BicycleBillVo();
        result.setAmount(BigDecimal.ZERO);
        return result;
    }

    /**
     * 三轮车应收账单列表页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleBillVo> listBillPage(BicycleBillSearchVo searchVo) {
        try {
            return bicycleBillClient.listBillPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }
}
