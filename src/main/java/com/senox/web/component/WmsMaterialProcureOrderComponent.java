package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.AdminUserVo;
import com.senox.wms.api.clients.MaterialProcureOrderClient;
import com.senox.wms.vo.EnterWarehouseCollectVo;
import com.senox.wms.vo.requisition.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-12
 **/
@RequiredArgsConstructor
@Component
public class WmsMaterialProcureOrderComponent {
    private final MaterialProcureOrderClient materialProcureOrderClient;

    /**
     * 保存采购订单明细
     *
     * @param procureOrderId        采购订单id
     * @param procureOrderGoodsList 采购订单商品集
     */
    public void saveGoods(Long procureOrderId, List<MaterialProcureOrderGoodsVo> procureOrderGoodsList) {
        try {
            materialProcureOrderClient.saveGoods(procureOrderId, procureOrderGoodsList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找采购订单
     *
     * @param procureOrderId 采购订单id
     * @return 返回查找到的采购订单
     */
    public MaterialProcureOrderVo findById(Long procureOrderId) {
        try {
            return materialProcureOrderClient.findById(procureOrderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据流程id查找采购订单
     *
     * @param flowId 流程id
     * @return 返回查找到的采购订单
     */
    public MaterialProcureOrderVo findByFlowId(Long flowId) {
        try {
            return materialProcureOrderClient.findByFlowId(flowId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<MaterialProcureOrderVo> list(MaterialProcureOrderSearchVo search) {
        try {
            return materialProcureOrderClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialProcureOrderVo> pageList(MaterialProcureOrderSearchVo search) {
        try {
            return materialProcureOrderClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 采购商品分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialProcureOrderGoodsVo> goodsPageList(MaterialProcureOrderGoodsSearchVo search) {
        try {
            return materialProcureOrderClient.goodsPageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 采购人列表
     * @return 返回采购人列表
     */
    public List<AdminUserVo> purchaserList(){
         try {
            return materialProcureOrderClient.purchaserList();
         }catch (FeignException e){
             FeignUtils.handleFeignException(e);
         }
         return Collections.emptyList();
    }

    /**
     * 确认商品并提交
     * @param procureOrderForm 采购单表单集
     */
    public EnterWarehouseCollectVo confirmGoodsAndSubmit(MaterialProcureOrderFormVo procureOrderForm) {
        try {
            return materialProcureOrderClient.confirmGoodsAndSubmit(procureOrderForm);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
