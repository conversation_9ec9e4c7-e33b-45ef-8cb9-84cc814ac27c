package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.LogisticLoaderIncomeClient;
import com.senox.tms.api.clients.LogisticLoaderSettlementClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
@RequiredArgsConstructor
@Component
public class LogisticLoaderComponent {
    private final LogisticLoaderIncomeClient loaderIncomeClient;
    private final LogisticLoaderSettlementClient loaderSettlementClient;

    /**
     * 添加搬运工结算
     *
     * @param loaderSettlementFormVo 搬运工结算表单
     */
    public void addLoaderSettlement(LogisticLoaderSettlementFormVo loaderSettlementFormVo) {
        try {
            loaderSettlementClient.add(loaderSettlementFormVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新搬运工结算
     *
     * @param loaderSettlementFormVo 搬运工结算表单
     */
    public void updateLoaderSettlement(LogisticLoaderSettlementFormVo loaderSettlementFormVo) {
        try {
            loaderSettlementClient.update(loaderSettlementFormVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取结算
     *
     * @param loaderSettlementId 搬运工结算id
     */
    public LogisticLoaderSettlementVo findLoaderSettlementById(Long loaderSettlementId) {
        try {
            return loaderSettlementClient.findById(loaderSettlementId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 删除搬运工结算
     *
     * @param loaderSettlementId 搬运工结算id
     */
    public void deleteLoaderSettlementById(Long loaderSettlementId) {
        try {
            loaderSettlementClient.deleteById(loaderSettlementId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 搬运工结算列表
     *
     * @param searchVo 查询
     * @return 返回搬运工结算列表
     */
    public List<LogisticLoaderSettlementVo> listLoaderSettlement(LogisticLoaderSettlementSearchVo searchVo) {
        try {
            return loaderSettlementClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 搬运工结算分页列表
     *
     * @param searchVo 查询
     * @return 返回搬运工结算列表
     */
    public BicycleTotalPageResult<LogisticLoaderSettlementVo> listPageLoaderSettlement(LogisticLoaderSettlementSearchVo searchVo) {
        try {
            return loaderSettlementClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return BicycleTotalPageResult.emptyPage();
    }


    /**
     * 生成搬运工收益日报表
     *
     * @param dateVo 时间
     */
    public void generateLoaderIncomeDayReport(BicycleDateVo dateVo) {
        try {
            loaderIncomeClient.generateDayReport(dateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 搬运工收益报表
     *
     * @param searchVo 查询
     * @return 返回搬运工收益报表
     */
    public BicycleTotalPageResult<LogisticLoaderIncomeVo> listLoaderIncomeStatistics(LogisticLoaderIncomeSearchVo searchVo) {
        try {
            return loaderIncomeClient.listPageStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return BicycleTotalPageResult.emptyPage();
    }


    /**
     * 搬运工收益统计报表
     *
     * @param searchVo 查询
     * @return 返回统计后的报表
     */
    public List<LogisticLoaderIncomeReportVo> listLoaderIncomeReportStatistics(LogisticLoaderIncomeSearchVo searchVo) {
        try {
            return loaderIncomeClient.reportStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
