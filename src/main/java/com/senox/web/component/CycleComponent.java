package com.senox.web.component;

import com.senox.car.api.clients.CycleClient;
import com.senox.car.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:18
 */
@Component
public class CycleComponent {

    @Autowired
    private CycleClient cycleClient;

    /**
     * 三轮车列表
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<CycleVo, CycleVo> listCyclePage(CycleSearchVo searchVo) {
        try {
            return cycleClient.listCyclePage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 三轮车账单列表
     * @param searchVo
     * @return
     */
    public CycleBillPageResult<CycleBillVo> listCycleBillPage(CycleBillSearchVo searchVo) {
        try {
            return cycleClient.listCycleBillPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return CycleBillPageResult.emptyPage();
    }

    /**
     * 根据id查找三轮车
     * @param id
     * @return
     */
    public CycleVo findById(Long id) {
        try {
            return cycleClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 新增三轮车
     * @param cycle
     * @return
     */
    public Long addCycle(CycleVo cycle) {
        try {
            return cycleClient.addCycle(cycle);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新三轮车
     * @param cycle
     */
    public void updateCycle(CycleVo cycle) {
        try {
            cycleClient.updateCycle(cycle);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新票据号
     * @param tollSerial
     */
    public void updateCycleTollSerial(TollSerialVo tollSerial) {
        try {
            cycleClient.updateCycleTollSerial(tollSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除三轮车
     * @param cycle
     */
    public void deleteCycle(CycleDeleteVo cycle) {
        try {
            cycleClient.deleteCycle(cycle);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 支付三轮车管理费
     * @param cyclePay
     */
    public void payCycleFee(CyclePayVo cyclePay) {
        try {
            cycleClient.payCycleFee(cyclePay);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    public void revokeCyclePay(Long id) {
        try {
            cycleClient.revokeCyclePay(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 生成号牌
     * @param prefix
     * @return
     */
    public String genLicense(String prefix) {
        try {
            return cycleClient.genLicense(prefix);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 判断号牌是否存在
     * @param license
     * @param id
     * @return
     */
    public boolean checkLicenseExit(String license, Long id) {
        try {
            return cycleClient.checkLicenseUsed(license, id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return false;
    }

    /**
     * 换新车牌
     * @param cycleVo
     * @return
     */
    public Long renewCycle(CycleVo cycleVo) {
        try {
            return cycleClient.renewCycle(cycleVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新用账单支付结果
     * @param billPaidVo
     */
    public void updateCycleBillStatus(BillPaidVo billPaidVo) {
        try {
            cycleClient.updateCycleBillStatus(billPaidVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销支付账单
     * @param id
     */
    public void revokeCycleBillPayment(Long id) {
        try {
            cycleClient.revokeCycleBillPayment(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取三轮车信息列表
     * @param ids
     * @return
     */
    public List<CycleVo> listByIds(List<Long> ids) {
        try {
            return cycleClient.listByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
