package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.clients.OutgoingTtlClient;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 13:53
 */
@Component
@RequiredArgsConstructor
public class OutgoingTtlComponent {

    private final OutgoingTtlClient outgoingTtlClient;

    /**
     * 批量添加太太乐外发
     * @param outgoingTtlVos
     */
    public void batchAddOutgoingTtl(List<OutgoingTtlVo> outgoingTtlVos) {
        try {
            outgoingTtlClient.batchAddOutgoingTtl(outgoingTtlVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加太太乐外发
     * @param outgoingTtlVo
     * @return
     */
    public Long addOutgoingTtl(OutgoingTtlVo outgoingTtlVo) {
        try {
            return outgoingTtlClient.addOutgoingTtl(outgoingTtlVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新太太乐外发
     * @param outgoingTtlVo
     */
    public void updateOutgoingTtl(OutgoingTtlVo outgoingTtlVo) {
        try {
            outgoingTtlClient.updateOutgoingTtl(outgoingTtlVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据Id获取太太乐外发
     * @param id
     * @return
     */
    public OutgoingTtlVo findById(Long id) {
        try {
            return outgoingTtlClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据Id删除太太乐外发
     * @param id
     */
    public void deleteById(Long id) {
        try {
            outgoingTtlClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 太太乐外发分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult(OutgoingTtlSearchVo searchVo) {
        try {
            return outgoingTtlClient.pageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }
}
