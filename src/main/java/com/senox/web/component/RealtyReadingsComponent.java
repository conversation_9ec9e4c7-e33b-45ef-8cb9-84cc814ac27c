package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.realty.api.clients.RealtyReadingsClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 8:55
 */
@RequiredArgsConstructor
@Component
public class RealtyReadingsComponent {

    private final RealtyReadingsClient realtyReadingsClient;

    /**
     * 批量添加水电数据
     * @param batchWeData
     */
    public void batchAddWeReadings(RealtyWeBatchVo batchWeData) {
        try {
            realtyReadingsClient.batchAddWe(batchWeData);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新水电数据
     * @param we
     */
    public void updateWeReadings(RealtyWeVo we) {
        try {
            realtyReadingsClient.updateWe(we);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除水电数据
     * @param ids
     */
    public void deleteWeReadings(List<Long> ids) {
        try {
            realtyReadingsClient.deleteWe(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 重置月水电读数
     * @param month
     */
    public void resetWeReadings(BillMonthVo month) {
        try {
            realtyReadingsClient.resetWeReadings(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 校验水电数据是否生成
     * @param month
     * @return
     */
    public boolean checkWeReadings(BillMonthVo month) {
        try {
            return realtyReadingsClient.checkWeReadings(month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return false;
    }

    /**
     * 根据 id 查找水电数据
     * @param id
     * @return
     */
    public RealtyWeVo findWeReadingsById(Long id) {
        try {
            return realtyReadingsClient.findWeById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 水电读数列表
     * @param search
     * @return
     */
    public RealtyWePageResult<RealtyWeVo> listWeReadingsPage(RealtyWeSearchVo search) {
        try {
            return realtyReadingsClient.listWePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RealtyWePageResult.emptyPage();
    }

}
