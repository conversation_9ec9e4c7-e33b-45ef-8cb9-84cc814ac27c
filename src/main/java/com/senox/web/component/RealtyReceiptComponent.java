package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.api.clients.RealtyReceiptClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-30
 */
@Component
@RequiredArgsConstructor
public class RealtyReceiptComponent {
    private final RealtyReceiptClient receiptClient;


    /**
     * 物业发票申请
     *
     * @param receiptManger 物业发票管理
     */
    public void apply(RealtyReceiptMangerVo receiptManger) {
        try {
            receiptClient.pcApply(receiptManger);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物业发票申请列表
     *
     * @param search 查询参数
     * @return 分页申请列表
     */
    public PageResult<RealtyReceiptVo> applyList(RealtyReceiptSearchVo search) {
        try {
            return receiptClient.applyList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 物业发票申请账单信息列表
     *
     * @param id 物业发票申请id
     * @return 申请账单信息列表
     */
    public List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(Long id) {
        try {
            return receiptClient.applyBillInfoList(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 发票申请列表
     *
     * @param id     物业发票申请id
     * @param detail 是否详细
     * @return 发票申请列表
     */
    public List<ReceiptApplyVo> applyInfoList(Long id, Boolean detail) {
        try {
            return receiptClient.applyInfoList(id, detail);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物业发票申请审核
     *
     * @param receiptApplyAudit  物业发票审核
     */
    public void applyAudit(ReceiptApplyAuditVo receiptApplyAudit) {
        try {
                receiptClient.applyAudit(receiptApplyAudit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
