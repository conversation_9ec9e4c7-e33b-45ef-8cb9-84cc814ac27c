package com.senox.web.component;

import com.senox.cold.api.clients.NormalTemperatureWarehousingBillClient;
import com.senox.cold.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.wechat.api.clients.WechatManageClient;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-21
 **/
@RequiredArgsConstructor
@Component
public class NormalTemperatureWarehousingBillComponent {
    private final NormalTemperatureWarehousingBillClient billClient;
    private final WechatManageClient wechatManageClient;

    /**
     * 添加账单
     *
     * @param year       年
     * @param month      月
     * @param addDtoList 账单集
     */
    public void add(Integer year, Integer month, Boolean isDuplicate, Boolean isOverride, List<NormalTemperatureWarehousingBillAddDto> addDtoList) {
        try {
            billClient.add(year, month, isDuplicate, isOverride, addDtoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除账单
     *
     * @param ids 账单id集
     */
    public void deleteByIds(List<Long> ids) {
        try {
            billClient.deleteByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单
     *
     * @param bill 账单
     */
    public void update(NormalTemperatureWarehousingBillVo bill) {
        try {
            billClient.update(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 检查重复
     * @param bill 账单
     * @return 返回检查结果
     */
    public Boolean checkDuplicate(NormalTemperatureWarehousingBillAddDto bill) {
        try {
            return billClient.checkDuplicate(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return false;
    }

    /**
     * 更新账单备注
     *
     * @param bills 账单集
     */
    public void updateRemark(List<NormalTemperatureWarehousingBillVo> bills) {
        try {
            billClient.updateRemark(bills);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表
     *
     * @param search 查询
     * @return 返回查询到的列表
     */
    public List<NormalTemperatureWarehousingBillVo> list(NormalTemperatureWarehousingBillSearchVo search) {
        try {
            return billClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 分页列表
     *
     * @param search 查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<NormalTemperatureWarehousingBillVo, NormalTemperatureWarehousingStatisticsVo> pageList(NormalTemperatureWarehousingBillSearchVo search) {
        try {
            return billClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 账单下发
     *
     * @param send 下发参数
     */
    public void send(RefrigerationBillSendVo send) {
        try {
            billClient.send(send);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 账单免除滞纳金
     *
     * @param penaltyIgnore 滞纳金减免参数
     */
    public void ignoreBillPenalty(BillPenaltyIgnoreVo penaltyIgnore) {
        try {
            billClient.ignoreBillPenalty(penaltyIgnore);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单状态
     *
     * @param billPaid 账单支付信息
     */
    public void updateBillStatus(@Validated BillPaidVo billPaid) {
        try {
            billClient.updateBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新票据号
     *
     * @param tollSerial 收费票据
     */
    public void updateSerial(TollSerialVo tollSerial) {
        try {
            billClient.updateSerial(tollSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取账单
     *
     * @param billId 账单id
     * @return 返回获取到的账单
     */
    public NormalTemperatureWarehousingBillVo findById(Long billId) {
        try {
            return billClient.findById(billId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 干仓月度账单通知
     *
     * @param year  年
     * @param month 月
     */
    public void notifyBill(Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            return;
        }
        try {
            wechatManageClient.notifyNormalTemperatureWarehousingBill(year, month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
