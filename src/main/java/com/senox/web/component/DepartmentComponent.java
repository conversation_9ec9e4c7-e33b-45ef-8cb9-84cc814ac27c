package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.DepartmentClient;
import com.senox.user.vo.DepartmentNode;
import com.senox.user.vo.DepartmentVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 14:20
 */
@Component
public class DepartmentComponent {

    @Autowired
    private DepartmentClient departmentClient;

    /**
     * 部门列表
     * @param parentId
     * @return
     */
    public List<DepartmentVo> listDepartment(Long parentId) {
        try {
            return departmentClient.listDepartment(parentId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 部门树
     * @return
     */
    public List<DepartmentNode> listDepartmentTree() {
        try {
            return departmentClient.listDepartmentTree();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取部门详情
     * @param id
     * @return
     */
    public DepartmentVo findById(Long id) {
        try {
            return departmentClient.getDepartment(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加部门
     * @param department
     * @return
     */
    public Long addDepartment(DepartmentVo department) {
        try {
            return departmentClient.addDepartment(department);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更细部门
     * @param department
     */
    public void updateDepartment(DepartmentVo department) {
        try {
            departmentClient.updateDepartment(department);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
