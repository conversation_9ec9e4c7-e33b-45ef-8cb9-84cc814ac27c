package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.BicycleBillSettlementClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@RequiredArgsConstructor
@Component
public class BicycleBillSettlementComponent {
    private final BicycleBillSettlementClient billSettlementClient;



    /**
     * 根据id查询结算单
     *
     * @param id id
     * @return 返回结算单
     */
    public BicycleBillSettlementVo findById(Long id) {
        try {
            return billSettlementClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 账单支付
     *
     * @param billOrderVo 订单
     * @return 返回支付结果
     */
    public OrderResultVo payBill(BicycleBillOrderVo billOrderVo) {
        try {
            return billSettlementClient.payBill(billOrderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 结算单列表
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    public List<BicycleBillSettlementVo> list(BicycleBillSettlementSearchVo searchVo) {
        try {
            return billSettlementClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }


    /**
     * 结算单分页列表
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    public BicycleBillSettlementPageResult<BicycleBillSettlementVo> listPage(BicycleBillSettlementSearchVo searchVo) {
        try {
            return billSettlementClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return BicycleBillSettlementPageResult.emptyPage();

    }

    /**
     * 根据结算单id查询详情
     *
     * @param settlementId 结算单id
     * @return 返回查询到的详情
     */
    public BicycleBillSettlementDetailVo detailBySettlement(Long settlementId) {
        try {
            return billSettlementClient.detailBySettlement(settlementId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 结算单下发
     *
     * @param sendVo 下发参数
     */
    public void send(BicycleBillSettlementSendVo sendVo) {
        try {
            billSettlementClient.send(sendVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 结算单下发通知
     *
     * @param sendVo 下发参数
     */
    public void notifySend(BicycleBillSettlementSendVo sendVo) {
        try {
            billSettlementClient.notifySend(sendVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 结算单生成
     *
     * @param sendVo 参数
     */
    public void generate(BicycleBillSettlementSendVo sendVo) {
        try {
            billSettlementClient.generate(sendVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
