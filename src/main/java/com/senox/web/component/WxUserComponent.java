package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.WxUserClient;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7 16:04
 */
@Component
public class WxUserComponent {

    @Autowired
    private WxUserClient wxUserClient;

    /**
     * 设置灰度用户
     * @param userId
     */
    public void setGrayUser(Long userId) {
        try {
            wxUserClient.setGrayUser(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消灰度用户
     * @param userId
     */
    public void cancelGrayUser(Long userId) {
        try {
            wxUserClient.cancelGrayUser(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 微信客户列表
     * @param searchVo
     * @return
     */
    public PageResult<WxUserVo> listWxUserPage(WxUserSearchVo searchVo) {
        try {
            return wxUserClient.listWxUserPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 绑定物业
     * @param userRealty
     */
    public void bindWxUserRealty(WxUserRealtyVo userRealty) {
        try {
            wxUserClient.bindWxUserRealty(userRealty);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 解绑物业
     * @param userRealty
     */
    public void unbindWxUserRealty(WxUserRealtyVo userRealty) {
        try {
            wxUserClient.unbindWxUserRealty(userRealty);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 微信客户物业列表
     * @param userId
     * @return
     */
    public List<WxUserRealtyVo> listWxUserRealty(Long userId) {
        try {
            return wxUserClient.listWxUserRealty(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 微信用户备注更新
     * @param remarkVo
     */
    public void updateWxUserRemark(WxUserRemarkVo remarkVo) {
        try {
            wxUserClient.updateWxUserRemark(remarkVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 解绑骑手
     * @param riderId
     */
    public void unbindRider(Long riderId) {
        try {
            wxUserClient.unbindRider(riderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
