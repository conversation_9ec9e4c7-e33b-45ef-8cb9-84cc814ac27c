package com.senox.web.component;


import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.wms.api.clients.WmsClient;
import com.senox.wms.api.clients.WmsMaterialClint;
import com.senox.wms.api.clients.WmsMaterialEnterClient;
import com.senox.wms.api.clients.WmsMaterialOutClient;
import com.senox.wms.dto.KuaimaiPrintDto;
import com.senox.wms.dto.MaterialDictFixedAssetsScrapBillDto;
import com.senox.wms.dto.MaterialDictFixedAssetsTransferBillDto;
import com.senox.wms.dto.MaterialDto;
import com.senox.wms.vo.*;
import com.senox.wms.vo.dict.DictMaterialCodeVo;
import com.senox.wms.vo.dict.DictTypeVo;
import com.senox.wms.vo.dict.DictUnitSearchVo;
import com.senox.wms.vo.dict.DictUnitVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-1-10
 */
@Component
@RequiredArgsConstructor
public class WmsComponent {
    private final WmsClient wmsClient;
    private final WmsMaterialEnterClient wmsMaterialEnterClient;
    private final WmsMaterialOutClient wmsMaterialOutClient;
    private final WmsMaterialClint wmsMaterialClint;

    /**
     * 新增物料编码
     *
     * @param dictMaterialCodeVo 物料编码Vo
     */
    public void addMaterialCode(DictMaterialCodeVo dictMaterialCodeVo) {
        try {
            wmsClient.addMaterialCode(dictMaterialCodeVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除物料编码
     *
     * @param ids 物料编码数组
     */
    public void deleteMaterialCode(Long[] ids) {
        try {
            wmsClient.deleteMaterialCode(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改物料编码
     *
     * @param materialCode 物料编码vo
     */
    public void updateMaterialCode(DictMaterialCodeVo materialCode) {
        try {
            wmsClient.updateMaterialCode(materialCode);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据当前用户查询编码列表
     *
     * @param materialSearch 查询参数
     * @return PageResult<DictMaterialCodeVo>
     */
    public PageResult<DictMaterialCodeVo> getMaterialCode(DictMaterialSearchVo materialSearch) {
        try {
            return wmsClient.getMaterialCode(materialSearch);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增物料类型
     *
     * @param dictType 物料类型Vo
     */
    public void addDictType(DictTypeVo dictType) {
        try {
            wmsClient.addDictType(dictType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除物料类型
     *
     * @param ids 物料类型id数组
     */
    public void deleteDictType(Long[] ids) {
        try {
            wmsClient.deleteDictType(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物料类型
     *
     * @param dictType 物料类型
     */
    public void updateDictType(DictTypeVo dictType) {
        try {
            wmsClient.updateDictType(dictType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取当前用户物料类型列表
     *
     * @return List<DictType>
     */
    public List<DictTypeVo> listDictTypeAndWarehouse(Long warehouseId) {
        try {
            return wmsClient.listDictTypeAndWarehouse(warehouseId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 新增单位
     *
     * @param unit 单位
     */
    public void addDictUnit(DictUnitVo unit) {
        try {
            wmsClient.addDictUnit(unit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除单位
     *
     * @param unitIds 单位id数组
     */
    public void deleteDictUnit(Long[] unitIds) {
        try {
            wmsClient.deleteDictUnit(unitIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 修改单位
     *
     * @param dictUnit 单位
     */
    public void updateDictUnit(DictUnitVo dictUnit) {
        try {
            wmsClient.updateDictUnit(dictUnit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取单位列表
     *
     * @param unitSearch 单位查询参数
     * @return PageResult<DictUnitVo>
     */
    public PageResult<DictUnitVo> getDictUnit(DictUnitSearchVo unitSearch) {
        try {
            return wmsClient.getDictUnit(unitSearch);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 获取对应用户仓列表
     *
     * @return Result<List < WarehouseTreeNode>>
     */
    public List<WarehouseTreeNode> warehouseUserList() {
        try {
            return wmsClient.warehouseUserList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据角色id查询该角色对仓的权限列表
     *
     * @param roleId 用户id
     * @return List<WarehouseTreeNode>
     */
    public List<WarehouseTreeNode> roleWarehouseList(Long roleId) {
        try {
            return wmsClient.roleWarehouseList(roleId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新角色仓权限
     *
     * @param roleId        角色id
     * @param warehouseList 仓id列表
     */
    public void warehouseRoleAdd(Long roleId, List<Long> warehouseList) {
        try {
            wmsClient.warehouseRoleAdd(roleId, warehouseList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 新增物料单
     *
     * @param form 入料入仓form
     * @return EnterWarehouseCollectVo
     */
    public EnterWarehouseCollectVo addEnterBill(MaterialEnterBillFormVo form) {
        try {
            return wmsMaterialEnterClient.addEnterBill(form);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 获取入仓单列表
     *
     * @param search 入仓单查询参数
     * @return PageResult<MaterialEnterBillVo>
     */
    public PageResult<MaterialEnterBillVo> enterBillList(MaterialEnterBillSearchVo search) {
        try {
            return wmsMaterialEnterClient.enterBillList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 入仓单号获取物料记录
     *
     * @param bill 入仓单号
     * @return Result<List < MaterialVo>
     */
    public List<MaterialVo> materialListByEnterBill(Long bill) {
        try {
            return wmsMaterialEnterClient.materialListByEnterBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据入仓单号返回入仓单
     *
     * @param bill 单号
     * @return EnterWarehouseCollectVo
     */
    public EnterWarehouseCollectVo getMaterialEnterBill(Long bill,Boolean merge) {
        try {
            return wmsMaterialEnterClient.getEnterWarehouseCollectByBill(bill,merge);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 入仓单数据列表
     *
     * @param search 查询参数
     */
    public EnterWarehouseCollectVo enterDataList(MaterialEnterBillSearchVo search) {
        try {
            return wmsMaterialEnterClient.materialEnterDataList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据单号撤回入仓
     *
     * @param bill 单号
     */
    public void enterBillCancel(Long bill) {
        try {
            wmsMaterialEnterClient.enterBillCancel(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除入仓单中的物料
     *
     * @param bill        单号
     * @param materialIds 物料集
     */
    public void enterBillRecordDelete(Long bill, Long[] materialIds) {
        try {
            wmsMaterialEnterClient.enterBillRecordDelete(bill, materialIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 入仓单追加物料
     *
     * @param bill         单号
     * @param materialList 物料集
     */
    public void enterBillRecordAdded(Long bill, List<MaterialVo> materialList) {
        try {
            wmsMaterialEnterClient.enterBillRecordAdded(bill, materialList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改入仓单的物料
     *
     * @param bill        单号
     * @param materialDto 物料dto
     */
    public void enterBillRecordUpdate(Long bill, MaterialDto materialDto) {
        try {
            wmsMaterialEnterClient.enterBillRecordUpdate(bill, materialDto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 入仓记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialEnterRecordInfoVo>
     */
    public PageResult<MaterialEnterRecordInfoVo> enterRecordList(MaterialEnterRecordSearchVo search) {
        try {
            return wmsMaterialEnterClient.enterRecordList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 创建物料出仓单
     *
     * @param outRecordFormVo 物料出仓form
     * @return MaterialOutConfirmFormVo
     */
    public MaterialOutConfirmFormVo createOutBill(MaterialOutRecordFormVo outRecordFormVo) {
        try {
            return wmsMaterialOutClient.createOutBill(outRecordFormVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 确认物料出仓
     *
     * @param outRecordForm 物料出仓单
     * @return OutWarehouseCollectVo
     */
    public OutWarehouseCollectVo confirmOutBill(MaterialOutConfirmFormVo outRecordForm) {
        try {
            return wmsMaterialOutClient.confirmOutBill(outRecordForm);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取出仓单列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialOutBillVo>
     */
    public PageResult<MaterialOutBillVo> billOutList(MaterialOutBillSearchVo search) {
        try {
            return wmsMaterialOutClient.billOutList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 根据出仓单号查询出仓记录
     *
     * @param bill 出仓单号
     * @return List<MaterialOutRecordVo>
     */
    public List<MaterialOutRecordVo> recordListByBill(Long bill) {
        try {
            return wmsMaterialOutClient.recordListByBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }


    /**
     * 根据出仓单号返回出仓单
     *
     * @param billId 单号
     * @param merge 合并
     * @return OutWarehouseCollectVo
     */
    public OutWarehouseCollectVo getOutBill(Long billId,Boolean merge) {
        try {
            return wmsMaterialOutClient.getOutBill(billId,merge);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 出仓单数据列表
     *
     * @param search 查询参数
     */
    public OutWarehouseCollectVo outDataList(MaterialOutBillSearchVo search) {
        try {
            return wmsMaterialOutClient.outDataList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 撤销出仓
     *
     * @param bill 单号
     */
    public void outBillCancel(Long bill) {
        try {
            wmsMaterialOutClient.outBillCancel(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 删除出仓单中的物料
     *
     * @param bill      单号
     * @param recordIds 出仓记录id集
     */
    public void deleteBillOutRecord(Long bill, Long[] recordIds) {
        try {
            wmsMaterialOutClient.deleteBillOutRecord(bill, recordIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新出仓物料数量
     *
     * @param bill        单号
     * @param outRecordId 出仓记录id
     * @param number      数量
     */
    public void updateBillOutRecord(Long bill, Long outRecordId, Long number) {
        try {
            wmsMaterialOutClient.updateBillOutRecord(bill, outRecordId, number);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 新增出仓物料
     *
     * @param billId                单号
     * @param materialOutRecordList 出仓id集
     */
    public void outBillRecordAdded(Long billId, List<MaterialOutRecordVo> materialOutRecordList) {
        try {
            wmsMaterialOutClient.outBillRecordAdded(billId, materialOutRecordList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 出仓记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialOutRecordInfoVo>
     */
    public MaterialOutRecordPageResult<MaterialOutRecordInfoVo> outRecordList(MaterialOutRecordSearchVo search) {
        try {
            return wmsMaterialOutClient.outRecordList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new MaterialOutRecordPageResult<>();
    }

    /**
     * 打印物料
     *
     * @param sn               设备sn
     * @param kuaimaiPrintDtos 打印dto
     */
    public void kuaimaiPrint(String sn, List<KuaimaiPrintDto> kuaimaiPrintDtos) {
        try {
            wmsClient.kuaimaiPrint(sn, kuaimaiPrintDtos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 打印机列表
     *
     * @return List<KuaimaiPrintVo>
     */
    public List<KuaimaiPrintVo> kuaimaiPrintList() {
        try {
            return wmsClient.kuaimaiPrintList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 盘点单记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialInventoryBillRecordVo>
     */
    public PageResult<MaterialInventoryBillRecordVo> materialInventoryRecordList(@RequestBody MaterialInventoryBillRecordSearchVo search) {
        try {
            return wmsClient.materialInventoryRecordList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 快速盘点
     *
     * @param search 查询参数
     */
    public void recordQuick(@RequestBody MaterialInventoryBillRecordSearchVo search) {
        try {
            wmsClient.recordQuick(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物料盘点
     *
     * @param materialInventorySearch 查询参数
     * @return PageResult < MaterialRedundancyVo>
     */
    public MaterialPageResult<MaterialVo> materialInventoryList(MaterialInventorySearchVo materialInventorySearch) {
        try {
            return wmsClient.materialInventoryList((materialInventorySearch));
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new MaterialPageResult<>();
    }

    /**
     * 物料编码物料库存列表
     *
     * @param dictMaterialSearch 查询参数
     * @return PageResult<DictMaterialCodeVo>
     */
    public PageResult<DictMaterialCodeVo> materialCodeStockList(DictMaterialSearchVo dictMaterialSearch) {
        try {
            return wmsClient.materialCodeStockList((dictMaterialSearch));
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 创建调拨单
     *
     * @param materialAllocationSingleVo 物料调拨单
     */
    public void materialAllocationAdd(MaterialAllocationSingleVo materialAllocationSingleVo) {
        try {
            wmsClient.materialAllocationAdd(materialAllocationSingleVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 调拨单列表
     *
     * @param materialAllocationSingleSearch 查询参数
     * @return PageResult<MaterialAllocationSingleVo>
     */
    public PageResult<MaterialAllocationSingleVo> materialAllocationSingleList(MaterialAllocationSingleSearchVo materialAllocationSingleSearch) {
        try {
            return wmsClient.materialAllocationSingleList(materialAllocationSingleSearch);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 审核调拨单
     *
     * @param id    id
     * @param state 状态
     */
    public void materialAllocationExamine(Long id, Integer state) {
        try {
            wmsClient.materialAllocationExamine(id, state);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取单个固定资产使用记录
     *
     * @param fixedAssetsUseId 固定资产使用记录id
     * @return MaterialFixedAssetsUseVo
     */
    public MaterialFixedAssetsUseVo getFixedAssetsUse(Long fixedAssetsUseId) {
        try {
            return wmsClient.getFixedAssetsUse(fixedAssetsUseId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 固定资产使用记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialFixedAssetsUseVo>
     */
    public PageResult<MaterialFixedAssetsUseVo> assetsUseList(MaterialFixedAssetsUseSearchVo search) {
        try {
            return wmsClient.assetsUseList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 固定资产报废
     *
     * @param materialDictFixedAssetsScrapBillDto 固定资产报废单
     * @return MaterialDictFixedAssetsScrapBillVo
     */
    public MaterialDictFixedAssetsScrapBillVo assetsScrapAdd(MaterialDictFixedAssetsScrapBillDto materialDictFixedAssetsScrapBillDto) {
        try {
            return wmsClient.assetsScrapAdd(materialDictFixedAssetsScrapBillDto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 固定资产报废列表
     *
     * @return List<MaterialDictFixedAssetsScrapBillVo>
     */
    public List<MaterialDictFixedAssetsScrapBillVo> assetsScrapList() {
        try {
            return wmsClient.assetsScrapList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 创建固定资产转移单
     *
     * @param transferBillDtoList 固定资产转移单集
     * @return 返回转移单集
     */
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferAdd(List<MaterialDictFixedAssetsTransferBillDto> transferBillDtoList) {
        try {
            return wmsClient.assetsTransferAdd(transferBillDtoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 固定资产转移列表
     *
     * @return List<MaterialDictFixedAssetsTransferBillVo>
     */
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferList() {
        try {
            return wmsClient.assetsTransferList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 生成仓报表
     */
    public void createWarehouseReport(Integer year, Integer month, Integer reportType) {
        try {
            wmsClient.createWarehouseReport(year, month, reportType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取仓报表记录
     *
     * @return List<WarehouseReportRecordVo>
     */
    public List<WarehouseReportRecordVo> reportRecordList() {
        try {
            return wmsClient.reportRecordList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取仓报表记录文件
     *
     * @param time 日期
     * @return List<WarehouseReportFileVo>
     */
    public List<WarehouseReportFileVo> reportFileList(String time) {
        try {
            return wmsClient.reportFileList(time);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物料绑定gtin
     *
     * @param materialCode 物料编码
     * @param gtin         gtin
     */
    public void materialBindGtin(Long materialCode, String gtin) {
        try {
            wmsMaterialClint.materialBindGtin(materialCode, gtin);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物料出仓记录打印
     *
     * @param outBill 出仓单
     * @param merge 合并
     */
    public void materialOutRecordPrint(Long outBill,Boolean merge) {
        try {
            wmsMaterialOutClient.outRecordPrint(outBill, merge);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
