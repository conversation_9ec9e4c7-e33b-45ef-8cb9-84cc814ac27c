package com.senox.web.component;

import com.senox.common.constant.device.EnergyType;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.EnergyMeteringPointClient;
import com.senox.realty.api.clients.RealtyEnergyClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-12
 */
@Component
@RequiredArgsConstructor
public class RealtyEnergyComponent {
    private final RealtyEnergyClient realtyEnergyClient;
    private final EnergyMeteringPointClient energyMeteringPointClient;

    /**
     * 计量点设备绑定尾页
     *
     * @param realtyBindPoint 绑定vo
     */
    public void meteringPointBindRealty(RealtyBindEnergyMeteringPointVo realtyBindPoint) {
        try {
            realtyEnergyClient.meteringPointBindRealty(realtyBindPoint);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物业计量点统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public Integer meteringPointCountList(RealtyToEnergyMeteringPointSearchVo search) {
        try {
            return realtyEnergyClient.meteringPointCountList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 物业计量点列表
     *
     * @param search 查询参数
     * @return 物业计量点分页
     */
    public PageResult<RealtyToEnergyMeteringPointVo> meteringPoint(RealtyToEnergyMeteringPointSearchVo search) {
        try {
            return realtyEnergyClient.meteringPoint(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 物业计量点表码列表
     *
     * @param search 查询参数
     * @return 物业计量点表码分页
     */
    public PageResult<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsList(RealtyToEnergyMeteringPointReadingsSearchVo search) {
        try {
            return realtyEnergyClient.meteringPointReadingsList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 同步计量点读数
     *
     * @param realtyWeBatch 参数
     */
    public void syncMeteringPoint(RealtyWeBatchVo realtyWeBatch) {
        try {
            realtyEnergyClient.syncMeteringPoint(realtyWeBatch);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 自动绑定计量设备
     *
     * @param search 查询参数
     * @return 返回处理结果
     */
    public PointBindRealtyResult automaticMeteringPointBindRealty(RealtyToEnergyMeteringPointSearchVo search) {
        try {
            return realtyEnergyClient.automaticMeteringPointBindRealty(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 计量点作废
     * @param meteringPointCode 计量点编号
     */
    public void meteringPointCancel(String meteringPointCode) {
        try {
            realtyEnergyClient.meteringPointCancel(meteringPointCode);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 计量点刷新
     * @param meteringPointCode 计量点编码
     * @return 返回刷新结果
     */
    public EnergyPointRefreshResult meteringPointRefresh(String meteringPointCode) {
        try {
            return energyMeteringPointClient.refresh(meteringPointCode);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加能源消费单元
     * @param unit
     * @return
     */
    public Long addConsumeUnit(EnergyConsumeUnitVo unit) {
        try {
            return realtyEnergyClient.addConsumeUnit(unit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新能源消费单元
     * @param unit
     */
    public void updateConsumeUnit(EnergyConsumeUnitVo unit) {
        try {
            realtyEnergyClient.updateConsumeUnit(unit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取能源消费单元详情
     * @param id
     * @return
     */
    public EnergyConsumeUnitVo findConsumeUnitById(Long id) {
        try {
            return realtyEnergyClient.findConsumeUnitById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 能源消费单元列表
     * @param type
     * @return
     */
    public List<EnergyConsumeUnitVo> listConsumeUnit(Integer type) {
        try {
            return realtyEnergyClient.listConsumeUnit(type);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 能源损益生成
     * @param billTime
     * @param type
     * @return
     */
    public EnergyProfitVo generateProfit(BillTimeVo billTime, EnergyType type) {
        try {
            return realtyEnergyClient.generateProfit(billTime, type);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 能源损益保存
     * @param profitEdit
     * @return
     */
    public EnergyProfitVo saveProfit(EnergyProfitEditVo profitEdit) {
        try {
            return realtyEnergyClient.saveProfit(profitEdit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 能源损益获取
     * @param id
     * @return
     */
    public EnergyProfitVo findProfitById(Long id) {
        try {
            return realtyEnergyClient.findProfitById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 能源损益页
     * @param search
     * @return
     */
    public PageResult<EnergyProfitVo> listProfitPage(EnergyProfitSearchVo search) {
        try {
            return realtyEnergyClient.listProfitPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 能源损益明细
     * @param profitId
     * @return
     */
    public List<EnergyProfitItemVo> listProfitItem(Long profitId) {
        try {
            return realtyEnergyClient.listProfitItem(profitId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
