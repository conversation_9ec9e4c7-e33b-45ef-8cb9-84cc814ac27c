package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.api.clients.MaintainClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/31 7:54
 */
@Component
@RequiredArgsConstructor
public class MaintainComponent {

    private final MaintainClient maintainClient;

    /**
     * 添加维修单
     *
     * @param maintainOrderVo
     * @return
     */
    public Long addMaintainOrder(MaintainOrderVo maintainOrderVo) {
        try {
            return maintainClient.addMaintainOrder(maintainOrderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 修改维修单
     *
     * @param maintainOrderVo
     */
    public void updateMaintainOrder(MaintainOrderVo maintainOrderVo) {
        try {
            maintainClient.updateMaintainOrder(maintainOrderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查询维修单
     *
     * @param id
     * @return
     */
    public MaintainOrderVo findMaintainOrder(Long id, Boolean media) {
        try {
            return maintainClient.findMaintainOrder(id, media);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 查询维修单
     *
     * @param search
     * @return
     */
    public PageResult<MaintainOrderVo> listMaintainOrder(MaintainOrderSearchVo search) {
        try {
            return maintainClient.listMaintainOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 删除维修单
     * @param id
     */
    public void deleteMaintainOrder(Long id) {
        try {
            maintainClient.deleteMaintainOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 维修单数量
     * @param searchVo
     * @return
     */
    public int countMaintainOrder(MaintainOrderSearchVo searchVo) {
        try {
            return maintainClient.countMaintainOrder(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 维修单列表导出所有处理节点
     * @param search
     * @return
     */
    public List<MaintainOrderVo> exportListMaintainOrder(MaintainOrderSearchVo search) {
        try {
            return maintainClient.exportListMaintainOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 维修单费用统计分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderStatisticVo> pageOrderStatistic(MaintainOrderSearchVo searchVo) {
        try {
            return maintainClient.pageOrderStatistic(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 维修单费用合计
     * @param searchVo
     * @return
     */
    public MaintainOrderStatisticVo sumOrderStatistic(MaintainOrderSearchVo searchVo) {
        try {
            return maintainClient.sumOrderStatistic(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 维修单评价
     * @param evaluateVo
     */
    public void evaluateOrder(MaintainOrderEvaluateVo evaluateVo) {
        try {
            maintainClient.evaluateOrder(evaluateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 重置维修单评价
     * @param orderId
     */
    public void resetEvaluate(Long orderId) {
        try {
            maintainClient.resetEvaluate(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 维修单评价分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderVo> evaluateOrderPage(MaintainOrderEvaluateSearchVo searchVo) {
        try {
            return maintainClient.evaluateOrderPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加派工单
     *
     * @param maintainJobVo
     * @return
     */
    public Long addMaintainJob(MaintainJobVo maintainJobVo) {
        try {
            return maintainClient.addMaintainJob(maintainJobVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 修改派工单
     *
     * @param maintainJobVo
     * @return
     */
    public void updateMaintainJob(MaintainJobVo maintainJobVo) {
        try {
            maintainClient.updateMaintainJob(maintainJobVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改派工人员信息
     *
     * @param maintainJobItemVo
     */
    public void updateMaintainJobItem(MaintainJobItemVo maintainJobItemVo) {
        try {
            maintainClient.updateMaintainJobItem(maintainJobItemVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取派工单
     *
     * @param id
     * @return
     */
    public MaintainJobVo findMaintainJob(Long id) {
        try {
            return maintainClient.findMaintainJob(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 查询派工单
     *
     * @param orderId
     * @return
     */
    public List<MaintainJobVo> listDispatchJobByOrderId(Long orderId) {
        try {
            return maintainClient.listDispatchJobByOrderId(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询派工单列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<MaintainDispatchJobVo> listDispatchJob(MaintainJobSearchVo searchVo) {
        try {
            return maintainClient.listDispatchJob(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据派工子单查询
     * @param itemId
     * @return
     */
    public MaintainDispatchJobVo findDispatchByJobItemId(Long itemId) {
        try {
            return maintainClient.findDispatchByJobItemId(itemId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除派工单
     * @param jobId
     */
    public void deleteMaintainJob(Long jobId) {
        try {
            maintainClient.deleteMaintainJob(jobId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加维修所需物料
     *
     * @param materialVo
     * @return
     */
    public Long saveMaintainMaterial(MaintainMaterialVo materialVo) {
        try {
            return maintainClient.saveMaintainMaterial(materialVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 删除维修物料
     *
     * @param id
     */
    public void deleteMaintainMaterial(Long id) {
        try {
            maintainClient.deleteMaintainMaterial(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量删除物料及明细
     * @param ids
     */
    public void batchDeleteMaterial(List<Long> ids) {
        try {
            maintainClient.batchDeleteMaterial(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物料列表
     *
     * @param search
     * @return
     */
    public PageResult<MaintainMaterialDataVo> listMaintainMaterial(MaintainMaterialSearchVo search) {
        try {
            return maintainClient.listMaintainMaterial(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新出库单号
     *
     * @param outNoVo
     */
    public void saveOutNo(MaintainMaterialOutNoVo outNoVo) {
        try {
            maintainClient.saveOutNo(outNoVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销出库
     *
     * @param outNo
     */
    public void cancelOutBound(String outNo) {
        try {
            maintainClient.cancelOutBound(outNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取维修所需物料
     *
     * @param orderId
     * @param jobId
     * @return
     */
    public List<MaintainMaterialItemVo> listMaterialByOrderIdAndJobId(Long orderId, Long jobId) {
        try {
            return maintainClient.listMaterialByOrderIdAndJobId(orderId, jobId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新维修物料单
     * @param materialVo
     */
    public void updateMaintainMaterial(MaintainMaterialVo materialVo) {
        try {
            maintainClient.updateMaintainMaterial(materialVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量添加维修所需物料
     * @param materialItemVos
     */
    public void batchSaveMaterial(List<MaintainMaterialItemVo> materialItemVos) {
        try {
            maintainClient.batchSaveMaterial(materialItemVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加维修收费帐单
     *
     * @param chargeVo
     */
    public void addMaintainCharge(MaintainChargeVo chargeVo) {
        try {
            maintainClient.addMaintainCharge(chargeVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 维修收费账单列表
     *
     * @param searchVo
     * @return
     */
    public MaintainChargePageResult<MaintainChargeDataVo> listMaintainCharge(MaintainChargeSearchVo searchVo) {
        try {
            return maintainClient.listMaintainCharge(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除维修账单
     *
     * @param id
     */
    public void deleteMaintainCharge(Long id) {
        try {
            maintainClient.deleteMaintainCharge(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除维修账单明细
     *
     * @param id
     */
    public void deleteMaintainChargeItem(Long id) {
        try {
            maintainClient.deleteMaintainChargeItem(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 维修收费账单详情
     *
     * @param id
     * @return
     */
    public MaintainChargeDataVo chargeDataVoById(Long id) {
        try {
            return maintainClient.chargeDataVoById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据订单号查询维修收费账单详情
     * @param orderId
     * @return
     */
    public List<MaintainChargeDataVo> listChargeDataVoByOrderId(Long orderId) {
        try {
            return maintainClient.listChargeDataVoByOrderId(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据收费单id查询物维收费单费项
     *
     * @param chargeId
     * @return
     */
    public List<MaintainChargeItemVo> chargeItemList(Long chargeId) {
        try {
            return maintainClient.chargeItemList(chargeId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据派工id查询物维收费单集合
     *
     * @param jobId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByJobId(Long jobId) {
        try {
            return maintainClient.listChargeItemByJobId(jobId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据订单id查询物维收费单集合
     * @param orderId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByOrderId(Long orderId) {
        try {
            return maintainClient.listChargeItemByOrderId(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id物维收费单集合
     *
     * @param ids
     * @return
     */
    public List<MaintainChargeVo> listMaintainChargeByIds(List<Long> ids) {
        try {
            return maintainClient.listMaintainChargeByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新收费账单状态
     *
     * @param billPaidVo
     */
    public void updateChargeStatus(BillPaidVo billPaidVo) {
        try {
            maintainClient.updateChargeStatus(billPaidVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单备注
     * @param remarkVoList
     */
    public void updateChargeRemark(List<MaintainChargeRemarkVo> remarkVoList) {
        try {
            maintainClient.updateChargeRemark(remarkVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量添加维修所需账单
     * @param chargeItemVos
     */
    public void batchSaveCharge(List<MaintainChargeItemVo> chargeItemVos) {
        try {
            maintainClient.batchSaveCharge(chargeItemVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 保存票据号
     *
     * @param chargeSerial
     */
    public void saveChargeSerial(MaintainChargeSerialVo chargeSerial) {
        try {
            maintainClient.saveChargeSerial(chargeSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物维单日报表分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> dayListPage(MaintainOrderDayReportSearchVo searchVo) {
        try {
            return maintainClient.dayListPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

    /**
     * 物维单月报表分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> monthListPage(MaintainOrderMonthReportSearchVo searchVo) {
        try {
            return maintainClient.monthListPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }
}
