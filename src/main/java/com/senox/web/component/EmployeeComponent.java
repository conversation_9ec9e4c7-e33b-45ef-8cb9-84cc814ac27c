package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.EmployeeClient;
import com.senox.user.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:53
 */
@Component
@RequiredArgsConstructor
public class EmployeeComponent {

    private final EmployeeClient employeeClient;

    /**
     * 员工列表
     * @param searchVo
     * @return
     */
    public PageResult<EmployeeVo> listEmployeePage(EmployeeSearchVo searchVo) {
        try {
            return employeeClient.listEmployeePage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id查找员工
     * @param id
     * @return
     */
    public EmployeeVo findById(Long id) {
        try {
            return employeeClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 新增员工
     * @param employee
     * @return
     */
    public Long addEmployee(EmployeeVo employee) {
        try {
            return employeeClient.addEmployee(employee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 修改员工信息
     * @param employee
     */
    public void updateEmployee(EmployeeVo employee) {
        try {
            employeeClient.updateEmployee(employee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 报餐列表
     * @param searchVo
     * @return
     */
    public PageResult<BookingMealVo> listBookingPage(BookingMealSearchVo searchVo) {
        try {
            return employeeClient.listBookingPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 报餐日报表
     * @param searchVo
     * @return
     */
    public PageResult<BookingMealCompanyDayReportVo> listBookingCompanyDayReportPage(BookingMealDayReportSearchVo searchVo) {
        try {
            return employeeClient.listBookingCompanyDayReportPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 报餐日报表详情
     * @param mealDate
     * @return
     */
    public List<BookingMealCompanyDayReportVo> listBookingCompanyDayReportDetail(String mealDate) {
        try {
            return employeeClient.listBookingCompanyDayReportDetail(mealDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 假期列表
     * @param searchVo
     * @return
     */
    public List<HolidayVo> listHoliday(HolidaySearchVo searchVo) {
        try {
            return employeeClient.listHoliday(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 保存假期设置
     * @param holidayList
     */
    public void saveHoliday(List<HolidayVo> holidayList) {
        try {
            employeeClient.saveHolidays(holidayList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除假期
     * @param dateList
     */
    public void deleteHoliday(List<LocalDate> dateList) {
        try {
            employeeClient.deleteHolidays(dateList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
