package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.flow.api.clients.WorkflowTaskClient;
import com.senox.flow.vo.WorkflowTreeNode;
import com.senox.flow.vo.WorkflowTaskItemSearchVo;
import com.senox.flow.vo.WorkflowTaskItemVo;
import com.senox.flow.vo.WorkflowTaskVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 14:25
 */
@Component
@RequiredArgsConstructor
public class WorkFlowTaskComponent {

    private final WorkflowTaskClient workflowTaskClient;

    /**
     * 根据实例id查询任务历史
     * @param instanceId 实例id
     * @return 返回结果
     */
    public List<WorkflowTaskVo> taskHistoryByInstanceId(Long instanceId) {
        try {
            return workflowTaskClient.taskHistoryByInstanceId(instanceId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据任务id查询任务历史
     *
     * @param taskId 任务id
     * @return 返回结果
     */
    public WorkflowTreeNode<WorkflowTaskVo> taskHistory(Long taskId) {
        try {
            return workflowTaskClient.taskHistory(taskId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据实例id查询当前处理人任务
     *
     * @param instanceId
     * @return
     */
    public WorkflowTaskVo currentAssigneeFindByInstanceId(Long instanceId) {
        try {
            List<WorkflowTaskVo> taskVos = workflowTaskClient.currentAssigneeFindByInstanceId(Collections.singletonList(instanceId));
            return CollectionUtils.isEmpty(taskVos) ? null : taskVos.get(0);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据任务id查询当前处理人任务
     *
     * @param taskId 任务id
     * @return 返回结果
     */
    public WorkflowTaskVo currentAssigneeFindByTaskId(Long taskId) {
        try {
            return workflowTaskClient.currentAssigneeFindByTaskId(taskId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 待办列表
     *
     * @param search 查询参数
     * @return 返回待办列表
     */
    public PageResult<WorkflowTaskItemVo> todoList(WorkflowTaskItemSearchVo search) {
        try {
            return workflowTaskClient.todoList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 待办任务数
     * @param search 查询参数
     * @return 返回任务数
     */
    public Integer countTodoList(WorkflowTaskItemSearchVo search) {
        try {
            return workflowTaskClient.countTodoList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 已办列表
     *
     * @param search 查询参数
     * @return 返回已办列表
     */
    public PageResult<WorkflowTaskItemVo> doneList(WorkflowTaskItemSearchVo search) {
        try {
            return workflowTaskClient.doneList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
