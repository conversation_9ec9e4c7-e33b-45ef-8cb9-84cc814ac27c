package com.senox.web.component;

import com.senox.cold.api.clients.*;
import com.senox.cold.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/27 13:48
 */
@RequiredArgsConstructor
@Component
public class DeliveryComponent {

    private final DeliveryWarehouseClient deliveryWarehouseClient;
    private final SkuClient skuClient;

    private final DeliveryOrderClient deliveryOrderClient;

    private final DeliveryEmployeeClient deliveryEmployeeClient;

    private final DeliveryReturnClient deliveryReturnClient;

    private final DeliveryReturnPlanClient deliveryReturnPlanClient;


    /**
     * 新增仓库
     *
     * @param warehouseVo
     * @return
     */
    public Long addDeliveryWarehouse(DeliveryWarehouseVo warehouseVo) {
        try {
            return deliveryWarehouseClient.addDeliveryWarehouse(warehouseVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 批量新增仓库
     *
     * @param list
     */
    public void batchAddDeliveryWarehouse(List<DeliveryWarehouseVo> list) {
        try {
            deliveryWarehouseClient.batchAddDeliveryWarehouse(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新仓库
     *
     * @param warehouseVo
     */
    public void updateDeliveryWarehouse(DeliveryWarehouseVo warehouseVo) {
        try {
            deliveryWarehouseClient.updateDeliveryWarehouse(warehouseVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除仓库
     *
     * @param id
     */
    public void deleteDeliveryWarehouse(Long id) {
        try {
            deliveryWarehouseClient.deleteDeliveryWarehouse(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取仓库
     *
     * @param id
     * @return
     */
    public DeliveryWarehouseVo findDeliveryWarehouseById(Long id) {
        try {
            return deliveryWarehouseClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 仓库列表
     *
     * @param search
     * @return
     */
    public PageResult<DeliveryWarehouseVo> listDeliveryWarehouse(DeliveryWarehouseSearchVo search) {
        try {
            return deliveryWarehouseClient.listDeliveryWarehouse(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增商品
     *
     * @param skuVo
     * @return
     */
    public Long addSku(SkuVo skuVo) {
        try {
            return skuClient.addSku(skuVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 批量新增商品
     *
     * @param list
     */
    public void batchAddSku(List<SkuVo> list) {
        try {
            skuClient.batchAddSku(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新商品
     *
     * @param skuVo
     */
    public void updateSku(SkuVo skuVo) {
        try {
            skuClient.updateSku(skuVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除商品
     *
     * @param id
     */
    public void deleteSku(Long id) {
        try {
            skuClient.deleteSku(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取商品
     *
     * @param id
     * @return
     */
    public SkuVo findSkuById(Long id) {
        try {
            return skuClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 商品列表
     *
     * @param search
     * @return
     */
    public PageResult<SkuVo> listSku(SkuSearchVo search) {
        try {
            return skuClient.listSku(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 所有商品
     * @return
     */
    public List<SkuVo> listSkuAll() {
        try {
            return skuClient.listAll();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加物流订单
     *
     * @param orderVo
     * @return
     */
    public Long addDeliveryOrder(DeliveryOrderVo orderVo) {
        try {
            return deliveryOrderClient.addDeliveryOrder(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 发货
     *
     * @param orderId
     */
    public void sendDeliveryOrder(Long orderId) {
        try {
            deliveryOrderClient.sendDeliveryOrder(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /***
     * 删除物流订单
     * @param id
     */
    public void deleteDeliveryOrder(Long id) {
        try {
            deliveryOrderClient.deleteDeliveryOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 用户物流订单列表
     *
     * @param search
     * @return
     */
    public List<DeliveryOrderVo> orderInfoList(DeliveryOrderSearchVo search) {
        try {
            return deliveryOrderClient.orderInfoList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取物流订单
     *
     * @param orderId
     * @return
     */
    public DeliveryOrderVo deliveryOrderById(Long orderId) {
        try {
            return deliveryOrderClient.deliveryOrderById(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据订单id集合获取物流订单
     *
     * @param ids
     * @return
     */
    public List<DeliveryOrderVo> deliveryOrderByIds(List<Long> ids) {
        try {
            return deliveryOrderClient.findByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物流订单列表
     *
     * @param search
     * @return
     */
    public PageResult<DeliveryOrderVo> orderList(DeliveryOrderSearchVo search) {
        try {
            return deliveryOrderClient.orderList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 最近一天未发货的订单数量
     *
     * @return
     */
    public Integer countStatusByUnSend() {
        try {
            return deliveryOrderClient.countStatusByUnSend();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 添加物流员工
     *
     * @param employeeVo
     * @return
     */
    public Long addDeliveryEmployee(DeliveryEmployeeVo employeeVo) {
        try {
            return deliveryEmployeeClient.addDeliveryEmployee(employeeVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新物流员工
     *
     * @param employeeVo
     */
    public void updateDeliveryEmployee(DeliveryEmployeeVo employeeVo) {
        try {
            deliveryEmployeeClient.updateDeliveryEmployee(employeeVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物流员工密码
     *
     * @param changePwdVo
     */
    public void changePassword(DeliveryEmployeeChangePwdVo changePwdVo) {
        try {
            deliveryEmployeeClient.changePassword(changePwdVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /***
     * 删除物流员工
     * @param id
     */
    public void deleteDeliveryEmployee(Long id) {
        try {
            deliveryEmployeeClient.deleteDeliveryEmployee(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取物流员工
     *
     * @param id
     * @return
     */
    public DeliveryEmployeeVo deliveryEmployeeById(Long id) {
        try {
            return deliveryEmployeeClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 物流员工列表
     *
     * @param search
     * @return
     */
    public PageResult<DeliveryEmployeeVo> deliveryEmployeeList(DeliveryEmployeeSearchVo search) {
        try {
            return deliveryEmployeeClient.deliveryEmployeeList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加物流退货单(草稿状态)
     *
     * @param returnVo
     * @return
     */
    public Long addDeliveryReturn(DeliveryReturnVo returnVo) {
        try {
            return deliveryReturnClient.addDeliveryReturn(returnVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新物流退货单
     *
     * @param returnVo
     */
    public void updateDeliveryReturn(DeliveryReturnVo returnVo) {
        try {
            deliveryReturnClient.updateDeliveryReturn(returnVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /***
     * 更新物流退货单状态为正常
     * @param id
     */
    public void updateDeliveryReturnState(Long id) {
        try {
            deliveryReturnClient.updateState(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /***
     * 删除物流退货单
     * @param id
     */
    public void deleteDeliveryReturn(Long id) {
        try {
            deliveryReturnClient.deleteDeliveryReturn(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取退货单
     *
     * @param id
     * @return
     */
    public DeliveryReturnVo deliveryReturnById(Long id) {
        try {
            return deliveryReturnClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据退货单id集合获取退货单
     *
     * @param ids
     * @return
     */
    public List<DeliveryReturnVo> deliveryReturnByIds(List<Long> ids) {
        try {
            return deliveryReturnClient.findByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 物流订单列表
     *
     * @param search
     * @return
     */
    public PageResult<DeliveryReturnVo> deliveryReturnList(DeliveryReturnSearchVo search) {
        try {
            return deliveryReturnClient.deliveryReturnList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 最近一天未提交的退货单数量
     *
     * @return
     */
    public Integer countStatusByDraft() {
        try {
            return deliveryReturnClient.countStatusByDraft();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 物流退货单详细列表
     *
     * @param search
     * @return
     */
    public List<DeliveryReturnVo> returnInfoList(DeliveryReturnSearchVo search) {
        try {
            return deliveryReturnClient.returnInfoList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 批量添加退货计划单
     * @param list
     */
    public void batchAddDeliveryReturnPlan(List<DeliveryReturnPlanVo> list) {
        try {
            deliveryReturnPlanClient.batchAddDeliveryReturnPlan(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加退货计划单
     * @param planVo
     */
    public void addDeliveryReturnPlan(DeliveryReturnPlanVo planVo) {
        try {
            deliveryReturnPlanClient.addDeliveryReturnPlan(planVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新退货计划单
     * @param planVo
     */
    public void updateDeliveryReturnPlan(DeliveryReturnPlanVo planVo) {
        try {
            deliveryReturnPlanClient.updateDeliveryReturnPlan(planVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取计划单
     * @param id
     * @return
     */
    public DeliveryReturnPlanVo deliveryReturnPlanById(Long id) {
        try {
            return deliveryReturnPlanClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除计划单
     * @param id
     */
    public void deleteReturnPlan(Long id) {
        try {
            deliveryReturnPlanClient.deleteReturnPlan(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量删除计划单
     * @param ids
     */
    public void deleteBatchByIds(List<Long> ids) {
        try {
            deliveryReturnPlanClient.deleteBatchByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 退货计划单日期列表
     * @param searchVo
     * @return
     */
    public PageResult<DeliveryReturnPlanDateVo> localDatePlanList(DeliveryReturnPlanSearchVo searchVo) {
        try {
            return deliveryReturnPlanClient.localDateList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 退货计划单列表
     * @param searchVo
     * @return
     */
    public List<DeliveryReturnPlanVo> listReturnPlan(DeliveryReturnPlanSearchVo searchVo) {
        try {
            return deliveryReturnPlanClient.listReturnPlan(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 检验是否导入退货计划单
     * @param planDate
     */
    public void checkDayPlan(LocalDate planDate) {
        try {
            deliveryReturnPlanClient.checkDayPlan(planDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
