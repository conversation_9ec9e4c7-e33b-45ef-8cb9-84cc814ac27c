package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleRiderClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 15:25
 */
@Component
@RequiredArgsConstructor
public class BicycleRiderComponent {

    private final BicycleRiderClient bicycleRiderClient;

    /**
     * 添加三轮车配送骑手
     * @param riderVo
     * @return
     */
    public Long addBicycleRider(BicycleRiderVo riderVo) {
        try {
            return bicycleRiderClient.addBicycleRider(riderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改三轮车配送骑手
     * @param riderVo
     */
    public void updateBicycleRider(BicycleRiderVo riderVo) {
        try {
            bicycleRiderClient.updateBicycleRider(riderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取三轮车骑手
     * @param id
     * @return
     */
    public BicycleRiderVo findById(Long id) {
        try {
            return bicycleRiderClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除三轮车配送骑手
     * @param id
     */
    public void deleteBicycleRider(Long id) {
        try {
            bicycleRiderClient.deleteBicycleRider(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 三轮车配送骑手列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderVo> listRider(BicycleRiderSearchVo searchVo) {
        try {
            return bicycleRiderClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id获取三轮车骑手考勤信息
     * @param id
     * @return
     */
    public BicycleRiderAttendanceVo findAttendanceById(Long id) {
        try {
            return bicycleRiderClient.findAttendanceById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 三轮车骑手考勤信息列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderAttendanceVo> listRiderAttendance(BicycleRiderAttendanceSearchVo searchVo) {
        try {
            return bicycleRiderClient.listRiderAttendance(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 三轮车配送骑手信息列表
     * @return
     */
    public List<BicycleRiderInfoVo> listRiderInfo() {
        try {
            return bicycleRiderClient.listRider();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
