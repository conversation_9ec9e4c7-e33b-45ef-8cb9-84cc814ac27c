package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.FeedBackClient;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/1 13:57
 */
@Component
public class FeedBackComponent {

    @Autowired
    private FeedBackClient feedBackClient;

    /**
     * 获取建议反馈及回复信息
     * @param id
     * @param isDetail
     * @return
     */
    public FeedBackVo findFeedBackById(Long id, Boolean isDetail){
        try {
            return feedBackClient.findFeedBackById(id, isDetail);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 建议反馈列表
     * @param search
     * @return
     */
    public PageResult<FeedBackVo> listFeedBack(FeedBackSearchVo search){
        try {
            return feedBackClient.listFeedBack(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加建议回复
     * @param feedBackReplyVo
     * @return
     */
    public Long addFeedBackReply(FeedBackReplyVo feedBackReplyVo){
        try {
            return feedBackClient.addFeedBackReply(feedBackReplyVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 获取建议回复
     * @param id
     * @return
     */
    public FeedBackReplyVo findFeedBackReplyById(Long id){
        try {
            return feedBackClient.findFeedBackReplyById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
