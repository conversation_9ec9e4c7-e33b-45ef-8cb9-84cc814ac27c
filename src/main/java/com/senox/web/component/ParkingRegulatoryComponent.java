package com.senox.web.component;

import com.senox.car.api.clients.ParkingRegulatoryClient;
import com.senox.car.vo.ParkingRegulatorySearchVo;
import com.senox.car.vo.ParkingRegulatoryVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:52
 */
@Component
@RequiredArgsConstructor
public class ParkingRegulatoryComponent {

    private final ParkingRegulatoryClient parkingRegulatoryClient;


    /**
     * 添加监管名单
     * @param parkingRegulatoryVo
     * @return
     */
    public Long addParkingRegulatory(ParkingRegulatoryVo parkingRegulatoryVo) {
        try {
            return parkingRegulatoryClient.addParkingRegulatory(parkingRegulatoryVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新监管名单
     * @param parkingRegulatoryVo
     */
    public void updateParkingRegulatory(ParkingRegulatoryVo parkingRegulatoryVo) {
        try {
            parkingRegulatoryClient.updateParkingRegulatory(parkingRegulatoryVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取监管名单
     * @param id
     * @return
     */
    public ParkingRegulatoryVo findById(Long id) {
        try {
            return parkingRegulatoryClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 监管名单分页
     * @param searchVo
     * @return
     */
    public PageResult<ParkingRegulatoryVo> page(ParkingRegulatorySearchVo searchVo) {
        try {
            return parkingRegulatoryClient.page(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
