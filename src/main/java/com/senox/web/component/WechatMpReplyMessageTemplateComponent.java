package com.senox.web.component;


import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.WechatMpReplyMessageTemplateClient;
import com.senox.user.vo.WxReplyMessageTemplateSearchVo;
import com.senox.user.vo.WxReplyMessageTemplateVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-04-21
 **/
@Component
@RequiredArgsConstructor
public class WechatMpReplyMessageTemplateComponent {
    private final WechatMpReplyMessageTemplateClient templateClient;

    /**
     * 添加模板
     * @param template 模板
     */
    public void add(WxReplyMessageTemplateVo template) {
        try {
            templateClient.add(template);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新
     * @param template 模板
     */
    public void update(WxReplyMessageTemplateVo template) {
        try {
            templateClient.update(template);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新状态
     * @param template 模板
     */
    public void updateStatus(WxReplyMessageTemplateVo template) {
        try {
            templateClient.updateStatus(template);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据模板id删除
     * @param templateId 模板id
     */
    public void deleteById(Long templateId) {
        try {
            templateClient.deleteById(templateId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找
     * @param templateId 模板id
     * @return 返回模板视图
     */
    public WxReplyMessageTemplateVo findById(Long templateId) {
        try {
            return templateClient.findById(templateId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public PageResult<WxReplyMessageTemplateVo> pageList(WxReplyMessageTemplateSearchVo search) {
        try {
            return templateClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
