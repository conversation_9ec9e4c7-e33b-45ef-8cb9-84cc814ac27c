package com.senox.web.component;

import com.senox.cold.api.clients.CloudWarehousingBillClient;
import com.senox.cold.vo.CloudWarehousingBillSearchVo;
import com.senox.cold.vo.CloudWarehousingBillVo;
import com.senox.cold.vo.RefrigerationBillRemarkVo;
import com.senox.cold.vo.RefrigerationBillSendVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 9:50
 */
@RequiredArgsConstructor
@Component
public class CloudWarehousingBillComponent {

    private final CloudWarehousingBillClient warehousingBillClient;

    /**
     * 添加云仓账单
     * @param billVo
     * @return
     */
    public Long addCloudWarehousingBill(CloudWarehousingBillVo billVo) {
        try {
            return warehousingBillClient.addCloudWarehousingBill(billVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 批量添加云仓账单
     * @param list
     */
    public void batchAdd(List<CloudWarehousingBillVo> list) {
        try {
            warehousingBillClient.batchAdd(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新云仓账单
     * @param billVo
     */
    public void updateCloudWarehousingBill(CloudWarehousingBillVo billVo) {
        try {
            warehousingBillClient.updateCloudWarehousingBill(billVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取云仓账单
     * @param id
     * @return
     */
    public CloudWarehousingBillVo findById(Long id) {
        try {
            return warehousingBillClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 批量删除云仓账单
     * @param ids
     */
    public void deleteByIds(List<Long> ids) {
        try {
            warehousingBillClient.deleteByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单备注
     * @param remarkVoList
     */
    public void updateBillRemark(List<RefrigerationBillRemarkVo> remarkVoList) {
        try {
            warehousingBillClient.updateBillRemark(remarkVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新票据号
     * @param tollSerial
     */
    public void updateBillSerial(TollSerialVo tollSerial) {
        try {
            warehousingBillClient.updateBillSerial(tollSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 下发云仓账单
     * @param send
     */
    public void sendBill(RefrigerationBillSendVo send) {
        try {
            warehousingBillClient.sendBill(send);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 免滞纳金
     * @param penaltyIgnore
     */
    public void ignoreBillPenalty(BillPenaltyIgnoreVo penaltyIgnore) {
        try {
            warehousingBillClient.ignoreBillPenalty(penaltyIgnore);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 云仓账单合计
     * @param search
     * @return
     */
    public CloudWarehousingBillVo sumBill(CloudWarehousingBillSearchVo search) {
        try {
            return warehousingBillClient.sumBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 云仓账单列表
     * @param search
     * @return
     */
    public PageResult<CloudWarehousingBillVo> listBill(CloudWarehousingBillSearchVo search) {
        try {
            return warehousingBillClient.listBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 更新账单状态
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        try {
            warehousingBillClient.updateBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取账单列表
     * @param ids
     * @return
     */
    public List<CloudWarehousingBillVo> listBillByIds(List<Long> ids) {
        try {
            return warehousingBillClient.listBillByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
