package com.senox.web.component;

import com.senox.car.api.clients.LotteryParkingClient;
import com.senox.car.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.OrderResultVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/5 14:20
 */
@Component
public class LotteryParkingComponent {

    @Autowired
    private LotteryParkingClient lotteryParkingClient;

    /**
     * 获取摇号停车配置
     * @return
     */
    public LotterySettingVo getLotterySetting() {
        try {
            return lotteryParkingClient.getLotterySetting();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 保存摇号配置
     * @param setting
     */
    public void saveLotterySetting(LotterySettingVo setting) {
        try {
            lotteryParkingClient.saveLotterySetting(setting);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 摇号车位列表
     * @return
     */
    public List<LotteryParkingVo> listLotteryParking() {
        try {
            return lotteryParkingClient.listLotteryParking();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取摇号车位信息
     * @param id
     * @return
     */
    public LotteryParkingVo findParkingById(Long id) {
        try {
            return lotteryParkingClient.getLotteryParking(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加摇号车位
     * @param parking
     * @return
     */
    public Long addParking(LotteryParkingVo parking) {
        try {
            return lotteryParkingClient.addParking(parking);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新摇号车位
     * @param parking
     */
    public void updateParking(LotteryParkingVo parking) {
        try {
            lotteryParkingClient.updateParking(parking);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 摇号进场
     * @param parkingRecord
     * @return
     */
    public LotteryParkingVo runLotteryAndOccupied(LotteryParkingRecordVo parkingRecord) {
        try {
            return lotteryParkingClient.runAndOccupied(parkingRecord);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 手工进场
     * @param parkingVo
     * @return
     */
    public LotteryParkingVo fill(LotteryParkingVo parkingVo) {
        try {
            return lotteryParkingClient.fill(parkingVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 出场
     * @param id
     * @return
     */
    public LotteryParkingRecordVo exitParking(Long id) {
        try {
            return lotteryParkingClient.exitParking(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 撤销退场
     * @param id
     * @return
     */
    public LotteryParkingVo revokeExitParking(Long id) {
        try {
            return lotteryParkingClient.revokeExitParking(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 最近摇号车位停车记录
     * @param size
     * @return
     */
    public List<LotteryParkingRecordVo> topLotteryParkingRecord(int size) {
        try {
            return lotteryParkingClient.topLotteryParkingRecord(size);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 摇号车位停车记录列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingRecordVo> listLotteryParkingRecord(LotteryParkingRecordSearchVo searchVo) {
        try {
            return lotteryParkingClient.listLotteryParkingRecord(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 摇号停车记金额统计
     * @param searchVo
     * @return
     */
    public LotteryParkingAmountVo summaryParkingAmount(LotteryParkingRecordSearchVo searchVo) {
        try {
            return lotteryParkingClient.summaryParkingAmount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 最近车位摇号日志
     * @param size
     * @return
     */
    public List<LotteryParkingLogVo> topLotteryParkingLog(int size) {
        try {
            return lotteryParkingClient.topLotteryParkingLog(size);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 车位摇号日志列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingLogVo> listLotteryParkingLog(LotteryParkingLogSearchVo searchVo) {
        try {
            return lotteryParkingClient.listLotteryParkingLog(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 车位摇号日报列表
     * @param searchVo
     * @return
     */
    public LotteryParkingPage<LotteryParkingDayReportVo> listLotteryParkingDayReport(LotteryParkingDayReportSearchVo searchVo) {
        try {
            return lotteryParkingClient.listLotteryParkingDayReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return LotteryParkingPage.emptyPage();
    }

    /**
     * 车位摇号月报列表
     * @param searchVo
     * @return
     */
    public LotteryParkingPage<LotteryParkingMonthReportVo> listLotteryParkingMonthReport(LotteryParkingMonthReportSearchVo searchVo) {
        try {
            return lotteryParkingClient.listParkingMonthReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return LotteryParkingPage.emptyPage();
    }

    /**
     * 获取车辆停车支付结果
     * @param carNo
     * @return
     */
    public LotteryParkingPaidVo getCarParkingPaid(String carNo) {
        try {
            return lotteryParkingClient.getParkingPaid(carNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 进场支付
     * @param parkingVo
     * @return
     */
    public OrderResultVo entryPay(LotteryParkingVo parkingVo) {
        try {
            return lotteryParkingClient.entryPay(parkingVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 车牌号获取停车信息
     *
     * @param carNo 车牌号
     * @return 停车信息
     */
    public LotteryParkingInfoVo infoByCarNo(String carNo) {
        try {
            return lotteryParkingClient.infoByCarNo(carNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 同步车位数量
     */
    public void asyncStock() {
        try {
            lotteryParkingClient.asyncStock();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 实时获取当天进出场数量
     * @return
     */
    public LotteryParkingDayCount parkingDayCount() {
        try {
            return lotteryParkingClient.parkingDayCount();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 查询进出场次数和设备记录次数
     * @param searchVo
     * @return
     */
    public LotteryParkingRecordCountVo findEntryAndExitCount(LotteryParkingRecordSearchVo searchVo) {
        try {
            return lotteryParkingClient.findEntryAndExitCount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 临期车位列表
     * @return
     */
    public List<LotteryParkingVo> adventParkingList() {
        try {
            return lotteryParkingClient.adventParkingList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 最近时间离场记录列表
     * @param minutes
     * @return
     */
    public List<LotteryParkingRecordVo> latestExitRecord(Long minutes) {
        try {
            return lotteryParkingClient.latestExitRecord(minutes);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 发送离场缴费账单
     * @param id
     */
    public void sendExitBill(Long id) {
        try {
            lotteryParkingClient.sendExitBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 监管临期列表
     * @return
     */
    public List<LotteryParkingVo> regulatoryAdventParkingList() {
        try {
            return lotteryParkingClient.regulatoryAdventParkingList();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 删除设备的离场时间
     * @param id
     */
    public void deleteDeviceExitTime(Long id) {
        try {
            lotteryParkingClient.deleteDeviceExitTime(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新离场支付状态为已支付
     * @param exitOrderId
     */
    public void updateExitPaid(Long exitOrderId) {
        try {
            lotteryParkingClient.updateExitPaid(exitOrderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
