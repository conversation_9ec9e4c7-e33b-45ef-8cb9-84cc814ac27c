package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.AuthCredentialsClient;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-4
 */
@RequiredArgsConstructor
@Component
public class AuthCredentialsComponent {
    private final AuthCredentialsClient authCredentialsClient;

    /**
     * 添加凭证
     *
     * @param userId 用户id
     */
    public void add(Long userId) {
        try {
            authCredentialsClient.add(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<AuthCredentialsVo> list(AuthCredentialsSearchVo searchVo) {
        try {
            return authCredentialsClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public PageResult<AuthCredentialsVo> listPage(AuthCredentialsSearchVo searchVo) {
        try {
            return authCredentialsClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
