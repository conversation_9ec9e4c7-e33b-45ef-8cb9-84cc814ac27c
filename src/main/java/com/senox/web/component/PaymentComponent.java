package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.pm.api.clients.PaymentClient;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.*;
import feign.FeignException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17 9:06
 */
@AllArgsConstructor
@Component
public class PaymentComponent {

    private final PaymentClient paymentClient;


    /**
     * 获取莞付平台客户端密钥
     *
     * @return
     */
    public BodSecretKeyVo getBodSecretKey() {
        try {
            return paymentClient.getBodSecretKey();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 新增/修改日期支付方式
     *
     * @param payWaySettingVoList 日期支付方式列表
     */
    public void payWaySettingSave(List<PayWaySettingVo> payWaySettingVoList) {
        try {
            paymentClient.payWaySettingSave(payWaySettingVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除支付方式设置
     *
     * @param payWayDeleteVoList 删除参数列表
     */
    public void payWaySettingDelete(List<PayWayDeleteVo> payWayDeleteVoList) {
        try {
            paymentClient.payWaySettingDelete(payWayDeleteVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取日期支付方式设置列表
     *
     * @param search 查询参数
     * @return List<PayWaySettingVo>
     */
    public List<PayWaySettingVo> payWaySettingList(PayWaySettingSearchVo search) {
        try {
            return paymentClient.payWaySettingList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取支付方式
     *
     * @param orderValue 订单类型value
     * @param date       日期
     * @return PayWay
     */
    public PayWay payWaySettingGet(Integer orderValue, LocalDate date) {
        try {
            return paymentClient.payWaySettingGet(orderValue, date);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取莞银通token
     *
     * @param appId appId
     * @return 返回token
     */
    public BodTokenVo getBodToken(String appId) {
        try {
            return paymentClient.getBodToken(appId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

}
