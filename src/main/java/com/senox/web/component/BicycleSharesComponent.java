package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleSharesClient;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-9-20
 */
@RequiredArgsConstructor
@Component
public class BicycleSharesComponent {
    private final BicycleSharesClient sharesClient;

    /**
     * 添加佣金
     *
     * @param sharesVo 佣金
     */
    public void addShares(BicycleSharesVo sharesVo) {
        try {
            sharesClient.add(sharesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除佣金
     *
     * @param sharesId 佣金id
     */
    public void deleteShares(Long sharesId) {
        try {
            sharesClient.delete(sharesId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改佣金
     */
    public void updateShares(BicycleSharesVo sharesVo) {
        try {
            sharesClient.update(sharesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 佣金列表
     *
     * @param searchVo 查询参数
     */
    public PageResult<BicycleSharesVo> listShares(BicycleSharesSearchVo searchVo) {
        try {
            return sharesClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
