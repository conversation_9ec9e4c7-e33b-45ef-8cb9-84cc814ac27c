package com.senox.web.component;

import com.senox.cold.api.clients.RefrigerationBillClient;
import com.senox.cold.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16 10:10
 */
@RequiredArgsConstructor
@Component
public class RefrigerationBillComponent {

    private final RefrigerationBillClient billClient;

    /**
     * 添加日账单
     * @param dto
     * @return
     */
    public Long addDayBill(RefrigerationDayBillDto dto) {
        try {
            return billClient.addDayBill(dto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新日账单
     * @param dto
     */
    public void updateDayBill(RefrigerationDayBillDto dto) {
        try {
            billClient.updateDayBill(dto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量保存日账单
     * @param list
     * @return
     */
    public String saveDayBillBatch(List<RefrigerationDayBillDto> list) {
        try {
            return billClient.saveDayBillBatch(list);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除日账单
     * @param ids
     */
    public void deleteDayBill(List<Long> ids) {
        try {
            billClient.deleteDayBill(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取日账单
     * @param id
     * @return
     */
    public RefrigerationDayBillVo findDayBillById(Long id) {
        try {
            return billClient.findDayBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 合计日账单
     * @param search
     * @return
     */
    public RefrigerationDayBillVo sumDayBill(RefrigerationDayBillSearchVo search) {
        return billClient.sumDayBill(search);
    }

    /**
     * 日账单列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationDayBillVo> listDayBill(RefrigerationDayBillSearchVo search) {
        try {
            return billClient.listDayBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 生成月账单
     * @param billMonth
     */
    public void generateMonthBill(RefrigerationBillMonthVo billMonth) {
        try {
            billClient.generateMonthBill(billMonth);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新月账单备注
     * @param billRemark
     */
    public void updateMonthBillRemark(RefrigerationBillRemarkVo billRemark) {
        try {
            billClient.updateMonthBillRemark(billRemark);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 免除滞纳金
     * @param penaltyIgnore
     */
    public void ignoreMonthBillPenalty(BillPenaltyIgnoreVo penaltyIgnore) {
        try {
            billClient.ignoreMonthBillPenalty(penaltyIgnore);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 坏账核算
     * @param badDebtList
     */
    public void updateMonthBillBadDebt(List<BillBadDebtVo> badDebtList) {
        try {
            billClient.updateMonthBillBadDebt(badDebtList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新账单状态
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        try {
            billClient.updateBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 保存票据号
     * @param tollSerial
     */
    public void saveBillSerial(TollSerialVo tollSerial) {
        try {
            billClient.saveMonthBillSerial(tollSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 下发月账单
     * @param send
     */
    public void sendMonthBill(RefrigerationBillSendVo send) {
        try {
            billClient.sendMonthBill(send);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除月账单
     * @param id
     */
    public void deleteMonthBill(Long id) {
        try {
            billClient.deleteMonthBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取月账单
     * @param id
     * @return
     */
    public RefrigerationMonthBillVo findMonthBillById(Long id) {
        try {
            return billClient.findMonthBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取月账单列表
     * @param ids
     * @return
     */
    public List<RefrigerationMonthBillVo> listMonthBillByIds(List<Long> ids) {
        try {
            return billClient.listMonthBillByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 月账单明细
     * @param id
     * @return
     */
    public List<RefrigerationDayBillVo> listMonthDayBill(Long id) {
        try {
            return billClient.listMonthDayBill(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 合计月账单
     * @param search
     * @return
     */
    public RefrigerationMonthBillVo sumMonthBill(RefrigerationMonthBillSearchVo search) {
        try {
            return billClient.sumMonthBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 月账单列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationMonthBillVo> listMonthBill(RefrigerationMonthBillSearchVo search) {
        try {
            return billClient.listMonthBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 冷藏发票账单
     * @param search
     * @return
     */
    public PageStatisticsResult<RefrigerationMonthBillVo ,RefrigerationMonthBillVo> listReceiptBill(RefrigerationMonthBillSearchVo search) {
        try {
            return billClient.listReceiptBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }
}
