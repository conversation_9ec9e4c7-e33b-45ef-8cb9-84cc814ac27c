package com.senox.web.component;

import com.senox.cold.api.clients.RefrigerationReceiptClient;
import com.senox.cold.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/2/20 13:36
 */
@RequiredArgsConstructor
@Component
public class RefrigerationReceiptComponent {

    private final RefrigerationReceiptClient receiptClient;

    /**
     * 冷藏发票申请
     * @param managerVo
     */
    public void apply(RefrigerationReceiptManagerVo managerVo) {
        try {
            receiptClient.apply(managerVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 冷藏发票状态重置
     * @param id
     */
    public void refreshRefrigerationReceipt(Long id) {
        try {
            receiptClient.refreshRefrigerationReceipt(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 冷藏发票分页
     * @param searchVo
     * @return
     */
    public PageResult<RefrigerationReceiptVo> page(RefrigerationReceiptSearchVo searchVo) {
        try {
            return receiptClient.page(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 冷藏账单发票信息详细列表
     * @param id
     * @return
     */
    public List<RefrigerationMonthBillReceiptInfoVo> applyBillInfoList(Long id) {
        try {
            return receiptClient.applyBillInfoList(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 冷藏发票列表
     * @param id
     * @param isDetail
     * @return
     */
    public List<ReceiptApplyVo> applyInfoList(Long id, Boolean isDetail) {
        try {
            return receiptClient.applyInfoList(id, isDetail);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据客户编号查询最近开票抬头
     * @param customerSerial
     * @return
     */
    public TaxHeaderVo lastTaxByCustomerSerial(String customerSerial) {
        try {
            return receiptClient.lastTaxByCustomerSerial(customerSerial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
