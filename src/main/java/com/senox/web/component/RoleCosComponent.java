package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.RoleCosClient;
import com.senox.user.vo.CosVo;
import com.senox.user.vo.RoleVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/28 11:10
 */
@Component
public class RoleCosComponent {

    @Lazy
    @Autowired
    private RoleCosClient roleCosClient;

    /**
     * 角色列表
     * @return
     */
    public List<RoleVo> listRole() {
        try {
            return roleCosClient.listRole();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取角色
     * @param id
     * @return
     */
    public RoleVo findRoleById(Long id) {
        try {
            return roleCosClient.getRole(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加角色
     * @param role
     * @return
     */
    public Long addRole(RoleVo role) {
        try {
            return roleCosClient.addRole(role);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新角色
     * @param role
     */
    public void updateRole(RoleVo role) {
        try {
            roleCosClient.updateRole(role);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除角色
     * @param id
     */
    public void deleteRole(Long id) {
        try {
            roleCosClient.deleteRole(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 权限项列表
     * @return
     */
    public List<CosVo> listCos() {
        try {
            return roleCosClient.listCos();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取权限项
     * @param id
     * @return
     */
    public CosVo findCosById(Long id) {
        try {
            return roleCosClient.getCos(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加权限项
     * @param cos
     * @return
     */
    public Long addCos(CosVo cos) {
        try {
            return roleCosClient.addCos(cos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新权限项
     * @param cos
     */
    public void updateCos(CosVo cos) {
        try {
            roleCosClient.updateCos(cos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除权限项
     * @param id
     */
    public void deleteCos(Long id) {
        try {
            roleCosClient.deleteCos(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
