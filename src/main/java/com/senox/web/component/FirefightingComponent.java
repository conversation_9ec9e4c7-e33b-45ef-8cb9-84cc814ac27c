package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.FirefightingClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 15:31
 */
@Component
@RequiredArgsConstructor
public class FirefightingComponent {

    private final FirefightingClient firefightingClient;

    /**
     * 添加公共消防设施
     * @param utility
     * @return
     */
    public Long addUtility(FirefightingUtilityVo utility) {
        try {
            return firefightingClient.addUtility(utility);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新公共消防设施
     * @param utility
     */
    public void updateUtility(FirefightingUtilityVo utility) {
        try {
            firefightingClient.updateUtility(utility);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除公共消防设施
     * @param id
     */
    public void deleteUtility(Long id) {
        try {
            firefightingClient.deleteUtility(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取公共消防设施
     * @param id
     * @return
     */
    public FirefightingUtilityVo findUtilityById(Long id) {
        try {
            return firefightingClient.findUtilityById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 公共消防设施页
     * @param search
     * @return
     */
    public PageResult<FirefightingUtilityVo> listUtilityPage(FirefightingUtilitySearchVo search) {
        try {
            return firefightingClient.listUtilityPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加店铺消防安全档案
     * @param file
     * @return
     */
    public Long addFile(FirefightingFileVo file) {
        try {
            return firefightingClient.addFile(file);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新店铺消防安全档案
     * @param file
     */
    public void updateFile(FirefightingFileVo file) {
        try {
            firefightingClient.updateFile(file);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除店铺消防安全档案
     * @param id
     */
    public void deleteFile(Long id) {
        try {
            firefightingClient.deleteFile(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取店铺消防安全档案详情
     * @param id
     * @return
     */
    public FirefightingFileVo findFileById(Long id) {
        try {
            return firefightingClient.findFileById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 店铺消防安全档案合计
     * @param search
     * @return
     */
    public int countFile(FirefightingFileSearchVo search) {
        try {
            return firefightingClient.countFile(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 店铺消防安全档案列表
     * @param search
     * @return
     */
    public PageResult<FirefightingFileBriefVo> listFileBriefPage(FirefightingFileSearchVo search) {
        try {
            return firefightingClient.listFileBriefPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加店铺消防安全告知单模板
     * @param template
     * @param newVersion
     * @return
     */
    public Long addTemplate(FirefightingTemplateVo template, Boolean newVersion) {
        try {
            return firefightingClient.addTemplate(template, newVersion);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新店铺消防安全告知单模板
     * @param template
     */
    public void updateTemplate(FirefightingTemplateVo template) {
        try {
            firefightingClient.updateTemplate(template);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 启用店铺消防安全告知单模板
     * @param id
     */
    public void enableTemplate(Long id) {
        try {
            firefightingClient.enableTemplate(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 停用店铺消防安全告知单模板
     * @param id
     */
    public void disableTemplate(Long id) {
        try {
            firefightingClient.disableTemplate(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除店铺消防安全告知单模板
     * @param id
     */
    public void deleteTemplate(Long id) {
        try {
            firefightingClient.deleteTemplate(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取店铺消防安全告知单模板
     * @param id
     * @return
     */
    public FirefightingTemplateVo findTemplateById(Long id) {
        try {
            return firefightingClient.findTemplateById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取最新店铺消防安全告知单模板
     * @param search
     * @return
     */
    public FirefightingTemplateVo findLatestTemplateByCode(FireFightingTemplateSearchVo search) {
        try {
            return firefightingClient.findLatestTemplateByCode(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 店铺消防安全告知单模板页
     * @param search
     * @return
     */
    public PageResult<FirefightingTemplateVo> listTemplatePage(FireFightingTemplateSearchVo search) {
        try {
            return firefightingClient.listTemplatePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加消防表单模板
     * @param formTemplate
     * @return
     */
    public Long addFormTemplate(FirefightingFormTemplateVo formTemplate) {
        try {
            return firefightingClient.addFormTemplate(formTemplate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 修改消防表单模板
     * @param formTemplate
     */
    public void updateFormTemplate(FirefightingFormTemplateVo formTemplate) {
        try {
            firefightingClient.updateFormTemplate(formTemplate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除消防表单模板
     * @param id
     */
    public void deleteFormTemplate(Long id) {
        try {
            firefightingClient.deleteFormTemplate(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取消防表单模板
     * @param id
     * @return
     */
    public FirefightingFormTemplateVo findFormTemplateById(Long id) {
        try {
            return firefightingClient.findFormTemplateById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 消防表单模板列表
     * @param form
     * @return
     */
    public List<FirefightingFormTemplateVo> listFormTemplate(String form) {
        try {
            return firefightingClient.listFormTemplate(form);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加店铺消防安全责任告知单
     * @param notice
     * @return
     */
    public Long addNotice(FirefightingNoticeVo notice) {
        try {
            return firefightingClient.addNotice(notice);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新店铺消防安全责任告知单
     * @param notice
     */
    public void updateNotice(FirefightingNoticeVo notice) {
        try {
            firefightingClient.updateNotice(notice);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除店铺消防安全责任告知单
     * @param id
     */
    public void deleteNotice(Long id) {
        try {
            firefightingClient.deleteNotice(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取店铺消防安全责任告知单
     * @param id
     * @return
     */
    public FirefightingNoticeVo findNoticeById(Long id) {
        try {
            return firefightingClient.findNoticeById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 店铺消防安全责任告知单页
     * @param search
     * @return
     */
    public PageResult<FirefightingNoticeVo> listNoticePage(FirefightingNoticeSearchVo search) {
        try {
            return firefightingClient.listNoticePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加公共消防设施巡检记录
     * @param inspection
     * @return
     */
    public Long addUtilityInspection(FirefightingUtilityInspectionVo inspection) {
        try {
            return firefightingClient.addUtilityInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新公共消防设施巡检记录
     * @param inspection
     */
    public void updateUtilityInspection(FirefightingUtilityInspectionVo inspection) {
        try {
            firefightingClient.updateUtilityInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除公共消防设施巡检记录
     * @param id
     */
    public void deleteUtilityInspection(Long id) {
        try {
            firefightingClient.deleteUtilityInspection(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取公共消防设施巡检记录
     * @param id
     * @return
     */
    public FirefightingUtilityInspectionVo findUtilityInspectionById(Long id) {
        try {
            return firefightingClient.findUtilityInspectionById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 公共消防设施巡检记录页
     * @param search
     * @return
     */
    public PageResult<FirefightingUtilityInspectionVo> listUtilityInspectionPage(FirefightingUtilityInspectionSearchVo search) {
        try {
            return firefightingClient.listUtilityInspectionPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加商铺消防巡检记录
     * @param inspection
     * @return
     */
    public Long addStoreInspection(FirefightingStoreInspectionVo inspection) {
        try {
            return firefightingClient.addStoreInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新商铺消防巡检记录
     * @param inspection
     */
    public void updateStoreInspection(FirefightingStoreInspectionVo inspection) {
        try {
            firefightingClient.updateStoreInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除商铺消防巡检记录
     * @param id
     */
    public void deleteStoreInspection(Long id) {
        try {
            firefightingClient.deleteStoreInspection(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取商铺消防巡检记录
     * @param id
     * @return
     */
    public FirefightingStoreInspectionVo findStoreInspectionById(Long id) {
        try {
            return firefightingClient.findStoreInspectionById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 商铺消防巡检记录数统计
     * @param search
     * @return
     */
    public int countStoreInspection(FirefightingStoreInspectionSearchVo search) {
        try {
            return firefightingClient.countStoreInspection(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 商铺消防巡检记录页
     * @param search
     * @return
     */
    public PageResult<FirefightingStoreInspectionBriefVo> listStoreInspectionPage(FirefightingStoreInspectionSearchVo search) {
        try {
            return firefightingClient.listStoreInspectionPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加三小场所、出租屋消防巡检记录
     * @param inspection
     * @return
     */
    public Long addSmallPlacesInspection(FirefightingSmallPlacesInspectionVo inspection) {
        try {
            return firefightingClient.addSmallPlacesInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新三小场所、出租屋消防巡检记录
     * @param inspection
     */
    public void updateSmallPlacesInspection(FirefightingSmallPlacesInspectionVo inspection) {
        try {
            firefightingClient.updateSmallPlacesInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除三小场所、出租屋消防巡检记录
     * @param id
     */
    public void deleteSmallPlacesInspection(Long id) {
        try {
            firefightingClient.deleteSmallPlacesInspection(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取三小场所、出租屋消防巡检记录
     * @param id
     * @return
     */
    public FirefightingSmallPlacesInspectionVo findSmallPlacesInspectionById(Long id) {
        try {
            return firefightingClient.findSmallPlacesInspectionById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 三小场所、出租屋消防巡检记录数统计
     * @param search
     * @return
     */
    public int countSmallPlacesInspection(FirefightingSmallPlacesInspectionSearchVo search) {
        try {
            return firefightingClient.countSmallPlacesInspection(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 三小场所、出租屋消防巡检记录列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspectionPage(FirefightingSmallPlacesInspectionSearchVo search) {
        try {
            return firefightingClient.listSmallPlacesInspectionPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 违规住人消防巡检记录
     * @param inspection
     * @return
     */
    public Long addAccommodateInspection(FirefightingAccommodateInspectionVo inspection) {
        try {
            return firefightingClient.addAccommodateInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新违规住人消防巡检记录
     * @param inspection
     */
    public void updateAccommodateInspection(FirefightingAccommodateInspectionVo inspection) {
        try {
            firefightingClient.updateAccommodateInspection(inspection);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除违规住人消防巡检记录
     * @param id
     */
    public void deleteAccommodateInspection(Long id) {
        try {
            firefightingClient.deleteAccommodateInspection(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取违规住人消防巡检记录
     * @param id
     * @return
     */
    public FirefightingAccommodateInspectionVo findAccommodateInspectionById(Long id) {
        try {
            return firefightingClient.findAccommodateInspectionById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 违规住人消防巡检记录数统计
     * @param search
     * @return
     */
    public int countAccommodateInspection(FirefightingAccommodateInspectionSearchVo search) {
        try {
            return firefightingClient.countAccommodateInspection(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 违规住人消防巡检记录列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingAccommodateInspectionBriefVo> listAccommodateInspectionPage(FirefightingAccommodateInspectionSearchVo search) {
        try {
            return firefightingClient.listAccommodateInspectionPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加巡检任务
     * @param task
     * @return
     */
    public Long addInspectTask(FirefightingInspectTaskVo task) {
        try {
            return firefightingClient.addInspectTask(task);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新巡检任务
     * @param task
     */
    public void updateInspectTask(FirefightingInspectTaskVo task) {
        try {
            firefightingClient.updateInspectTask(task);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取巡检任务详情
     * @param id
     * @return
     */
    public FirefightingInspectTaskVo findInspectTaskById(Long id) {
        try {
            return firefightingClient.findTaskById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 巡检任务列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingInspectTaskVo> listInspectTaskPage(FirefightingInspectTaskSearchVo search) {
        try {
            return firefightingClient.listTaskPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 删除巡检子任务
     * @param drop
     */
    public void deleteInspectTaskItem(FirefightingTaskItemDropVo drop) {
        try {
            firefightingClient.deleteTaskItem(drop);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 巡检子任务页
     * @param search
     * @return
     */
    public PageResult<FirefightingInspectPropertyTaskVo> listTaskItemPage(FirefightingInspectTaskItemSearchVo search) {
        try {
            return firefightingClient.listTaskItemPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
