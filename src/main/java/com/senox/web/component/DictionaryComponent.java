package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.DictionaryClient;
import com.senox.user.vo.AreaVo;
import com.senox.user.vo.ProfessionVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/14 16:44
 */
@Component
public class DictionaryComponent {

    @Lazy
    @Autowired
    private DictionaryClient dictionaryClient;

    /**
     * 省/直辖市列表
     * @return
     */
    public List<AreaVo> listProvince() {
        try {
            return dictionaryClient.listProvinces();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 市列表
     * @param parentId
     * @return
     */
    public List<AreaVo> listCity(Long parentId) {
        try {
            return dictionaryClient.listCities(parentId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取地区信息
     * @param id
     * @return
     */
    public AreaVo findAreaById(Long id) {
        try {
            return dictionaryClient.getArea(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加地区
     * @param area
     * @return
     */
    public Long addArea(AreaVo area) {
        try {
            return dictionaryClient.addArea(area);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新地区
     * @param area
     */
    public void updateArea(AreaVo area) {
        try {
            dictionaryClient.updateArea(area);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 行业列表
     * @return
     */
    public List<ProfessionVo> listProfession() {
        try {
            return dictionaryClient.listProfessions();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取行业信息
     * @param id
     * @return
     */
    public ProfessionVo findProfessionById(Long id) {
        try {
            return dictionaryClient.getProfession(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加行业
     * @param profession
     * @return
     */
    public Long addProfession(ProfessionVo profession) {
        try {
            return dictionaryClient.addProfession(profession);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新行业
     * @param profession
     */
    public void updateProfession(ProfessionVo profession) {
        try {
            dictionaryClient.updateProfession(profession);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
