package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.Result;
import com.senox.wechat.api.clients.WechatManageClient;
import com.senox.wechat.api.clients.WechatMerchantClient;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/24 10:14
 */
@RequiredArgsConstructor
@Component
public class WechatComponent {

    private final WechatManageClient wechatManageClient;
    private final WechatMerchantClient wechatMerchantClient;




    /**
     * 通知物业月度账单
     * @param year
     * @param month
     */
    public void notifyRealtyMonthlyBill(Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            return;
        }
        try {
            wechatManageClient.notifyRealtyMonthlyBill(year, month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 通知冷藏月度账单
     * @param year
     * @param month
     */
    public void notifyRefrigerationBill(Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            return;
        }
        try {
            wechatManageClient.notifyRefrigerationBill(year, month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 通知云仓月度账单
     * @param year
     * @param month
     */
    public void notifyCloudWarehousingBill(Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            return;
        }
        try {
            wechatManageClient.notifyCloudWarehousingBill(year, month);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * openid获取商户id列表
     *
     * @param openid openid
     * @return 返回商户id列表
     */
    public List<Long> listMerchantIdByOpenid(String openid){
        try {
            Result<List<Long>> result = wechatMerchantClient.listMerchantIdByOpenid(openid);
            return result.getData();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

}
