package com.senox.web.component;


import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.api.clients.DeviceClient;
import com.senox.dm.vo.*;
import com.senox.pm.api.clients.PaymentClient;
import com.senox.pm.vo.PaymentDeviceSearchVo;
import com.senox.pm.vo.PaymentDeviceVo;
import feign.FeignException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021-12-24
 */
@AllArgsConstructor
@Component
public class DeviceComponent {

    private final DeviceClient deviceClient;
    private final PaymentClient paymentClient;


    /**
     * 添加设备
     * @param device
     */
    public Long addAccessDevice(AccessDeviceVo device) {
        try {
            return deviceClient.addDevice(device);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新设备
     * @param device
     */
    public void updateAccessDevice(AccessDeviceVo device) {
        try {
            deviceClient.updateDevice(device);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找防疫设备
     * @param id
     * @return
     */
    public AccessDeviceVo findById(Long id) {
        try {
            return deviceClient.getDevice(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 门禁设备列表
     *
     * @param search
     * @return
     */
    public PageResult<AccessDeviceVo> listAccessDevice(AccessDeviceSearchVo search) {
        try {
            return deviceClient.listDevice(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 门禁设备列表
     *
     * @param switcher
     */
    public void switchAccessDeviceDataReport(Covid19DeviceReportSwitcherVo switcher) {
        try {
            deviceClient.switchDeviceDataReport(switcher);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 防疫门禁设备数据上报
     * @param data
     * @return
     */
    public Covid19AccessResponse reportCovid19AccessData(Covid19AccessDeviceDataVo data) {
        try {
            return deviceClient.reportCovid19Data(data);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Covid19AccessResponse.fail();
    }

    /**
     * 添加支付终端
     * @param device
     * @return
     */
    public Long addPaymentDevice(PaymentDeviceVo device) {
        try {
            return paymentClient.addPaymentDevice(device);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新支付终端
     * @param device
     */
    public void updatePaymentDevice(PaymentDeviceVo device) {
        try {
            paymentClient.updatePaymentDevice(device);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除支付终端
     * @param id
     */
    public void delPaymentDevice(Long id) {
        try {
            paymentClient.delPaymentDevice(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找支付终端
     * @param id
     * @return
     */
    public PaymentDeviceVo findPaymentDeviceById(Long id) {
        try {
            return paymentClient.findPaymentDeviceById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 支付终端列表
     * @param search
     * @return
     */
    public List<PaymentDeviceVo> listPaymentDevice(PaymentDeviceSearchVo search) {
        try {
            return paymentClient.listPaymentDevice(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
