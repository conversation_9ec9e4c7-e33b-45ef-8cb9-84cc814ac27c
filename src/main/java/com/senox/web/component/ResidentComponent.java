package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.ResidentClient;
import com.senox.user.vo.*;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 15:12
 */
@Component
public class ResidentComponent {

    @Autowired
    private ResidentClient residentClient;

    /**
     * 添加住户
     *
     * @param residentVo
     * @return
     */
    public String addResident(ResidentVo residentVo) {
        try {
            return residentClient.addResident(residentVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 只修改住户，不修改人脸
     *
     * @param residentVo
     */
    public void updateResident(ResidentVo residentVo) {
        try {
            residentClient.updateResident(residentVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 只修改人脸
     *
     * @param residentFaceUrlVo
     */
    public void updateFaceUrl(ResidentFaceUrlVo residentFaceUrlVo) {
        try {
            residentClient.updateFaceUrl(residentFaceUrlVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除住户
     *
     * @param id
     */
    public void deleteResident(Long id) {
        try {
            residentClient.deleteResident(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取住户
     *
     * @param id
     * @return
     */
    public ResidentVo findResidentById(Long id) {
        try {
            return residentClient.findResidentById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据住户编号查找住户
     *
     * @param residentNo
     * @return
     */
    public ResidentVo findResidentByResidentNo(String residentNo) {
        try {
            return residentClient.findResidentByResidentNo(residentNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据身份证查找住户
     *
     * @param idNum
     * @return
     */
    public ResidentVo findResidentByIdNum(String idNum) {
        try {
            return residentClient.findResidentByIdNum(idNum);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 住户列表
     *
     * @param search
     * @return
     */
    public PageResult<ResidentVo> residentList(ResidentSearchVo search) {
        try {
            return residentClient.residentList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加住户权限
     *
     * @param residentAccessVo
     */
    public void addResidentAccess(ResidentAccessVo residentAccessVo) {
        try {
            residentClient.addResidentAccess(residentAccessVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除住户权限
     *
     * @param ids
     */
    public void deleteResidentAccess(List<Long> ids) {
        try {
            residentClient.deleteResidentAccess(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据住户编号查询住户权限
     *
     * @param residentNo
     * @return
     */
    public ResidentAccessResultVo residentAccessResultByNo(String residentNo) {
        try {
            return residentClient.residentAccessResultByNo(residentNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 门禁权限同步
     * @param deviceId
     * @param targetDeviceId
     */
    public void residentAccessSync(Long deviceId, Long targetDeviceId) {
        try {
            residentClient.residentAccessSync(deviceId, targetDeviceId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
