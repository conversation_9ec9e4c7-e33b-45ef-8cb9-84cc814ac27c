package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleDeliveryOrderClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/21 13:54
 */
@Component
@RequiredArgsConstructor
public class BicycleDeliveryOrderComponent {

    private final BicycleDeliveryOrderClient bicycleDeliveryOrderClient;

    /**
     * 添加三轮车配送单
     * @param orderVo
     * @return
     */
    public Long addBicycleDeliveryOrder(BicycleDeliveryOrderVo orderVo) {
        try {
            return bicycleDeliveryOrderClient.addBicycleDeliveryOrder(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取配送单
     * @param id
     * @return
     */
    public BicycleDeliveryOrderVo findDeliveryOrderById(Long id) {
        try {
            return bicycleDeliveryOrderClient.findDeliveryOrderById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取配送详细单
     * @param id
     * @return
     */
    public BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(Long id) {
        try {
            return bicycleDeliveryOrderClient.findDeliveryOrderDetailById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据订单流水号获取配送详细单
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(String orderSerialNo) {
        try {
            return bicycleDeliveryOrderClient.findDeliveryOrderDetailByOrderSerialNo(orderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 指定骑手配送三轮车配送单
     * @param orderVo
     */
    public void appointBicycleDeliveryOrderReider(BicycleDeliveryOrderVo orderVo) {
        try {
            bicycleDeliveryOrderClient.appointBicycleDeliveryOrderReider(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消骑手配送
     * @param id
     */
    public void cancelRiderDeliveryOrderById(Long id) {
        try {
            bicycleDeliveryOrderClient.cancelRiderDeliveryOrderById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查询骑手情况
     * @param searchVo
     * @return
     */
    public BicycleDayCountRiderVo riderStatistics(BicycleStatisticsSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.riderStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<BicycleOrderCountVo> statistics(BicycleStatisticsSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.statistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 合并配送单
     * @param orderSerialNoList
     * @param deliveryOrderSerialNo
     */
    public void merged(List<String> orderSerialNoList, String deliveryOrderSerialNo) {
        try {
            bicycleDeliveryOrderClient.merged(orderSerialNoList, deliveryOrderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消合并
     * @param orderSerialNoList
     */
    public void cancelMerged(List<String> orderSerialNoList) {
        try {
            bicycleDeliveryOrderClient.cancelMerged(orderSerialNoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 今日骑手完成单量统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderCountList(BicycleRiderCountSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.riderCountList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 骑手当日统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderCount(BicycleRiderCountSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.sumRiderCount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 骑手配送历史统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderHistoryCountList(BicycleRiderCountSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.riderHistoryCountList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 骑手历史配送统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderHistoryCount(BicycleRiderCountSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.sumRiderHistoryCount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加费用
     * @param chargeVo
     */
    public void addBicycleOrderCharge(BicycleOrderChargeVo chargeVo) {
        try {
            bicycleDeliveryOrderClient.addBicycleOrderCharge(chargeVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新货物明细
     * @param goodsDetailVos
     */
    public void updateBicycleOrderDetail(List<BicycleOrderGoodsDetailVo> goodsDetailVos) {
        try {
            bicycleDeliveryOrderClient.updateBicycleOrderDetail(goodsDetailVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 配送单提货信息分页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(BicycleDeliveryInfoSearchVo searchVo) {
        try {
            return bicycleDeliveryOrderClient.deliveryInfoPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     *
     * 根据订单编号查询配送订单详细信息
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(String orderSerialNo) {
        try {
            return bicycleDeliveryOrderClient.deliveryDetailInfoByOrderSerialNo(orderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
