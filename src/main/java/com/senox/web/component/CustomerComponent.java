package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.CustomerClient;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/15 11:42
 */
@Component
public class CustomerComponent {

    @Lazy
    @Autowired
    private CustomerClient customerClient;

    /**
     * 客户列表
     * @param search
     * @return
     */
    public PageResult<CustomerVo> listCustomerPage(CustomerSearchVo search) {
        try {
            return customerClient.listCustomerPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 客户列表（无分页）
     * @param search
     * @return
     */
    public List<CustomerVo> listCustomer(CustomerSearchVo search) {
        try {
            return customerClient.listCustomer(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取客户信息
     * @param id
     * @return
     */
    public CustomerVo findById(Long id) {
        try {
            return customerClient.getCustomer(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据证件号获取客户信息
     * @param idcard
     * @return
     */
    public CustomerVo findByIdcard(String idcard) {
        try {
            return customerClient.getCustomer(idcard);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加客户
     * @param customer
     * @return
     */
    public Long addCustomer(CustomerVo customer) {
        try {
            return customerClient.addCustomer(customer);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新客户
     * @param customer
     */
    public void updateCustomer(CustomerVo customer) {
        try {
            customerClient.updateCustomer(customer);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除客户
     * @param id
     */
    public void deleteCustomer(Long id) {
        try {
            customerClient.deleteCustomer(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
