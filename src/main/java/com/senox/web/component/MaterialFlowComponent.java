package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.wms.api.clients.MaterialFlowClient;
import com.senox.wms.constant.MaterialRequisitionStatus;
import com.senox.wms.vo.MaterialFlowInfoVo;
import com.senox.wms.vo.requisition.FlowRequest;
import com.senox.wms.vo.requisition.MaterialRequisitionSubmitVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-09-13
 **/
@RequiredArgsConstructor
@Component
public class MaterialFlowComponent {

    private final MaterialFlowClient materialFlowClient;

    /**
     * 提交流程
     *
     * @param submit 提交参数
     * @return 返回状态
     */
    public MaterialRequisitionStatus submitFlow(MaterialRequisitionSubmitVo submit) {
        try {
            return materialFlowClient.submitFlow(submit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取流程
     *
     * @param flowRequest 流程请求参数
     * @return 返回流程信息
     */
    public MaterialFlowInfoVo getFlow(FlowRequest flowRequest) {
        try {
            return materialFlowClient.getFlow(flowRequest);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
