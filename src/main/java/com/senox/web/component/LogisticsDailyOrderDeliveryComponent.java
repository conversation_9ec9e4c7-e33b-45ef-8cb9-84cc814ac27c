package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.clients.LogisticsDailyOrderDeliveryClient;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@RequiredArgsConstructor
@Component
public class LogisticsDailyOrderDeliveryComponent {
    private final LogisticsDailyOrderDeliveryClient dailyOrderDeliveryClient;

    /**
     * 批量添加
     *
     * @param dailyOrderDeliveryVos 物流日订单配送列表
     */
    public void addBatch(List<LogisticsDailyOrderDeliveryVo> dailyOrderDeliveryVos) {
        try {
            dailyOrderDeliveryClient.addBatch(dailyOrderDeliveryVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找配送
     *
     * @param id id
     * @return 返回查找到的配送
     */
    public LogisticsDailyOrderDeliveryVo findById(Long id) {
        try {
            return dailyOrderDeliveryClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改
     *
     * @param dailyOrderDeliveryVo 物流日订单配送
     */
    public void update(LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo){
        try {
            dailyOrderDeliveryClient.update(dailyOrderDeliveryVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除
     *
     * @param id 物流日订单配送id
     */
    public void deleteById(Long id) {
        try {
            dailyOrderDeliveryClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 列表查询
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    public List<LogisticsDailyOrderDeliveryVo> list(LogisticsDailyOrderDeliverySearchVo searchVo) {
        try {
            return dailyOrderDeliveryClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 列表分页查询
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<LogisticsDailyOrderDeliveryVo, LogisticsDailyOrderDeliveryTotalAmountVo> listPage(LogisticsDailyOrderDeliverySearchVo searchVo) {
        try {
            return dailyOrderDeliveryClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return new PageStatisticsResult<>();
    }

}
