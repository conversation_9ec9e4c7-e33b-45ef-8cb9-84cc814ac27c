package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.VoteClient;
import com.senox.user.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/17 9:56
 */
@Component
@RequiredArgsConstructor
public class VoteComponent {

    private final VoteClient voteClient;

    /**
     * 添加投票分类
     * @param categoryVo
     */
    public void saveVoteCategory(VoteCategoryVo categoryVo) {
        try {
            voteClient.saveVoteCategory(categoryVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取投票分类
     * @param id
     * @return
     */
    public VoteCategoryVo findCategoryById(Long id) {
        try {
            return voteClient.findCategoryById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除投票类别
     * @param id
     */
    public void deleteVoteCategoryById(Long id) {
        try {
            voteClient.deleteVoteCategoryById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 投票类别分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteCategoryVo> pageCategoryResult(VoteCategorySearchVo searchVo) {
        try {
            return voteClient.pageCategoryResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增投票资源
     * @param resourcesVo
     */
    public void saveVoteResources(VoteResourcesVo resourcesVo) {
        try {
            voteClient.saveVoteResources(resourcesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取投票资源
     * @param id
     * @return
     */
    public VoteResourcesVo findResourcesById(Long id) {
        try {
            return voteClient.findResourcesById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除投票资源
     * @param id
     */
    public void deleteVoteResourcesById(Long id) {
        try {
            voteClient.deleteVoteResourcesById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 投票资源分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteResourcesVo> pageResourcesResult(VoteResourcesSearchVo searchVo) {
        try {
            return voteClient.pageResourcesResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 新增投票记录
     * @param recordsVo
     */
    public void saveVoteRecords(VoteRecordsVo recordsVo) {
        try {
            voteClient.saveVoteRecords(recordsVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
