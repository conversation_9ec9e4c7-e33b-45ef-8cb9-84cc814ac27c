package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleOrderClient;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/19 11:27
 */
@Component
@RequiredArgsConstructor
public class BicycleOrderComponent {

    private final BicycleOrderClient bicycleOrderClient;

    /**
     * 添加三轮车配送订单
     * @param orderVo
     * @return
     */
    public Long addBicycleOrder(BicycleOrderVo orderVo) {
        try {
            return bicycleOrderClient.addBicycleOrder(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id获取配送订单
     * @param id
     * @param containStatus
     * @return
     */
    public BicycleOrderVo findOrderVoById(Long id, Boolean containStatus) {
        try {
            return bicycleOrderClient.findOrderVoById(id, containStatus);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 配送订单合计
     * @param searchVo
     * @return
     */
    public BicycleOrderVo sumOrder(BicycleOrderSearchVo searchVo) {
        try {
            return bicycleOrderClient.sumOrder(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 配送订单分页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderVo> page(BicycleOrderSearchVo searchVo) {
        try {
            return bicycleOrderClient.page(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 三轮车配送订单合计
     * @param search
     * @return
     */
    public BicycleOrderVo sumDeliveryOrder(BicycleOrderSearchVo search) {
        try {
            return bicycleOrderClient.sumDeliveryOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return null;
    }

    /**
     * 三轮车配送订单页
     * @param search
     * @return
     */
    public PageResult<BicycleOrderVo> listDeliveryPage(BicycleOrderSearchVo search) {
        try {
            return bicycleOrderClient.listDeliveryPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 导出订单列表
     * @param searchVo
     * @return
     */
    public List<BicycleOrderVo> exportOrderList(BicycleOrderSearchVo searchVo) {
        try {
            return bicycleOrderClient.exportOrderList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 计算配送费用
     * @param orderVo
     * @param useChargesId
     * @return
     */
    public BicycleOrderCalculateEstimateVo calculateCharges(BicycleOrderVo orderVo, Boolean useChargesId) {
        try {
            return bicycleOrderClient.calculateEstimateCharges(orderVo, useChargesId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    public BicycleOperateAnalysisVo operateAnalysisStatistics(BicycleStatisticsSearchVo searchVo) {
        try {
            return bicycleOrderClient.operateAnalysisStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 配送地点使用统计
     * @param isStart
     * @return
     */
    public List<BicyclePointCountVo> listPointCount(Boolean isStart) {
        try {
            return bicycleOrderClient.listPointCount(isStart);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    public BicycleCountRankingVo rankingStatistics(BicycleStatisticsSearchVo searchVo) {
        try {
            return bicycleOrderClient.rankingStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 未处理的订单数量
     * @return
     */
    public Integer undoOrderCount() {
        try {
            return bicycleOrderClient.undoOrderCount();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 历史运营分析记录列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        try {
            return bicycleOrderClient.listOperateAnalysis(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 历史运营分析记录合计
     * @param searchVo
     * @return
     */
    public BicycleOperateAnalysisVo sumOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        try {
            return bicycleOrderClient.sumOperateAnalysis(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 今日下单客户统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleCustomerCountVo> customerCountList(BicycleCustomerCountSearchVo searchVo) {
        try {
            return bicycleOrderClient.customerCountList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 客户下单统计合计
     * @param searchVo
     * @return
     */
    public BicycleCustomerCountVo sumCustomerCount(BicycleCustomerCountSearchVo searchVo) {
        try {
            return bicycleOrderClient.sumCustomerCount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 查询订单统计数量情况
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCount(BicycleOrderCountSearchVo searchVo) {
        try {
            return bicycleOrderClient.orderCount(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除未生成结算单的订单
     * @param id
     */
    public void cancelBicycleOrderById(Long id) {
        try {
            bicycleOrderClient.cancelBicycleOrderById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量删除未生成结算单的订单
     * @param ids
     */
    public void cancelBicycleOrderByIds(List<Long> ids) {
        try {
            bicycleOrderClient.cancelBicycleOrderByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消订单
     * @param cancelVo
     */
    public void cancelBicycleOrder(BicycleOrderCancelVo cancelVo) {
        try {
            bicycleOrderClient.cancelBicycleOrder(cancelVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id删除未配送的订单
     * @param id
     */
    public void deleteUndeliveredBicycleOrder(Long id) {
        try {
            bicycleOrderClient.deleteUndeliveredBicycleOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 订单导入
     *
     * @param orderImportDtoList 订单导入列表
     */
    public void orderImport(List<BicycleOrderImportDto> orderImportDtoList) {
        try {
            bicycleOrderClient.orderImport(orderImportDtoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 客户每月下单统计
     * @param searchVo
     * @return
     */
    public List<BicycleCustomerMonthInfoVo> customerMonthInfoList(BicycleCustomerMonthInfoSearchVo searchVo) {
        try {
            return bicycleOrderClient.customerMonthInfoList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 三轮车配送订单分页V2
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryOrderV2Vo> pageOrderV2(BicycleOrderV2SearchVo searchVo) {
        try {
            return bicycleOrderClient.pageOrderV2(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据订单id查询货物信息
     * @param orderId
     * @return
     */
    public List<BicycleOrderGoodsDetailVo> goodsDetailByOrderId(Long orderId) {
        try {
            return bicycleOrderClient.goodsDetailByOrderId(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询订单统计数量情况V2
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCountV2(BicycleOrderCountSearchVo searchVo) {
        try {
            return bicycleOrderClient.orderCountV2(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 三轮车配送订单合计V2
     * @param searchVo
     * @return
     */
    public BicycleOrderV2Vo sumOrderV2(BicycleOrderV2SearchVo searchVo) {
        try {
            return bicycleOrderClient.sumOrderV2(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
