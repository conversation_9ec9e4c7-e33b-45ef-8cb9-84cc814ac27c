package com.senox.web.component;

import com.senox.car.api.clients.ParkingReceiptClient;
import com.senox.car.dto.ParkingReceiptManagerDto;
import com.senox.car.vo.ReceiptOrderSearchVo;
import com.senox.car.vo.ReceiptOrderVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-8-31
 */
@RequiredArgsConstructor
@Component
public class ParkingReceiptComponent {
    private final ParkingReceiptClient parkingReceiptClient;

    /**
     * 停车发票申请
     *
     * @param receiptManagerDto 发票管理
     * @return 单据编号
     */
    public String receiptApply(ParkingReceiptManagerDto receiptManagerDto) {
        try {
            return parkingReceiptClient.receiptApply(receiptManagerDto);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return StringUtils.EMPTY;
    }


    /**
     * 更新发票申请
     *
     * @param order
     */
    public void updateReceiptOrder(ReceiptOrderVo order) {
        try {
            parkingReceiptClient.updateReceiptOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 重置发票申请结果
     *
     * @param id
     */
    public void resetReceiptOrder(Long id) {
        try {
            parkingReceiptClient.resetReceiptOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找开票申请
     *
     * @param id
     * @return
     */
    public ReceiptOrderVo findOrderById(Long id) {
        try {
            return parkingReceiptClient.getReceiptOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 开票申请列表
     *
     * @param search
     * @return
     */
    public PageResult<ReceiptOrderVo> listOrder(ReceiptOrderSearchVo search) {
        try {
            return parkingReceiptClient.listReceiptOrder(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }
}
