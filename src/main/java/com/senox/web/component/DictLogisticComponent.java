package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.DictLogisticClient;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-12-20
 */
@RequiredArgsConstructor
@Component
public class DictLogisticComponent {
    private final DictLogisticClient dictLogisticClient;

    /**
     * 添加物流字典
     * @param category 类型
     * @param dictLogisticVo 物流字典
     */
    public void addDictLogistic(DictLogisticCategory category,DictLogisticVo dictLogisticVo) {
        try {
            dictLogisticClient.add(category,dictLogisticVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新物流字典
     *
     * @param dictLogisticVo 物流字典
     */
    public void updateDictLogistic(DictLogisticVo dictLogisticVo) {
        try {
            dictLogisticClient.update(dictLogisticVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除物流字典
     *
     * @param dictLogisticId 物流字典id
     */
    public void deleteDictLogisticById(Long dictLogisticId) {
        try {
            dictLogisticClient.deleteById(dictLogisticId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 物流字典列表列表
     *
     * @param category 类型
     * @param searchVo 查询
     * @return 返回物流字典列表
     */
    public PageResult<DictLogisticVo> listDictLogistic(DictLogisticCategory category,DictLogisticSearchVo searchVo) {
        try {
            return dictLogisticClient.listPage(category,searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
