package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.FeeClient;
import com.senox.realty.vo.*;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/19 17:21
 */
@Component
public class OneTimeFeeComponent {

    @Autowired
    private FeeClient feeClient;

    /**
     * 一次性收费项目列表
     * @param search
     * @return
     */
    public PageResult<OneTimeFeeVo> listOneTimeFee(OneTimeFeeSearchVo search) {
        try {
            return feeClient.listOneTimeFee(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 部门一次性收入项目列表
     * @param departmentIds
     * @return
     */
    public List<OneTimeFeeVo> listDepartmentOneTimeFee(List<Long> departmentIds) {
        try {
            return feeClient.listDepartmentOneTimeFee(departmentIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取一次性收费项目详情
     * @param id
     * @return
     */
    public OneTimeFeeVo findOneTimeFeeById(Long id) {
        try {
            return feeClient.findOneTimeFeeById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加一次性收费项目
     * @param fee
     * @return
     */
    public Long addOneTimeFee(OneTimeFeeVo fee) {
        try {
            return feeClient.addOneTimeFee(fee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新一次性收费项目
     * @param fee
     */
    public void updateOneTimeFee(OneTimeFeeVo fee) {
        try {
            feeClient.updateOneTimeFee(fee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新状态结果
     * @param billPaid
     */
    public void updateOneTimeFeeBillStatus(BillPaidVo billPaid) {
        try {
            feeClient.updateOneTimeFeeBillStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 一次性收费账单列表
     * @param search
     * @return
     */
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBill(OneTimeFeeBillSearchVo search) {
        try {
            return feeClient.listOneTimeFeeBill(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RefundBillPageResult.emptyPage();
    }

    /**
     * 一次性收费账单交易
     * @param search
     * @return
     */
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search) {
        try {
            return feeClient.listOneTimeFeeBillTrade(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return RefundBillPageResult.emptyPage();
    }

    /**
     * 获取一次性收费账单信息
     * @param id
     * @return
     */
    public OneTimeFeeBillVo findOneTimeFeeBillById(Long id) {
        try {
            return feeClient.findOneTimeFeeBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取一次性收费账单信息列表
     * @param ids
     * @return
     */
    public List<OneTimeFeeBillVo> listOneTimeFeeBillByIds(List<Long> ids) {
        try {
            return feeClient.listOneTimeFeeBillByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取一次性收费账单信息及缴费退费信息
     * @param id
     * @return
     */
    public OneTimeFeeBillTradeVo findOneTimeFeeBillDetailById(Long id) {
        try {
            return feeClient.findOneTimeFeeBillDetailById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 一次性收费账单缴费
     * @param id
     * @param toll
     */
    public void payOneTimeFeeBill(Long id, BillTollVo toll) {
        try {
            feeClient.payOneTimeFeeBill(id, toll);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销一次性收费账单缴费
     * @param id
     */
    public void revokeOneTimeFeeBillPayment(Long id) {
        try {
            feeClient.revokeOneTimeFeeBillPayment(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 一次性收费账单退费
     * @param id
     * @param toll
     */
    public void refundOneTimeFeeBill(Long id, BillTollVo toll) {
        try {
            feeClient.refundOneTimeFeeBill(id, toll);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 撤销一次性收费账单退费
     * @param id
     */
    public void revokeOneTimeFeeBillRefund(Long id) {
        try {
            feeClient.revokeOneTimeFeeBillRefund(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加一次性收费账单
     * @param bill
     * @return
     */
    public Long addOneTimeFeeBill(OneTimeFeeBillVo bill) {
        try {
            return feeClient.addOneTimeFeeBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新一次性收费账单
     * @param bill
     */
    public void updateOneTimeFeeBill(OneTimeFeeBillVo bill) {
        try {
            feeClient.updateOneTimeFeeBill(bill);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新一次性收费账单票据
     * @param serial
     */
    public void updateOneTimeFeeBillSerial(TollSerialVo serial) {
        try {
            feeClient.updateOneTimeFeeBillSerial(serial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 一次性收费押金列表
     * @param search
     * @return
     */
    public List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(OneTimeFeeDepositSearchVo search) {
        try {
            return feeClient.listOneTimeFeeDeposit(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }
}
