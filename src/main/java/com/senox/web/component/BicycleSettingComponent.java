package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.BicycleSettingClient;
import com.senox.tms.vo.BicycleSettingVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 10:51
 */
@Component
@RequiredArgsConstructor
public class BicycleSettingComponent {

    private final BicycleSettingClient bicycleSettingClient;

    /**
     * 添加三轮车配置信息
     * @param settingVo
     * @return
     */
    public Long addBicycleSetting(BicycleSettingVo settingVo) {
        try {
            return bicycleSettingClient.addBicycleSetting(settingVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改三轮车配置信息
     * @param settingVo
     */
    public void updateBicycleSetting(BicycleSettingVo settingVo) {
        try {
            bicycleSettingClient.updateBicycleSetting(settingVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取三轮车配置信息
     * @param id
     * @return
     */
    public BicycleSettingVo findById(Long id) {
        try {
            return bicycleSettingClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除三轮车配置信息
     * @param id
     */
    public void deleteById(Long id) {
        try {
            bicycleSettingClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 三轮车配置信息列表
     * @return
     */
    public List<BicycleSettingVo> listBicycleSetting() {
        try {
            return bicycleSettingClient.listBicycleSetting();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
