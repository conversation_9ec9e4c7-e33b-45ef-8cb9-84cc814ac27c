package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.DiningInformationClient;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
public class DiningInformationComponent {

    private final DiningInformationClient diningInformationClient;

    /**
     * 批量添加报餐
     * @param diningInformationVoList
     * @return
     */
    public void addBatchDiningInformation(List<DiningInformationVo> diningInformationVoList) {
        try {
            diningInformationClient.addBatchDiningInformation(diningInformationVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加报餐记录
     * @param diningInformationVo
     */
    public void addDiningInformation(DiningInformationVo diningInformationVo){
        try {
            diningInformationClient.addDiningInformation(diningInformationVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改报餐数据
     * @param diningInformationVo
     */
    public void updateDiningInformation(DiningInformationVo diningInformationVo){
        try {
            diningInformationClient.updateDiningInformation(diningInformationVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id报餐信息
     * @param id
     * @return
     */
    public DiningInformationVo getDiningInformation(Long id){
        try {
            return diningInformationClient.getDiningInformation(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除报餐数据
     * @param id
     */
    public void deleteDiningInformation(Long id){
        try {
            diningInformationClient.deleteDiningInformation(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查询报餐数据
     * @param search
     * @return
     */
    public PageResult<DiningInformationVo> listDiningInformation(DiningInformationSearchVo search){
        try {
            return diningInformationClient.listDiningInformation(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
