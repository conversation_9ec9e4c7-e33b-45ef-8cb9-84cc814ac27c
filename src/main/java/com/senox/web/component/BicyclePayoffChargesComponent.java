package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.BicyclePayoffChargesClient;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-4-2
 */
@RequiredArgsConstructor
@Component
public class BicyclePayoffChargesComponent {
    private final BicyclePayoffChargesClient payoffChargesClient;

    /**
     * 更新
     *
     * @param payoffChargesVo 应付费用
     */
    public void update(BicyclePayoffChargesVo payoffChargesVo) {
        try {
            payoffChargesClient.update(payoffChargesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查找应付费用
     *
     * @return 返回应付费用
     */
    public BicyclePayoffChargesVo find() {
        try {
            return payoffChargesClient.find();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

}
