package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.api.clients.JadeBirdFireDeviceEventClient;
import com.senox.dm.vo.JadeBirdFireDeviceEventSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceEventVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Component
public class JadeBirdFireDeviceEventComponent {
    private final JadeBirdFireDeviceEventClient fireDeviceEventClient;

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public List<JadeBirdFireDeviceEventVo> list(JadeBirdFireDeviceEventSearchVo search) {
        try {
            return fireDeviceEventClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<JadeBirdFireDeviceEventVo> pageList(JadeBirdFireDeviceEventSearchVo search) {
        try {
            return fireDeviceEventClient.pageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

}
