package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.ReceiptApplyClient;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-7-21
 */
@Component
@RequiredArgsConstructor
public class PaymentReceiptApplyComponent {
    private final ReceiptApplyClient receiptApplyClient;

    /**
     * 发票申请列表
     *
     * @param search 查询参数
     * @return 返回分页后的发票申请列表
     */
    public PageResult<ReceiptApplyVo> list(ReceiptApplySearchVo search) {
        try {
            return receiptApplyClient.list(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据单据编号查询发票申请
     *
     * @param serialNo 单据编号
     * @return 发票申请信息
     */
    public ReceiptApplyVo findBySerialNo(String serialNo) {
        try {
            return receiptApplyClient.findBySerialNo(serialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 查询最近发票申请
     *
     * @param taxHeader 发票抬头
     * @return 发票申请
     */
    public ReceiptApplyVo top(String taxHeader) {
        try {
            return receiptApplyClient.top(taxHeader);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
