package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.PrizeClient;
import com.senox.user.vo.PrizeRecordsSearchVo;
import com.senox.user.vo.PrizeRecordsVo;
import com.senox.user.vo.PrizeSearchVo;
import com.senox.user.vo.PrizeVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/15 15:08
 */
@Component
@RequiredArgsConstructor
public class PrizeComponent {

     private final PrizeClient prizeClient;

     /**
      * 添加抽奖奖品
      * @param prizeVo
      * @return
      */
     public Long addPrize(PrizeVo prizeVo) {
          try {
               return prizeClient.addPrize(prizeVo);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
          return 0L;
     }

     /**
      * 更新抽奖奖品
      * @param prizeVo
      */
     public void updatePrize(PrizeVo prizeVo) {
          try {
               prizeClient.updatePrize(prizeVo);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
     }

     /**
      * 根据id获取抽奖奖品
      * @param id
      * @return
      */
     public PrizeVo findById(Long id) {
          try {
               return prizeClient.findById(id);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
          return null;
     }

     /**
      * 根据id删除抽奖奖品
      * @param id
      */
     public void deleteById(Long id) {
          try {
               prizeClient.deleteById(id);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
     }

     /**
      * 抽奖奖品分页
      * @param searchVo
      * @return
      */
     public PageResult<PrizeVo> pagePrize(PrizeSearchVo searchVo) {
          try {
               return prizeClient.pagePrize(searchVo);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
          return PageResult.emptyPage();
     }

     /**
      * 根据id获取抽奖记录
      * @param id
      * @return
      */
     public PrizeRecordsVo findRecordsById(Long id) {
          try {
               return prizeClient.findRecordsById(id);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
          return null;
     }

     /**
      * 抽奖记录分页
      * @param searchVo
      * @return
      */
     public PageResult<PrizeRecordsVo> pageRecords(PrizeRecordsSearchVo searchVo) {
          try {
               return prizeClient.pageRecords(searchVo);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
          return PageResult.emptyPage();
     }

     /**
      * 根据uuid兑奖
      * @param uuid
      */
     public void verifyPrize(String uuid) {
          try {
               prizeClient.verifyPrize(uuid);
          } catch (FeignException e) {
               FeignUtils.handleFeignException(e);
          }
     }
}
