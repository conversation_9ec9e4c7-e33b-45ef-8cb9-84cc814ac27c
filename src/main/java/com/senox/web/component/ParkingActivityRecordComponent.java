package com.senox.web.component;

import com.senox.car.api.clients.ParkingActivityRecordClient;
import com.senox.car.vo.ParkingActivityRecordSearchVo;
import com.senox.car.vo.ParkingActivityRecordVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Component
public class ParkingActivityRecordComponent {
    private final ParkingActivityRecordClient activityRecordClient;


    /**
     * 添加活动记录
     *
     * @param activityRecordVo 活动记录
     * @return 返回id
     */
    public Long addActivityRecord(ParkingActivityRecordVo activityRecordVo) {
        try {
            return activityRecordClient.add(activityRecordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新活动记录
     *
     * @param activityRecordVo 活动记录
     */
    public void updateActivityRecord(ParkingActivityRecordVo activityRecordVo) {
        try {
            activityRecordClient.update(activityRecordVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取活动记录
     *
     * @param id 活动记录id
     * @return 返回获取到的活动记录
     */
    public ParkingActivityRecordVo findActivityRecordById(Long id) {
        try {
            return activityRecordClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除活动记录
     *
     * @param id 活动记录id
     */
    public void deleteActivityRecordById(Long id) {
        try {
            activityRecordClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 活动记录分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityRecordVo> activityRecordListPage(ParkingActivityRecordSearchVo searchVo) {
        try {
            return activityRecordClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
