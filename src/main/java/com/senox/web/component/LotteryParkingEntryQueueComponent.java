package com.senox.web.component;

import com.senox.car.api.clients.LotteryParkingEntryQueueClient;
import com.senox.car.vo.LotteryParkingEntryQueueSearchVo;
import com.senox.car.vo.LotteryParkingEntryQueueVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.OrderResultVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:25
 */
@Component
@RequiredArgsConstructor
public class LotteryParkingEntryQueueComponent {

    private final LotteryParkingEntryQueueClient lotteryParkingEntryQueueClient;

    /**
     * 排队支付
     * @param entryQueueVo
     * @return
     */
    public OrderResultVo entryQueuePay(LotteryParkingEntryQueueVo entryQueueVo) {
        try {
            return lotteryParkingEntryQueueClient.entryQueuePay(entryQueueVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新入场队列信息,返回车位id
     * @param id
     * @param status
     * @return
     */
    public Long updateEntryQueueStatus(Long id, Integer status) {
        try {
            return lotteryParkingEntryQueueClient.updateEntryQueueStatus(id, status);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 进入临时车位
     * @param id
     * @param parkingId
     */
    public void fillEntryQueue(Long id, Long parkingId) {
        try {
            lotteryParkingEntryQueueClient.fillEntryQueue(id, parkingId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 入场队列列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingEntryQueueVo> entryQueuePage(LotteryParkingEntryQueueSearchVo searchVo) {
        try {
            return lotteryParkingEntryQueueClient.entryQueuePage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 入场队列合计
     * @param searchVo
     * @return
     */
    public LotteryParkingEntryQueueVo sumEntryQueue(LotteryParkingEntryQueueSearchVo searchVo) {
        try {
            return lotteryParkingEntryQueueClient.sumEntryQueue(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 重排
     * @param id
     */
    public void rearrangement(Long id) {
        try {
            lotteryParkingEntryQueueClient.rearrangement(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
