package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.StatisticsClient;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import com.senox.realty.vo.RealtyStatisticsVo;
import com.senox.realty.vo.StatisticsSearchVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:12
 */
@Component
@RequiredArgsConstructor
public class StatisticsComponent {

    private final StatisticsClient statisticsClient;

    /**
     * 物业统计分页
     * @param searchVo
     * @return
     */
    public PageResult<RealtyStatisticsVo> realtyStatisticsPageResult(StatisticsSearchVo searchVo) {
        try {
            return statisticsClient.realtyStatisticsPageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据统计日期获取物业统计记录
     * @param statisticsDate
     * @return
     */
    public RealtyStatisticsVo findRealtyStatisticsByDate(LocalDate statisticsDate) {
        try {
            return statisticsClient.findRealtyStatisticsByDate(statisticsDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 广告位统计分页
     * @param searchVo
     * @return
     */
    public PageResult<AdvertisingStatisticsVo> advertisingStatisticsPageResult(StatisticsSearchVo searchVo) {
        try {
            return statisticsClient.advertisingStatisticsPageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据统计日期获取广告位统计记录
     * @param statisticsDate
     * @return
     */
    public AdvertisingStatisticsVo findAdvertisingStatisticsByDate(LocalDate statisticsDate) {
        try {
            return statisticsClient.findAdvertisingStatisticsByDate(statisticsDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
