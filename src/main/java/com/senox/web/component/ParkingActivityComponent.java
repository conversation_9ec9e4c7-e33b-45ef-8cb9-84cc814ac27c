package com.senox.web.component;

import com.senox.car.api.clients.ParkingActivityClient;
import com.senox.car.vo.ParkingActivitySearchVo;
import com.senox.car.vo.ParkingActivityVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Component
public class ParkingActivityComponent {
    private final ParkingActivityClient activityClient;

    /**
     * 添加活动
     *
     * @param activityVo 活动
     */
    public void addActivity(ParkingActivityVo activityVo) {
        try {
            activityClient.add(activityVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新活动
     *
     * @param activityVo 活动
     */
    public void updateActivity(ParkingActivityVo activityVo) {
        try {
            activityClient.update(activityVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取活动
     *
     * @param id 活动id
     * @return 返回获取到的活动
     */
    public ParkingActivityVo findActivityById(Long id) {
        try {
            return activityClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除停车活动
     *
     * @param id 活动id
     */
    public void deleteActivityById(Long id) {
        try {
            activityClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 活动列表分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityVo> activityListPage(ParkingActivitySearchVo searchVo) {
        try {
            return activityClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
