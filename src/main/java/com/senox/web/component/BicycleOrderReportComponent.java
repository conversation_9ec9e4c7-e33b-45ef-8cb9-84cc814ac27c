package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleOrderReportClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/8 10:29
 */
@Component
@RequiredArgsConstructor
public class BicycleOrderReportComponent {

    private final BicycleOrderReportClient bicycleOrderReportClient;

    /**
     * 生成日报表
     * @param dateVo
     */
    public void generateDayReport(BicycleReportDateVo dateVo) {
        try {
            bicycleOrderReportClient.generateDayReport(dateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新日报表
     * @param dayReportVo
     */
    public void updateDayReport(BicycleOrderDayReportVo dayReportVo) {
        try {
            bicycleOrderReportClient.updateDayReport(dayReportVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id集合查询日报表
     * @param ids
     * @return
     */
    public List<BicycleOrderDayReportVo> listDayReport(List<Long> ids) {
        try {
            bicycleOrderReportClient.listDayReport(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 删除日报表
     * @param ids
     */
    public void deleteDayReport(List<Long> ids) {
        try {
            bicycleOrderReportClient.deleteDayReport(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取日报表
     * @param id
     * @return
     */
    public BicycleOrderDayReportVo findDayReportById(Long id) {
        try {
            return bicycleOrderReportClient.findDayReportById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 日报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderDayReportVo sumDayReport(BicycleOrderDayReportSearchVo searchVo) {
        try {
            return bicycleOrderReportClient.sumDayReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        BicycleOrderDayReportVo result = new BicycleOrderDayReportVo();
        result.setTotalCount(0);
        result.setTotalPieces(BigDecimal.ZERO);
        result.setDeliveryCharge(BigDecimal.ZERO);
        result.setOtherCharge(BigDecimal.ZERO);
        result.setTotalCharge(BigDecimal.ZERO);
        return result;
    }

    /**
     * 日报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderDayReportVo> listDayReport(BicycleOrderDayReportSearchVo searchVo) {
        try {
            return bicycleOrderReportClient.listDayReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 生成月报表
     * @param dateVo
     */
    public void generateMonthReport(BicycleReportDateVo dateVo) {
        try {
            bicycleOrderReportClient.generateMonthReport(dateVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新月报表
     * @param monthReportVo
     */
    public void updateMonthReport(BicycleOrderMonthReportVo monthReportVo) {
        try {
            bicycleOrderReportClient.updateMonthReport(monthReportVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id集合查询月报表
     * @param ids
     * @return
     */
    public List<BicycleOrderMonthReportVo> listMonthReport(List<Long> ids) {
        try {
            return bicycleOrderReportClient.listMonthReport(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 删除月报表
     * @param ids
     */
    public void deleteMonthReport(List<Long> ids) {
        try {
            bicycleOrderReportClient.deleteMonthReport(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取月报表
     * @param id
     * @return
     */
    public BicycleOrderMonthReportVo findMonthReportById(Long id) {
        try {
            return bicycleOrderReportClient.findMonthReportById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 月报表明细
     * @param id
     * @return
     */
    public List<BicycleOrderDayReportVo> listMonthDayReport(Long id) {
        try {
            return bicycleOrderReportClient.listMonthDayReport(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 月报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderMonthReportVo sumMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        try {
            return bicycleOrderReportClient.sumMonthReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        BicycleOrderMonthReportVo result = new BicycleOrderMonthReportVo();
        result.setTotalCount(0);
        result.setTotalPieces(BigDecimal.ZERO);
        result.setDeliveryCharge(BigDecimal.ZERO);
        result.setOtherCharge(BigDecimal.ZERO);
        result.setTotalCharge(BigDecimal.ZERO);
        return result;
    }

    /**
     * 月报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderMonthReportVo> listMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        try {
            return bicycleOrderReportClient.listMonthReport(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
