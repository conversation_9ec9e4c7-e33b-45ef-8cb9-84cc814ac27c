package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.realty.api.clients.BusinessRegionClient;
import com.senox.realty.api.clients.FeeClient;
import com.senox.realty.api.clients.StreetClient;
import com.senox.realty.vo.BusinessRegionVo;
import com.senox.realty.vo.FeeVo;
import com.senox.realty.vo.StreetVo;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/18 15:44
 */
@Component
public class RealtyDictionaryComponent {

    @Autowired
    private FeeClient feeClient;
    @Autowired
    private BusinessRegionClient regionClient;
    @Autowired
    private StreetClient streetClient;

    /**
     * 费项列表
     * @param category
     * @return
     */
    public List<FeeVo> listFee(Integer category) {
        try {
            return feeClient.listFee(category);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取费项
     * @param id
     * @return
     */
    public FeeVo findFeeById(Long id) {
        try {
            return feeClient.getFee(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加费项
     * @param fee
     * @return
     */
    public Long addFee(FeeVo fee) {
        try {
            return feeClient.addFee(fee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新费项
     * @param fee
     */
    public void updateFee(FeeVo fee) {
        try {
            feeClient.updateFee(fee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 水/电价类别列表
     * @param type
     * @return
     */
    public List<WaterElectricPriceTypeVo> listPriceType(Integer type) {
        try {
            return feeClient.listPriceType(type);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取水/电价类别
     * @param id
     * @return
     */
    public WaterElectricPriceTypeVo findPriceTypeById(Long id) {
        try {
            return feeClient.getPriceType(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加水/电价类别
     * @param priceType
     * @return
     */
    public Long addPriceType(WaterElectricPriceTypeVo priceType) {
        try {
            return feeClient.addPriceType(priceType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新水/电价类别
     * @param priceType
     */
    public void updatePriceType(WaterElectricPriceTypeVo priceType) {
        try {
            feeClient.updatePriceType(priceType);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 经营区域列表
     * @return
     */
    public List<BusinessRegionVo> listRegion() {
        try {
            return regionClient.listRegion();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取经营区域
     * @param id
     * @return
     */
    public BusinessRegionVo findRegionById(Long id) {
        try {
            return regionClient.getRegion(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加经营区域
     * @param region
     * @return
     */
    public Long addRegion(BusinessRegionVo region) {
        try {
            return regionClient.addRegion(region);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新经营区域
     * @param region
     */
    public void updateRegion(BusinessRegionVo region) {
        try {
            regionClient.updateRegion(region);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 街道列表
     * @return
     */
    public List<StreetVo> listStreet() {
        try {
            return streetClient.listStreet();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 区域街道列表
     * @param regionId
     * @return
     */
    public List<StreetVo> listRegionStreet(Long regionId) {
        try {
            return streetClient.listRegionStreet(regionId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取街道
     * @param id
     * @return
     */
    public StreetVo findStreetById(Long id) {
        try {
            return streetClient.getStreet(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加街道
     * @param street
     * @return
     */
    public Long addStreet(StreetVo street) {
        try {
            return streetClient.addStreet(street);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新街道
     * @param street
     */
    public void updateStreet(StreetVo street) {
        try {
            streetClient.updateStreet(street);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
