package com.senox.web.component;

import com.senox.car.api.clients.ParkingActivityLinkClient;
import com.senox.car.vo.ParkingActivityItemSearchVo;
import com.senox.car.vo.ParkingActivityItemVo;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Component
public class ParkingActivityLinkComponent {
    private final ParkingActivityLinkClient activityLinkClient;

    /**
     * 添加活动链接
     *
     * @param activityItemVo 链接
     * @return 返回链接
     */
    public String addActivityLink(ParkingActivityItemVo activityItemVo) {
        try {
            return activityLinkClient.add(activityItemVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 更新活动链接
     *
     * @param activityItemVo 活动链接
     */
    public void updateActivityLink(ParkingActivityItemVo activityItemVo) {
        try {
            activityLinkClient.update(activityItemVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取活动链接
     *
     * @param id 活动链接id
     * @return 返回获取到的活动链接
     */
    public ParkingActivityItemVo findActivityLinkById(Long id) {
        try {
            return activityLinkClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据code获取活动链接
     *
     * @param code 活动链接编码
     * @return 返回获取到的活动链接
     */
    public ParkingActivityItemVo findActivityLinkByCode(String code) {
        try {
            return activityLinkClient.findByCode(code);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除活动链接
     *
     * @param id 活动链接id
     */
    public void deleteActivityLinkById(Long id) {
        try {
            activityLinkClient.deleteById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 绑定活动链接
     *
     * @param activityItemVo 活动链接
     * @return 返回活动链接
     */
    public ParkingActivityItemVo bindActivityLink(ParkingActivityItemVo activityItemVo) {
        try {
            activityLinkClient.bind(activityItemVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 活动链接分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityItemVo> activityLinkListPage(ParkingActivityItemSearchVo searchVo) {
        try {
            return activityLinkClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
