package com.senox.web.component;

import com.senox.car.api.clients.ParkingClient;
import com.senox.car.api.clients.ParkingTollManPaymentClient;
import com.senox.car.vo.*;
import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11 10:56
 */
@Component
@RequiredArgsConstructor
public class ParkingComponent {

    private final ParkingClient parkingClient;
    private final ParkingTollManPaymentClient parkingTollManPaymentClient;

    /**
     * 停车缴费统计备注
     * @param remark
     */
    public void remarkParkingPaymentStatistic(ParkingPaymentRemarkVo remark) {
        try {
            parkingClient.remarkParkingPaymentStatistic(remark);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 停车缴费统计合计
     * @param search
     * @return
     */
    public ParkingPaymentStatisticVo sumParkingPaymentStatistic(ParkingPaymentSearchVo search) {
        try {
            return parkingClient.sumParkingPaymentStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return null;
    }

    /**
     * 停车缴费统计列表
     * @param search
     * @return
     */
    public List<ParkingPaymentStatisticVo> listParkingPaymentStatistic(ParkingPaymentSearchVo search) {
        try {
            return parkingClient.listParkingPaymentStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 停车缴费统计页
     * @param search
     * @return
     */
    public PageResult<ParkingPaymentStatisticVo> listParkingPaymentStatisticPage(ParkingPaymentSearchVo search) {
        try {
            return parkingClient.listParkingPaymentStatisticPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

    /**
     * 收费员收费记录合计
     * @param searchVo
     * @return
     */
    public ParkingTollManPaymentVo sumParkingTollManPayment(ParkingPaymentSearchVo searchVo) {
        try {
            return parkingTollManPaymentClient.sumParkingTollManPayment(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 收费员收费记录列表
     * @param searchVo
     * @return
     */
    public List<ParkingTollManPaymentVo> listParkingTollManPayment(ParkingPaymentSearchVo searchVo) {
        try {
            return parkingTollManPaymentClient.listParkingTollManPayment(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 收费员收费记录页
     * @param searchVo
     * @return
     */
    public PageResult<ParkingTollManPaymentVo> listParkingTollManPaymentPage(ParkingPaymentSearchVo searchVo) {
        try {
            return parkingTollManPaymentClient.listParkingTollManPaymentPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 停车系统统计
     * @param search
     * @return
     */
    public ParkingStatisticsVo parkingPaymentStatistic(ParkingPaymentSearchVo search) {
        try {
            return parkingClient.parkingPaymentStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return null;
    }

    /**
     * 停车系统大屏统计
     * @param search
     * @return
     */
    public List<ParkingStatisticsVo> parkingPaymentStatisticScreen(ParkingPaymentStatisticSearchVo search) {
        try {
            return parkingClient.parkingPaymentStatisticScreen(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 入场登记车辆大屏统计
     * @param search
     * @return
     */
    public List<ParkingVehicleDayReportVo> parkingVehicleStatisticScreen(ParkingVehicleStatisticsSearchVo search) {
        try {
            return parkingClient.parkingVehicleStatisticScreen(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }
}
