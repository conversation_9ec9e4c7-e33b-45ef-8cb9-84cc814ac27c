package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.OrderClient;
import com.senox.pm.constant.OrderType;
import com.senox.pm.vo.*;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/18 10:09
 */
@Component
public class OrderComponent {

    @Autowired
    private OrderClient orderClient;

    /**
     * 支付下单
     * @param order
     * @return
     */
    public OrderResultVo addOrder(OrderVo order) {
        try {
            return orderClient.addOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 退费下单
     * @param refundOrder
     * @return
     */
    public OrderResultVo refundOrder(RefundOrderVo refundOrder) {
        try {
            return orderClient.refundOrder(refundOrder);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 部分退费
     * @param refundOrderFee
     * @return
     */
    public OrderResultVo refundOrderPart(RefundOrderFeeVo refundOrderFee) {
        try {
            return orderClient.refundOrderPart(refundOrderFee);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 关闭订单
     * @param orderClose
     */
    public void closeOrders(OrderCloseVo orderClose) {
        if (CollectionUtils.isEmpty(orderClose.getOrderIds())) {
            return;
        }
        try {
            orderClient.batchCloseOrder(orderClose);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    public void updateOrderSerial(OrderSerialVo serial) {
        try {
            orderClient.updateOrderSerial(serial);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查询订单状态
     * @param tradeNo
     * @param realTime
     * @return
     */
    public OrderResultVo queryOrderStatus(String tradeNo, Boolean realTime) {
        try {
            return orderClient.queryOrderStatus(tradeNo, realTime);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取订单明细
     * @param orderId
     * @return
     */
    public OrderVo findWithDetail(Long orderId) {
        try {
            return orderClient.findWithDetail(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取订单项目明细
     * @param orderId
     * @param productId
     * @return
     */
    public OrderProductDetailVo getOrderProduct(Long orderId, Long productId) {
        try {
            return orderClient.getOrderProduct(orderId, productId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 获取订单项目明细
     * @param orderType
     * @param productId
     * @return
     */
    public List<OrderProductDetailVo> listOrderProduct(OrderType orderType, Long productId) {
        if (orderType == null || !WrapperClassUtils.biggerThanLong(productId, 0L)) {
            return Collections.emptyList();
        }

        try {
            return orderClient.listProductOrders(orderType, productId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 账单费用明细
     * @param search
     * @return
     */
    public List<OrderItemDetailVo> listOrderItemDetail(OrderItemDetailSearchVo search) {
        try {
            return orderClient.listOrderItemDetail(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 订单列表页
     * @param search
     * @return
     */
    public OrderPageResult<OrderListVo> listOrderPage(OrderSearchVo search) {
        try {
            return orderClient.listOrderPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return OrderPageResult.emptyPage();
    }

    /**
     * 收款明细列表
     * @param search
     * @return
     */
    public OrderPageResult<OrderTollVo> listOrderTollPage(OrderTollSearchVo search) {
        try {
            return orderClient.listOrderTollPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return OrderPageResult.emptyPage();
    }

    /**
     * 收款费项明细
     * @param search
     * @return
     */
    public PageResult<OrderTollDetailVo> listOrderTollDetail(OrderTollSearchVo search) {
        try {
            return orderClient.listOrderTollDetail(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 冷藏缴费账单合计
     * @param search
     * @return
     */
    public RefrigerationTollVo sumRefrigerationToll(RefrigerationTollSearchVo search) {
        try {
            return orderClient.sumRefrigerationToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return  null;
    }


    /**
     * 冷藏缴费账单列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationTollVo> listRefrigerationToll(RefrigerationTollSearchVo search) {
        try {
            return orderClient.listRefrigerationToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 物维缴费账单合计
     * @param search
     * @return
     */
    public MaintainChargeTollVo sumMaintainChargeToll(MaintainChargeTollSearchVo search) {
        try {
            return orderClient.sumMaintainChargeToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return  null;
    }

    /**
     * 物维缴费账单列表
     * @param search
     * @return
     */
    public PageResult<MaintainChargeTollVo> listMaintainChargeToll(MaintainChargeTollSearchVo search) {
        try {
            return orderClient.listMaintainChargeToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 根据id获取物维账单
     * @param id
     * @return
     */
    public MaintainChargeTollVo maintainChargeTollById(Long id){
        try {
            return orderClient.maintainChargeTollById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return  null;
    }

    /**
     * 押金交易流水统计
     * @param search
     * @return
     */
    public DepositTollVo sumDepositToll(DepositTollSearchVo search) {
        try {
            return orderClient.sumDepositToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 押金交易流水列表
     * @param search
     * @return
     */
    public List<DepositTollVo> listDepositToll(DepositTollSearchVo search) {
        try {
            return orderClient.listDepositToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 押金交易流水页
     * @param search
     * @return
     */
    public PageResult<DepositTollVo> listDepositTollPage(DepositTollSearchVo search) {
        try {
            return orderClient.listDepositTollPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 广告账单结算
     * @param search
     * @return
     */
    public AdvertisingTollVo sumAdvertisingToll(AdvertisingTollSearchVo search) {
        try {
            return orderClient.sumAdvertisingToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 广告结算列表
     * @param search
     * @return
     */
    public PageResult<AdvertisingTollVo> listAdvertisingToll(AdvertisingTollSearchVo search) {
        try {
            return orderClient.listAdvertisingToll(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 收费员日收费统计合计
     * @param search
     * @return
     */
    public TollManDailyStatisticVo sumTollManDailyStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.sumTollManDailyStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 收费员日收费统计列表
     * @param search
     * @return
     */
    public List<TollManDailyStatisticVo> listTollManDailyStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.listTollManDailyStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 收费员日收费统计页
     * @param search
     * @return
     */
    public PageResult<TollManDailyStatisticVo> listTollManDailyStatisticPage(TollStatisticSearchVo search) {
        try {
            return orderClient.listTollManDailyStatisticPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 支付通道日统计合计
     * @param search
     * @return
     */
    public PayWayDailyStatisticVo sumPayWayDailyStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.sumPayWayDailyStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 支付通道日统计列表
     * @param search
     * @return
     */
    public List<PayWayDailyStatisticVo> listPayWayDailyStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.listPayWayDailyStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 支付通道日统计页
     * @param search
     * @return
     */
    public PageResult<PayWayDailyStatisticVo> listPayWayDailyStatisticPage(TollStatisticSearchVo search) {
        try {
            return orderClient.listPayWayDailyStatisticPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 支付通道日明细合计
     * @param search
     * @return
     */
    public TollPayWayStatisticVo sumTollPayWayStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.sumTollPayWayStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 支付通道日明细列表
     * @param search
     * @return
     */
    public List<TollPayWayStatisticVo> listTollPayWayStatistic(TollStatisticSearchVo search) {
        try {
            return orderClient.listTollPayWayStatistic(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 支付通道日明细页
     * @param search
     * @return
     */
    public PageResult<TollPayWayStatisticVo> listTollPayWayStatisticPage(TollStatisticSearchVo search) {
        try {
            return orderClient.listTollPayWayStatisticPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 支付通道日明细备注
     * @param ext
     */
    public void remarkTollPayWay(TollPayWayExtVo ext) {
        try {
            orderClient.remarkTollPayWay(ext);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 收费扩展信息列表
     * @param payWay
     * @param tollDates
     * @return
     */
    public List<TollPayWayExtVo> listTollPayWayExt(Integer payWay, List<LocalDate> tollDates) {
        try {
            return orderClient.listTollPayWayExt(payWay, tollDates);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

}
