package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.UnloadingClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 8:53
 */
@RequiredArgsConstructor
@Component
public class UnloadingComponent {

    private final UnloadingClient unloadingClient;

    /**
     * 批量添加字典
     * @param dictVos
     */
    public void batchAddDict(List<UnloadingDictVo> dictVos) {
        try {
            unloadingClient.batchAddDict(dictVos);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加字典
     * @param dictVo
     */
    public void addDict(UnloadingDictVo dictVo) {
        try {
            unloadingClient.addDict(dictVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新字典
     * @param dictVo
     */
    public void updateDict(UnloadingDictVo dictVo) {
        try {
            unloadingClient.updateDict(dictVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询字典
     * @param id
     * @return
     */
    public UnloadingDictVo findDictById(Long id) {
        try {
            return unloadingClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除字典
     * @param id
     */
    public void deleteDictById(Long id) {
        try {
            unloadingClient.deleteDict(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 字典分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingDictVo> dictPageResult(UnloadingDictSearchVo searchVo) {
        try {
            return unloadingClient.dictPageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 保存搬运工
     * @param workerVo
     */
    public void saveWorker(UnloadingWorkerVo workerVo) {
        try {
            unloadingClient.saveWorker(workerVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    public void updateWorkerStatus(Long id, Integer status) {
        try {
            unloadingClient.updateWorkerStatus(id, status);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据搬运工编号更新搬运工状态
     * @param workerNo
     * @param status
     */
    public void updateWorkerStatusByWorkerNo(String workerNo, Integer status) {
        try {
            unloadingClient.updateWorkerStatusByWorkerNo(workerNo, status);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    public void updateFaceUrl(UnloadingWorkerFaceUrlVo faceUrlVo) {
        try {
            unloadingClient.updateFaceUrl(faceUrlVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 指定位置排序
     * @param id
     * @param num
     */
    public void appointResetOrderNum(Long id, Integer num) {
        try {
            unloadingClient.appointResetOrderNum(id, num);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 搬运工排序置底
     * @param id
     */
    public void bottomUp(Long id) {
        try {
            unloadingClient.bottomUp(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    public UnloadingWorkerVo findWorkById(Long id) {
        try {
            return unloadingClient.findWorkById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 搬运工请假
     * @param workerVo
     */
    public void workerLeave(UnloadingWorkerVo workerVo) {
        try {
            unloadingClient.workerLeave(workerVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id删除搬运工
     * @param id
     */
    public void deleteWorker(Long id) {
        try {
            unloadingClient.deleteWorker(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 搬运工总数
     * @param searchVo
     * @return
     */
    public int countWorker(UnloadingWorkerSearchVo searchVo) {
        try {
            return unloadingClient.countWorker(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0;
    }

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorkerVo> listWorker(UnloadingWorkerSearchVo searchVo) {
        try {
            return unloadingClient.listWorker(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 搬运工分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorkerVo> pageWorker(UnloadingWorkerSearchVo searchVo) {
        try {
            return unloadingClient.pageWorker(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 搬运工考勤记录分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingAttendanceVo> pageAttendance(UnloadingAttendanceSearchVo searchVo) {
        try {
            return unloadingClient.pageAttendance(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 更新考勤记录备注
     * @param id
     * @param remark
     */
    public void updateRemark(Long id, String remark) {
        try {
            unloadingClient.updateRemark(id, remark);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加搬运工设备权限
     * @param accessVoList
     */
    public void addWorkerAccess(List<UnloadingWorkerAccessVo> accessVoList) {
        try {
            unloadingClient.addWorkerAccess(accessVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id删除搬运工设备权限
     * @param id
     */
    public void deleteAccessById(Long id) {
        try {
            unloadingClient.deleteAccessById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据Id获取搬运工设备权限
     * @param workerId
     * @return
     */
    public List<UnloadingWorkerAccessVo> listAccessByWorkerId(Long workerId) {
        try {
            return unloadingClient.listAccessByWorkerId(workerId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 搬运工顺序列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorkerVo> listSequenceWorker(UnloadingWorkerSearchVo searchVo) {
        try {
            return unloadingClient.listSequenceWorker(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id查询搬运工异常日志
     * @param id
     * @return
     */
    public UnloadingWorkerLogVo findWorkerLogById(Long id) {
        try {
            return unloadingClient.findWorkerLogById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 搬运工异常日志分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorkerLogVo> pageWorkerLog(UnloadingWorkerLogSearchVo searchVo) {
        try {
            return unloadingClient.pageWorkerLog(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 批量添加排期计划
     * @param batchVo
     */
    public void batchAddSchedule(UnloadingNightScheduleBatchVo batchVo) {
        try {
            unloadingClient.batchAddSchedule(batchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 添加排期计划
     * @param scheduleVo
     */
    public void addSchedule(UnloadingNightScheduleVo scheduleVo) {
        try {
            unloadingClient.addSchedule(scheduleVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新排期计划
     * @param scheduleVo
     */
    public void updateSchedule(UnloadingNightScheduleVo scheduleVo) {
        try {
            unloadingClient.updateSchedule(scheduleVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询排期计划
     * @param id
     * @return
     */
    public UnloadingNightScheduleVo findScheduleById(Long id) {
        try {
            return unloadingClient.findScheduleById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据日期获取排期计划
     * @param scheduleDate
     * @return
     */
    public List<UnloadingNightScheduleVo> findByScheduleDate(LocalDate scheduleDate) {
        try {
            return unloadingClient.findByScheduleDate(scheduleDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id删除排期计划
     * @param id
     */
    public void deleteScheduleById(Long id) {
        try {
            unloadingClient.deleteScheduleById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据日期删除排期计划
     * @param scheduleDate
     */
    public void deleteByScheduleDate(LocalDate scheduleDate) {
        try {
            unloadingClient.deleteByScheduleDate(scheduleDate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 搬运工排期计划分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingNightScheduleVo> pageSchedule(UnloadingNightScheduleSearchVo searchVo) {
        try {
            return unloadingClient.pageSchedule(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加搬运订单
     * @param orderVo
     */
    public void saveOrder(UnloadingOrderVo orderVo) {
        try {
            unloadingClient.saveOrder(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除搬运订单
     * @param id
     */
    public void deleteOrder(Long id) {
        try {
            unloadingClient.deleteOrder(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id搬运订单及详细
     * @param id
     * @return
     */
    public UnloadingOrderVo findDetailById(Long id) {
        try {
            return unloadingClient.findDetailById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 搬运订单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageResult(UnloadOrderSearchVo searchVo) {
        try {
            return unloadingClient.pageResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 搬运订单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo) {
        try {
            return unloadingClient.sumOrder(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 搬运订单详细分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageDetailResult(UnloadOrderSearchVo searchVo) {
        try {
            return unloadingClient.pageDetailResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }


    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    public void appointWorkerNum(Long orderId, Integer workerNum) {
        try {
            unloadingClient.appointWorkerNum(orderId, workerNum);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 分派搬运工
     * @param orderVo
     */
    public void assignWorkers(UnloadingOrderVo orderVo) {
        try {
            unloadingClient.assignWorkers(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 完成订单
     * @param orderId
     * @param amount
     */
    public void finishOrder(Long orderId, BigDecimal amount) {
        try {
            unloadingClient.finishOrder(orderId, amount);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 新增加急费
     * @param orderId
     * @param urgentAmount
     */
    public void addUrgentAmount(Long orderId, BigDecimal urgentAmount) {
        try {
            unloadingClient.addUrgentAmount(orderId, urgentAmount);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消订单
     * @param orderId
     */
    public void cancelOrder(Long orderId) {
        try {
            unloadingClient.cancelOrder(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 补录搬运订单
     * @param orderVo
     * @return 返回的订单号
     */
    public String supplementOrder(UnloadingOrderVo orderVo) {
        try {
            return unloadingClient.supplementOrder(orderVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 订单顺序分派
     * @param assignVo
     */
    public void sequenceAssignWorkers(UnloadingOrderWorkersAssignVo assignVo) {
        try {
            unloadingClient.sequenceAssignWorkers(assignVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    public UnloadingOperateAnalysisVo operateAnalysisStatistics(UnloadingStatisticSearchVo searchVo) {
        try {
            return unloadingClient.operateAnalysisStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    public UnloadingCountRankingVo rankingStatistics(UnloadingStatisticSearchVo searchVo) {
        try {
            return unloadingClient.rankingStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderCountVo> statistics(UnloadingStatisticSearchVo searchVo) {
        try {
            return unloadingClient.statistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加分佣
     * @param sharesVo
     */
    public void saveShares(UnloadingSharesVo sharesVo) {
        try {
            unloadingClient.saveShares(sharesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取分佣
     * @param id
     * @return
     */
    public UnloadingSharesVo findSharesById(Long id) {
        try {
            return unloadingClient.findSharesById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除分佣
     * @param id
     */
    public void deleteSharesById(Long id) {
        try {
            unloadingClient.deleteSharesById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingSharesVo> pageShares(UnloadingSharesSearchVo searchVo) {
        try {
            return unloadingClient.pageShares(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 生成应收账单
     * @param monthVo
     */
    public void generateBill(UnloadingMonthVo monthVo) {
        try {
            unloadingClient.generateBill(monthVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量支付应收账单
     * @param ids
     */
    public void batchPayByIds(List<Long> ids) {
        try {
            unloadingClient.batchPayByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 支付应收账单
     * @param searchVo
     */
    public void batchPay(UnloadingOrderBillSearchVo searchVo) {
        try {
            unloadingClient.batchPay(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    public UnloadingOrderBillVo findBillById(Long id) {
        try {
            return unloadingClient.findBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除应收账单
     * @param id
     */
    public void deleteBillById(Long id) {
        try {
            unloadingClient.deleteBillById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 应收账单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderBillVo> pageBill(UnloadingOrderBillSearchVo searchVo) {
        try {
            return unloadingClient.pageBill(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderBillVo sumBill(UnloadingOrderBillSearchVo searchVo) {
        try {
            return unloadingClient.sumBill(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 更新订单应收金额
     * @param orderNo
     * @param amount
     */
    public void updateOrderBill(String orderNo, BigDecimal amount) {
        try {
            unloadingClient.updateOrderBill(orderNo, amount);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 生成应付账单
     * @param monthVo
     */
    public void generateOrderPayoff(UnloadingMonthVo monthVo) {
        try {
            unloadingClient.generateOrderPayoff(monthVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询应付记录
     * @param id
     * @return
     */
    public UnloadingOrderPayoffVo findPayoffById(Long id) {
        try {
            return unloadingClient.findPayoffById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据订单号删除应付记录
     * @param orderNo
     */
    public void deletePayoffByOrderNo(String orderNo) {
        try {
            unloadingClient.deletePayoffByOrderNo(orderNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新应付金额
     * @param payoffVoList
     */
    public void updateSharesAmount(List<UnloadingOrderPayoffVo> payoffVoList) {
        try {
            unloadingClient.updateSharesAmount(payoffVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 应付分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderPayoffVo> pageOrderPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        try {
            return unloadingClient.pagePayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderPayoffVo sumPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        try {
            return unloadingClient.sumPayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据订单编号查询应付记录
     * @param orderNo
     * @return
     */
    public List<UnloadingOrderPayoffVo> listPayoffByOrderNo(String orderNo) {
        try {
            return unloadingClient.listPayoffByOrderNo(orderNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 搬运工应付日信息
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderPayoffVo> payoffDailyList(UnloadingOrderPayoffSearchVo searchVo) {
        try {
            return unloadingClient.payoffDailyList(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据应付月账单id查询应付记录
     * @param monthPayoffId
     * @return
     */
    public List<UnloadingOrderPayoffVo> findByMonthPayoffId(Long monthPayoffId) {
        try {
            return unloadingClient.findByMonthPayoffId(monthPayoffId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 生成月应付账单
     * @param payoffVo
     */
    public void generateMonthPayoff(UnloadingMonthPayoffVo payoffVo) {
        try {
            unloadingClient.generateMonthPayoff(payoffVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新月应付账单备注
     * @param remarkVo
     */
    public void updateRemark(UnloadingMonthPayoffRemarkVo remarkVo) {
        try {
            unloadingClient.updateRemark(remarkVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询月应付账单
     * @param id
     * @return
     */
    public UnloadingOrderMonthPayoffVo findMonthPayoffById(Long id) {
        try {
            return unloadingClient.findMonthPayoffById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除月应付账单
     * @param id
     */
    public void deleteMonthPayoff(Long id) {
        try {
            unloadingClient.deleteMonthPayoff(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 月应付账单批量支付
     * @param ids
     */
    public void batchMonthPayoffByIds(List<Long> ids) {
        try {
            unloadingClient.batchMonthPayoffByIds(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 月应付账单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderMonthPayoffVo> pageMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        try {
            return unloadingClient.pageMonthPayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 月应付账单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderMonthPayoffVo sumMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        try {
            return unloadingClient.sumMonthPayoff(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 查询搬运工情况
     * @param searchVo
     * @return
     */
    public UnloadingDayCountWorkerVo workerStatistics(UnloadingStatisticSearchVo searchVo) {
        try {
            return unloadingClient.workerStatistics(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
