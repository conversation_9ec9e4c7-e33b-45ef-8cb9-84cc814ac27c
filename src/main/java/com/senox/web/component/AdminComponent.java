package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.AdminUserClient;
import com.senox.user.vo.*;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/1/13 11:45
 */
@Component
public class AdminComponent {

    @Lazy
    @Autowired
    private AdminUserClient adminUserClient;

    /**
     * 校验密码
     * @param loginVo
     * @return
     */
    public AdminUserVo checkPassword(AdminUserLoginVo loginVo) {
        try {
            return adminUserClient.checkPassword(loginVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改用户密码
     * @param changePwd
     */
    public void changePassword(AdminUserChangePwdVo changePwd) {
        try {
            adminUserClient.modifyPassword(changePwd);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取收费信息
     * @return
     */
    public TollManSerialVo findAdminToll() {
        try {
            return adminUserClient.findAdminToll();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 修改票据编号
     * @param adminToll
     */
    public void modifyAdminToll(TollManSerialVo adminToll) {
        try {
            adminUserClient.modifyAdminToll(adminToll);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id获取用户
     * @param id
     * @return
     */
    public AdminUserVo findById(Long id) {
        try {
            return adminUserClient.getAdminUser(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 用户列表
     * @param searchVo
     * @return
     */
    public PageResult<AdminUserVo> listAdminUserPage(AdminUserSearchVo searchVo) {
        try {
            return adminUserClient.listAdminUserPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 用户部门列表
     * @param userId
     * @return
     */
    public List<Long> listUserDepartment(Long userId) {
        try {
            return adminUserClient.listUserDepartment(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 用户角色列表
     * @param userId
     * @return
     */
    public List<Long> listUserRole(Long userId) {
        try {
            return adminUserClient.listUserRole(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 用户权限列表
     * @param userId
     * @return
     */
    public List<String> listUserCos(Long userId) {
        try {
            return adminUserClient.listUserCos(userId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 添加用户
     * @param userVo
     * @return
     */
    public Long addAdminUser(AdminUserVo userVo) {
        try {
            return adminUserClient.addAdminUser(userVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新用户
     * @param userVo
     */
    public void updateAdminUser(AdminUserVo userVo) {
        try {
            adminUserClient.updateAdminUser(userVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
