package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.EnterpriseClient;
import com.senox.user.vo.BusinessCategoryVo;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:05
 */
@Component
@RequiredArgsConstructor
public class EnterpriseComponent {

    private final EnterpriseClient enterpriseClient;

    /**
     * 添加经营范围字典
     * @param category
     * @return
     */
    public Long addBusinessCategory(BusinessCategoryVo category) {
        try {
            return enterpriseClient.addBusinessCategory(category);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新经营范围字典
     * @param category
     */
    public void updateBusinessCategory(BusinessCategoryVo category) {
        try {
            enterpriseClient.updateBusinessCategory(category);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除经营范围字典
     * @param id
     */
    public void deleteBusinessCategory(Long id) {
        try {
            enterpriseClient.deleteBusinessCategory(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 经营范围字典列表
     * @return
     */
    public List<BusinessCategoryVo> listBusinessCategory() {
        try {
            return enterpriseClient.listBusinessCategory();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 保存经营户
     * @param enterprise
     * @return
     */
    public Long saveEnterprise(EnterpriseEditVo enterprise) {
        try {
            return enterpriseClient.saveEnterprise(enterprise);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 删除经营户
     * @param enterpriseId
     */
    public void deleteEnterprise(Long enterpriseId) {
        try {
            enterpriseClient.deleteEnterprise(enterpriseId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 设定经营户消防重点场所
     * @param enterpriseIds
     */
    public void saveEnterpriseFirefightingEmphasis(List<Long> enterpriseIds) {
        try {
            enterpriseClient.saveEnterpriseFirefightingEmphasis(enterpriseIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 取消经营户消防重点场所
     * @param enterpriseIds
     */
    public void cancelEnterpriseFirefightingEmphasis(List<Long> enterpriseIds) {
        try {
            enterpriseClient.cancelEnterpriseFirefightingEmphasis(enterpriseIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找经营户
     * @param enterpriseId
     * @return
     */
    public EnterpriseViewVo findEnterpriseById(Long enterpriseId) {
        try {
            return enterpriseClient.findEnterpriseById(enterpriseId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return null;
    }

    /**
     * 经营户列表
     * @param search
     * @return
     */
    public List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search) {
        try {
            return enterpriseClient.listEnterprise(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return Collections.emptyList();
    }

    /**
     * 经营户列表页
     * @param search
     * @return
     */
    public PageResult<EnterpriseViewVo> listEnterprisePage(EnterpriseSearchVo search) {
        try {
            return enterpriseClient.listEnterprisePage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }
}
