package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.AdminRemoteAccessClient;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/5/26 15:04
 */
@Component
public class AdminRemoteAccessComponent {

    @Autowired
    private AdminRemoteAccessClient remoteAccessClient;

    /**
     * 添加远程权限
     *
     * @param remoteAccessVo
     * @return
     */
    public Long addRemoteAccess(AdminRemoteAccessVo remoteAccessVo) {
        try {
            return remoteAccessClient.addRemoteAccess(remoteAccessVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 删除远程权限
     *
     * @param id
     */
    public void deleteRemoteAccess(Long id) {
        try {
            remoteAccessClient.deleteRemoteAccess(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取远程权限
     *
     * @param id
     * @return
     */
    public AdminRemoteAccessVo findRemoteAccessById(Long id) {
        try {
            return remoteAccessClient.findRemoteAccessById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 远程权限列表
     *
     * @param search
     * @return
     */
    public PageResult<AdminRemoteAccessVo> remoteAccessList(AdminRemoteAccessSearchVo search) {
        try {
            return remoteAccessClient.remoteAccessList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
