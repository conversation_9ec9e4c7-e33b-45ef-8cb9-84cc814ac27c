package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.ReceiptClient;
import com.senox.pm.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 17:15
 */
@RequiredArgsConstructor
@Component
public class ReceiptComponent {

    private final ReceiptClient receiptClient;

    /**
     * 添加发票抬头
     * @param header
     * @return
     */
    public Long addTaxHeader(TaxHeaderVo header) {
        try {
            return receiptClient.addTaxHeader(header);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新发票抬头
     * @param header
     */
    public void updateTaxHeader(TaxHeaderVo header) {
        try {
            receiptClient.updateTaxHeader(header);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除发票抬头
     * @param headerBatch
     */
    public void delTaxHeader(TaxHeaderBatchVo headerBatch) {
        try {
            receiptClient.delTaxHeader(headerBatch);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找发票抬头
     * @param id
     * @return
     */
    public TaxHeaderVo findTaxHeaderById(Long id) {
        try {
            return receiptClient.getTaxHeader(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 发票抬头列表
     * @param search
     * @return
     */
    public List<TaxHeaderVo> listTaxHeader(TaxHeaderSearchVo search) {
        try {
            return receiptClient.listTaxHeader(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
