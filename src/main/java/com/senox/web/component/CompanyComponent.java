package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.CompanyClient;
import com.senox.user.vo.CompanyVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:45
 */
@Component
public class CompanyComponent {

    @Autowired
    private CompanyClient companyClient;

    /**
     * 企业列表
     * @return
     */
    public List<CompanyVo> listCompany() {
        try {
            return companyClient.listCompany();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据id获取企业信息
     * @param id
     * @return
     */
    public CompanyVo findById(Long id) {
        try {
            return companyClient.getCompany(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加企业
     * @param company
     * @return
     */
    public Long addCompany(CompanyVo company) {
        try {
            return companyClient.addCompany(company);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新企业
     * @param company
     */
    public void updateCompany(CompanyVo company) {
        try {
            companyClient.updateCompany(company);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
