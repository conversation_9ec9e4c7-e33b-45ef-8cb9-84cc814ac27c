package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.BicycleChargesClient;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-19
 */
@RequiredArgsConstructor
@Component
public class BicycleChargesComponent {
    private final BicycleChargesClient chargesClient;

    /**
     * 添加收费标准
     *
     * @param chargesVo 收费标准
     */
    public void addCharges(BicycleChargesVo chargesVo) {
        try {
            chargesClient.addCharges(chargesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查询收费标准
     *
     * @param chargesId 收费标准id
     */
    public BicycleChargesVo getChargesById(Long chargesId) {
        try {
            return chargesClient.getChargesById(chargesId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 删除收费标准
     *
     * @param chargesId 收费标准id
     */
    public void deleteCharges(Long chargesId) {
        try {
            chargesClient.deleteCharges(chargesId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }


    /**
     * 修改收费标准
     *
     * @param chargesVo 收费标准
     */
    public void updateCharges(BicycleChargesVo chargesVo) {
        try {
            chargesClient.updateCharges(chargesVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 收费标准分页列表
     *
     * @param searchVo 查询参数
     * @return 分页结果
     */
    public PageResult<BicycleChargesVo> chargesListPage(BicycleChargesSearchVo searchVo) {
        try {
            return chargesClient.listPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 添加收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    public void addChargesDetail(BicycleChargesDetailVo chargesDetailVo) {
        try {
            chargesClient.addChargesDetail(chargesDetailVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 修改收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    public void updateChargesDetail(BicycleChargesDetailVo chargesDetailVo) {
        try {
            chargesClient.updateChargesDetail(chargesDetailVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除收费标准明细
     *
     * @param chargesDetailIds 收费标准明细id列表
     */
    public void deleteChargesDetail(List<Long> chargesDetailIds) {
        try {
            chargesClient.deleteChargesDetail(chargesDetailIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据收费标准查询明细
     *
     * @param chargesId 收费标准id
     * @return 查询到的明细列表
     */
    public List<BicycleChargesDetailVo> listDetailByCharges(Long chargesId) {
        try {
            return chargesClient.listDetailByCharges(chargesId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

}
