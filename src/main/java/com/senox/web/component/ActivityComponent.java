package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.ActivityClient;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:47
 */
@Component
@RequiredArgsConstructor
public class ActivityComponent {

    private final ActivityClient activityClient;

    /**
     * 添加活动
     * @param activityVo
     * @return
     */
    public Long addActivity(ActivityVo activityVo) {
        try {
            return activityClient.addActivity(activityVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return 0L;
    }

    /**
     * 更新活动
     * @param activityVo
     */
    public void updateActivity(ActivityVo activityVo) {
        try {
            activityClient.updateActivity(activityVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 生成活动链接
     * @param id
     * @return
     */
    public String generateActivityUrl(Long id) {
        try {
            return activityClient.generateActivityUrl(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据id获取活动
     * @param id
     * @return
     */
    public ActivityVo findActivityById(Long id) {
        try {
            return activityClient.findActivityById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id删除活动
     * @param id
     */
    public void deleteActivityById(Long id) {
        try {
            activityClient.deleteActivityById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 活动分页
     * @param searchVo
     * @return
     */
    public PageResult<ActivityVo> pageActivityResult(ActivitySearchVo searchVo) {
        try {
            return activityClient.pageActivityResult(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
