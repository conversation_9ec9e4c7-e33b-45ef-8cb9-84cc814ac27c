package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.clients.LogisticTransportClient;
import com.senox.tms.vo.*;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Component
@RequiredArgsConstructor
public class LogisticTransportComponent {
    private final LogisticTransportClient transportClient;

    /**
     * 添加订单
     *
     * @param orders 订单集
     */
    public void addOrder(List<LogisticTransportOrderVo> orders) {
        try {
            transportClient.addOrder(orders);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 更新订单
     *
     * @param orders 订单集
     */
    public void updateOrder(List<LogisticTransportOrderVo> orders) {
        try {
            transportClient.updateOrder(orders);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 订单审核
     *
     * @param orderAudit 订单审核参数
     */
    public void auditOrder(LogisticTransportOrderAuditVo orderAudit) {
        try {
            transportClient.auditOrder(orderAudit);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 根据id查找订单
     *
     * @param orderId 订单id
     * @return 返回订单
     */
    public LogisticTransportOrderVo findOrderById(Long orderId) {
        try {
            return transportClient.findOrderById(orderId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据编号查找订单
     *
     * @param orderSerialNo 订单编号
     * @return 返回订单
     */
    public LogisticTransportOrderVo findOrderBySerialNo(String orderSerialNo) {
        try {
            return transportClient.findOrderBySerialNo(orderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 根据编号删除订单
     *
     * @param orderSerialNo 订单编号
     */
    public void deleteOrderBySerialNo(String orderSerialNo) {
        try {
            transportClient.deleteOrderBySerialNo(orderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 订单列表
     *
     * @param search 查询参数
     * @return 返回分页列表
     */
    public List<LogisticTransportOrderVo> orderList(LogisticTransportOrderSearchVo search) {
        try {
            return transportClient.orderList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 订单分页列表
     *
     * @param search 查询参数
     * @return 返回分页列表
     */
    public PageResult<LogisticTransportOrderVo> orderPageList(LogisticTransportOrderSearchVo search) {
        try {
            return transportClient.orderPageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 订单统计
     * @param search 查询参数
     * @return 返回订单统计
     */
    public LogisticTransportOrderStatisticsVo orderStatistics(LogisticTransportOrderSearchVo search) {
        try {
            return transportClient.orderStatistics(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 生成账单结算单
     *
     * @param billGenerate – 账单生成参数
     */
    public List<Long> generateBillSettlement(LogisticTransportBillGenerateVo billGenerate) {
        try {
            transportClient.generateBillSettlement(billGenerate);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 账单列表
     *
     * @param search 查询参数
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billList(LogisticTransportBillSearchVo search) {
        try {
            return transportClient.billList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据账单id获取账单列表
     *
     * @param billIds 账单id
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billListById(List<Long> billIds) {
        try {
            return transportClient.billListById(billIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据订单编号查询账单
     *
     * @param orderSerialNo 订单编号
     * @return 返回账单
     */
    public LogisticTransportBillVo findByOrderSerialNo(String orderSerialNo) {
        try {
            return transportClient.findByOrderSerialNo(orderSerialNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据账单id删除账单
     *
     * @param billId 账单id
     */
    public void billDeleteById(Long billId) {
        try {
            transportClient.billDeleteById(billId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 账单分页列表
     *
     * @param search 查询参数
     * @return 返回账单分页列表
     */
    public PageResult<LogisticTransportBillVo> billPageList(LogisticTransportBillSearchVo search) {
        try {
            return transportClient.billPageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 账单结算列表
     *
     * @param search 查询参数
     * @return 返回账单结算列表
     */
    public List<LogisticTransportBillSettlementVo> billSettlementList(LogisticTransportBillSettlementSearchVo search) {
        try {
            return transportClient.billSettlementList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 账单结算分页列表
     *
     * @param search 查询参数
     * @return 返回账单结算分页列表
     */
    public PageResult<LogisticTransportBillSettlementVo> billSettlementPageList(LogisticTransportBillSettlementSearchVo search) {
        try {
            return transportClient.billSettlementPageList(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }

    /**
     * 结算单统计
     * @param search 查询参数
     * @return 返回结算单统计
     */
    public LogisticTransportBillSettlementStatisticsVo statisticsSettlement(LogisticTransportBillSettlementSearchVo search) {
        try {
            return transportClient.statisticsSettlement(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 结算单支付结果
     *
     * @param billPaid 账单支付信息
     */
    public void billSettlementStatus(BillPaidVo billPaid) {
        try {
            transportClient.billSettlementStatus(billPaid);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 结算单下发
     *
     * @param send 下发参数
     */
    public void billSettlementSend(LogisticTransportBillSettlementSendVo send) {
        try {
            transportClient.billSettlementSend(send);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 结算单删除
     *
     * @param ids id集
     */
    public void billSettlementDeleteById(List<Long> ids) {
        try {
            transportClient.billSettlementDeleteById(ids);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 结算单详情
     *
     * @param settlementId 结算单id
     * @return 返回结算单详情
     */
    public LogisticTransportBillSettlementDetailVo billSettlementDetail(Long settlementId) {
        try {
            return transportClient.billSettlementDetail(settlementId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据id查找结算单
     *
     * @param settlementId 结算单id
     * @return 返回查找到的结算单
     */
    public LogisticTransportBillSettlementVo billSettlementFindById(Long settlementId) {
        try {
           return transportClient.billSettlementFindById(settlementId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
