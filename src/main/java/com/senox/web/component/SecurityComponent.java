package com.senox.web.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.clients.SecurityClient;
import com.senox.realty.constant.SecurityEvent;
import com.senox.realty.vo.SecurityJournalSearchVo;
import com.senox.realty.vo.SecurityJournalVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/27 14:40
 */
@RequiredArgsConstructor
@Component
public class SecurityComponent {

    private final SecurityClient securityClient;

    /**
     * 安保日志类别清单
     * @return
     */
    public SecurityEvent[] listSecurityEventTypes() {
        try {
            return securityClient.listSecurityEventTypes();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return new SecurityEvent[0];
    }

    /**
     * 添加安保日志
     * @param journal
     * @return
     */
    public Long addSecurityJournal(SecurityJournalVo journal) {
        try {
            return securityClient.addJournal(journal);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return 0L;
    }

    /**
     * 更新安保日志
     * @param journal
     */
    public void updateSecurityJournal(SecurityJournalVo journal) {
        try {
            securityClient.updateJournal(journal);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除安保日志
     * @param id
     */
    public void deleteSecurityJournal(Long id) {
        try {
            securityClient.deleteJournal(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 获取安保日志
     * @param id
     * @return
     */
    public SecurityJournalVo findSecurityJournalById(Long id) {
        try {
            return securityClient.findJournalById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return null;
    }

    /**
     * 安保日志列表页
     * @param search
     * @return
     */
    public PageResult<SecurityJournalVo> listSecurityJournalPage(SecurityJournalSearchVo search) {
        try {
            return securityClient.listJournalPage(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }

        return PageResult.emptyPage();
    }

}
