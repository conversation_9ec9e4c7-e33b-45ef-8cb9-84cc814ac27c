package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/20 13:49
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class EnterpriseRealtyExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(24)
    @ExcelProperty("企业名")
    private String name;

    @ColumnWidth(30)
    @ExcelProperty("营业执照名称")
    private String fullName;

    @ExcelProperty("负责人")
    private String chargeMan;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(20)
    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty(value = "经营范围")
    private String categoryDesc;

    @ExcelProperty("联系方式1")
    private String contact1;

    @ExcelProperty("联系方式2")
    private String contact2;

    @ExcelProperty("备注")
    private String remark;
}
