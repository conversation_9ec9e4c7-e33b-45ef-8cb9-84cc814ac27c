package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/1 15:04
 */
@Getter
@Setter
public class LogisticStatisticsDayReportExcelVo {

    @ExcelProperty("日期")
    private String reportDate;

    @ExcelProperty("运营部门")
    private String operationsDepartment;

    @ExcelProperty("发货人")
    private String shipper;

    @ExcelProperty("物流单号")
    private String logisticsNo;

    @ExcelProperty("收入类别")
    private String incomeType;

    @ExcelProperty("司机名")
    private String driverName;

    @ExcelProperty("车牌")
    private String carNo;

    @ExcelProperty("是否包车")
    private String charteredBus;

    @ExcelProperty("始发站")
    private String departureStation;

    @ExcelProperty("目的站")
    private String destinationStation;

    @ExcelProperty("件数")
    private BigDecimal pieces;

    @ExcelProperty("装载重量")
    private BigDecimal loadingWeight;

    @ExcelProperty("入库重量")
    private BigDecimal storageWeight;

    @ExcelProperty("体积")
    private BigDecimal volume;

    @ExcelProperty("运费收入金额")
    private BigDecimal freightIncomeAmount;

    @ExcelProperty("收款日期")
    private String paymentTime;

    @ExcelProperty("进仓单号")
    private String warehousingNo;

    @ExcelProperty("进口冻品优惠")
    private BigDecimal frozenGoodsDiscounts;

    @ExcelProperty("备注")
    private String remark;
}
