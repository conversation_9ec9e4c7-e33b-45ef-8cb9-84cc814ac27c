package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/28 16:56
 */
@ApiModel("打印对象")
public class TollPrintVo implements Serializable {

    private static final long serialVersionUID = 800864922032675709L;

    @ApiModelProperty("票据号")
    private String billSerial;

    @ApiModelProperty("通知单号")
    private String notifySerial;

    @ApiModelProperty("收费员")
    private String tollMan;

    @ApiModelProperty("收款时间")
    private LocalDateTime tollTime;

    @ApiModelProperty("付款客户名")
    private String payer;

    @ApiModelProperty("付款客户名2")
    private String payer2;

    @ApiModelProperty("付款方描述")
    private String payerDesc;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("收款单位")
    private String tollUnit;

    @ApiModelProperty("明细")
    private List<TollPrintItemVo> details;


    public String getBillSerial() {
        return billSerial;
    }

    public void setBillSerial(String billSerial) {
        this.billSerial = billSerial;
    }

    public String getNotifySerial() {
        return notifySerial;
    }

    public void setNotifySerial(String notifySerial) {
        this.notifySerial = notifySerial;
    }

    public String getTollMan() {
        return tollMan;
    }

    public void setTollMan(String tollMan) {
        this.tollMan = tollMan;
    }

    public LocalDateTime getTollTime() {
        return tollTime;
    }

    public void setTollTime(LocalDateTime tollTime) {
        this.tollTime = tollTime;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getPayerDesc() {
        return payerDesc;
    }

    public String getPayer2() {
        return payer2;
    }

    public void setPayer2(String payer2) {
        this.payer2 = payer2;
    }

    public void setPayerDesc(String payerDesc) {
        this.payerDesc = payerDesc;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTollUnit() {
        return tollUnit;
    }

    public void setTollUnit(String tollUnit) {
        this.tollUnit = tollUnit;
    }

    public List<TollPrintItemVo> getDetails() {
        return details;
    }

    public void setDetails(List<TollPrintItemVo> details) {
        this.details = details;
    }
}
