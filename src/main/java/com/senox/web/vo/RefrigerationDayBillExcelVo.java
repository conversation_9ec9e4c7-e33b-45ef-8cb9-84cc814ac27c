package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 冷藏日账单 excel 格式
 * <AUTHOR>
 * @date 2022/12/16 10:52
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RefrigerationDayBillExcelVo implements Serializable {

    private static final long serialVersionUID = 2751101062035364843L;

    @ExcelProperty(value = "费用日期", converter = LocalDateExcelConverter.class)
    private LocalDate billDate;

    @ExcelProperty(value = "客户编码")
    private String customerSerial;

    @ExcelProperty(value = "客户名称")
    private String customerName;

    @ExcelProperty(value = "库存费")
    private BigDecimal storageCharge;

    @ExcelProperty(value = "处置费")
    private BigDecimal disposalCharge;

    @ExcelProperty(value = "装卸费")
    private BigDecimal handlingCharge;

    @ExcelProperty(value = "过车费")
    private BigDecimal passingCharge;

    @ExcelProperty(value = "拉膜费")
    private BigDecimal membraneCharge;

    @ExcelProperty(value = "分拣费")
    private BigDecimal sortingCharge;

    @ExcelProperty(value = "加班费")
    private BigDecimal overtimeCharge;

    @ExcelProperty(value = "其他费用")
    private BigDecimal otherCharge;

    @ExcelProperty(value = "配送费用")
    private BigDecimal deliveryCharge;

    @ExcelProperty(value = "滞纳金")
    private BigDecimal penaltyCharge;

    @ExcelProperty(value = "总金额")
    private BigDecimal totalCharge;
}
