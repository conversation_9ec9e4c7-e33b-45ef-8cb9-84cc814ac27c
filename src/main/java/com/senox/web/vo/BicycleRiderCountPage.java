package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/13 15:02
 */
@Getter
@Setter
@ToString
@ApiModel("骑手当日统计页")
public class BicycleRiderCountPage<T> extends PageResult<T> {

    @ApiModelProperty("总单数")
    private Integer totalCount;

    @ApiModelProperty("总收益")
    private BigDecimal totalIncome;

    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    public BicycleRiderCountPage() {
        init();
    }

    public BicycleRiderCountPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.totalIncome = BigDecimal.ZERO;
        this.totalPieces = BigDecimal.ZERO;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> BicycleRiderCountPage<T> emptyPage() {
        return new BicycleRiderCountPage<>();
    }
}
