package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/26 14:44
 */
@ApiModel("费项明细")
@Data
public class MaintainChargeTollPrintItemVo implements Serializable {

    private static final long serialVersionUID = 3564456343028248895L;

    @ApiModelProperty("项目名称")
    private String fee;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("年月")
    private String time;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("物料数量")
    private Integer quantity;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("维修类型")
    private String maintainType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("费项编码")
    private Long feeItemCode;

    @ApiModelProperty("费项名称")
    private String feeItemName;
}
