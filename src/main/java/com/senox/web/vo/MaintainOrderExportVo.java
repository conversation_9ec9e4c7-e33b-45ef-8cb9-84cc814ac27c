package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/1 17:26
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class MaintainOrderExportVo implements Serializable {

    private static final long serialVersionUID = 8254078991575734154L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(14)
    @ExcelProperty("客户名")
    private String customerName;

    @ColumnWidth(20)
    @ExcelProperty("提交时间")
    private LocalDateTime createTime;

    @ColumnWidth(14)
    @ExcelProperty("维修类型")
    private String maintainType;

    @ColumnWidth(20)
    @ExcelProperty("修改时间")
    private LocalDateTime modifiedTime;

    @ColumnWidth(20)
    @ExcelProperty("区域")
    private String regionName;

    @ColumnWidth(20)
    @ExcelProperty("街道")
    private String streetName;

    @ColumnWidth(20)
    @ExcelProperty("地址")
    private String address;

    @ColumnWidth(20)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(20)
    @ExcelProperty("问题描述")
    private String problem;

    @ColumnWidth(14)
    @ExcelProperty("处理人")
    private String handlerName;

    @ColumnWidth(14)
    @ExcelProperty("状态")
    private String status;
}
