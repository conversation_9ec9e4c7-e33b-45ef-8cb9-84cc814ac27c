package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import com.senox.web.convert.PaywayExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/21 9:50
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class AdvertisingTollExcelVo implements Serializable {

    private static final long serialVersionUID = -3033700334992773639L;

    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty(value = "收款日期", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收款人")
    private String tollMan;

    @ExcelProperty("合同编号")
    private String contractNo;

    @ExcelProperty("广告位编号")
    private String spaceSerial;

    @ExcelProperty("广告位名")
    private String spaceName;

    @ExcelProperty("客户名称")
    private String customerName;

    @ExcelProperty(value = "开始时间", converter = LocalDateExcelConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "结束时间", converter = LocalDateExcelConverter.class)
    private LocalDate endDate;

    @ExcelProperty("应收金额")
    private BigDecimal amount;

    @ExcelProperty("实收金额")
    private BigDecimal paidAmount;

    @ExcelProperty(value = "支付方式", converter = PaywayExcelConverter.class)
    private Integer payWay;

    @ExcelProperty(value = "发票", converter = BooleanExcelConvertor.class)
    private Boolean receipt;

    @ExcelProperty(value = "开票时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime receiptTime;

    @ExcelProperty("发票备注")
    private String receiptTitle;
}
