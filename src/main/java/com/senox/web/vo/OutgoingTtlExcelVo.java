package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.senox.web.convert.LocalDateExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/4/8 14:22
 */
@Getter
@Setter
public class OutgoingTtlExcelVo {

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private String serialNo;

    @ColumnWidth(12)
    @ExcelProperty(value = "收货日期", converter = LocalDateExcelConverter.class)
    private LocalDate receivingDate;

    @ColumnWidth(12)
    @ExcelProperty("托运方客户")
    private String shipperName;

    @ColumnWidth(15)
    @ExcelProperty("收货方客户")
    private String recipientName;

    @ColumnWidth(15)
    @ExcelProperty("收货方地址")
    private String recipientAddress;

    @ColumnWidth(15)
    @ExcelProperty("收货方电话")
    private String recipientContact;

    @ColumnWidth(10)
    @ExcelProperty("物流公司")
    private String logisticsCompany;

    @ColumnWidth(12)
    @ExcelProperty("物流单号")
    private String logisticsNo;

    @ColumnWidth(8)
    @ExcelProperty("托运方件数")
    private Integer shipperPieces;

    @ColumnWidth(10)
    @ExcelProperty("实收运费")
    private BigDecimal actualFreightAmount;

    @ColumnWidth(10)
    @ExcelProperty("实发运费")
    private BigDecimal actualShippingAmount;

    @ColumnWidth(8)
    @ExcelProperty("利润")
    private BigDecimal profitAmount;

    @ColumnWidth(8)
    @ExcelProperty("结算类型")
    private String settlementType;
}
