package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-07-04
 **/
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class CloudWarehousingBilLExportVo {

    /**
     * 账单日期
     */
    @ExcelProperty("账单日期")
    private String billYearMonth;

    /**
     * 客户编号
     */
    @ExcelProperty("客户编号")
    private String rcSerial;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String name;

    /**
     * 退转服务费
     */
    @ExcelProperty("退转服务费")
    private BigDecimal refundServiceCharge;

    /**
     * 租金
     */
    @ExcelProperty("租金")
    private BigDecimal rentCharge;

    /**
     * 配送费
     */
    @ExcelProperty("配送费")
    private BigDecimal deliveryCharge;

    /**
     * 技术服务费
     */
    @ExcelProperty("技术服务费")
    private BigDecimal technicalServiceCharge;

    /**
     * 中班补货
     */
    @ExcelProperty("中班补货费")
    private BigDecimal midReplenishmentCharge;

    /**
     * 尾波补货费
     */
    @ExcelProperty("尾波补货费")
    private BigDecimal codaReplenishmentCharge;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收滞纳金
     */
    @ExcelProperty("应收滞纳金")
    private BigDecimal penaltyAmount;

    /**
     * 实收滞纳金
     */
    @ExcelProperty("实收滞纳金")
    private BigDecimal penaltyPaidAmount;

    /**
     * 应收金额
     */
    @ExcelProperty("应收金额")
    private BigDecimal totalAmount;

    /**
     * 实收金额
     */
    @ExcelProperty("实收金额")
    private BigDecimal paidAmount;

    /**
     * 支付状态
     */
    @ExcelProperty("支付状态")
    private String paidStatus;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payWay;

    /**
     * 支付时间
     */
    @ExcelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

}
