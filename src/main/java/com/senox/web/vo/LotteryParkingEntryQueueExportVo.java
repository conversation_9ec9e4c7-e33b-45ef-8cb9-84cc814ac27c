package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/15 14:59
 */
@ColumnWidth(16)
@ContentRowHeight(16)
@Data
public class LotteryParkingEntryQueueExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private String serialNo;

    @ExcelProperty("车牌")
    private String occupiedNo;

    @ExcelProperty("子车牌")
    private String subCarNo;

    @ExcelProperty("联系方式")
    private String contact;

    @ExcelProperty(value = "收费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("入场费")
    private BigDecimal entryAmount;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("支付方式")
    private String payWay;

}
