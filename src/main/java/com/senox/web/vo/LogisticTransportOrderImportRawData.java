package com.senox.web.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-06-05
 **/
@Getter
@Setter
public class LogisticTransportOrderImportRawData {
    private String column1;
    private String column2;
    private String column3;
    private String column4;
    private String column5;
    private String column6;
    private String column7;
    private String column8;
    private String column9;
    private String column10;
    private String column11;
    private String column12;
    private String column13;
    private String column14;
    private String column15;
    private String column16;
    private String column17;
    private String column18;
    private String column19;
    private String column20;
    private String column21;
    private String column22;
    private String column23;

    public boolean isEmpty() {
        return  null == column5
                && null == column7
                && null == column8
                && null == column9
                && null == column10
                && null == column11
                && null == column12
                && null == column13
                && null == column14
                && null == column15
                && null == column16
                && null == column17
                && null == column18
                && null == column19;
    }
}
