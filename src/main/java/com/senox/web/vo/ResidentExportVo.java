package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-03 16:30
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class ResidentExportVo implements Serializable {

    private static final long serialVersionUID = 3596793600032903257L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("出生日期")
    private String bornDate;

    @ExcelProperty("民族")
    private String nature;

    @ExcelProperty("性别")
    private String gender;

    @ExcelProperty("住址")
    private String address;

    @ExcelProperty("电话号码")
    private String telephone;

    @ExcelProperty("住户类型")
    private String residentType;

    @ExcelProperty("上一次扫脸时间")
    private LocalDateTime lastScanTime;
}
