package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/8 9:19
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class MerchantExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("商户名")
    private String name;

    @ExcelProperty("联系方式")
    private String contact;

    @ExcelProperty("身份证")
    private String idcard;

    @ExcelProperty("通讯地址")
    private String address;

    @ExcelProperty("冷藏客户编号")
    private String rcSerial;

    @ExcelProperty("冷藏抬头")
    private String rcTaxHeader;

    @ExcelProperty("结算周期")
    private String settlePeriod;

    @ExcelProperty("三轮车权限")
    private String bicycleAuth;

    @ExcelProperty("三轮车收费标准")
    private String bicycleCharges;

    @ExcelProperty("干仓权限")
    private String dryAuth;

    @ExcelProperty("鹏翔权限")
    private String unloadingAuth;

    @ExcelProperty("冷藏权限")
    private String rcAuth;
}
