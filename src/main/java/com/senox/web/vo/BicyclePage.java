package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/16 14:02
 */
@Getter
@Setter
@ToString
@ApiModel("三轮车报表页")
public class BicyclePage<T> extends PageResult<T> {

    @ApiModelProperty("总单数")
    private Integer totalCount;

    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    public BicyclePage() {
        init();
    }

    public BicyclePage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.totalCount = 0;
        this.totalPieces = BigDecimal.ZERO;
        this.deliveryCharge = BigDecimal.ZERO;
        this.otherCharge = BigDecimal.ZERO;
        this.handlingCharge = BigDecimal.ZERO;
        this.upstairsCharge = BigDecimal.ZERO;
        this.totalCharge = BigDecimal.ZERO;
    }

    public static <T> BicyclePage<T> emptyPage() {
        return new BicyclePage<>();
    }
}
