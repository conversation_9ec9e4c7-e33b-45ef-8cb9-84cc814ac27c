package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/28 8:52
 */
@Data
@ColumnWidth(16)
@ContentRowHeight(20)
public class ReservationRecordExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(10)
    @ExcelProperty("预约类型")
    private String type;

    @ColumnWidth(14)
    @ExcelProperty("访客名字")
    private String visitorName;

    @ColumnWidth(14)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(14)
    @ExcelProperty("随行人数")
    private Integer togetherNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "拜访时间起", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime visitTimeStart;

    @ColumnWidth(20)
    @ExcelProperty(value = "拜访时间止", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime visitTimeEnd;

    @ColumnWidth(28)
    @ExcelProperty("车牌号")
    private String carNo;
}
