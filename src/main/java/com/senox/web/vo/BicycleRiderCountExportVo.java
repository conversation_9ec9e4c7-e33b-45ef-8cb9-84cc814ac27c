package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/3/22 11:56
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class BicycleRiderCountExportVo implements Serializable {

    private static final long serialVersionUID = 4844655230182588304L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(14)
    @ExcelProperty("日期")
    private String billDate;

    @ColumnWidth(14)
    @ExcelProperty("骑手姓名")
    private String riderName;

    @ColumnWidth(14)
    @ExcelProperty("完成单量")
    private Integer todayCount;

    @ColumnWidth(14)
    @ExcelProperty("平均用时")
    private String avgDeliveryTime;

    @ColumnWidth(14)
    @ExcelProperty("配送件数")
    private BigDecimal todayPieces;

    @ColumnWidth(14)
    @ExcelProperty("收益")
    private BigDecimal income;
}
