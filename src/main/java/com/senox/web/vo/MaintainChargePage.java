package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/21 9:42
 */
@Getter
@Setter
@ToString
@ApiModel("物维账单页")
public class MaintainChargePage<T> extends PageResult<T> {

    @ApiModelProperty("人工费合计")
    private BigDecimal laborTotalAmount;

    @ApiModelProperty("物料费合计")
    private BigDecimal materialTotalAmount;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    public MaintainChargePage() {
        init();
    }

    public MaintainChargePage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.laborTotalAmount = BigDecimal.ZERO;
        this.materialTotalAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }


    public BigDecimal getLaborTotalAmount() {
        return laborTotalAmount;
    }

    public void setLaborTotalAmount(BigDecimal laborTotalAmount) {
        this.laborTotalAmount = laborTotalAmount;
    }

    public BigDecimal getMaterialTotalAmount() {
        return materialTotalAmount;
    }

    public void setMaterialTotalAmount(BigDecimal materialTotalAmount) {
        this.materialTotalAmount = materialTotalAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> MaintainChargePage<T> emptyPage() {
        return new MaintainChargePage<>();
    }
}
