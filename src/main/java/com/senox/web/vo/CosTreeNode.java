package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/28 11:18
 */
@ApiModel("权限项节点")
public class CosTreeNode implements Serializable {

    private static final long serialVersionUID = 8564189587873700081L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("权限项代码")
    private String name;

    @ApiModelProperty("权限项名称")
    private String displayName;

    @ApiModelProperty("子节点")
    private List<CosTreeNode> childNodes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public List<CosTreeNode> getChildNodes() {
        return childNodes;
    }

    public void setChildNodes(List<CosTreeNode> childNodes) {
        this.childNodes = childNodes;
    }
}
