package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/10 15:50
 */
@ApiModel("凭证绑定")
@Data
public class ParkingActivityCredentialVo implements Serializable {

    /**
     * 设备信息
     */
    @ApiModelProperty("设备信息")
    private String deviceId;

    /**
     * 链接编码
     */
    @ApiModelProperty("链接编码")
    private String code;

    /**
     * 验证时间
     */
    @ApiModelProperty("验证时间")
    private LocalDateTime currentTime;
}
