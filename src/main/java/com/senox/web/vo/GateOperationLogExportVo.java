package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/18 11:24
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class GateOperationLogExportVo implements Serializable {

    private static final long serialVersionUID = -641121060777120984L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(30)
    @ExcelProperty("设备名")
    private String deviceName;

    @ColumnWidth(20)
    @ExcelProperty("设备ip")
    private String deviceIp;

    @ColumnWidth(30)
    @ExcelProperty(value = "开门时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime opTime1;

    @ColumnWidth(30)
    @ExcelProperty(value = "关门时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime opTime2;

    @ColumnWidth(20)
    @ExcelProperty("操作时长（秒）")
    private Integer opDuration;

    @ExcelProperty(value = "超时", converter = BooleanExcelConvertor.class)
    private Boolean overTime;

    @ExcelProperty(value = "误报", converter = BooleanExcelConvertor.class)
    private Boolean falseAlarm;
}
