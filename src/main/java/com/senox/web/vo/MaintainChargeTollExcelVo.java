package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/21 9:50
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class MaintainChargeTollExcelVo implements Serializable {

    private static final long serialVersionUID = 1943268410366596231L;

    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty(value = "收款日期", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收款人")
    private String tollMan;

    @ExcelProperty("物维账单年")
    private Integer chargeYear;

    @ExcelProperty("物维账单月")
    private Integer chargeMonth;

    @ExcelProperty("客户名")
    private String customerName;

    @ExcelProperty("维修地址")
    private String address;

    @ExcelProperty("维修类型")
    private String maintainType;

    @ExcelProperty("支付方式")
    private String payWay;

    @ExcelProperty("人工费")
    private BigDecimal laborAmount;

    @ExcelProperty("物料费")
    private BigDecimal materialAmount;

    @ExcelProperty("合计")
    private BigDecimal totalAmount;
}
