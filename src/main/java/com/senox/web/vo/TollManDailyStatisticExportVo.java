package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.NullToZeroDecimalConvertor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/2/29 15:32
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class TollManDailyStatisticExportVo implements Serializable {

    private static final long serialVersionUID = -891568724041654706L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ExcelProperty(value = "收费日期", converter = LocalDateExcelConverter.class)
    private LocalDate tollDate;

    @ExcelProperty("收费员")
    private String tollMan;

    @ExcelProperty(value = "物业收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal realtyAmount;

    @ExcelProperty(value = "冷藏收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal refrigerationAmount;

    @ExcelProperty(value = "广告收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal advertisingAmount;

    @ExcelProperty(value = "押金收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal depositAmount;

    @ExcelProperty(value = "押金退费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal depositRefund;

    @ExcelProperty(value = "一次性收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal oneTimeFeeAmount;

    @ExcelProperty(value = "一次性退费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal oneTimeFeeRefund;

    @ExcelProperty(value = "园区月卡停车现金收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal marketMonthParkingCashAmount;

    @ExcelProperty(value = "云仓收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cloudWarehousingAmount;

    @ExcelProperty(value = "干仓收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal normalTemperatureWarehousingAmount;

    @ExcelProperty(value = "三轮车车牌费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cycleAmount;

    @ExcelProperty(value = "珠三角收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal logisticFreightAmount;

    @ExcelProperty(value = "城际运输收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal logisticTransportAmount;

    @ExcelProperty(value = "收费合计", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal totalAmount;
}
