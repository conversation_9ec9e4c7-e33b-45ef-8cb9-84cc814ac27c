package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/1 10:58
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class BicyclePayoffExportVo implements Serializable {
    private static final long serialVersionUID = 735368393160803392L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(20)
    @ExcelProperty("日期")
    private LocalDateTime createTime;

    @ColumnWidth(16)
    @ExcelProperty("订单流水号")
    private String orderSerialNo;

    @ColumnWidth(16)
    @ExcelProperty("商户名")
    private String merchantName;

    @ColumnWidth(14)
    @ExcelProperty("骑手名称")
    private String payeeName;

    @ColumnWidth(14)
    @ExcelProperty("订单总件数")
    private BigDecimal orderTotalPieces;

    @ColumnWidth(14)
    @ExcelProperty("订单总金额")
    private BigDecimal orderTotalCharge;

    @ColumnWidth(8)
    @ExcelProperty("是否推荐费")
    private String referral;

    @ColumnWidth(10)
    @ExcelProperty("配送件数")
    private Integer pieces;

    @ColumnWidth(14)
    @ExcelProperty("推荐费")
    private BigDecimal referralAmount;

    @ColumnWidth(14)
    @ExcelProperty("分佣金额")
    private BigDecimal shareAmount;
}
