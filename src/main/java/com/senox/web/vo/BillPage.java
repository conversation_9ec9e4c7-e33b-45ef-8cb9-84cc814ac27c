package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/28 16:54
 */
@Getter
@Setter
@ToString
@ApiModel("账单页")
public class BillPage<T> extends PageResult<T> {

    @ApiModelProperty("应收金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("实收金额")
    private BigDecimal paidAmount;

    public BillPage() {
        init();
    }

    public BillPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.totalAmount = BigDecimal.ZERO;
        this.paidAmount = BigDecimal.ZERO;
    }

    public static <T> BillPage<T> emptyPage() {
        return new BillPage<>();
    }
}
