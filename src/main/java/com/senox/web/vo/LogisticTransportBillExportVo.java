package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-05
 **/
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class LogisticTransportBillExportVo {

    /**
     * 收费时间
     */
    @ExcelProperty("收费时间")
    private LocalDateTime paidTime;

    /**
     * 订单编号
     */
    @ExcelProperty("订单编号")
    private String orderSerialNo;

    /**
     * 订单日期
     */
    @ExcelProperty("订单日期")
    private String orderYearMonthDay;

    /**
     * 商户名
     */
    @ExcelProperty("商户名")
    private String merchantName;

    /**
     * 付款人
     */
    @ExcelProperty("付款人")
    private String payer;

    /**
     * 司机
     */
    @ExcelProperty("司机")
    private String driverName;

    /**
     * 车牌
     */
    @ExcelProperty("车牌")
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    @ExcelProperty("是否包车")
    private String charter;

    /**
     * 始发站
     */
    @ExcelProperty("始发站")
    private String departureStation;

    /**
     * 目的站
     */
    @ExcelProperty("目的站")
    private String destinationStation;

    /**
     * 件数
     */
    @ExcelProperty("件数")
    private Integer pieces;

    /**
     * 装载重量
     */
    @ExcelProperty("装载重量")
    private BigDecimal loadingWeight;

    /**
     * 运费
     */
    @ExcelProperty("运费")
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收
     */
    @ExcelProperty("应收")
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payWay;

    /**
     * 实收
     */
    @ExcelProperty("实收")
    private BigDecimal paidAmount;

    /**
     * 未收
     */
    @ExcelProperty("未收")
    private BigDecimal paidStillAmount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}
