package com.senox.web.vo;


import com.alibaba.excel.metadata.CellExtra;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @date 2024-4-18
 */
@Getter
@Setter
public class ExcelAnalysisResult<T> {

    /**
     * 头行数
     */
    private Integer headRowNumber;

    /**
     * excel数据map
     */
    private MultiValueMap<String, T> excelDataMap;

    /**
     * extraMap
     */
    private MultiValueMap<String, CellExtra> extraMap;

    public ExcelAnalysisResult(Integer headRowNumber, MultiValueMap<String, T> excelDataMap, MultiValueMap<String, CellExtra> extraMap) {
        this.headRowNumber = headRowNumber;
        this.excelDataMap = excelDataMap;
        this.extraMap = extraMap;
    }
}
