package com.senox.web.vo;

import com.senox.pm.constant.PayWay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/23 10:40
 */
@ApiModel("订单退款参数")
@Getter
@Setter
public class RefundOrderRequestVo implements Serializable {

    private static final long serialVersionUID = 6976847163114595843L;

    @ApiModelProperty("订单流水号")
    private String tradeNo;

    @ApiModelProperty("支付方式")
    private PayWay payWay;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty(value = "请求IP", hidden = true)
    private String requestIp;
}
