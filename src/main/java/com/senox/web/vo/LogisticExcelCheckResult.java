package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/3 8:11
 */
@Getter
@Setter
@ToString
@ApiModel("物流excel检查结果")
public class LogisticExcelCheckResult implements Serializable {

    @ApiModelProperty("错误数据")
    private Map<Integer, LogisticOrderExcelVo> errorData;

    @ApiModelProperty("重复数据")
    private List<LogisticOrderExcelVo> duplicatedData;


    public LogisticExcelCheckResult() {
    }

    public LogisticExcelCheckResult(Map<Integer, LogisticOrderExcelVo> errorData) {
        this.errorData = errorData;
    }

    public LogisticExcelCheckResult(List<LogisticOrderExcelVo> duplicatedData) {
        this.duplicatedData = duplicatedData;
    }
}
