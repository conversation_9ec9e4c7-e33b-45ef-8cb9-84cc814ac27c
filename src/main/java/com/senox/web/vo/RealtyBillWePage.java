package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:47
 */
@Getter
@Setter
@ToString
@ApiModel("物业水电账单页")
public class RealtyBillWePage<T> extends PageResult<T> {

    @ApiModelProperty("上次水读数")
    private Long lastWaterReadings;

    @ApiModelProperty("本次水读数")
    private Long waterReadings;

    @ApiModelProperty("水实耗")
    private Long waterCost;

    @ApiModelProperty("水公摊")
    private Integer waterShare;

    @ApiModelProperty("水费")
    private BigDecimal waterAmount;

    @ApiModelProperty("上次电读数")
    private Long lastElectricReadings;

    @ApiModelProperty("本次电读数")
    private Long electricReadings;

    @ApiModelProperty("电实耗")
    private Long electricCost;

    @ApiModelProperty("电公摊")
    private Integer electricShare;

    @ApiModelProperty("电费")
    private BigDecimal electricAmount;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;


    public RealtyBillWePage() {
        init();
    }

    public RealtyBillWePage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.lastWaterReadings = 0L;
        this.waterReadings = 0L;
        this.waterCost = 0L;
        this.waterShare = 0;
        this.waterAmount = BigDecimal.ZERO;
        this.lastElectricReadings = 0L;
        this.electricReadings = 0L;
        this.electricCost = 0L;
        this.electricShare = 0;
        this.electricAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> RealtyBillWePage<T> emptyPage() {
        return new RealtyBillWePage<>();
    }

}
