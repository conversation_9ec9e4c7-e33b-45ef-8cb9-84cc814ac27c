package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/16 9:20
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RefrigerationCustomerExcelVo implements Serializable {

    private static final long serialVersionUID = -2705023123984786566L;


    @ExcelProperty("编码")
    private String serialNo;

    @ExcelProperty("客户名")
    private String name;

    @ExcelProperty("身份证")
    private String idcard;

    @ExcelProperty("电话")
    private String contact;
}
