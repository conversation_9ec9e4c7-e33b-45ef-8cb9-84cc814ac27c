package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/3/31 14:20
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class OneTimeFeeBillExportVo implements Serializable {

    private static final long serialVersionUID = -3804732603715270268L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ColumnWidth(12)
    @ExcelProperty(value = "录入日期", converter = LocalDateExcelConverter.class)
    private LocalDate operateDate;

    @ColumnWidth(20)
    @ExcelProperty("收费项目")
    private String fee;

    @ExcelProperty("客户编号")
    private String customerSerial;

    @ColumnWidth(20)
    @ExcelProperty("客户")
    private String customer;

    @ColumnWidth(12)
    @ExcelProperty("金额")
    private BigDecimal amount;

    @ColumnWidth(12)
    @ExcelProperty("退费金额")
    private BigDecimal refundAmount;

    @ColumnWidth(24)
    @ExcelProperty("备注")
    private String remark;

    @ColumnWidth(12)
    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(20)
    @ExcelProperty("物业名")
    private String realtyName;

    @ColumnWidth(8)
    @ExcelProperty("年份")
    private Integer billYear;

    @ColumnWidth(8)
    @ExcelProperty("月份")
    private Integer billMonth;

    @ColumnWidth(12)
    @ExcelProperty("录入员")
    private String creator;

    @ExcelProperty("部门")
    private String department;

    @ColumnWidth(12)
    @ExcelProperty("票据号")
    private String tollSerial;

    @ColumnWidth(24)
    @ExcelProperty(value = "收费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime tollTime;

    @ColumnWidth(12)
    @ExcelProperty("退费人")
    private String refundMan;

    @ColumnWidth(24)
    @ExcelProperty(value = "退费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime refundTime;

    @ColumnWidth(12)
    @ExcelProperty("退费单号")
    private String refundSerial;

    @ColumnWidth(12)
    @ApiModelProperty("支付状态")
    private String status;

}
