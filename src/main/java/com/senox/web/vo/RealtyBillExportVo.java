package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/9/15 16:05
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyBillExportVo implements Serializable {

    private static final long serialVersionUID = -1445580320500651157L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("合同号")
    private String contractNo;

    @ExcelProperty("物业号")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty("物业区域1")
    private String realtyRegion1;

    @ExcelProperty("物业区域2")
    private String realtyRegion2;

    @ExcelProperty("档主")
    private String realtyOwnerName;

    @ExcelProperty("账单年份")
    private Integer billYear;

    @ExcelProperty("账单月份")
    private Integer billMonth;

    @ExcelProperty("应收金额")
    private BigDecimal totalAmount;

    @ExcelProperty("实收金额")
    private BigDecimal paidAmount;

    @ExcelProperty("待收金额")
    private BigDecimal paidStillAmount;

    @ExcelProperty("管理费")
    private BigDecimal manageAmount;

    @ExcelProperty("租金")
    private BigDecimal rentAmount;

    @ExcelProperty("水费")
    private BigDecimal waterAmount;

    @ExcelProperty("电费")
    private BigDecimal electricAmount;

    @ExcelProperty("应收滞纳金")
    private BigDecimal penaltyAmount;

    @ExcelProperty("实收滞纳金")
    private BigDecimal penaltyPaidAmount;

    @ExcelProperty("退费金额")
    private BigDecimal refundAmount;

    @ExcelProperty("缴费状态")
    private String paidStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "支付时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty(value = "返租", converter = BooleanExcelConvertor.class)
    private Boolean backLease;

    @ExcelProperty(value = "代租", converter = BooleanExcelConvertor.class)
    private Boolean replaceLease;

    @ExcelProperty(value = "代收租", converter = BooleanExcelConvertor.class)
    private Boolean collectionLease;

    @ExcelProperty("客户名")
    private String customerName;

    @ExcelProperty("客户联系方式")
    private String customerContact;

    @ExcelProperty("客户相联地址")
    private String customerAddress;

    @ExcelProperty("客户公司统一代码")
    private String customerEnterpriseCode;

    @ExcelProperty("客户公司法人")
    private String customerLegalPerson;

    @ExcelProperty("客户身份证")
    private String customerIdCard;

    @ExcelProperty("业主名")
    private String ownerName;

    @ExcelProperty("业主联系方式")
    private String ownerContact;

    @ExcelProperty("业主身份证")
    private String ownerIdCard;

    @ExcelProperty("担保物业")
    private String guaranteeRealty;
}
