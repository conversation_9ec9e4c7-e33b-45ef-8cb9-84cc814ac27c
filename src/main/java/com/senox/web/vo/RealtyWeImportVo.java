package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/2 12:01
 */
@Getter
@Setter
@ToString
@ApiModel("水电上传参数")
public class RealtyWeImportVo implements Serializable {

    private static final long serialVersionUID = 9102441821995078841L;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("覆盖，默认是")
    private Boolean isOverWrite;
}
