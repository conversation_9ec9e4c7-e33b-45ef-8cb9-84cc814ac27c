package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/13 15:14
 */
@Getter
@Setter
@ToString
@ApiModel("客户当日统计页")
public class BicycleCustomerCountPage<T> extends PageResult<T> {

    @ApiModelProperty("总单数")
    private Integer totalCount;

    @ApiModelProperty("总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    public BicycleCustomerCountPage() {
        init();
    }

    public BicycleCustomerCountPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.totalCost = BigDecimal.ZERO;
        this.totalPieces = BigDecimal.ZERO;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> BicycleCustomerCountPage<T> emptyPage() {
        return new BicycleCustomerCountPage<>();
    }
}
