package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:45
 */
@Getter
@Setter
@ToString
@ApiModel("账单优惠信息")
public class BillDiscountVo implements Serializable {

    private static final long serialVersionUID = -6570262803445169836L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("坏账核销金额")
    private BigDecimal badDebtAmount;
}
