package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/28 10:36
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyBillReceiptExportVo implements Serializable {

    private static final long serialVersionUID = 2765554814891732876L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty(value = "物业号")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty(value = "物业名")
    private String realtyName;

    @ExcelProperty(value = "返租", converter = BooleanExcelConvertor.class)
    private Boolean backLease;

    @ExcelProperty(value = "代租", converter = BooleanExcelConvertor.class)
    private Boolean replaceLease;

    @ExcelProperty(value = "代收租", converter = BooleanExcelConvertor.class)
    private Boolean collectionLease;

    @ExcelProperty("档主")
    private String realtyOwnerName;

    @ExcelProperty("管理费")
    private BigDecimal manageAmount;

    @ExcelProperty("租金")
    private BigDecimal rentAmount;

    @ExcelProperty("水费")
    private BigDecimal waterAmount;

    @ExcelProperty("电费")
    private BigDecimal electricAmount;

    @ExcelProperty("滞纳金")
    private BigDecimal penaltyPaidAmount;

    @ExcelProperty("合计")
    private BigDecimal paidAmount;

    @ExcelProperty("开票员")
    private String receiptMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "开票时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime receiptTime;

    @ExcelProperty("发票备注")
    private String receiptRemark;
}
