package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.senox.web.convert.NullToZeroIntConvertor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/26 11:40
 */
@Data
public class SkuDataRead {

    @ExcelProperty(value = {"", "", "客户"}, index = 0)
    private String customerName;
    @ExcelProperty(value = {"", "", "商品名称"}, index = 1)
    private String skuName;
    @ExcelProperty(value = {"", "", "商品代码"}, index = 2)
    private String skuCode;
    @ExcelProperty(value = {"东莞仓", "一、送货", "送货份数"}, index = 3, converter = NullToZeroIntConvertor.class)
    private Integer dgSendNumber;
    @ExcelProperty(value = {"东莞仓", "一、送货", "送货件数"}, index = 4, converter = NullToZeroIntConvertor.class)
    private Integer dgSendPiece;
    @ExcelProperty(value = {"东莞仓", "二、退货", "退转入"}, index = 5, converter = NullToZeroIntConvertor.class)
    private Integer dgReturn;
    @ExcelProperty(value = {"东莞仓", "二、退货", "回云仓"}, index = 6, converter = NullToZeroIntConvertor.class)
    private Integer dgReturnWarehouse;
    @ExcelProperty(value = {"水濂1仓", "一、送货", "送货份数"}, index = 7, converter = NullToZeroIntConvertor.class)
    private Integer sl1SendNumber;
    @ExcelProperty(value = {"水濂1仓", "一、送货", "送货件数"}, index = 8, converter = NullToZeroIntConvertor.class)
    private Integer sl1SendPiece;
    @ExcelProperty(value = {"水濂1仓", "二、退货", "退转入"}, index = 9, converter = NullToZeroIntConvertor.class)
    private Integer sl1Return;
    @ExcelProperty(value = {"水濂1仓", "二、退货", "回云仓"}, index = 10, converter = NullToZeroIntConvertor.class)
    private Integer sl1ReturnWarehouse;
    @ExcelProperty(value = {"水濂2仓", "一、送货", "送货份数"}, index = 11, converter = NullToZeroIntConvertor.class)
    private Integer sl2SendNumber;
    @ExcelProperty(value = {"水濂2仓", "一、送货", "送货件数"}, index = 12, converter = NullToZeroIntConvertor.class)
    private Integer sl2SendPiece;
    @ExcelProperty(value = {"水濂2仓", "二、退货", "退转入"}, index = 13, converter = NullToZeroIntConvertor.class)
    private Integer sl2Return;
    @ExcelProperty(value = {"水濂2仓", "二、退货", "回云仓"}, index = 14, converter = NullToZeroIntConvertor.class)
    private Integer sl2ReturnWarehouse;
    @ExcelProperty(value = {"惠州1仓", "一、送货", "送货份数"}, index = 15, converter = NullToZeroIntConvertor.class)
    private Integer hz1SendNumber;
    @ExcelProperty(value = {"惠州1仓", "一、送货", "送货件数"}, index = 16, converter = NullToZeroIntConvertor.class)
    private Integer hz1SendPiece;
    @ExcelProperty(value = {"惠州1仓", "二、退货", "退转入"}, index = 17, converter = NullToZeroIntConvertor.class)
    private Integer hz1Return;
    @ExcelProperty(value = {"惠州1仓", "二、退货", "回云仓"}, index = 18, converter = NullToZeroIntConvertor.class)
    private Integer hz1ReturnWarehouse;
    @ExcelProperty(value = {"惠州2仓", "一、送货", "送货份数"}, index = 19, converter = NullToZeroIntConvertor.class)
    private Integer hz2SendNumber;
    @ExcelProperty(value = {"惠州2仓", "一、送货", "送货件数"}, index = 20, converter = NullToZeroIntConvertor.class)
    private Integer hz2SendPiece;
    @ExcelProperty(value = {"惠州2仓", "二、退货", "退转入"}, index = 21, converter = NullToZeroIntConvertor.class)
    private Integer hz2Return;
    @ExcelProperty(value = {"惠州2仓", "二、退货", "回云仓"}, index = 22, converter = NullToZeroIntConvertor.class)
    private Integer hz2ReturnWarehouse;
    @ExcelProperty(value = {"箱规", "箱规", "箱规"}, index = 23, converter = NullToZeroIntConvertor.class)
    private Integer quantity;
    @ExcelProperty(value = {"合计/出+退", "合计/出+退", "合计/出+退"}, index = 29, converter = NullToZeroIntConvertor.class)
    private Integer totalNumber;
}
