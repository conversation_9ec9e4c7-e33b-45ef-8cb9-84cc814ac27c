package com.senox.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/9 8:44
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentDailyWayStatisticVo implements Serializable {

    private static final long serialVersionUID = 6451220451353549674L;

    @ApiModelProperty("收费日期")
    private LocalDate tollDate;

    @ApiModelProperty("农商行聚合支付")
    private BigDecimal drcAmount;

    @ApiModelProperty("农商015聚合支付")
    private BigDecimal drc015Amount;

    @ApiModelProperty("农商行转账")
    private BigDecimal drcTransferAmount;

    @ApiModelProperty("农商行托收")
    private BigDecimal drcWithholdAmount;

    @ApiModelProperty("莞银通聚合支付")
    private BigDecimal bodAmount;

    @ApiModelProperty("莞银通转账")
    private BigDecimal bodTransferAmount;

    @ApiModelProperty("莞银通扫码")
    private BigDecimal bodScanAmount;

    @ApiModelProperty("现金支付")
    private BigDecimal cashAmount;

    @ApiModelProperty("刷卡")
    private BigDecimal cardAmount;

    @ApiModelProperty("罚没")
    private BigDecimal confiscateAmount;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("备注")
    private String remark;
}
