package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024-2-4
 */
@Getter
@Setter
public class LogisticsFreightExcelVo {

    /**
     * 收货日期
     */
    @ExcelProperty("收货日期")
    private String receivingDate;

    /**
     * 收货单号
     */
    @ExcelProperty("收货单号")
    private String receivingNo;

    /**
     * 寄件客户
     */
    @ExcelProperty("托运方客户")
    private String senderCustomerName;

    /**
     * 托运方电话
     */
    @ExcelProperty("托运方电话")
    private String senderCustomerContact;

    /**
     * 托运方件数
     */
    @ExcelProperty("托运方件数")
    private String senderPieces;

    /**
     * 托运方运费
     */
    @ExcelProperty("托运方运费")
    private String senderFreightCharge;

    /**
     * 结算类型
     */
    @ExcelProperty("结算类型")
    private String senderSettlementType;

    /**
     * 收货方客户
     */
    @ExcelProperty("收货方客户")
    private String receivingCustomerName;

    /**
     * 收货方地址
     */
    @ExcelProperty("收货方地址")
    private String receivingCustomerAddress;

    /**
     * 收货方电话
     */
    @ExcelProperty("收货方电话")
    private String receivingCustomerContact;

    /**
     * 物流公司
     */
    @ExcelProperty("物流公司")
    private String transferLogisticsCompany;

    /**
     * 物流单号
     */
    @ExcelProperty("物流单号")
    private String transferLogisticsNo;

    /**
     * 中转方运费
     */
    @ExcelProperty("中转方运费")
    private String transferCharge;
}
