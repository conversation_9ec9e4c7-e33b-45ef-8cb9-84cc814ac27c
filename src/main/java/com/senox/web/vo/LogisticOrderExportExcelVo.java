package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/15 13:45
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ColumnWidth(16)
@ContentRowHeight(20)
public class LogisticOrderExportExcelVo implements Serializable {

    private static final long serialVersionUID = -5776031088040034735L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ExcelProperty("商户")
    private String merchant;

    @ColumnWidth(18)
    @ExcelProperty("订单编号")
    private String orderNo;

    @ColumnWidth(26)
    @ExcelProperty("送货地址")
    private String destination;

    @ColumnWidth(20)
    @ExcelProperty("市场")
    private String market;

    @ExcelProperty(value = "发货日期", converter = LocalDateExcelConverter.class)
    private LocalDate shipDate;

    @ExcelProperty("收货人电话")
    private String receiverContact;

    @ColumnWidth(20)
    @ExcelProperty("商品名称")
    private String product;

    @ColumnWidth(14)
    @ExcelProperty("商品总类")
    private String productType1;

    @ColumnWidth(14)
    @ExcelProperty("商品分类")
    private String productType2;

    @ExcelProperty("单位")
    private String member;

    @ColumnWidth(12)
    @ExcelProperty("车牌")
    private String vehicleNo;

    @ColumnWidth(12)
    @ExcelProperty("区域")
    private String area;

    @ColumnWidth(12)
    @ExcelProperty("订单件数")
    private BigDecimal productCount;

    @ColumnWidth(12)
    @ExcelProperty("实发数量")
    private BigDecimal shipCount;

    @ColumnWidth(10)
    @ExcelProperty("差异")
    private BigDecimal shipDiversity;

    @ColumnWidth(12)
    @ExcelProperty("商品单价")
    private BigDecimal productPrice;

    @ColumnWidth(12)
    @ExcelProperty("商品总价")
    private BigDecimal productAmount;

    @ColumnWidth(10)
    @ExcelProperty("总重量")
    private BigDecimal productWeight;

    @ColumnWidth(10)
    @ExcelProperty("体积")
    private BigDecimal productSize;

    @ColumnWidth(12)
    @ExcelProperty("商品优惠金额")
    private BigDecimal productDeduction;

    @ColumnWidth(10)
    @ExcelProperty("满减额")
    private BigDecimal productFullReduction;

    @ColumnWidth(12)
    @ExcelProperty("应收金额")
    private BigDecimal productTotalAmount;

    @ColumnWidth(12)
    @ExcelProperty({"已收款记录", "实收金额"})
    private BigDecimal productPaid;

    @ColumnWidth(10)
    @ExcelProperty({"已收款记录", "差异"})
    private BigDecimal productDiversity;

    @ColumnWidth(10)
    @ExcelProperty({"已收款记录", "司机"})
    private String driver;

    @ColumnWidth(10)
    @ExcelProperty({"已收款记录", "交款人"})
    private String productPaidMan;

    @ColumnWidth(10)
    @ExcelProperty({"欠款记录", "金额"})
    private BigDecimal productOwe;

    @ExcelProperty("备注")
    private String remark;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "件数"})
    private BigDecimal shipCount2;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "件重"})
    private BigDecimal pieceWeight;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "总重量"})
    private BigDecimal shipWeight;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "件体积"})
    private BigDecimal pieceSize;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "总体积"})
    private BigDecimal shipSize;

    @ColumnWidth(14)
    @ExcelProperty({"物流运费核算", "运费单价"})
    private BigDecimal shipPrice;

    @ColumnWidth(14)
    @ExcelProperty({"物流运费核算", "物流费倍率"})
    private BigDecimal shipMultiplying;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "物流费"})
    private BigDecimal shipAmount;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "折扣率"})
    private BigDecimal shipDiscount;

    @ColumnWidth(12)
    @ExcelProperty(value = {"物流运费核算", "免分拣费"}, converter = BooleanExcelConvertor.class)
    private Boolean sortCharge;

    @ColumnWidth(10)
    @ExcelProperty({"物流运费核算", "分拣费"})
    private BigDecimal sortAmount;

    @ColumnWidth(12)
    @ExcelProperty({"物流运费核算", "实际运费"})
    private BigDecimal shipTotalAmount;

    @ExcelProperty("应付款")
    private BigDecimal totalAmount;

    @ColumnWidth(18)
    @ExcelProperty(value = "录入时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime createTime;

    @ExcelProperty("录入人")
    private String creator;



}
