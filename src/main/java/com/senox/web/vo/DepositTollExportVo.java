package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import com.senox.web.convert.PaywayExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/2/27 13:42
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class DepositTollExportVo implements Serializable {

    private static final long serialVersionUID = 3136248657735883175L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty("客户")
    private String customerName;

    @ExcelProperty("押金类型")
    private String feeName;

    @ExcelProperty(value = "缴费方式", converter = PaywayExcelConverter.class)
    private Integer payWay;

    @ExcelProperty("金额")
    private BigDecimal totalAmount;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(30)
    @ExcelProperty("备注")
    private String remark;
}
