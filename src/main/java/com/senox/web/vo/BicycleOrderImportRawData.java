package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.senox.common.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;

import static java.time.format.DateTimeFormatter.ofPattern;

/**
 * <AUTHOR>
 * @date 2024-4-18
 */
@Getter
@Setter
public class BicycleOrderImportRawData {
    @ExcelProperty(converter = LocalDateToStringExcelConverter.class)
    private String column1;
    @ExcelProperty(converter = LocalDateToStringExcelConverter.class)
    private String column2;
    private String column3;
    private String column4;
    private String column5;
    private String column6;
    private String column7;
    private String column8;
    private String column9;
    private String column10;
    private String column11;
    private String column12;
    private String column13;
    private String column14;
    private String column15;

    public boolean isEmpty() {
        return null == column1
                && null == column2
                && null == column3
                && null == column4
                && null == column5
                && null == column6
                && null == column7
                && null == column8
                && null == column9
                && null == column10
                && null == column11
                && null == column12
                && null == column13
                && null == column14
                && null == column15;
    }

    /**
     * <AUTHOR>
     * @date 2024-4-22
     */
    public static class LocalDateToStringExcelConverter implements Converter<String> {

        @Override
        public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (CellDataTypeEnum.NUMBER.equals(cellData.getType())) {
                if (cellData.getNumberValue().compareTo(BigDecimal.ONE) <= 0) {
                    String timeString = getTimeString(cellData);
                    return LocalTime.parse(timeString, ofPattern("HH:mm")).format(ofPattern("HH:mm"));
                } else {
                    LocalDate date = LocalDate.of(1900, 1, 1);
                    return DateUtils.formatYearMonth(date.plusDays(cellData.getNumberValue().longValue() - 2), DateUtils.PATTERN_FULL_DATE);
                }
            }
            return cellData.getStringValue();
        }

        private static String getTimeString(ReadCellData<?> cellData) {
            BigDecimal numberValue = cellData.getNumberValue();
            BigDecimal localTime = numberValue.multiply(new BigDecimal("24")).setScale(2, RoundingMode.UP);
            long hours = localTime.longValue();
            int minutes = localTime.subtract(BigDecimal.valueOf(hours)).multiply(BigDecimal.valueOf(60)).intValue();
            String formattedHours = String.format("%02d", hours);
            String formattedMinutes = String.format("%02d", minutes);
            return String.format("%s:%s", formattedHours, formattedMinutes);
        }
    }


}

