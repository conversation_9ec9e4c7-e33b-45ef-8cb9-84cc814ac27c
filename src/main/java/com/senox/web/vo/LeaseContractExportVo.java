package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/9/11 9:40
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class LeaseContractExportVo {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(16)
    @ExcelProperty("合同编号")
    private String contractNo;

    @ColumnWidth(10)
    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(16)
    @ExcelProperty("物业名称")
    private String realtyName;

    @ColumnWidth(10)
    @ExcelProperty("客户编号")
    private String customerSerial;

    @ColumnWidth(16)
    @ExcelProperty("客户名称")
    private String customerName;

    @ColumnWidth(16)
    @ExcelProperty("客户联系方式")
    private String customerContact;

    @ColumnWidth(20)
    @ExcelProperty("合同开始日期")
    private String startDate;

    @ColumnWidth(20)
    @ExcelProperty("合同结束日期")
    private String endDate;

    @ColumnWidth(8)
    @ExcelProperty("状态")
    private String status;

    @ColumnWidth(8)
    @ExcelProperty("面积")
    private BigDecimal area;

    @ColumnWidth(8)
    @ExcelProperty("租金金额")
    private BigDecimal rentAmount;

    @ColumnWidth(8)
    @ExcelProperty("管理费")
    private BigDecimal manageAmount;

    @ColumnWidth(8)
    @ExcelProperty("押金费")
    private BigDecimal depositAmount;

    @ColumnWidth(8)
    @ExcelProperty("押金状态")
    private String depositStatus;

    @ColumnWidth(16)
    @ExcelProperty("代租合同号")
    private String rentProxyContractNo;

    @ColumnWidth(12)
    @ExcelProperty("业主名")
    private String ownerName;

    @ColumnWidth(20)
    @ExcelProperty("业主联系方式")
    private String ownerContact;
}
