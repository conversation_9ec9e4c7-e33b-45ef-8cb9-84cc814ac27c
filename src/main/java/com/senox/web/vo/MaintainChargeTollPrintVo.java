package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 13:43
 */
@ApiModel("物维收费单打印对象")
public class MaintainChargeTollPrintVo implements Serializable {

    private static final long serialVersionUID = 800864922032675709L;

    @ApiModelProperty("票据号")
    private String chargeSerial;

    @ApiModelProperty("通知单号")
    private String chargeNo;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("收费员")
    private String tollMan;

    @ApiModelProperty("收款时间")
    private LocalDateTime tollTime;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("明细")
    private List<MaintainChargeTollPrintItemVo> details;

    public String getChargeSerial() {
        return chargeSerial;
    }

    public void setChargeSerial(String chargeSerial) {
        this.chargeSerial = chargeSerial;
    }

    public String getChargeNo() {
        return chargeNo;
    }

    public void setChargeNo(String chargeNo) {
        this.chargeNo = chargeNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getTollMan() {
        return tollMan;
    }

    public void setTollMan(String tollMan) {
        this.tollMan = tollMan;
    }

    public LocalDateTime getTollTime() {
        return tollTime;
    }

    public void setTollTime(LocalDateTime tollTime) {
        this.tollTime = tollTime;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<MaintainChargeTollPrintItemVo> getDetails() {
        return details;
    }

    public void setDetails(List<MaintainChargeTollPrintItemVo> details) {
        this.details = details;
    }
}
