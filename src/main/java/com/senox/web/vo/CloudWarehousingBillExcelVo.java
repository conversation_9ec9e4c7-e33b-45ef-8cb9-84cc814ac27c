package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/14 15:00
 */
@Data
public class CloudWarehousingBillExcelVo implements Serializable {

    private static final long serialVersionUID = 2033147457941618875L;

    @ExcelProperty(value = "客户编码")
    private String rcSerial;

    @ExcelProperty(value = "客户")
    private String name;

    @ExcelProperty(value = "退转服务费")
    private BigDecimal refundServiceCharge;

    @ExcelProperty(value = "租金")
    private BigDecimal rentCharge;

    @ExcelProperty(value = "配送费")
    private BigDecimal deliveryCharge;

    @ExcelProperty(value = "技术服务费")
    private BigDecimal technicalServiceCharge;

    @ExcelProperty(value = "中班补货")
    private BigDecimal midReplenishmentCharge;

    @ExcelProperty(value = "尾波补货")
    private BigDecimal codaReplenishmentCharge;

    @ExcelProperty(value = "其他费用")
    private BigDecimal otherCharge;

    @ExcelProperty(value = "合计")
    private BigDecimal totalCharge;
}
