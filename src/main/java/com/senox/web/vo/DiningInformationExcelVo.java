package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class DiningInformationExcelVo implements Serializable {

    private static final long serialVersionUID = -2705023123984786566L;

    @ExcelProperty("用户")
    private String employeeName;

    @ExcelProperty(value = "就餐日期", converter = LocalDateExcelConverter.class)
    private LocalDate mealDate;

    @ExcelProperty(value =
            "就餐时间", converter = LocalTimeExcelConverter.class)
    private LocalTime mealTime;
}
