package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class NormalTemperatureWarehousingBillExportVo {

    /**
     * 账单日期
     */
    @ExcelProperty("账单日期")
    private String billYearMonth;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String merchantName;

    /**
     * 入库装卸费
     */
    @ExcelProperty("入库装卸费")
    private BigDecimal handlingAmount;

    /**
     * 出库装车费
     */
    @ExcelProperty("出库装车费")
    private BigDecimal loadingAmount;

    /**
     * 出库拣货费
     */
    @ExcelProperty("出库拣货费")
    private BigDecimal sortingAmount;

    /**
     * 库存费
     */
    @ExcelProperty("库存费")
    private BigDecimal storageAmount;

    /**
     * 运费
     */
    @ExcelProperty("小仓库/运费")
    private BigDecimal freightAmount;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用")
    private BigDecimal otherAmount;

    /**
     * 应收滞纳金
     */
    @ExcelProperty("应收滞纳金")
    private BigDecimal penaltyAmount;

    /**
     * 实收滞纳金
     */
    @ExcelProperty("实收滞纳金")
    private BigDecimal penaltyPaidAmount;

    /**
     * 应收金额
     */
    @ExcelProperty("应收金额")
    private BigDecimal totalAmount;

    /**
     * 实收金额
     */
    @ExcelProperty("实收金额")
    private BigDecimal paidAmount;

    /**
     * 支付状态
     */
    @ExcelProperty("支付状态")
    private String paidStatus;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payWay;

    /**
     * 支付时间
     */
    @ExcelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}
