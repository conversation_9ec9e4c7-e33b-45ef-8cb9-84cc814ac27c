package com.senox.web.vo;

import com.senox.pm.constant.ReceiptOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/10/18 17:25
 */
@Getter
@Setter
@ToString
@ApiModel("发票审批")
public class ReceiptAuditVo {

    @Min(value = 1, message = "无效的id")
    @NotNull(message = "无效的id")
    @ApiModelProperty("id")
    private Long id;

    @NotNull(message = "无效的审批结果")
    @ApiModelProperty("审批审批结果")
    private ReceiptOrderStatus status;

    @ApiModelProperty("备注")
    private String remark;

}
