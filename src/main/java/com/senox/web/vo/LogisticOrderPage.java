package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:31
 */
@Getter
@Setter
@ToString
@ApiModel("物流订单页")
public class LogisticOrderPage<T> extends PageResult<T> {

    @ApiModelProperty("商品件数")
    private BigDecimal productCount;

    @ApiModelProperty("商品总重量")
    private BigDecimal productWeight;

    @ApiModelProperty("商品总体积")
    private BigDecimal productSize;

    @ApiModelProperty("商品总价")
    private BigDecimal productAmount;

    @ApiModelProperty("商品优惠金额")
    private BigDecimal productDeduction;

    @ApiModelProperty("商品满减额")
    private BigDecimal productFullReduction;

    @ApiModelProperty("商品合计")
    private BigDecimal productTotalAmount;

    @ApiModelProperty("实发件数")
    private BigDecimal shipCount;

    @ApiModelProperty("实发重量")
    private BigDecimal shipWeight;

    @ApiModelProperty("实发体积")
    private BigDecimal shipSize;

    @ApiModelProperty("物流费")
    private BigDecimal shipAmount;

    @ApiModelProperty("分拣费")
    private BigDecimal sortAmount;

    @ApiModelProperty("实际运费")
    private BigDecimal shipTotalAmount;

    @ApiModelProperty("已收货款金额")
    private BigDecimal productPaid;

    @ApiModelProperty("已收货款差异金额")
    private BigDecimal productDiversity;

    @ApiModelProperty("已收货款欠款金额")
    private BigDecimal productOwe;

    @ApiModelProperty("应付合计")
    private BigDecimal totalAmount;


    public LogisticOrderPage() {
        this.init();
    }

    public LogisticOrderPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.productCount = BigDecimal.ZERO;
        this.productWeight = BigDecimal.ZERO;
        this.productSize = BigDecimal.ZERO;
        this.productAmount = BigDecimal.ZERO;
        this.productDeduction = BigDecimal.ZERO;
        this.productFullReduction = BigDecimal.ZERO;
        this.productTotalAmount = BigDecimal.ZERO;

        this.shipCount = BigDecimal.ZERO;
        this.shipWeight = BigDecimal.ZERO;
        this.shipSize = BigDecimal.ZERO;
        this.shipAmount = BigDecimal.ZERO;
        this.sortAmount = BigDecimal.ZERO;
        this.shipTotalAmount = BigDecimal.ZERO;

        this.productPaid = BigDecimal.ZERO;
        this.productDiversity = BigDecimal.ZERO;
        this.productOwe = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> LogisticOrderPage<T> emptyPage() {
        return new LogisticOrderPage<>();
    }

}
