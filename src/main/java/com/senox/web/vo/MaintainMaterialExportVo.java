package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/1/10 9:14
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class MaintainMaterialExportVo implements Serializable {

    private static final long serialVersionUID = 586388406325917111L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("账单年月")
    private String chargeDate;

    @ExcelProperty("派工单号")
    private String jobNo;

    @ExcelProperty("维修类型")
    private String maintainType;

    @ExcelProperty("出库单号")
    private String outNo;

    @ExcelProperty("部门")
    private String handlerDeptName;

    @ExcelProperty("是否收费")
    private String paidStatus;

    @ExcelProperty("是否领料")
    private String pickingStatus;

    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("领料人")
    private String receivePerson;

    @ExcelProperty("物料编码")
    private Long materialCode;

    @ExcelProperty("物料名称")
    private String materialName;

    @ExcelProperty("物料成本单价")
    private BigDecimal price;

    @ExcelProperty("物料数量")
    private Integer quantity;

    @ExcelProperty("物料金额")
    private BigDecimal amount;

}
