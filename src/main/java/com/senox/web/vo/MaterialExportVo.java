package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-2-12
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9,horizontalAlignment= HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class MaterialExportVo implements Serializable {

    @ExcelProperty("物料名称")
    private String materialName;

    @ExcelProperty("物料编号")
    private Long materialDictId;

    @ExcelProperty("物料规格")
    private String materialNorms;

    @ExcelProperty("单位")
    private String unitName;

    @ExcelProperty("入仓数量")
    private Long billNumber;

    @ExcelProperty("出仓数量")
    private Long outNumber;

    @ExcelProperty("结余")
    private Long surplusNumber;

    @ExcelProperty("总价")
    private BigDecimal totalPrice;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    public Long getMaterialDictId() {
        return materialDictId;
    }

    public void setMaterialDictId(Long materialDictId) {
        this.materialDictId = materialDictId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialNorms() {
        return materialNorms;
    }

    public void setMaterialNorms(String materialNorms) {
        this.materialNorms = materialNorms;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(Long billNumber) {
        this.billNumber = billNumber;
    }

    public Long getOutNumber() {
        return outNumber;
    }

    public void setOutNumber(Long outNumber) {
        this.outNumber = outNumber;
    }

    public Long getSurplusNumber() {
        return surplusNumber;
    }

    public void setSurplusNumber(Long surplusNumber) {
        this.surplusNumber = surplusNumber;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
}
