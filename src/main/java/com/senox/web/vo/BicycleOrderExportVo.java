package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:11
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class BicycleOrderExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(20)
    @ExcelProperty("订单流水号")
    private String orderSerialNo;

    @ColumnWidth(20)
    @ExcelProperty("下单时间")
    private LocalDateTime orderTime;

    @ColumnWidth(14)
    @ExcelProperty("客户名")
    private String sender;

    @ColumnWidth(14)
    @ExcelProperty("结算周期")
    private String settlePeriod;

    @ColumnWidth(14)
    @ExcelProperty("起点")
    private String startPointName;

    @ColumnWidth(20)
    @ExcelProperty("终点")
    private String endPointName;

    @ColumnWidth(20)
    @ExcelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    @ColumnWidth(14)
    @ExcelProperty("骑手")
    private String riderName;

    @ColumnWidth(14)
    @ExcelProperty("件数")
    private BigDecimal pieces;

    @ColumnWidth(14)
    @ExcelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ColumnWidth(14)
    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    @ColumnWidth(14)
    @ExcelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ColumnWidth(14)
    @ExcelProperty("上楼费")
    private BigDecimal upstairsCharge;

    @ColumnWidth(14)
    @ExcelProperty("合计")
    private BigDecimal totalCharge;

    @ColumnWidth(14)
    @ExcelProperty("状态描述")
    private String statusDescribe;
}
