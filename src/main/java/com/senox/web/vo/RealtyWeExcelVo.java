package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.senox.web.convert.ReadingsExcelConvertor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * excel 格式
 * 物业代码,水上月读数,水本月读数,水基数,电上月读数,电本月读数,电基数
 * <AUTHOR>
 * @date 2022/11/2 10:23
 */
@Getter
@Setter
@ToString
public class RealtyWeExcelVo implements Serializable {

    private static final long serialVersionUID = -1828973439978402611L;

    @ExcelProperty("物业代码")
    private String realtySerial;

    @ExcelProperty(value = "水上月读数", converter = ReadingsExcelConvertor.class)
    private Integer lastWaterReadings;

    @ExcelProperty(value = "水本月读数", converter = ReadingsExcelConvertor.class)
    private Integer waterReadings;

    @ExcelProperty(value = "水基数", converter = ReadingsExcelConvertor.class)
    private Integer waterBase;

    @ExcelProperty(value = "电上月读数", converter = ReadingsExcelConvertor.class)
    private Integer lastElectricReadings;

    @ExcelProperty(value = "电本月读数", converter = ReadingsExcelConvertor.class)
    private Integer electricReadings;

    @ExcelProperty(value = "电基数", converter = ReadingsExcelConvertor.class)
    private Integer electricBase;

}
