package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-1-19
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class LogisticLoaderSettlementExportVo {

    /**
     * 日期
     */
    @ExcelProperty("日期")
    private String date;

    /**
     * 装卸类型
     */
    @ExcelProperty("装卸类型")
    private String freightType;

    /**
     * 客户
     */
    @ExcelProperty("客户")
    private String customerName;

    /**
     * 商品类型
     */
    @ExcelProperty("商品品类")
    private String goodsType;

    /**
     * 搬运工列表
     */
    @ExcelProperty("搬运工工号及姓名")
    private String loaders;

    /**
     * 参与人数
     */
    @ExcelProperty("作业人数")
    private Integer participationNumber;

    /**
     * 平均搬运量
     */
    @ExcelProperty("人均搬运")
    private BigDecimal transportAvg;

    /**
     * 总搬运量
     */
    @ExcelProperty("总搬运量")
    private BigDecimal transportTotal;

    /**
     * 装卸单价
     */
    @ExcelProperty("装卸费单价 元/吨")
    private BigDecimal freightUnitPrice;

    /**
     * 装卸总金额
     */
    @ExcelProperty("装卸费（总搬运量*装卸单价）")
    private BigDecimal freightTotalAmount;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    private BigDecimal pieces;

    /**
     * 商品单价
     */
    @ExcelProperty("计件单价（元/件）")
    private BigDecimal goodsUnitPrice;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用（计件*计件单价）")
    private BigDecimal otherCharge;

    /**
     * 分拣费
     */
    @ExcelProperty("分拣费")
    private BigDecimal sortingFee;

    /**
     * 车牌号
     */
    @ExcelProperty("车牌号")
    private String carNo;

    /**
     * 工时
     */
    @ExcelProperty("工时")
    private BigDecimal workingHours;

    /**
     * 餐补
     */
    @ExcelProperty("餐补")
    private BigDecimal mealAllowanceAmount;

    /**
     * 小计
     */
    @ExcelProperty("小计")
    private BigDecimal subtotalAmount;

    /**
     * 合计
     */
    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    /**
     *  备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty("录入时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ExcelProperty("录入员")
    private String creatorName;
}
