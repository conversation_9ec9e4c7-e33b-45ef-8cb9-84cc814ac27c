package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/17 16:41
 */
@Getter
@Setter
@ToString
@ApiModel("广告位租赁情况")
public class AdvertisingSpaceRentVo implements Serializable {

    @ApiModelProperty("已租统计")
    private int rentCount;

    @ApiModelProperty("未租统计")
    private int idleCount;

}
