package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/3/17 8:41
 */
@ColumnWidth(16)
@ContentRowHeight(20)
public class ParkingRecordExportVo implements Serializable {

    private static final long serialVersionUID = 7509827116699846155L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(14)
    @ExcelProperty("车位名")
    private String parkingName;

    @ColumnWidth(14)
    @ExcelProperty("车牌号")
    private String carNo;

    @ColumnWidth(20)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(20)
    @ExcelProperty(value = "进场时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime entryTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "退场时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime exitTime;

    @ColumnWidth(20)
    @ExcelProperty("停车时长")
    private String durationDescription;

    @ColumnWidth(14)
    @ExcelProperty("进场费")
    private BigDecimal entryAmount;

    @ColumnWidth(14)
    @ExcelProperty("进场费支付方式")
    private String entryPayWay;

    @ColumnWidth(20)
    @ExcelProperty(value = "进场缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime entryPaidTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "退场缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime exitPaidTime;

    @ColumnWidth(14)
    @ExcelProperty("超时费")
    private BigDecimal exceedAmount;

    @ColumnWidth(14)
    @ExcelProperty("超时费支付方式")
    private String exceedPayWay;

    @ColumnWidth(14)
    @ExcelProperty("小计")
    private BigDecimal amount;

    @ColumnWidth(14)
    @ExcelProperty("类型")
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSerial() {
        return serial;
    }

    public void setSerial(Integer serial) {
        this.serial = serial;
    }

    public String getParkingName() {
        return parkingName;
    }

    public void setParkingName(String parkingName) {
        this.parkingName = parkingName;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public LocalDateTime getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(LocalDateTime entryTime) {
        this.entryTime = entryTime;
    }

    public LocalDateTime getExitTime() {
        return exitTime;
    }

    public void setExitTime(LocalDateTime exitTime) {
        this.exitTime = exitTime;
    }

    public String getDurationDescription() {
        return durationDescription;
    }

    public void setDurationDescription(String durationDescription) {
        this.durationDescription = durationDescription;
    }

    public BigDecimal getEntryAmount() {
        return entryAmount;
    }

    public void setEntryAmount(BigDecimal entryAmount) {
        this.entryAmount = entryAmount;
    }

    public BigDecimal getExceedAmount() {
        return exceedAmount;
    }

    public void setExceedAmount(BigDecimal exceedAmount) {
        this.exceedAmount = exceedAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getEntryPayWay() {
        return entryPayWay;
    }

    public void setEntryPayWay(String entryPayWay) {
        this.entryPayWay = entryPayWay;
    }

    public LocalDateTime getEntryPaidTime() {
        return entryPaidTime;
    }

    public void setEntryPaidTime(LocalDateTime entryPaidTime) {
        this.entryPaidTime = entryPaidTime;
    }

    public LocalDateTime getExitPaidTime() {
        return exitPaidTime;
    }

    public void setExitPaidTime(LocalDateTime exitPaidTime) {
        this.exitPaidTime = exitPaidTime;
    }

    public String getExceedPayWay() {
        return exceedPayWay;
    }

    public void setExceedPayWay(String exceedPayWay) {
        this.exceedPayWay = exceedPayWay;
    }
}
