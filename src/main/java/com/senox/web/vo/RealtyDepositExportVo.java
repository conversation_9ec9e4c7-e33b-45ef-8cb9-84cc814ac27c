package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BillStatusExcelConvertor;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/4/20 10:18
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyDepositExportVo implements Serializable {

    private static final long serialVersionUID = -8349546906498731457L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ExcelProperty("合同编号")
    private String contractNo;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(20)
    @ExcelProperty("物业名")
    private String realtyName;

    @ColumnWidth(20)
    @ExcelProperty("客户名")
    private String customerName;

    @ExcelProperty("金额")
    private BigDecimal amount;

    @ExcelProperty(value = "开单日期", converter = LocalDateExcelConverter.class)
    private LocalDate operateDate;

    @ExcelProperty(value = "状态", converter = BillStatusExcelConvertor.class)
    private Integer status;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "收费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime tollTime;

    @ExcelProperty("退档人")
    private String refundMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "退档时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime refundTime;


}
