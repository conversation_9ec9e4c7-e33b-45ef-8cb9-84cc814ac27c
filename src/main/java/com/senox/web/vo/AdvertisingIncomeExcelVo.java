package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/8/24 14:06
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class AdvertisingIncomeExcelVo implements Serializable {

    private static final long serialVersionUID = -4102200373833097601L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("广告位置编号")
    private String spaceSerial;

    @ColumnWidth(20)
    @ExcelProperty("广告位置")
    private String spaceName;

    @ExcelProperty("区域")
    private String spaceRegion;

    @ColumnWidth(20)
    @ExcelProperty("街道")
    private String spaceStreet;

    @ExcelProperty("广告合同")
    private String contractNo;

    @ExcelProperty("客户名称")
    private String customerName;

    @ExcelProperty(value = "开始日期", converter = LocalDateExcelConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "结束日期", converter = LocalDateExcelConverter.class)
    private LocalDate endDate;

    @ExcelProperty("收入")
    private BigDecimal amount;

    @ExcelProperty("画布成本")
    private BigDecimal cost;

    @ExcelProperty("净利润")
    private BigDecimal benefit;

    @ExcelProperty("物业号")
    private String realtySerial;

    @ColumnWidth(20)
    @ExcelProperty("物业地址")
    private String realtyName;

    @ExcelProperty("业主名称")
    private String realtyOwner;

    @ExcelProperty("分成金额")
    private BigDecimal shareAmount;

    @ExcelProperty("业主领取分成金额")
    private BigDecimal sharePaidAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "业主领取分成时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime shareTime;
}
