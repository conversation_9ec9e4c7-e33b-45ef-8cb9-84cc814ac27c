package com.senox.web.vo;

import com.alibaba.excel.metadata.CellExtra;
import com.senox.common.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-12
 */
@Getter
@Setter
@ToString
public class ExcelRowResult<T> {

    private String sheetName;

    private List<CellExtra> cellExtraList = new ArrayList<>();

    private List<T> dataList = new ArrayList<>();


    public void clear(){
        sheetName = StringUtils.EMPTY;
        cellExtraList.clear();
        dataList.clear();
    }

}
