package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/15 9:05
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class MaintainChargeExportVo implements Serializable {

    private static final long serialVersionUID = -1445580320500651157L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("收费单号")
    private String chargeNo;

    @ExcelProperty("年份")
    private Integer chargeYear;

    @ExcelProperty("月份")
    private Integer chargeMonth;

    @ExcelProperty("派工单号")
    private String jobNo;

    @ExcelProperty("客户名")
    private String customerName;

    @ColumnWidth(14)
    @ExcelProperty("联系方式")
    private String contact;

    @ExcelProperty("维修类型")
    private String maintainType;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ExcelProperty("已收金额")
    private BigDecimal paidAmount;

    @ExcelProperty("人工费")
    private BigDecimal laborAmount;

    @ExcelProperty("物料费")
    private BigDecimal materialAmount;

    @ExcelProperty("缴费状态")
    private String paidStatus;

    @ExcelProperty("支付方式")
    private String payWay;
}
