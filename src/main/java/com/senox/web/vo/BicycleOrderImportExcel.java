package com.senox.web.vo;

import com.senox.tms.vo.BicycleOrderGoodsDetailVo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-12
 */
@Getter
@Setter
public class BicycleOrderImportExcel {

    /**
     * 合并序号
     */
    private Integer mergeNumber;

    /**
     * 标记
     */
    private String mark;

    /**
     * 参与人员
     */
    private String participant;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 时间
     */
    private LocalTime time;

    /**
     * 起点
     */
    private String startPoint;

    /**
     * 终点
     */
    private String endPoint;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 货物名
     */
    private String goodsName;

    /**
     * 货物类型
     */
    private String goodsType;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal size;

    /**
     * 耗时
     */
    private int consumedTime;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 费用
     */
    private BigDecimal charge;

    /**
     * 骑手编码
     */
    private String riderCode;

    /**
     * 骑手件数
     */
    private BigDecimal riderPieces;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单商品明细集
     */
    private List<BicycleOrderGoodsDetailVo> orderGoodsDetails;

    /**
     * 类型(0:非详细;1:详细)
     */
    private Integer type;
}
