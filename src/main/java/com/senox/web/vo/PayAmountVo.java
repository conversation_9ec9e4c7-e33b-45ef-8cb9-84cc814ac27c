package com.senox.web.vo;

import com.senox.pm.constant.PayWay;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/3 14:10
 */
@Getter
@Setter
@ToString
public class PayAmountVo implements Serializable {

    private static final long serialVersionUID = -1574355630978135177L;

    @ApiModelProperty("支付方式")
    private PayWay payWay;

    @ApiModelProperty("支付金额")
    private BigDecimal amount;

    @ApiModelProperty("付款码")
    private String authCode;

    @ApiModelProperty("支付终端序列号")
    private String deviceSn;

    public PayAmountVo() {
    }

    public PayAmountVo(PayWay payWay, BigDecimal amount) {
        this.payWay = payWay;
        this.amount = amount;
    }
}
