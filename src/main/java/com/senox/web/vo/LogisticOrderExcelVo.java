package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.senox.common.utils.DecimalUtils;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.NullToZeroDecimalConvertor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/5 15:07
 */
@Getter
@Setter
@ToString
@ExcelIgnoreUnannotated
public class LogisticOrderExcelVo implements Serializable {

    private static final long serialVersionUID = 3026165374650216298L;

    @ExcelProperty("商户")
    private String merchant;

    @ExcelProperty("会员名字")
    private String member;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("商品名称")
    private String product;

    @ExcelProperty("商品总类")
    private String productType1;

    @ExcelProperty("商品分类")
    private String productType2;

    @ExcelProperty(value = "商品单价", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal productPrice;

    @ExcelProperty(value = "订单件数", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal productCount;

    @ExcelProperty(value = "商品总价", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal productAmount;

    @ExcelProperty(value = "总重量", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal productWeight;

    @ExcelProperty(value = "体积", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal productSize;

    @ExcelProperty("优惠金额")
    private BigDecimal productDeduction;

    @ExcelProperty("满减额")
    private BigDecimal productFullReduction;

    @ExcelProperty("送货地址")
    private String destination;

    @ExcelProperty("市场")
    private String market;

    @ExcelProperty("区域")
    private String area;

    @ExcelProperty(value = "发货日期", converter = LocalDateExcelConverter.class)
    private LocalDate shipDate;

    @ExcelProperty(value = "实发数量", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal shipCount;

    @ExcelProperty("车牌")
    private String vehicleNo;

    @ExcelProperty("收货人电话")
    private String receiverContact;

    @ExcelProperty(value = "运费单价", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal shipPrice;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty(value = "是否收取分拣费",converter = BooleanExcelConvertor.class)
    private Boolean sortCharge;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticOrderExcelVo that = (LogisticOrderExcelVo) o;
        return Objects.equals(merchant, that.merchant)
                && Objects.equals(orderNo, that.orderNo)
                && Objects.equals(product, that.product)
                && DecimalUtils.equals(productCount, that.productCount)
                && Objects.equals(shipDate, that.shipDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(merchant, orderNo, product, productCount, shipDate);
    }
}
