package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/7/24 10:24
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class MaintainOrderStatisticExportVo implements Serializable {

    private static final long serialVersionUID = -2106981809479793483L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(14)
    @ExcelProperty("订单号")
    private String orderNo;

    @ColumnWidth(14)
    @ExcelProperty("维修类型")
    private String maintainType;

    @ColumnWidth(14)
    @ExcelProperty("状态")
    private String status;

    @ColumnWidth(14)
    @ExcelProperty("客户名")
    private String customerName;

    @ColumnWidth(14)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(20)
    @ExcelProperty("提交时间")
    private LocalDateTime createTime;

    @ColumnWidth(24)
    @ExcelProperty("问题描述")
    private String problem;

    @ColumnWidth(10)
    @ExcelProperty("支付状态")
    private String payStatus;

    @ColumnWidth(14)
    @ExcelProperty("成本价")
    private BigDecimal costPrice;

    @ColumnWidth(14)
    @ExcelProperty("人工费")
    private BigDecimal laborAmount;

    @ColumnWidth(14)
    @ExcelProperty("材料费")
    private BigDecimal incomeAmount;

    @ColumnWidth(14)
    @ExcelProperty("总费用")
    private BigDecimal totalAmount;
}
