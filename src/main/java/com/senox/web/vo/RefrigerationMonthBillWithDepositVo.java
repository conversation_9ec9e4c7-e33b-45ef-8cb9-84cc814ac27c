package com.senox.web.vo;

import com.senox.cold.vo.RefrigerationMonthBillVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/1/30 14:41
 */
@Getter
@Setter
@ToString
@ApiModel("带押金信息的冷藏月账单")
public class RefrigerationMonthBillWithDepositVo extends RefrigerationMonthBillVo {

    private static final long serialVersionUID = 6025316756854662902L;

    @ApiModelProperty("押金金额")
    private BigDecimal depositAmount;

    public RefrigerationMonthBillWithDepositVo() {
    }

    public RefrigerationMonthBillWithDepositVo(RefrigerationMonthBillVo bill) {
        setId(bill.getId());
        setBillYear(bill.getBillYear());
        setBillMonth(bill.getBillMonth());
        setCustomerSerial(bill.getCustomerSerial());
        setCustomerName(bill.getCustomerName());
        setStorageCharge(bill.getStorageCharge());
        setDisposalCharge(bill.getDisposalCharge());
        setHandlingCharge(bill.getHandlingCharge());
        setPassingCharge(bill.getPassingCharge());
        setMembraneCharge(bill.getMembraneCharge());
        setSortingCharge(bill.getSortingCharge());
        setOvertimeCharge(bill.getOvertimeCharge());
        setOtherCharge(bill.getOtherCharge());
        setDeliveryCharge(bill.getDeliveryCharge());
        setPenaltyCharge(bill.getPenaltyCharge());
        setTotalCharge(bill.getTotalCharge());
        setPenaltyIgnore(bill.getPenaltyIgnore());
        setDiscountAmount(bill.getDiscountAmount());
        setBadDebtAmount(bill.getBadDebtAmount());
        setPaidAmount(bill.getPaidAmount());
        setPenaltyPaid(bill.getPenaltyPaid());
        setTollSerial(bill.getTollSerial());
        setTollMan(bill.getTollMan());
        setSend(bill.getSend());
        setStatus(bill.getStatus());
        setPaidTime(bill.getPaidTime());
        setRemoteOrderId(bill.getRemoteOrderId());
        setRemark(bill.getRemark());
        setReceipt(bill.getReceipt());
        setReceiptMan(bill.getReceiptMan());
        setReceiptRemark(bill.getReceiptRemark());
        setReceiptTime(bill.getReceiptTime());
    }
}
