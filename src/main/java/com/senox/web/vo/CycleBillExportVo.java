package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/8/3 16:26
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class CycleBillExportVo implements Serializable {

    private static final long serialVersionUID = -1516988197889714208L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("票据号")
    private String billSerial;

    @ColumnWidth(30)
    @ExcelProperty("收费项目")
    private String feeName;

    @ColumnWidth(30)
    @ExcelProperty("支付方式")
    private String payWay;

    @ExcelProperty("收费金额")
    private BigDecimal amount;

    @ExcelProperty("实收金额")
    private BigDecimal actualAmount;

    @ColumnWidth(20)
    @ExcelProperty("三轮车牌")
    private String license;

    @ColumnWidth(20)
    @ExcelProperty("原车牌")
    private String oldLicense;

    @ColumnWidth(20)
    @ExcelProperty("物业编码")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名/部门名")
    private String realtyName;

    @ExcelProperty("负责人")
    private String owner;

    @ExcelProperty("录入人")
    private String inputMan;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

}
