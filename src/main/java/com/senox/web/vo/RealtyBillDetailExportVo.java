package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/9/23 16:03
 */
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyBillDetailExportVo implements Serializable {

    private static final long serialVersionUID = 6627412065489518754L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("合同号")
    private String contractNo;

    @ExcelProperty("账单年月")
    private String billYearMonth;

    @ExcelProperty("经营区域")
    private String realtyRegion;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty("档主")
    private String realtyOwner;

    @ExcelProperty("管理费")
    private BigDecimal manageAmount;

    @ExcelProperty("租金")
    private BigDecimal rentAmount;

    @ExcelProperty("水费")
    private BigDecimal waterAmount;

    @ExcelProperty("电费")
    private BigDecimal electricAmount;

    @ExcelProperty("滞纳金")
    private BigDecimal penaltyAmount;

    @ExcelProperty("免滞纳金合计")
    private BigDecimal totalAmountIgnorePenalty;

    @ExcelProperty("合计")
    private BigDecimal totalAmount;


    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getBillYearMonth() {
        return billYearMonth;
    }

    public void setBillYearMonth(String billYearMonth) {
        this.billYearMonth = billYearMonth;
    }

    public String getRealtyRegion() {
        return realtyRegion;
    }

    public void setRealtyRegion(String realtyRegion) {
        this.realtyRegion = realtyRegion;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getRealtyOwner() {
        return realtyOwner;
    }

    public void setRealtyOwner(String realtyOwner) {
        this.realtyOwner = realtyOwner;
    }

    public BigDecimal getManageAmount() {
        return manageAmount;
    }

    public void setManageAmount(BigDecimal manageAmount) {
        this.manageAmount = manageAmount;
    }

    public BigDecimal getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(BigDecimal rentAmount) {
        this.rentAmount = rentAmount;
    }

    public BigDecimal getWaterAmount() {
        return waterAmount;
    }

    public void setWaterAmount(BigDecimal waterAmount) {
        this.waterAmount = waterAmount;
    }

    public BigDecimal getElectricAmount() {
        return electricAmount;
    }

    public void setElectricAmount(BigDecimal electricAmount) {
        this.electricAmount = electricAmount;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getTotalAmountIgnorePenalty() {
        return totalAmountIgnorePenalty;
    }

    public void setTotalAmountIgnorePenalty(BigDecimal totalAmountIgnorePenalty) {
        this.totalAmountIgnorePenalty = totalAmountIgnorePenalty;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
