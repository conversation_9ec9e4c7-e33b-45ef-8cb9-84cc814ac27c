package com.senox.web.vo;

import com.senox.pm.constant.PayWay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/19 8:34
 */
@Getter
@Setter
@ApiModel("物业账单支付请求参数")
public class BillPayRequestVo implements Serializable {

    private static final long serialVersionUID = -5273244624350805268L;

    @NotNull(message = "无效的支付方式")
    @ApiModelProperty("支付方式")
    private PayWay payWay;

    @ApiModelProperty("付款码")
    private String authCode;

    @ApiModelProperty("支付终端序列号")
    private String deviceSn;

    @ApiModelProperty(value = "请求ip", hidden = true)
    private String requestIp;

    @ApiModelProperty("账单id")
    private List<Long> billIds;

    @ApiModelProperty("账单信息")
    private List<BillDiscountVo> bills;

    @ApiModelProperty("免滞纳金账单")
    private List<Long> ignorePenaltyIds;

    @ApiModelProperty("滞纳金减免金额")
    private BigDecimal ignorePenaltyAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "收费员", hidden = true)
    private Long tollMan;

    @ApiModelProperty(value = "退费", hidden = true)
    private Boolean refund;


}
