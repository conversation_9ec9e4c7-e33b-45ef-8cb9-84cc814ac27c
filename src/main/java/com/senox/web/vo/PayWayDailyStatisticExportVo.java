package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.NullToZeroDecimalConvertor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/2/29 15:32
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class PayWayDailyStatisticExportVo implements Serializable {

    private static final long serialVersionUID = -891568724041654706L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private String serialNo;

    @ExcelProperty(value = "收费日期", converter = LocalDateExcelConverter.class)
    private LocalDate tollDate;

    @ExcelProperty(value = "农商行聚合支付", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal drcAmount;

    @ExcelProperty(value = "农商015聚合支付", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal drc015Amount;

    @ExcelProperty(value = "农商行转账", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal drcTransferAmount;

    @ExcelProperty(value = "农商行托收", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal drcWithholdAmount;

    @ExcelProperty(value = "莞银通聚合支付", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal bodAmount;

    @ExcelProperty(value = "莞银通转账", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal bodTransferAmount;

    @ExcelProperty(value = "莞银通扫码", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal bodScanAmount;

    @ExcelProperty(value = "现金支付", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cashAmount;

    @ExcelProperty(value = "刷卡", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cardAmount;

    @ExcelProperty(value = "罚没", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal confiscateAmount;

    @ExcelProperty(value = "合计", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal totalAmount;
}
