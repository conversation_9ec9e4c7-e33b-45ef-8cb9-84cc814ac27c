package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-1-26
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class LogisticsFreightExportVo {

    /**
     * 运营部门
     */
    @ExcelProperty("运营部门")
    private String operationsDepartment;

    /**
     * 收货日期
     */
    @ExcelProperty({"收货信息", "收货日期"})
    private String receivingDate;

    /**
     * 收货单号
     */
    @ExcelProperty({"收货信息", "收货单号"})
    private String receivingNo;

    /**
     * 寄件客户名
     */
    @ExcelProperty({"托运方信息", "客户"})
    private String senderCustomerName;

    /**
     * 寄件客户联系方式
     */
    @ExcelProperty({"托运方信息", "电话"})
    private String senderCustomerContact;

    /**
     * 寄件数
     */
    @ExcelProperty({"托运方信息", "件数"})
    private Integer senderPieces;

    /**
     * 寄件运费
     */
    @ExcelProperty({"托运方信息", "运费"})
    private BigDecimal senderFreightCharge;

    /**
     * 寄件结算类型
     */
    @ExcelProperty({"托运方信息", "结算类型"})
    private String senderSettlementType;

    /**
     * 收货客户名
     */
    @ExcelProperty({"收货方信息", "客户"})
    private String receivingCustomerName;

    /**
     * 收货客户地址
     */
    @ExcelProperty({"收货方信息", "地址"})
    private String receivingCustomerAddress;

    /**
     * 收货客户联系方式
     */
    @ExcelProperty({"收货方信息", "电话"})
    private String receivingCustomerContact;

    /**
     * 中转物流公司
     */
    @ExcelProperty({"中转方信息", "物流公司"})
    private String transferLogisticsCompany;

    /**
     * 中转物流单号
     */
    @ExcelProperty({"中转方信息", "物流单号"})
    private String transferLogisticsNo;

    /**
     * 中转运费
     */
    @ExcelProperty({"中转方信息", "运费"})
    private BigDecimal transferCharge;

    /**
     *
     */
    @ExcelProperty("利润")
    private BigDecimal profitAmount;

    /**
     * 录入时间
     */
    @ExcelProperty("录入时间")
    private String createTime;

    /**
     * 录入员
     */
    @ExcelProperty("录入员")
    private String creatorName;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;


    /**
     * 支付金额
     */
    @ExcelProperty("支付金额")
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @ExcelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 账单状态：0初始化 1已支付
     */
    @ExcelProperty("支付状态")
    private String status;

    /**
     * 支付备注
     */
    @ExcelProperty("支付备注")
    private String paidRemark;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payWay;

    /**
     * 收费员
     */
    @ExcelProperty("收费员")
    private String tollMan;
}
