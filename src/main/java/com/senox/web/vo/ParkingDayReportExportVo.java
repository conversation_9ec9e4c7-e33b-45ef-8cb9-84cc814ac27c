package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/5/7 15:45
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class ParkingDayReportExportVo implements Serializable {

    private static final long serialVersionUID = 854224001545289585L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String reportDate;

    @ColumnWidth(14)
    @ExcelProperty("进场车次")
    private Integer entryCount;

    @ColumnWidth(14)
    @ExcelProperty("离场车次")
    private Integer exitCount;

    @ColumnWidth(14)
    @ExcelProperty("排队车次")
    private Integer queueCount;

    @ColumnWidth(14)
    @ExcelProperty("交易车次")
    private Integer tradeCount;

    @ColumnWidth(14)
    @ExcelProperty("交易费")
    private BigDecimal tradeAmount;

    @ColumnWidth(14)
    @ExcelProperty("超时费用")
    private BigDecimal exceedAmount;

    @ColumnWidth(14)
    @ExcelProperty("合计金额")
    private BigDecimal sumAmount;

}
