package com.senox.web.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-6-20
 */
@Getter
@Setter
public class NormalTemperatureWarehousingBillImportExcelVo {

    /**
     * 商户名
     */
    private String merchantName;

    /**
     * 入库装卸费
     */
    private BigDecimal handlingCharges;

    /**
     * 出库装车费
     */
    private BigDecimal loadingCharges;

    /**
     * 出库拣货费
     */
    private BigDecimal sortingCharges;

    /**
     * 库存费
     */
    private BigDecimal storageCharges;

    /**
     * 运费
     */
    private BigDecimal freightCharges;

    /**
     * 其他费用
     */
    private BigDecimal otherCharges;

    /**
     * 备注
     */
    private String remark;

}
