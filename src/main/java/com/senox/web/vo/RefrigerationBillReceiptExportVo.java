package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/2/28 14:50
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RefrigerationBillReceiptExportVo implements Serializable {

    private static final long serialVersionUID = 740094252353236927L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("客户编号")
    private String customerSerial;

    @ExcelProperty("客户名")
    private String customerName;

    @ExcelProperty("库存费")
    private BigDecimal storageCharge;

    @ExcelProperty("处置费")
    private BigDecimal disposalCharge;

    @ExcelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ExcelProperty("过车费")
    private BigDecimal passingCharge;

    @ExcelProperty("拉膜费")
    private BigDecimal membraneCharge;

    @ExcelProperty("分拣费")
    private BigDecimal sortingCharge;

    @ExcelProperty("加班费")
    private BigDecimal overtimeCharge;

    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    @ExcelProperty("合计")
    private BigDecimal totalCharge;

    @ColumnWidth(20)
    @ExcelProperty(value = "开票时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime receiptTime;

    @ExcelProperty("开票人")
    private String receiptMan;

    @ExcelProperty("发票备注")
    private String receiptRemark;
}
