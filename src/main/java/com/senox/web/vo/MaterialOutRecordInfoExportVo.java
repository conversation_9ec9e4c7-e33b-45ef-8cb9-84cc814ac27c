package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ContentRowHeight(18)
@HeadRowHeight(21)
public class MaterialOutRecordInfoExportVo {

    /**
     * 出仓单号
     */
    @ExcelProperty("出仓单号")
    private Long outBill;

    /**
     * 出仓时间
     */
    @ExcelProperty("出仓时间")
    private LocalDateTime outTime;

    /**
     * 出仓类型(0:普通出仓,1:调拨出仓)
     */
    @ExcelProperty("出仓类型")
    private String outType;

    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    private Long materialCode;

    /**
     * 物料编码名
     */
    @ExcelProperty("物料编码名")
    private String materialCodeName;

    /**
     * 类型
     */
    @ExcelProperty("类型")
    private String typeName;

    /**
     * 物料规格
     */
    @ExcelProperty("物料规格")
    private String materialNorms;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String materialRemark;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unitName;

    /**
     * 请领数量
     */
    @ExcelProperty("请领数量")
    private Long giveNumber;

    /**
     * 单价
     */
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @ExcelProperty("总价")
    private BigDecimal totalPrice;

    /**
     * 领用部门
     */
    @ExcelProperty("领用部门")
    private String departmentName;
}
