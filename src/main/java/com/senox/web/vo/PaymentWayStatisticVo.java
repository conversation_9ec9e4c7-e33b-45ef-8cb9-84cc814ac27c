package com.senox.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/3/12 15:12
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentWayStatisticVo  {

    private static final long serialVersionUID = 985827148724154136L;

    @ApiModelProperty("收费日期")
    private LocalDate tollDate;

    @ApiModelProperty("蛋品停车收费")
    private BigDecimal parkingAmount;

    @ApiModelProperty("物业收费")
    private BigDecimal realtyAmount;

    @ApiModelProperty("一次性收费")
    private BigDecimal oneTimeFeeAmount;

    @ApiModelProperty("一次性退费")
    private BigDecimal oneTimeFeeRefund;

    @ApiModelProperty("冷藏收费")
    private BigDecimal refrigerationAmount;

    @ApiModelProperty("物维收费")
    private BigDecimal maintainAmount;

    @ApiModelProperty("广告收费")
    private BigDecimal advertisingAmount;

    @ApiModelProperty("三轮配送收费")
    private BigDecimal bicycleAmount;

    @ApiModelProperty("押金收费")
    private BigDecimal depositAmount;

    @ApiModelProperty("押金退费")
    private BigDecimal depositRefund;

    @ApiModelProperty("云仓收费")
    private BigDecimal cloudWarehousingAmount;

    @ApiModelProperty("干仓收费")
    private BigDecimal normalTemperatureWarehousingAmount;

    @ApiModelProperty("三轮车车牌费")
    private BigDecimal cycleAmount;

    @ApiModelProperty("园区月卡停车收费")
    private BigDecimal marketMonthParkingAmount;

    @ApiModelProperty("园区临卡停车收费")
    private BigDecimal marketParkingAmount;

    @ApiModelProperty("蛋品区押金收费")
    private BigDecimal eggDepositAmount;

    @ApiModelProperty("蛋品区押金退费")
    private BigDecimal eggDepositRefundAmount;

    @ApiModelProperty("珠三角收费")
    private BigDecimal logisticFreightAmount;

    @ApiModelProperty("城际运输收费")
    private BigDecimal logisticTransportAmount;

    @ApiModelProperty("收费合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("备注")
    private String remark;

}
