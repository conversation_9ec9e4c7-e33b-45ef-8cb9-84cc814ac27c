package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/8/10 8:13
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class CycleExportVo implements Serializable {

    private static final long serialVersionUID = -1886416016795310901L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty("三轮车牌")
    private String license;

    @ColumnWidth(20)
    @ExcelProperty("原车牌")
    private String oldLicense;

    @ExcelProperty("区域")
    private String region;

    @ColumnWidth(20)
    @ExcelProperty("物业编码")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名称")
    private String realtyName;

    @ExcelProperty("负责人")
    private String owner;

    @ColumnWidth(30)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(30)
    @ExcelProperty("收费金额")
    private BigDecimal amount;

    @ColumnWidth(30)
    @ExcelProperty("实收金额")
    private BigDecimal actualAmount;

    @ExcelProperty("录入人")
    private String inputMan;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "录入时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime inputTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "缴费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;
}
