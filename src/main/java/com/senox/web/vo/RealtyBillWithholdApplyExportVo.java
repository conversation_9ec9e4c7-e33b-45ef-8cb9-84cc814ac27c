package com.senox.web.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/17 17:20
 */
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyBillWithholdApplyExportVo implements Serializable {

    private static final long serialVersionUID = 1678540585036247491L;

    /**
     * 序号
     */
    @ColumnWidth(8)
    private Integer serialNo;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 申请日期 yyyyMMdd
     */
    private String applyDate;
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 账号名
     */
    private String accountName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 状态
     */
    @ColumnWidth(8)
    private Integer status;

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
