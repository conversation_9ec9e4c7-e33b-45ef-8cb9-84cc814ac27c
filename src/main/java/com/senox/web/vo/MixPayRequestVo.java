package com.senox.web.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/3 14:10
 */
@Getter
@Setter
@ToString
public class MixPayRequestVo implements Serializable {

    private static final long serialVersionUID = -7985928341018314489L;

    @ApiModelProperty("账单id")
    private Long billId;

    @ApiModelProperty("账单id列表")
    private List<Long> billIds;

    @Min(value = 0, message = "无效的滞纳金减免金额")
    @ApiModelProperty("滞纳金减免金额")
    private BigDecimal penaltyIgnoreAmount;

    @NotNull(message = "支付明细不能为空")
    @ApiModelProperty("支付明细")
    private List<PayAmountVo> details;

    @ApiModelProperty(value = "收费员", hidden = true)
    private Long tollMan;

    @ApiModelProperty(value = "请求ip", hidden = true)
    private String requestIp;

}
