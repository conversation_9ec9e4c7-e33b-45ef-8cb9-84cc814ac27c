package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/4/20 10:49
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyDepositTollExportVo implements Serializable {

    private static final long serialVersionUID = -3461014863719287540L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "收费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime tollTime;

    @ExcelProperty("档位编号")
    private String realtySerial;

    @ColumnWidth(20)
    @ExcelProperty("档位名称")
    private String realtyName;

    @ColumnWidth(20)
    @ExcelProperty("档主")
    private String customerName;

    @ExcelProperty("押金类型")
    private String feeName;

    @ExcelProperty("金额")
    private BigDecimal amount;

    @ExcelProperty("收据编号")
    private String tollSerial;

    @ExcelProperty("收费员")
    private String tollMan;

    @ExcelProperty("备注")
    private String remark;
}
