package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-12-1
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class BicycleBillExportVo {

    /**
     * 账单日
     */
    @ExcelProperty("账单日")
    private String billDate;

    /**
     * 账单年份
     */
    @ExcelProperty("账单年份")
    private Integer billYear;

    /**
     * 账单月份
     */
    @ExcelProperty("账单月份")
    private Integer billMonth;

    /**
     * 配送单流水号
     */
    @ExcelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    @ExcelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 商户名
     */
    @ExcelProperty("商户")
    private String merchantName;

    /**
     * 冷藏编号
     */
    @ExcelProperty("冷藏编号")
    private String rcSerial;

    /**
     * 结算周期
     */
    @ExcelProperty("结算周期")
    private String settlePeriod;
    /**
     * 配送金额
     */
    @ExcelProperty("配送金额")
    private BigDecimal deliveryAmount;

    /**
     * 其他金额
     */
    @ExcelProperty("其他金额")
    private BigDecimal otherAmount;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    private BigDecimal amount;


}
