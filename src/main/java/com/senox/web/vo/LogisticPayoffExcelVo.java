package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/1/16 8:37
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ColumnWidth(16)
@ContentRowHeight(20)
public class LogisticPayoffExcelVo implements Serializable {

    private static final long serialVersionUID = -6440171156572655601L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ExcelProperty(value = "发货日期", converter = LocalDateExcelConverter.class)
    private LocalDate billDate;

    @ExcelProperty("商户")
    private String merchant;

    @ExcelProperty("订单件数")
    private BigDecimal productCount;

    @ExcelProperty("订单总价")
    private BigDecimal productAmount;

    @ExcelProperty("满减额")
    private BigDecimal productFullReduction;

    @ExcelProperty("应收金额")
    private BigDecimal productToPaid;

    @ExcelProperty("已收金额")
    private BigDecimal productPaid;

    @ExcelProperty("欠款")
    private BigDecimal productOwe;

    @ExcelProperty("其他减款")
    private BigDecimal productDeduction;

    @ExcelProperty("差异金额")
    private BigDecimal productDiversity;

    @ExcelProperty("物流费")
    private BigDecimal shipAmount;

    @ExcelProperty("应结算金额")
    private BigDecimal totalAmount;

    @ExcelProperty("差异原因")
    private String remark;

}
