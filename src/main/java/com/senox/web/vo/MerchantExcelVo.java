package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/6 13:53
 */
@Data
public class MerchantExcelVo implements Serializable {

    private static final long serialVersionUID = 1155709841582553029L;

    @ExcelProperty(value = "货主名称")
    private String name;

    @ExcelProperty(value = "电话")
    private String contact;

    @ExcelProperty(value = "客户编码")
    private String rcSerial;

    @ExcelProperty(value = "冷藏抬头")
    private String rcTaxHeader;

    @ExcelIgnore
    private Boolean bicycleAuth;

    @ExcelIgnore
    private Boolean dryAuth;

    @ExcelIgnore
    private Boolean duoduo;
}
