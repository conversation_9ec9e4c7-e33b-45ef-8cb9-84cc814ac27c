package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/12/16 16:29
 */
@ColumnWidth(16)
public class MonthlyCarBillExportVo implements Serializable {

    private static final long serialVersionUID = -3011992598199792292L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ExcelProperty("车牌")
    private String carNo;

    @ColumnWidth(12)
    @ExcelProperty("车型")
    private String carType;

    @ExcelProperty(value = "起租日期", converter = LocalDateExcelConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "终止日期", converter = LocalDateExcelConverter.class)
    private LocalDate endDate;

    @ColumnWidth(8)
    @ExcelProperty("月数")
    private Integer months;

    @ColumnWidth(8)
    @ExcelProperty("天数")
    private Integer days;

    @ColumnWidth(10)
    @ExcelProperty("手续费")
    private BigDecimal serviceCharged;

    @ColumnWidth(10)
    @ExcelProperty("单价")
    private BigDecimal price;

    @ColumnWidth(10)
    @ExcelProperty("租金")
    private BigDecimal amount;

    @ExcelProperty("优惠后租金")
    private BigDecimal finalAmount;

    @ColumnWidth(10)
    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    @ExcelProperty("签订方式")
    private String signType;

    @ColumnWidth(12)
    @ExcelProperty("原车牌")
    private String oldCarNo;

    @ExcelProperty("客户")
    private String customerName;

    @ColumnWidth(18)
    @ExcelProperty("身份证号")
    private String customerIdno;

    @ExcelProperty("手机号")
    private String customerContact;

    @ColumnWidth(20)
    @ExcelProperty("档位地址")
    private String realtyAddress;

    @ExcelProperty("所在场地")
    private String inboard;

    @ExcelProperty("操作员")
    private String creator;

    @ColumnWidth(20)
    @ExcelProperty(value = "操作时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime createTime;

    @ExcelProperty("收费状态")
    private String status;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "收费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime tollTime;

    @ExcelProperty("退费金额")
    private BigDecimal refundAmount;

    @ExcelProperty("退费单号")
    private String refundSerial;

    @ExcelProperty("退费人")
    private String refundMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "退费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime refundTime;

    @ExcelProperty("合计")
    private BigDecimal total;


    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Integer getMonths() {
        return months;
    }

    public void setMonths(Integer months) {
        this.months = months;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public BigDecimal getServiceCharged() {
        return serviceCharged;
    }

    public void setServiceCharged(BigDecimal serviceCharged) {
        this.serviceCharged = serviceCharged;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getOldCarNo() {
        return oldCarNo;
    }

    public void setOldCarNo(String oldCarNo) {
        this.oldCarNo = oldCarNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerIdno() {
        return customerIdno;
    }

    public void setCustomerIdno(String customerIdno) {
        this.customerIdno = customerIdno;
    }

    public String getCustomerContact() {
        return customerContact;
    }

    public void setCustomerContact(String customerContact) {
        this.customerContact = customerContact;
    }

    public String getRealtyAddress() {
        return realtyAddress;
    }

    public void setRealtyAddress(String realtyAddress) {
        this.realtyAddress = realtyAddress;
    }

    public String getInboard() {
        return inboard;
    }

    public void setInboard(String inboard) {
        this.inboard = inboard;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTollSerial() {
        return tollSerial;
    }

    public void setTollSerial(String tollSerial) {
        this.tollSerial = tollSerial;
    }

    public String getTollMan() {
        return tollMan;
    }

    public void setTollMan(String tollMan) {
        this.tollMan = tollMan;
    }

    public LocalDateTime getTollTime() {
        return tollTime;
    }

    public void setTollTime(LocalDateTime tollTime) {
        this.tollTime = tollTime;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundSerial() {
        return refundSerial;
    }

    public void setRefundSerial(String refundSerial) {
        this.refundSerial = refundSerial;
    }

    public String getRefundMan() {
        return refundMan;
    }

    public void setRefundMan(String refundMan) {
        this.refundMan = refundMan;
    }

    public LocalDateTime getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(LocalDateTime refundTime) {
        this.refundTime = refundTime;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }
}
