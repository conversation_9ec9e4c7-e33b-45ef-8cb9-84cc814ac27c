package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import com.senox.web.convert.PaywayExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/13 13:49
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RefrigerationTollExcelVo implements Serializable {

    private static final long serialVersionUID = 1943268410366596231L;

    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty(value = "收款日期", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("票据号")
    private String tollSerial;

    @ExcelProperty("收款人")
    private String tollMan;

    @ExcelProperty("账单年")
    private Integer billYear;

    @ExcelProperty("账单月")
    private Integer billMonth;

    @ExcelProperty("客户编号")
    private String customerSerial;

    @ExcelProperty("客户名")
    private String customerName;

    @ExcelProperty(value = "支付方式", converter = PaywayExcelConverter.class)
    private Integer payWay;

    @ExcelProperty("库存费")
    private BigDecimal storageCharge;

    @ExcelProperty("处置费")
    private BigDecimal disposalCharge;

    @ExcelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ExcelProperty("过车费")
    private BigDecimal passingCharge;

    @ExcelProperty("拉膜费")
    private BigDecimal membraneCharge;

    @ExcelProperty("分拣费")
    private BigDecimal sortingCharge;

    @ExcelProperty("加班费")
    private BigDecimal overtimeCharge;

    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    @ExcelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ExcelProperty("滞纳金（应）")
    private BigDecimal penaltyChargeToPaid;

    @ExcelProperty("滞纳金（实）")
    private BigDecimal penaltyCharge;

    @ExcelProperty("合计")
    private BigDecimal totalCharge;
}
