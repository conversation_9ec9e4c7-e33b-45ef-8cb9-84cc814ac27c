package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.GenderExcelConverter;
import com.senox.web.convert.LocalDateExcelConverter;
import lombok.Data;
import org.checkerframework.checker.units.qual.C;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2021/1/25 14:58
 */
@Data
@ColumnWidth(16)
@ContentRowHeight(20)
public class CustomerExportVo implements Serializable {

    private static final long serialVersionUID = -2335203842623102569L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private String serialNo;

    @ColumnWidth(14)
    @ExcelProperty("姓名")
    private String name;

    @ColumnWidth(8)
    @ExcelProperty(value = "企业", converter = BooleanExcelConvertor.class)
    private Boolean enterprise;

    @ColumnWidth(14)
    @ExcelProperty("企业信用代码")
    private String enterpriseCode;

    @ColumnWidth(14)
    @ExcelProperty("法人")
    private String legalPerson;

    @ColumnWidth(10)
    @ExcelProperty(value = "性别", converter = GenderExcelConverter.class)
    private Integer gender;

    @ExcelProperty(value = "出生日期", converter = LocalDateExcelConverter.class)
    private LocalDate bornDate;

    @ColumnWidth(10)
    @ExcelProperty("民族")
    private String nation;

    @ColumnWidth(24)
    @ExcelProperty("证件号")
    private String idcard;

    @ColumnWidth(20)
    @ExcelProperty("手机号")
    private String telephone;

    @ColumnWidth(20)
    @ExcelProperty("邮箱")
    private String email;

    @ColumnWidth(25)
    @ExcelProperty({"物业号", "区域"})
    private String workplaceRegionName;

    @ColumnWidth(25)
    @ExcelProperty({"物业号", "街道"})
    private String workplaceStreetName;

    @ColumnWidth(30)
    @ExcelProperty({"物业号", "地址"})
    private String workplaceAddress;

    @ColumnWidth(60)
    @ExcelProperty(value = "地址")
    private String address;


}
