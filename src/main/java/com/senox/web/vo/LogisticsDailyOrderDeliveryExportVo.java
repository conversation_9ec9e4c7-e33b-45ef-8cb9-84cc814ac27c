package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-1-25
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class LogisticsDailyOrderDeliveryExportVo {

    /**
     * 下单日期
     */
    @ExcelProperty("下单日期")
    private String orderTime;

    /**
     * 送货时间
     */
    @ExcelProperty("送货时间")
    private String sendTime;

    /**
     * 下单人
     */
    @ExcelProperty("下单人")
    private String orderPerson;

    /**
     * 类别
     */
    @ExcelProperty("类别")
    private String type;

    /**
     * 配送单号
     */
    @ExcelProperty("配送单号")
    private String orderDeliveryNo;

    /**
     * 下单件数
     */
    @ExcelProperty("下单件数")
    private Integer orderPieces;

    /**
     * 下单重量（KG）
     */
    @ExcelProperty("下单重量（KG）")
    private BigDecimal orderTotalKilograms;

    /**
     * 配送车牌
     */
    @ExcelProperty("配送车牌")
    private String orderDeliveryCarNo;

    /**
     * 件数
     */
    @ExcelProperty({"物流费核算", "件数"})
    private Integer pieces;

    /**
     * 重量（KG）
     */
    @ExcelProperty({"物流费核算", "重量（KG）"})
    private BigDecimal kilograms;

    /**
     * 运费单价
     */
    @ExcelProperty({"物流费核算", "运费单价"})
    private BigDecimal freightUnitPrice;

    /**
     * 应收运费金额
     */
    @ExcelProperty({"物流费核算", "应收运费金额"})
    private BigDecimal receivableFreightCharge;

    /**
     * 实收运费金额
     */
    @ExcelProperty({"物流费核算", "实收运费金额"})
    private BigDecimal receivedFreightCharge;

    /**
     * 差额
     */
    @ExcelProperty("差额")
    private BigDecimal discrepancyAmount;

    /**
     * 录入时间
     */
    @ExcelProperty("录入时间")
    private String createTime;

    /**
     * 录入人
     */
    @ExcelProperty("录入人")
    private String creatorName;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}
