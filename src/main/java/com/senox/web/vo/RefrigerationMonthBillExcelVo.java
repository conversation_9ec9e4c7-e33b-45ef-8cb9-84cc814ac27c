package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BillStatusExcelConvertor;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 冷藏日账单 excel 格式
 * <AUTHOR>
 * @date 2022/12/16 10:52
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RefrigerationMonthBillExcelVo implements Serializable {

    private static final long serialVersionUID = 2281015684662060131L;

    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty(value = "年份")
    private Integer billYear;

    @ExcelProperty(value = "月份")
    private Integer billMonth;

    @ExcelProperty(value = "客户编号")
    private String customerSerial;

    @ExcelProperty(value = "客户名称")
    private String customerName;

    @ExcelProperty(value = "库存费")
    private BigDecimal storageCharge;

    @ExcelProperty(value = "处置费")
    private BigDecimal disposalCharge;

    @ExcelProperty(value = "装卸费")
    private BigDecimal handlingCharge;

    @ExcelProperty(value = "过车费")
    private BigDecimal passingCharge;

    @ExcelProperty(value = "拉膜费")
    private BigDecimal membraneCharge;

    @ExcelProperty(value = "分拣费")
    private BigDecimal sortingCharge;

    @ExcelProperty(value = "加班费")
    private BigDecimal overtimeCharge;

    @ExcelProperty(value = "其他费用")
    private BigDecimal otherCharge;

    @ExcelProperty(value = "配送费用")
    private BigDecimal deliveryCharge;

    @ExcelProperty(value = "滞纳金（应）")
    private BigDecimal penaltyCharge;

    @ExcelProperty(value = "滞纳金（实）")
    private BigDecimal penaltyPaid;

    @ExcelProperty(value = "应收")
    private BigDecimal totalCharge;

    @ExcelProperty(value = "实收")
    private BigDecimal paidAmount;

    @ExcelProperty(value = "坏账核销金额")
    private BigDecimal badDebtAmount;

    @ExcelProperty(value = "押金")
    private BigDecimal depositAmount;

    @ExcelProperty(value = "支付状态", converter = BillStatusExcelConvertor.class)
    private Integer status;

    @ExcelProperty(value = "支付时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty(value = "备注")
    private String remark;
}
