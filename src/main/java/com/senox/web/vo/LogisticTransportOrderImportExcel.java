package com.senox.web.vo;

import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-05
 **/
@Getter
@Setter
public class LogisticTransportOrderImportExcel {

    /**
     * 发货人id
     */
    private Long consignorId;

    /**
     * 发货人姓名
     */
    private String consignorName;

    /**
     * 发货人编码
     */
    private String consignorCode;

    /**
     * 发货人电话
     */
    private String consignorPhone;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货人电话
     */
    private String consigneePhone;

    /**
     * 类别
     */
    private LogisticTransportCategory category;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    private Boolean charter;

    /**
     * 始发站
     */
    private String departureStation;

    /**
     * 目的站
     */
    private String destinationStation;

    /**
     * 件数
     */
    private Integer pieces;

    /**
     * 装载重量(kg)
     */
    private BigDecimal loadingWeight;

    /**
     * 运费
     */
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    private BigDecimal otherCharge;

    /**
     * 应收费用
     */
    private BigDecimal receivableFreightCharge;

    /**
     * 付款人
     */
    private LogisticTransportOrderPayer payer;

    /**
     * 备注
     */
    private String remark;
}
