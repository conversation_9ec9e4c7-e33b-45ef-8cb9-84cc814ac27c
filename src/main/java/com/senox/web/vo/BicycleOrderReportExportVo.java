package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/10 14:42
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class BicycleOrderReportExportVo implements Serializable {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String reportDate;

    @ColumnWidth(14)
    @ExcelProperty("商户名")
    private String merchantName;

    @ColumnWidth(14)
    @ExcelProperty("总件数")
    private BigDecimal totalPieces;

    @ColumnWidth(14)
    @ExcelProperty("总单数")
    private Integer totalCount;

    @ColumnWidth(14)
    @ExcelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ColumnWidth(14)
    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    @ColumnWidth(14)
    @ExcelProperty("合计")
    private BigDecimal totalCharge;
}
