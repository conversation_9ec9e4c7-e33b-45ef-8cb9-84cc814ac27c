package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/25 11:48
 */
@Getter
@Setter
@ToString
@ApiModel("应付账单页")
public class RealtyPayoffPage<T> extends PageResult<T> {

    @ApiModelProperty("租金")
    private BigDecimal rentAmount;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    public RealtyPayoffPage() {
        init();
    }

    public RealtyPayoffPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.rentAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }


    public static <T> RealtyPayoffPage<T> emptyPage() {
        return new RealtyPayoffPage<>();
    }
}
