package com.senox.web.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/2/1 11:29
 */
@Getter
@Setter
@ToString
@ApiModel("滞纳金配置")
public class PenaltySettingVo implements Serializable {

    private static final long serialVersionUID = 430454101883668147L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的年月")
    @ApiModelProperty("年月")
    private String billYearMonth;

    @NotNull(message = "无效的类型")
    @ApiModelProperty("类型 1 物业；2 冷藏")
    private Integer billType;

    @ApiModelProperty("滞纳金起征日期")
    private LocalDate penaltyStartDate;

    @ApiModelProperty("滞纳金起算日期")
    private LocalDate penaltyCalDate;

    @ApiModelProperty("免收滞纳金")
    private Boolean penaltyFree;

}
