package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/28 9:02
 */
@Data
public class CustomerBusinessExcelVo implements Serializable {

    private static final long serialVersionUID = 8773492284872999250L;


    @ExcelProperty(value = "物业号", index = 1)
    private String realtySerialNo;

    @ExcelProperty(value = "招牌号", index = 2)
    private String signNo;

    @ExcelProperty(value = "营业执照全称", index = 3)
    private String businessLicense;

    @ExcelProperty(value = "法人代表", index = 4)
    private String legalRepresentative;

    @ExcelProperty(value = "联系手机", index = 5)
    private String contract;

    @ExcelProperty(value = "备注", index = 8)
    private String remark;

    @ExcelIgnore
    private String mainContract;

    @ExcelIgnore
    private String sideContract;

    @ExcelIgnore
    private Boolean eggArea;
}
