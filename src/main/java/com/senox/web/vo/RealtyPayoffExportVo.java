package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/25 13:57
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyPayoffExportVo implements Serializable {

    private static final long serialVersionUID = -7579449063888719258L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ExcelProperty("合同号")
    private String contractNo;

    @ExcelProperty("物业号")
    private String realtySerial;

    @ColumnWidth(30)
    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty("档主")
    private String customerName;

    @ExcelProperty("年份")
    private Integer billYear;

    @ExcelProperty("月份")
    private Integer billMonth;

    @ExcelProperty("租金")
    private BigDecimal rentAmount;

    @ExcelProperty("首月手续费")
    private BigDecimal firstCharge;

    @ExcelProperty("业主手续费")
    private BigDecimal ownerCharge;

    @ExcelProperty("应付租金")
    private BigDecimal rentToPay;

    @ExcelProperty("缴费状态")
    private String paidStatus;
}
