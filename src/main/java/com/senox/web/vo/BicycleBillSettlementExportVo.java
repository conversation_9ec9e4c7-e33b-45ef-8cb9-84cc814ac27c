package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-11-10
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class BicycleBillSettlementExportVo {

    /**
     * id
     */
    @ExcelProperty("编号")
    private String id;

    /**
     * 账单日期
     */
    @ExcelProperty("账单日期")
    private String billYearMonth;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String merchantName;

    /**
     * 客户编号
     */
    @ExcelProperty("客户编号")
    private String rcSerial;

    /**
     * 应收
     */
    @ExcelProperty("应收")
    private BigDecimal amount;

    /**
     * 实收
     */
    @ExcelProperty("实收")
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @ExcelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 是否缴费
     */
    @ExcelProperty("是否缴费")
    private String status;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payWay;

    /**
     * 收费员
     */
    @ExcelProperty("收费员")
    private String tollManName;

}
