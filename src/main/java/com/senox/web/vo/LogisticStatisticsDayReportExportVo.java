package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/15 10:06
 */
@ColumnWidth(16)
@ContentRowHeight(16)
@Data
public class LogisticStatisticsDayReportExportVo implements Serializable {

    private static final long serialVersionUID = 7710592710821281338L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private String serialNo;

    @ColumnWidth(14)
    @ExcelProperty(value = "日期", converter = LocalDateExcelConverter.class)
    private LocalDate reportDate;

    @ColumnWidth(14)
    @ExcelProperty("运营部门")
    private String operationsDepartment;

    @ColumnWidth(14)
    @ExcelProperty("发货人")
    private String shipper;

    @ColumnWidth(14)
    @ExcelProperty("物流单号")
    private String logisticsNo;

    @ColumnWidth(14)
    @ExcelProperty("收入类别")
    private String incomeType;

    @ColumnWidth(14)
    @ExcelProperty("司机名")
    private String driverName;

    @ColumnWidth(14)
    @ExcelProperty("车牌")
    private String carNo;

    @ColumnWidth(6)
    @ExcelProperty("是否包车")
    private String charteredBus;

    @ColumnWidth(14)
    @ExcelProperty("始发站")
    private String departureStation;

    @ColumnWidth(14)
    @ExcelProperty("目的站")
    private String destinationStation;

    @ColumnWidth(6)
    @ExcelProperty("件数")
    private BigDecimal pieces;

    @ColumnWidth(8)
    @ExcelProperty({"重量（吨）", "装载重量"})
    private BigDecimal loadingWeight;

    @ColumnWidth(18)
    @ExcelProperty({"重量（吨）", "入库重量"})
    private BigDecimal storageWeight;

    @ColumnWidth(18)
    @ExcelProperty({"重量（吨）", "未入库重量"})
    private BigDecimal unStockedWeight;

    @ColumnWidth(6)
    @ExcelProperty("体积")
    private BigDecimal volume;

    @ColumnWidth(8)
    @ExcelProperty("运费收入金额")
    private BigDecimal freightIncomeAmount;

    @ColumnWidth(12)
    @ExcelProperty(value = {"货运收款明细", "收款日期"}, converter = LocalDateExcelConverter.class)
    private LocalDate paymentTime;

    @ColumnWidth(8)
    @ExcelProperty({"货运收款明细", "实收运费金额"})
    private BigDecimal actualFreightAmount;

    @ColumnWidth(14)
    @ExcelProperty({"货运收款明细", "进仓单号"})
    private String warehousingNo;

    @ColumnWidth(8)
    @ExcelProperty({"货运收款明细", "进口冻品优惠"})
    private BigDecimal frozenGoodsDiscounts;

    @ColumnWidth(8)
    @ExcelProperty({"货运收款明细", "未收款金额"})
    private BigDecimal unpaidAmount;

    @ColumnWidth(14)
    @ExcelProperty(value = "录入时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime createTime;

    @ColumnWidth(8)
    @ExcelProperty(value = "录入员")
    private String createName;

    @ColumnWidth(10)
    @ExcelProperty("备注")
    private String remark;
}
