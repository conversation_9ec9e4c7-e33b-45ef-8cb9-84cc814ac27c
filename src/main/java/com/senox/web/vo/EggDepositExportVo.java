package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/11 10:14
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class EggDepositExportVo implements Serializable {

    private static final long serialVersionUID = -402077496287139326L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ExcelProperty("车牌")
    private String occupiedNo;

    @ExcelProperty("联系方式")
    private String contact;

    @ExcelProperty("保证金金额")
    private BigDecimal amount;

    @ExcelProperty("扣款金额")
    private BigDecimal deductionAmount;

    @ExcelProperty("退费金额")
    private BigDecimal refundAmount;

    @ExcelProperty("支付时间")
    private LocalDateTime paidTime;

    @ExcelProperty("退费时间")
    private LocalDateTime refundTime;

    @ExcelProperty("退费员")
    private String refundMan;

    @ExcelProperty("审批状态")
    private String auditStatus;

    @ExcelProperty("退款状态")
    private String status;

    @ExcelProperty("支付方式")
    private String payWay;

    @ExcelProperty("退费方式")
    private String refundPayWay;
}
