package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.BooleanExcelConvertor;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/9/27 16:46
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class OrderTollExportVo implements Serializable {

    private static final long serialVersionUID = -2332534880791396533L;

    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("交易流水号")
    private String tradeNo;

    @ExcelProperty(value = "收款日期", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime paidTime;

    @ExcelProperty("收款人")
    private String tollMan;

    @ExcelProperty("票据号")
    private String billSerial;

    @ExcelProperty("应收年月")
    private String billYearMonth;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ExcelProperty("物业名")
    private String realtyName;

    @ExcelProperty(value = "返租", converter = BooleanExcelConvertor.class)
    private Boolean backLease;

    @ExcelProperty(value = "代租", converter = BooleanExcelConvertor.class)
    private Boolean replaceLease;

    @ExcelProperty(value = "代收租", converter = BooleanExcelConvertor.class)
    private Boolean collectionLease;

    @ExcelProperty("客户")
    private String customerName;

    @ExcelProperty("管理费")
    private BigDecimal manageAmount;

    @ExcelProperty("租金")
    private BigDecimal rentAmount;

    @ExcelProperty("水费")
    private BigDecimal waterAmount;

    @ExcelProperty("电费")
    private BigDecimal electricAmount;

    @ExcelProperty("滞纳金")
    private BigDecimal penaltyAmount;

    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    @ExcelProperty("支付方式")
    private String payWay;

    @ExcelProperty("订单状态")
    private String status;

    @ExcelProperty(value = "发票", converter = BooleanExcelConvertor.class)
    private Boolean receipt;

    @ExcelProperty(value = "发票时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime receiptTime;

    @ExcelProperty("发票备注")
    private String receiptRemark;


}
