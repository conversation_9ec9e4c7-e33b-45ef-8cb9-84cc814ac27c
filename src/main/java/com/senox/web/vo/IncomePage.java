package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/11 14:20
 */
@Getter
@Setter
@ToString
@ApiModel("收益页")
public class IncomePage<T> extends PageResult<T> {

    @ApiModelProperty("收入金额金额")
    private BigDecimal amount;

    @ApiModelProperty("成本金额")
    private BigDecimal cost;

    @ApiModelProperty("分润金额")
    private BigDecimal shareAmount;

    @ApiModelProperty("已支付分润金额")
    private BigDecimal paidShareAmount;

    public IncomePage() {
        init();
    }

    public IncomePage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.amount = BigDecimal.ZERO;
        this.cost = BigDecimal.ZERO;
        this.shareAmount = BigDecimal.ZERO;
        this.paidShareAmount = BigDecimal.ZERO;
    }

    public static <T> IncomePage<T> emptyPage() {
        return new IncomePage<>();
    }
}
