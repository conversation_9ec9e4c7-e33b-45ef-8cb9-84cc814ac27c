package com.senox.web.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/16 14:02
 */
@Getter
@Setter
@ToString
@ApiModel("冷藏账单页")
public class RefrigerationBillPage<T> extends PageResult<T> {

    @ApiModelProperty("库存费")
    private BigDecimal storageCharge;

    @ApiModelProperty("处置费")
    private BigDecimal disposalCharge;

    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ApiModelProperty("过车费")
    private BigDecimal passingCharge;

    @ApiModelProperty("拉膜费")
    private BigDecimal membraneCharge;

    @ApiModelProperty("分拣费")
    private BigDecimal sortingCharge;

    @ApiModelProperty("加班费")
    private BigDecimal overtimeCharge;

    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ApiModelProperty("滞纳金")
    private BigDecimal penaltyCharge;

    @ApiModelProperty("总金额")
    private BigDecimal totalCharge;

    @ApiModelProperty("折扣金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("坏账核销金额")
    private BigDecimal badDebtAmount;

    @ApiModelProperty("实收金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("滞纳金实收")
    private BigDecimal penaltyPaid;


    public RefrigerationBillPage() {
        init();
    }

    public RefrigerationBillPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.storageCharge = BigDecimal.ZERO;
        this.disposalCharge = BigDecimal.ZERO;
        this.handlingCharge = BigDecimal.ZERO;
        this.passingCharge = BigDecimal.ZERO;
        this.membraneCharge = BigDecimal.ZERO;
        this.sortingCharge = BigDecimal.ZERO;
        this.overtimeCharge = BigDecimal.ZERO;
        this.penaltyCharge = BigDecimal.ZERO;
        this.totalCharge = BigDecimal.ZERO;
        this.discountAmount = BigDecimal.ZERO;
        this.badDebtAmount = BigDecimal.ZERO;
        this.paidAmount = BigDecimal.ZERO;
        this.penaltyPaid = BigDecimal.ZERO;
    }

    public static <T> RefrigerationBillPage<T> emptyPage() {
        return new RefrigerationBillPage<>();
    }
}
