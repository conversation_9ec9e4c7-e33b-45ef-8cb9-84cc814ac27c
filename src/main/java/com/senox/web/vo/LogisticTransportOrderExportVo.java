package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class LogisticTransportOrderExportVo {

    /**
     * 订单编号
     */
    @ExcelProperty("订单编号")
    private String serialNo;

    /**
     * 订单日期
     */
    @ExcelProperty("订单日期")
    private String yearMonthDay;

    /**
     * 发货人编码
     */
    @ExcelProperty("发货人编码")
    private String consignorCode;

    /**
     * 发货人
     */
    @ExcelProperty("发货人")
    private String consignorName;

    /**
     * 发货人电话
     */
    @ExcelProperty("发货人电话")
    private String consignorPhone;

    /**
     * 类别
     */
    @ExcelProperty("类别")
    private String category;

    /**
     * 司机
     */
    @ExcelProperty("司机")
    private String driverName;

    /**
     * 车牌
     */
    @ExcelProperty("车牌")
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    @ExcelProperty("是否包车")
    private String charter;

    /**
     * 始发站
     */
    @ExcelProperty("始发站")
    private String departureStation;

    /**
     * 目的站
     */
    @ExcelProperty("目的站")
    private String destinationStation;

    /**
     * 件数
     */
    @ExcelProperty("件数")
    private Integer pieces;

    /**
     * 装载重量(kg)
     */
    @ExcelProperty("装载重量")
    private BigDecimal loadingWeight;

    /**
     * 运费
     */
    @ExcelProperty("运费")
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收运费
     */
    @ExcelProperty("应收运费")
    private BigDecimal receivableFreightCharge;


    /**
     * 付款人
     */
    @ExcelProperty("付款人")
    private String payer;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 审核人
     */
    @ExcelProperty("审核人")
    private String auditorName;

    /**
     * 审核时间
     */
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;
}
