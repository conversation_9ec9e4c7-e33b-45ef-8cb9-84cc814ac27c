package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/14 9:15
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class UnloadingOrderBillExportVo implements Serializable {
    private static final long serialVersionUID = -8349669883888028065L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(12)
    @ExcelProperty("账单日")
    private String billDate;

    @ColumnWidth(12)
    @ExcelProperty("订单编号")
    private String orderNo;

    @ColumnWidth(16)
    @ExcelProperty("客户名")
    private String customerName;

    @ColumnWidth(16)
    @ExcelProperty("装卸地址")
    private String address;

    @ColumnWidth(8)
    @ExcelProperty("车牌")
    private String licensePlate;

    @ColumnWidth(12)
    @ExcelProperty("联系方式")
    private String contact;

    @ColumnWidth(8)
    @ExcelProperty("金额")
    private BigDecimal amount;

    @ColumnWidth(14)
    @ExcelProperty("货物数量")
    private String quantity;

    @ColumnWidth(8)
    @ExcelProperty("支付状态")
    private String status;

    @ColumnWidth(16)
    @ExcelProperty("支付时间")
    private String paidTime;
}
