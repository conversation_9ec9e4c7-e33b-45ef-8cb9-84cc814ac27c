package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-03 15:33
 */
@Getter
@Setter
@ColumnWidth(16)
@ContentRowHeight(20)
public class RealtyExportVo implements Serializable {

    private static final long serialVersionUID = 2241268937320478480L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty("物业编号")
    private String realtySerialNo;

    @ExcelProperty("物业名")
    private String name;

    @ExcelProperty("物业性质")
    private String nature;

    @ExcelProperty("区域名")
    private String regionName;

    @ExcelProperty("街道名")
    private String streetName;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("面积")
    private BigDecimal area;

    @ExcelProperty("区域1")
    private String region1;

    @ExcelProperty("区域2")
    private String region2;
}
