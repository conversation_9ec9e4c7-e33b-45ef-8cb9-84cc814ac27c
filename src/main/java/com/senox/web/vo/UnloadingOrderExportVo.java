package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/6 10:50
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class UnloadingOrderExportVo implements Serializable {
    private static final long serialVersionUID = -6578366127944594981L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    /**
     * 装卸地址
     */
    @ColumnWidth(15)
    @ExcelProperty("装卸地址")
    private String address;

    /**
     * 货物名称
     */
    @ColumnWidth(15)
    @ExcelProperty("货物名称")
    private String goodsName;

    /**
     * 数量
     */
    @ColumnWidth(8)
    @ExcelProperty("数量")
    private String goodsNum;

    /**
     * 装卸组工号
     */
    @ColumnWidth(12)
    @ExcelProperty("装卸组工号")
    private String workers;

    /**
     * 装卸费
     */
    @ColumnWidth(8)
    @ExcelProperty("装卸费")
    private BigDecimal amount;

    /**
     * 缴费日期
     */
    @ColumnWidth(12)
    @ExcelProperty("缴费日期")
    private String billPaidDate;

    /**
     * 缴费人
     */
    @ColumnWidth(8)
    @ExcelProperty("缴费人")
    private String paidMan;

    /**
     * 收费人
     */
    @ColumnWidth(8)
    @ExcelProperty("收费人")
    private String tollMan;

    /**
     * 备注
     */
    @ColumnWidth(15)
    @ExcelProperty("备注")
    private String remark;
}
