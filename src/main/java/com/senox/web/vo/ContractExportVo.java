package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024/9/11 9:12
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class ContractExportVo {

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(16)
    @ExcelProperty("合同编号")
    private String contractNo;

    @ColumnWidth(10)
    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(16)
    @ExcelProperty("物业名称")
    private String realtyName;

    @ColumnWidth(10)
    @ExcelProperty("客户编号")
    private String customerSerial;

    @ColumnWidth(16)
    @ExcelProperty("客户名称")
    private String customerName;

    @ColumnWidth(16)
    @ExcelProperty("客户联系方式")
    private String customerContact;

    @ColumnWidth(20)
    @ExcelProperty("合同开始日期")
    private String startDate;

    @ColumnWidth(20)
    @ExcelProperty("合同结束日期")
    private String endDate;

    @ColumnWidth(8)
    @ExcelProperty("状态")
    private String status;
}
