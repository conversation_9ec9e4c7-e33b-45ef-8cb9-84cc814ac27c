package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/6/18 11:24
 */
@ColumnWidth(16)
@ContentRowHeight(20)
@Data
public class GateOperationDayReportExportVo implements Serializable {

    private static final long serialVersionUID = -641121060777120984L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serial;

    @ColumnWidth(20)
    @ExcelProperty(value = "操作日期", converter = LocalDateExcelConverter.class)
    private LocalDate opDate;

    @ColumnWidth(30)
    @ExcelProperty("设备名")
    private String deviceName;

    @ExcelProperty("开门统计")
    private Integer openCount;

    @ExcelProperty("报警次数")
    private Integer alarmCount;

    @ExcelProperty("误报次数")
    private Integer falseAlarmCount;
}
