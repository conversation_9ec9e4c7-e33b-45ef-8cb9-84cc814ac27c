package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.NullToZeroDecimalConvertor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/3/11 11:17
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class ParkingPaymentStatisticExportVo implements Serializable {

    private static final long serialVersionUID = 7438725295083452140L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private String serialNo;

    @ExcelProperty(value = "收费日期", converter = LocalDateExcelConverter.class)
    private LocalDate tollDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "包月聚合支付", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal monthDrcAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "包月扫码", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal monthScanAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "包月现金", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal monthCashAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "包月聚合手续费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal monthDrcHandlingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "临卡岗亭扫码", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal parkingScanAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "临卡小程序", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal parkingAppAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "临卡现金", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal parkingCashAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "合计", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal totalAmount;

    @ColumnWidth(40)
    @ExcelProperty("备注")
    private String remark;
}
