package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/9 16:51
 */
@Data
public class SkuExcelVo implements Serializable {

    private static final long serialVersionUID = -2705023123984786566L;

    @ExcelProperty(value = "货主")
    private String customerSerial;

    @ExcelProperty(value = "客户")
    private String customerName;

    @ExcelProperty(value = "商品编码")
    private String code;

    @ExcelProperty(value = "商品名称")
    private String name;

    @ExcelProperty(value = "长*宽*高")
    private String size;

    @ExcelProperty(value = "毛重")
    private BigDecimal weight;

    @ExcelIgnore
    private Integer quantity;

}
