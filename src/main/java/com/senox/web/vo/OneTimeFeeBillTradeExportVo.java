package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import com.senox.web.convert.PaywayExcelConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/10/26 10:50
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class OneTimeFeeBillTradeExportVo implements Serializable {

    private static final long serialVersionUID = 4127366496812003529L;

    @ColumnWidth(8)
    @ExcelProperty("编号")
    private Integer serialNo;

    @ExcelProperty(value = "收费日期", converter = LocalDateExcelConverter.class)
    private LocalDate tollDate;

    @ExcelProperty(value = "支付方式", converter = PaywayExcelConverter.class)
    private Integer payWay;

    @ExcelProperty("客户名称")
    private String customer;

    @ColumnWidth(20)
    @ExcelProperty("收据号")
    private String tollSerial;

    @ExcelProperty("通知单号")
    private String billNo;

    @ExcelProperty("收费金额")
    private BigDecimal amount;

    @ColumnWidth(28)
    @ExcelProperty("收费项目")
    private String fee;

    @ExcelProperty("录入人")
    private String creator;

    @ExcelProperty("收费员")
    private String tollMan;

    @ColumnWidth(24)
    @ExcelProperty("部门")
    private String department;

    @ExcelProperty("物业编号")
    private String realtySerial;

    @ColumnWidth(24)
    @ExcelProperty("物业名称")
    private String realtyName;

    @ExcelProperty("退费金额")
    private BigDecimal refundAmount;

    @ExcelProperty("合计")
    private BigDecimal totalAmount;

    @ExcelProperty("退费人")
    private String refundMan;

    @ColumnWidth(20)
    @ExcelProperty(value = "退费时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime refundTime;

    @ExcelProperty("退费单号")
    private String refundSerial;

    @ColumnWidth(28)
    @ExcelProperty("备注")
    private String remark;



}
