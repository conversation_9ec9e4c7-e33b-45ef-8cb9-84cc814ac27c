package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.senox.web.convert.LocalDateTimeExcelConverter;
import com.senox.web.convert.ReceiptOrderStatusExcelConvertor;
import com.senox.web.convert.TaxCategoryExcelConvertor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 停车开票申请
 * <AUTHOR>
 * @date 2022/12/2 14:14
 */
@Getter
@Setter
@ToString
@ColumnWidth(16)
@ContentRowHeight(20)
public class ReceiptOrderExportVo implements Serializable {

    private static final long serialVersionUID = 3490260692746181330L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private Integer serialNo;

    @ColumnWidth(20)
    @ExcelProperty("发票抬头")
    private String taxHeader;

    @ExcelProperty("税号")
    private String taxSerial;

    @ExcelProperty(value = "抬头类型", converter = TaxCategoryExcelConvertor.class)
    private Integer taxCategory;

    @ExcelProperty("联系方式")
    private String contact;

    @ExcelProperty("车牌")
    private String vehicleNo;

    @ExcelProperty("金额")
    private String remark;

    @ExcelProperty(value = "申请时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime createTime;

    @ExcelProperty(value = "事件时间", converter = LocalDateTimeExcelConverter.class)
    private LocalDateTime eventTime;

    @ExcelProperty(value = "状态", converter = ReceiptOrderStatusExcelConvertor.class)
    private Integer status;
}
