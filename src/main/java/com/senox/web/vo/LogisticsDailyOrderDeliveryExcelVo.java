package com.senox.web.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-1-25
 */
@Getter
@Setter
public class LogisticsDailyOrderDeliveryExcelVo {

    /**
     * 配送单号
     */
    @ExcelProperty("配送单号")
    private String orderDeliveryNo;

    /**
     * 配送车牌
     */
    @ExcelProperty("配送车牌")
    private String orderDeliveryCarNo;

    /**
     * 下单件数
     */
    @ExcelProperty("下单件数")
    private Integer orderPieces;

    /**
     * 下单总重量（KG）
     */
    @ExcelProperty("下单总重量（KG）")
    private BigDecimal orderTotalKilograms;

    /**
     * 下单日期
     */
    @ExcelProperty("下单日期")
    private String orderTime;

    /**
     * 下单人
     */
    @ExcelProperty("下单人")
    private String orderPerson;

    /**
     * 送货日期
     */
    @ExcelProperty("送货日期")
    private String sendTime;

    /**
     * 类型
     */
    @ExcelProperty("类别")
    private String type;

    /**
     * 应收运费金额
     */
    @ExcelProperty("应收运费金额")
    private BigDecimal receivableFreightCharge;
}
