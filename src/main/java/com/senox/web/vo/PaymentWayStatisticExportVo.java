package com.senox.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.senox.web.convert.LocalDateExcelConverter;
import com.senox.web.convert.NullToZeroDecimalConvertor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/2/29 15:32
 */
@Getter
@Setter
@ContentRowHeight(18)
@HeadRowHeight(21)
public class PaymentWayStatisticExportVo implements Serializable {

    private static final long serialVersionUID = -891568724041654706L;

    @ColumnWidth(8)
    @ExcelProperty("序号")
    private String serialNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "收费日期", converter = LocalDateExcelConverter.class)
    private LocalDate tollDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "物业收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal realtyAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "冷藏收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal refrigerationAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "广告收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal advertisingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "押金收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal depositAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "押金退费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal depositRefund;

    @ColumnWidth(20)
    @ExcelProperty(value = "云仓收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cloudWarehousingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "干仓收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal normalTemperatureWarehousingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "三轮车牌费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal cycleAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "一次性收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal oneTimeFeeAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "一次性退费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal oneTimeFeeRefund;

    @ColumnWidth(20)
    @ExcelProperty(value = "蛋品停车收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal parkingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "物维收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal maintainAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "三轮配送费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal bicycleAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "园区月卡停车收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal marketMonthParkingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "园区临卡停车收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal marketParkingAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "蛋品区押金收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal eggDepositAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "蛋品区押金退费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal eggDepositRefundAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "珠三角收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal logisticFreightAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "城际运输收费", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal logisticTransportAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "收费合计", converter = NullToZeroDecimalConvertor.class)
    private BigDecimal totalAmount;

    @ColumnWidth(30)
    @ExcelProperty("备注")
    private String remark;
}
