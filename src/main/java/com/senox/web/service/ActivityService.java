package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import com.senox.web.component.ActivityComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:50
 */
@Service
@RequiredArgsConstructor
public class ActivityService {

    private final ActivityComponent activityComponent;

    /**
     * 添加活动
     * @param activityVo
     * @return
     */
    public Long addActivity(ActivityVo activityVo) {
        return activityComponent.addActivity(activityVo);
    }

    /**
     * 更新活动
     * @param activityVo
     */
    public void updateActivity(ActivityVo activityVo) {
        activityComponent.updateActivity(activityVo);
    }

    /**
     * 生成活动链接
     * @param id
     * @return
     */
    public String generateActivityUrl(Long id) {
        return activityComponent.generateActivityUrl(id);
    }

    /**
     * 根据id获取活动
     * @param id
     * @return
     */
    public ActivityVo findActivityById(Long id) {
        return activityComponent.findActivityById(id);
    }

    /**
     * 根据id删除活动
     * @param id
     */
    public void deleteActivityById(Long id) {
        activityComponent.deleteActivityById(id);
    }

    /**
     * 活动分页
     * @param searchVo
     * @return
     */
    public PageResult<ActivityVo> pageActivityResult(ActivitySearchVo searchVo) {
        return activityComponent.pageActivityResult(searchVo);
    }
}
