package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.GateOperationLogSearchVo;
import com.senox.dm.vo.GateOperationLogVo;
import com.senox.web.component.AccessControlComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:13
 */
@Service
@RequiredArgsConstructor
public class GateOperationLogService {

    private final AccessControlComponent controlComponent;

    /**
     * 设置日志误报
     * @param id
     * @param marked
     */
    public void markOperationFalse(Long id, Boolean marked) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        controlComponent.markOperationFalse(id, marked);
    }

    /**
     * 门禁日志列表
     * @param search
     * @return
     */
    public List<GateOperationLogVo> listOperationLog(GateOperationLogSearchVo search) {
        return controlComponent.listOperationLog(search);
    }

    /**
     * 门禁日志列表页
     * @param search
     * @return
     */
    public PageResult<GateOperationLogVo> listOperationLogPage(GateOperationLogSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return controlComponent.listOperationLogPage(search);
    }
}
