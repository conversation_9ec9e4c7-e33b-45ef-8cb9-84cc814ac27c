package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.tms.vo.*;
import com.senox.web.component.BicycleOrderReportComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/8 11:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BicycleOrderReportService {
    private final BicycleOrderReportComponent bicycleOrderReportComponent;

    /**
     * 生成日报表
     * @param dateVo
     */
    public void generateDayReport(BicycleReportDateVo dateVo) {
        bicycleOrderReportComponent.generateDayReport(dateVo);
    }

    /**
     * 更新日报表
     * @param dayReportVo
     */
    public void updateDayReport(BicycleOrderDayReportVo dayReportVo) {
        bicycleOrderReportComponent.updateDayReport(dayReportVo);
    }

    /**
     * 根据id集合查询日报表
     * @param ids
     * @return
     */
    public List<BicycleOrderDayReportVo> listDayReport(List<Long> ids) {
        return bicycleOrderReportComponent.listDayReport(ids);
    }

    /**
     * 删除日报表
     * @param ids
     */
    public void deleteDayReport(List<Long> ids) {
        bicycleOrderReportComponent.deleteDayReport(ids);
    }

    /**
     * 获取日报表
     * @param id
     * @return
     */
    public BicycleOrderDayReportVo findDayReportById(Long id) {
        return bicycleOrderReportComponent.findDayReportById(id);
    }

    /**
     * 日报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderDayReportVo sumDayReport(BicycleOrderDayReportSearchVo searchVo) {
        return bicycleOrderReportComponent.sumDayReport(searchVo);
    }

    /**
     * 日报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderDayReportVo> listDayReport(BicycleOrderDayReportSearchVo searchVo) {
        return bicycleOrderReportComponent.listDayReport(searchVo);
    }

    /**
     * 生成月报表
     * @param dateVo
     */
    public void generateMonthReport(BicycleReportDateVo dateVo) {
        bicycleOrderReportComponent.generateMonthReport(dateVo);
    }

    /**
     * 更新月报表
     * @param monthReportVo
     */
    public void updateMonthReport(BicycleOrderMonthReportVo monthReportVo) {
        bicycleOrderReportComponent.updateMonthReport(monthReportVo);
    }

    /**
     * 根据id集合查询月报表
     * @param ids
     * @return
     */
    public List<BicycleOrderMonthReportVo> listMonthReport(List<Long> ids) {
        return bicycleOrderReportComponent.listMonthReport(ids);
    }

    /**
     * 删除月报表
     * @param ids
     */
    public void deleteMonthReport(List<Long> ids) {
        bicycleOrderReportComponent.deleteMonthReport(ids);
    }

    /**
     * 获取月报表
     * @param id
     * @return
     */
    public BicycleOrderMonthReportVo findMonthReportById(Long id) {
        return bicycleOrderReportComponent.findMonthReportById(id);
    }

    /**
     * 月报表明细
     * @param id
     * @return
     */
    public List<BicycleOrderDayReportVo> listMonthDayReport(Long id) {
        return bicycleOrderReportComponent.listMonthDayReport(id);
    }

    /**
     * 月报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderMonthReportVo sumMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        return bicycleOrderReportComponent.sumMonthReport(searchVo);
    }

    /**
     * 月报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderMonthReportVo> listMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        return bicycleOrderReportComponent.listMonthReport(searchVo);
    }
}
