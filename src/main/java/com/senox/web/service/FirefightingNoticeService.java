package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingNoticeSearchVo;
import com.senox.realty.vo.FirefightingNoticeVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:59
 */
@Service
@RequiredArgsConstructor
public class FirefightingNoticeService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加告知单
     * @param notice
     * @return
     */
    public Long addNotice(FirefightingNoticeVo notice) {
        return firefightingComponent.addNotice(notice);
    }

    /**
     * 更新告知单
     * @param notice
     */
    public void updateNotice(FirefightingNoticeVo notice) {
        if (!WrapperClassUtils.biggerThanLong(notice.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateNotice(notice);
    }

    /**
     * 删除告知单
     * @param id
     */
    public void deleteNotice(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteNotice(id);
    }

    /**
     * 获取告知单
     * @param id
     * @return
     */
    public FirefightingNoticeVo findNoticeById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findNoticeById(id) : null;
    }

    /**
     * 告知单页
     * @param search
     * @return
     */
    public PageResult<FirefightingNoticeVo> listNoticePage(FirefightingNoticeSearchVo search) {
        return firefightingComponent.listNoticePage(search);
    }
}
