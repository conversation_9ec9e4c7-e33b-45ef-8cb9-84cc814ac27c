package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.WmsMaterialRequisitionComponent;
import com.senox.wms.vo.requisition.MaterialFlowResult;
import com.senox.wms.vo.requisition.MaterialRequisitionSearchVo;
import com.senox.wms.vo.requisition.MaterialRequisitionVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class WmsMaterialRequisitionService {
    private final WmsMaterialRequisitionComponent materialRequisitionComponent;


    /**
     * 保存申购
     *
     * @param requisition 申购参数
     */
    public Long save(MaterialRequisitionVo requisition) {
        return materialRequisitionComponent.save(requisition);
    }

    /**
     * 保存申购并提交
     * @param requisition 申购参数
     */
    public void saveAndSubmit(MaterialRequisitionVo requisition) {
        materialRequisitionComponent.saveAndSubmit(requisition);
    }

    /**
     * 根据流程id查找申购
     *
     * @param flowId 流程id
     * @return 返回查找到的数据
     */
    public MaterialRequisitionVo findByFlowId(Long flowId) {
        if (!WrapperClassUtils.biggerThanLong(flowId, 0)) {
            return null;
        }
        return materialRequisitionComponent.findByFlowId(flowId);
    }

    /**
     * 申购列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */

    public List<MaterialRequisitionVo> list(MaterialRequisitionSearchVo search) {
        return materialRequisitionComponent.list(search);
    }

    /**
     * 申购列表分页
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialRequisitionVo> pageList(MaterialRequisitionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return materialRequisitionComponent.pageList(search);
    }

    /**
     * 申购历史
     * @param instanceId 实例id
     * @return 返回申购历史
     */
    public MaterialFlowResult history(Long instanceId) {
        if (!WrapperClassUtils.biggerThanLong(instanceId, 0)) {
            return null;
        }
        return materialRequisitionComponent.history(instanceId);
    }
}
