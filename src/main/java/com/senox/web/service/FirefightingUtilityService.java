package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import com.senox.realty.vo.FirefightingUtilityVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/13 15:29
 */
@Service
@RequiredArgsConstructor
public class FirefightingUtilityService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加公共消防设施
     * @param utility
     * @return
     */
    public Long addUtility(FirefightingUtilityVo utility) {
        return firefightingComponent.addUtility(utility);
    }

    /**
     * 更新公共消防设施
     * @param utility
     */
    public void updateUtility(FirefightingUtilityVo utility) {
        if (!WrapperClassUtils.biggerThanLong(utility.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateUtility(utility);
    }

    /**
     * 删除公共消防设施
     * @param id
     */
    public void deleteUtility(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteUtility(id);
    }

    /**
     * 获取公共消防设施
     * @param id
     * @return
     */
    public FirefightingUtilityVo findUtilityById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findUtilityById(id) : null;
    }

    /**
     * 公共消防设施页
     * @param search
     * @return
     */
    public PageResult<FirefightingUtilityVo> listUtilityPage(FirefightingUtilitySearchVo search) {
        return firefightingComponent.listUtilityPage(search);
    }


}
