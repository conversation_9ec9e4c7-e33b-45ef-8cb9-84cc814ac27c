package com.senox.web.service;

import com.senox.common.exception.InvalidParameterException;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17 16:42
 */
@Service
public class RealtyBillWithholdService {

    @Autowired
    private RealtyComponent realtyComponent;

    /**
     * 申请银行托收报盘
     * @param withhold
     */
    public void applyBillWithhold(BankWithholdVo withhold) {
        realtyComponent.applyBillWithhold(withhold);
    }

    /**
     * 取消银行报盘申请
     * @param withhold
     */
    public void cancelBillWithhold(BankWithholdVo withhold) {
        realtyComponent.cancelBillWithhold(withhold);
    }

    /**
     * 银行托收回盘
     * @param withhold
     */
    public void backBillWithhold(BankWithholdVo withhold) {
        realtyComponent.backBillWithhold(withhold);
    }

    /**
     * 银行托收支付
     * @param withholdBack
     */
    public void payBillWithhold(WithholdBackVo withholdBack) {
        if (withholdBack.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParameterException("无效的金额");
        }
        realtyComponent.withholdPay(withholdBack);
    }

    /**
     * 获取银行报盘信息
     * @param year
     * @param month
     * @return
     */
    public BankWithholdVo getBankWithHold(Integer year, Integer month) {
        return realtyComponent.getBankWithHold(year, month);
    }

    /**
     * 银行托收报盘记录
     * @param search
     * @return
     */
    public WithholdPage<BankOfferRealtyBillVo> listApplyPage(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return WithholdPage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.listBillWithholdPage(search);
    }

    /**
     * 银行托收报盘记录
     * @param search
     * @return
     */
    public List<BankOfferRealtyBillVo> listApply(RealtyBillSearchVo search) {
        return realtyComponent.listBillWithhold(search);
    }
}
