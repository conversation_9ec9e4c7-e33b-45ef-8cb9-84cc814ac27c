package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import com.senox.tms.vo.LogisticPayoffVo;
import com.senox.web.component.LogisticComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18 17:02
 */
@Service
@RequiredArgsConstructor
public class LogisticPayoffService {

    private final LogisticComponent logisticComponent;

    /**
     * 添加物流客户应付账单
     * @param payoff
     * @return
     */
    public Long addPayoff(LogisticPayoffVo payoff) {
        return logisticComponent.addLogisticPayoff(payoff);
    }

    /**
     * 更新物流客户应付账单
     * @param payoff
     */
    public void updatePayoff(LogisticPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            return;
        }

        logisticComponent.updateLogisticPayoff(payoff);
    }

    /**
     * 删除物流客户应付账单
     * @param ids
     */
    public void deletePayoff(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        logisticComponent.deleteLogisticPayoff(ids);
    }

    /**
     * 生成物流客户应付账单
     * @param generate
     */
    public void generatePayoff(LogisticPayoffGenerateVo generate) {
        logisticComponent.generateLogisticPayoff(generate);
    }

    /**
     * 获取物流客户应付账单
     * @param id
     * @return
     */
    public LogisticPayoffVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? logisticComponent.findPayoffById(id) : null;
    }

    /**
     * 获取物流客户应付账单合计
     * @param search
     * @return
     */
    public LogisticPayoffVo sumPayoff(LogisticPayoffSearchVo search) {
        return logisticComponent.sumPayoff(search);
    }

    /**
     * 物流客户应付账单列表
     * @param search
     * @return
     */
    public List<LogisticPayoffVo> listPayoff(LogisticPayoffSearchVo search) {
        return logisticComponent.listPayoff(search);
    }

    /**
     * 物流客户应付账单页
     * @param search
     * @return
     */
    public PageResult<LogisticPayoffVo> listPayoffPage(LogisticPayoffSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return logisticComponent.listPayoffPage(search);
    }
}
