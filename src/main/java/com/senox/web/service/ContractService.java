package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.ContractBankVo;
import com.senox.realty.vo.ContractSearchVo;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.LeaseContractListVo;
import com.senox.realty.vo.RealtyContractSuspendRequestDto;
import com.senox.web.component.ContractComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/2/20 9:23
 */
@Service
public class ContractService {

    @Autowired
    private ContractComponent contractComponent;

    /**
     * 添加合同
     * @param contract
     * @return
     */
    public Long addContract(ContractVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getRealtyId(), 0L)
                && !WrapperClassUtils.biggerThanLong(contract.getCustomerId(), 0L)) {
            return 0L;
        }
        return contractComponent.addContract(contract);
    }

    /**
     * 更新合同
     * @param contract
     */
    public void updateContract(ContractVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return;
        }
        contractComponent.updateContract(contract);
    }

    /**
     * 删除合同
     * @param id
     */
    public void deleteContract(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        ContractVo contract = new ContractVo();
        contract.setId(id);
        contract.setDisabled(Boolean.TRUE);
        updateContract(contract);
    }

    /**
     * 启用合同
     * @param contractNo
     */
    public void enableContract(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return;
        }
        contractComponent.enableContract(contractNo);
    }

    /**
     * 停用合同
     * @param suspendReq
     */
    public void suspendContract(RealtyContractSuspendRequestDto suspendReq) {
        contractComponent.suspendContract(suspendReq);
    }

    /**
     * 根据id查找合同
     * @param id
     * @return
     */
    public ContractVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return contractComponent.findById(id);
    }

    /**
     * 根据合同号获取合同
     * @param contractNo
     */
    public ContractVo getByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }
        return contractComponent.getByContractNo(contractNo);
    }

    /**
     * 查找续签合同源
     * @param id
     * @return
     */
    public ContractVo findRenewFrom(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return contractComponent.findRenewFrom(id);
    }

    /**
     * 合同列表页
     * @param search
     * @return
     */
    public PageResult<ContractVo> listContractPage(ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return contractComponent.listContractPage(search);
    }

    /**
     * 租赁合同列表
     * @param search
     * @return
     */
    public PageResult<LeaseContractListVo> listLeaseContractPage(ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return contractComponent.listLeaseContractPage(search);
    }

    /**
     * 合同代扣银行信息
     * @param search
     * @return
     */
    public PageResult<ContractBankVo> listContractBank(ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return contractComponent.listContractBank(search);
    }
}
