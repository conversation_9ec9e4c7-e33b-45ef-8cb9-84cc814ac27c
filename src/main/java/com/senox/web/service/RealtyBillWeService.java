package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import com.senox.web.component.RealtyComponent;
import com.senox.web.vo.RealtyBillWePage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/11/7 16:41
 */
@RequiredArgsConstructor
@Service
public class RealtyBillWeService {

    private final RealtyComponent realtyComponent;

    /**
     * 生成物业水电账单
     * @param month
     */
    public void generate(BillMonthVo month) {
        realtyComponent.generateRealtyWeBill(month);
    }

    /**
     * 更新物业水电账单
     * @param bill
     */
    public void updateBill(RealtyBillWeVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }

        realtyComponent.updateRealtyWeBill(bill);
    }

    /**
     * 删除物业水电账单
     * @param id
     */
    public void deleteBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        realtyComponent.deleteRealtyWeBill(id);
    }

    public void syncWeBill2RealtyBill(Long id, BillMonthVo billMonth) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        realtyComponent.syncWeBill2RealtyBill(id, billMonth);
    }

    /**
     * 根据 id 查找物业水电账单
     * @param id
     * @return
     */
    public RealtyBillWeVo findBillById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? realtyComponent.findRealtyWeBillById(id) : null;
    }

    /**
     * 水电账单列表
     * @param search
     * @return
     */
    public RealtyBillWePage<RealtyBillWeVo> listPage(RealtyBillWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillWePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillWeVo> page = realtyComponent.listRealtyWeBill(search);

        // result
        RealtyBillWePage<RealtyBillWeVo> resultPage = new RealtyBillWePage<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // sum
        RealtyBillWeVo sum = realtyComponent.sumRealtyWeBill(search);
        if (sum != null) {
            resultPage.setLastWaterReadings(sum.getLastWaterReadings());
            resultPage.setWaterReadings(sum.getWaterReadings());
            resultPage.setWaterCost(sum.getWaterCost());
            resultPage.setWaterShare(sum.getWaterShare());
            resultPage.setWaterAmount(sum.getWaterAmount());
            resultPage.setLastElectricReadings(sum.getLastElectricReadings());
            resultPage.setElectricReadings(sum.getElectricReadings());
            resultPage.setElectricCost(sum.getElectricCost());
            resultPage.setElectricShare(sum.getElectricShare());
            resultPage.setElectricAmount(sum.getElectricAmount());
            resultPage.setTotalAmount(sum.getTotalAmount());
        }
        return resultPage;
    }
}
