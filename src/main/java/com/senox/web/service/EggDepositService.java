package com.senox.web.service;

import com.senox.car.vo.*;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.component.EggDepositComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/9 10:47
 */
@Service
@RequiredArgsConstructor
public class EggDepositService {

    private final EggDepositComponent eggDepositComponent;

    /**
     * 押金支付
     * @param eggDepositVo
     * @return
     */
    public OrderResultVo eggDepositPay(EggDepositVo eggDepositVo) {
        return eggDepositComponent.eggDepositPay(eggDepositVo);
    }

    /**
     * 根据id获取押金
     * @param id
     * @return
     */
    public EggDepositVo findEggDepositById(Long id) {
        return eggDepositComponent.findEggDepositById(id);
    }

    /**
     * 押金分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<EggDepositVo, EggDepositVo> pageResult(EggDepositSearchVo searchVo) {
        return eggDepositComponent.pageResult(searchVo);
    }

    /**
     * 押金退费申请
     * @param applyVo
     * @return
     */
    public Long refundApply(EggDepositRefundApplyVo applyVo) {
        return eggDepositComponent.refundApply(applyVo);
    }

    /**
     * 押金退费预申请
     * @param applyVo
     * @return
     */
    public Long refundPreApply(EggDepositRefundApplyVo applyVo) {
        return eggDepositComponent.refundPreApply(applyVo);
    }

    /**
     * 根据id获取押金申请记录
     * @param id
     * @return
     */
    public EggDepositRefundApplyVo findEggDepositRefundById(Long id) {
        return eggDepositComponent.findEggDepositRefundById(id);
    }

    /**
     * 退费申请分页
     * @param searchVo
     * @return
     */
    public PageResult<EggDepositRefundApplyVo> applyPage(EggDepositRefundApplySearchVo searchVo) {
        return eggDepositComponent.applyPage(searchVo);
    }

    /**
     * 押金退费提交
     * @param submitVo
     */
    public void submit(EggDepositRefundSubmitVo submitVo) {
        eggDepositComponent.submit(submitVo);
    }

    /**
     * 退费
     * @param applyId
     * @param payWay
     */
    public void refundByDepositApply(Long applyId, PayWay payWay) {
        eggDepositComponent.refundByDepositApply(applyId, payWay);
    }
}
