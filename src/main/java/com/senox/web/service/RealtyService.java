package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/2 15:34
 */
@Service
public class RealtyService {

    @Autowired
    private RealtyComponent realtyComponent;

    /**
     * 添加物业
     *
     * @param realty
     * @return
     */
    public Long addRealty(RealtyVo realty) {
        if (StringUtils.isBlank(realty.getSerialNo()) || StringUtils.isBlank(realty.getName())) {
            return 0L;
        }
        return realtyComponent.addRealty(realty);
    }

    /**
     * 更新物业
     *
     * @param realty
     */
    public void updateRealty(RealtyVo realty) {
        if (!WrapperClassUtils.biggerThanLong(realty.getId(), 0L)) {
            return;
        }

        realtyComponent.updateRealty(realty);
    }

    /**
     * 更新物业担保
     * @param guarantee
     */
    public void updateRealtyGuarantee(RealtyGuaranteeVo guarantee) {
        if (!WrapperClassUtils.biggerThanLong(guarantee.getRealtyId(), 0L)) {
            return;
        }

        realtyComponent.updateRealtyGuarantee(guarantee);
    }


    /**
     * 删除物业
     *
     * @param id
     */
    public void deleteRealty(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        RealtyVo realty = new RealtyVo();
        realty.setId(id);
        realty.setDisabled(Boolean.TRUE);
        updateRealty(realty);
    }

    /**
     * 根据id查找物业
     *
     * @param id
     * @param withOwner
     * @return
     */
    public RealtyVo findById(Long id, boolean withOwner) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return withOwner ? realtyComponent.findWithOwnerById(id) : realtyComponent.findById(id);
    }

    /**
     * 获取最新水电读数
     * @param id
     * @return
     */
    public List<RealtyReadingsVo> getLatestWeReadings(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return Collections.emptyList();
        }
        return realtyComponent.getLatestWeReadings(id);
    }

    /**
     * 更新水电读数
     * @param list
     */
    public void updateWeReadings(List<RealtyReadingsVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        realtyComponent.updateWeReadings(list);
    }

    /**
     * 物业列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<RealtyVo> listRealtyPage(RealtySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return realtyComponent.listRealtyPage(searchVo);
    }

    /**
     * 新增物业副档
     *
     * @param realtyId
     * @param aliasList
     */
    public void batchSaveRealtyAlias(Long realtyId, List<RealtyAliasVo> aliasList) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            return;
        }

        realtyComponent.batchSaveRealtyAlias(realtyId, aliasList);
    }



    /**
     * 获取物业副档
     *
     * @param realtyId 主档id
     */
    public List<RealtyAliasVo> listRealtyAlias(Long realtyId) {
        if (!WrapperClassUtils.biggerThanLong(realtyId,0L)){
            return Collections.emptyList();
        }

        return realtyComponent.listRealtyAlias(realtyId);
    }

    /**
     * 添加物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    public void saveRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        if (CollectionUtils.isEmpty(realtyTaxRate.getRealtySerials()) || null == realtyTaxRate.getTaxCode()) {
            return;
        }
        realtyComponent.saveRealtyTaxRate(realtyTaxRate);
    }

    /**
     * 取消物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    public void cancelRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        if (CollectionUtils.isEmpty(realtyTaxRate.getRealtySerials())){
            return;
        }
        realtyComponent.cancelRealtyTaxRate(realtyTaxRate);
    }

    /**
     * 物业税率分页列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<RealtyVo> pageListRealtyTaxRate(RealtySearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.pageListRealtyTaxRate(search);
    }
}
