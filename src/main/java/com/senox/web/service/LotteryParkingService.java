package com.senox.web.service;

import com.senox.car.constant.LotteryParkingStatus;
import com.senox.car.vo.*;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.component.LotteryParkingComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/5 14:50
 */
@Service
public class LotteryParkingService {

    @Autowired
    private LotteryParkingComponent parkingComponent;

    /**
     * 保存摇号配置
     * @param setting
     */
    public void saveLotterySetting(LotterySettingVo setting) {
        parkingComponent.saveLotterySetting(setting);
    }

    /**
     * 获取摇号停车配置
     * @return
     */
    public LotterySettingVo getLotterySetting() {
        return parkingComponent.getLotterySetting();
    }

    /**
     * 添加摇号车位
     * @param parking
     * @return
     */
    public Long addParking(LotteryParkingVo parking) {
        if (StringUtils.isBlank(parking.getName())) {
            return 0L;
        }
        return parkingComponent.addParking(parking);
    }

    /**
     * 更新摇号车位
     * @param parking
     */
    public void updateParking(LotteryParkingVo parking) {
        if (!WrapperClassUtils.biggerThanLong(parking.getId(), 0L)) {
            return;
        }
        parkingComponent.updateParking(parking);
    }

    /**
     * 车位状态
     * @param id
     * @param parkingStatus
     */
    public void changeParkingStatus(Long id, LotteryParkingStatus parkingStatus) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        LotteryParkingVo parking = new LotteryParkingVo();
        parking.setId(id);
        parking.setStatus(parkingStatus.ordinal());
        updateParking(parking);
    }

    /**
     * 删除车位
     * @param id
     */
    public void deleteParking(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        LotteryParkingVo parking = new LotteryParkingVo();
        parking.setId(id);
        parking.setDisabled(Boolean.TRUE);
        updateParking(parking);
    }

    /**
     * 获取摇号车位信息
     * @param id
     * @return
     */
    public LotteryParkingVo findParkingById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return parkingComponent.findParkingById(id);
    }

    /**
     * 摇号车位列表
     * @return
     */
    public List<LotteryParkingVo> listLotteryParking() {
        return parkingComponent.listLotteryParking();
    }

    /**
     * 摇号进场
     * @param parkingRecord
     * @return
     */
    public LotteryParkingVo runAndOccupied(LotteryParkingRecordVo parkingRecord) {
        return parkingComponent.runLotteryAndOccupied(parkingRecord);
    }

    /**
     * 手工进场
     * @param parkingVo
     * @return
     */
    public LotteryParkingVo fill(LotteryParkingVo parkingVo) {
        return parkingComponent.fill(parkingVo);
    }

    /**
     * 退场
     * @param id
     * @return
     */
    public LotteryParkingRecordVo exitParking(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return parkingComponent.exitParking(id);
    }

    /**
     * 撤销退场
     * @param id
     * @return
     */
    public LotteryParkingVo revokeExitParking(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return parkingComponent.revokeExitParking(id);
    }

    /**
     * 最近摇号停车记录
     * @param size
     * @return
     */
    public List<LotteryParkingRecordVo> topParkingRecord(int size) {
        if (size < 1) {
            return Collections.emptyList();
        }
        return parkingComponent.topLotteryParkingRecord(size);
    }

    /**
     * 摇号停车记录列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingRecordVo> listParkingRecord(LotteryParkingRecordSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return parkingComponent.listLotteryParkingRecord(searchVo);
    }

    /**
     * 摇号停车记金额统计
     * @param searchVo
     * @return
     */
    public LotteryParkingAmountVo summaryParkingAmount(LotteryParkingRecordSearchVo searchVo) {
        return parkingComponent.summaryParkingAmount(searchVo);
    }

    /**
     * 最近摇号停车日志
     * @param size
     * @return
     */
    public List<LotteryParkingLogVo> topParkingLog(int size) {
        if (size < 1) {
            return Collections.emptyList();
        }
        return parkingComponent.topLotteryParkingLog(size);
    }

    /**
     * 摇号停车日志列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingLogVo> listParkingLog(LotteryParkingLogSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return parkingComponent.listLotteryParkingLog(searchVo);
    }

    /**
     * 摇号停车日报列表
     * @param searchVo
     * @return
     */
    public LotteryParkingPage<LotteryParkingDayReportVo> listParkingDayReport(LotteryParkingDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return LotteryParkingPage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return parkingComponent.listLotteryParkingDayReport(searchVo);
    }

    /**
     * 摇号停车月报列表
     * @param searchVo
     * @return
     */
    public LotteryParkingPage<LotteryParkingMonthReportVo> listParkingMonthReport(LotteryParkingMonthReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return LotteryParkingPage.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return parkingComponent.listLotteryParkingMonthReport(searchVo);
    }


    /**
     * 查找车辆停车支付结果
     * @param carNo
     * @return
     */
    public LotteryParkingPaidVo findCarParkingPaid(String carNo) {
        if (StringUtils.isBlank(carNo)) {
            return null;
        }
        return parkingComponent.getCarParkingPaid(carNo);
    }

    /**
     * 进场支付
     * @param parkingVo
     * @return
     */
    public OrderResultVo entryPay(LotteryParkingVo parkingVo) {
        if (StringUtils.isBlank(parkingVo.getOccupiedNo()) || parkingVo.getPayWay() == null) {
            throw new BusinessException("无效的车牌或支付方式");
        }
        return parkingComponent.entryPay(parkingVo);
    }

    /**
     * 车牌号获取停车信息
     *
     * @param carNo 车牌号
     * @return 停车信息
     */
    public LotteryParkingInfoVo infoByCarNo(String carNo) {
        if (StringUtils.isBlank(carNo)) {
            throw new InvalidParameterException();
        }
        return parkingComponent.infoByCarNo(carNo);
    }

    /**
     * 同步车位数量
     */
    public void asyncStock() {
        parkingComponent.asyncStock();
    }

    /**
     * 实时获取当天进出场数量
     * @return
     */
    public LotteryParkingDayCount parkingDayCount() {
        return parkingComponent.parkingDayCount();
    }


    /**
     * 查询进出场次数和设备记录次数
     * @param searchVo
     * @return
     */
    public LotteryParkingRecordCountVo findEntryAndExitCount(LotteryParkingRecordSearchVo searchVo) {
        return parkingComponent.findEntryAndExitCount(searchVo);
    }

    /**
     * 临期车位列表
     * @return
     */
    public List<LotteryParkingVo> adventParkingList() {
        return parkingComponent.adventParkingList();
    }

    /**
     * 最近时间离场记录列表
     * @param minutes
     * @return
     */
    public List<LotteryParkingRecordVo> latestExitRecord(Long minutes) {
        return parkingComponent.latestExitRecord(minutes);
    }

    /**
     * 发送离场缴费账单
     * @param id
     */
    public void sendExitBill(Long id) {
        parkingComponent.sendExitBill(id);
    }

    /**
     * 监管临期列表
     * @return
     */
    public List<LotteryParkingVo> regulatoryAdventParkingList() {
        return parkingComponent.regulatoryAdventParkingList();
    }

    /**
     * 删除设备的离场时间
     * @param id
     */
    public void deleteDeviceExitTime(Long id) {
        parkingComponent.deleteDeviceExitTime(id);
    }

    /**
     * 更新离场支付状态为已支付
     * @param exitOrderId
     */
    public void updateExitPaid(Long exitOrderId) {
        parkingComponent.updateExitPaid(exitOrderId);
    }
}
