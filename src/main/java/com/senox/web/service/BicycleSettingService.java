package com.senox.web.service;

import com.senox.tms.vo.BicycleSettingVo;
import com.senox.web.component.BicycleSettingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 10:59
 */
@Service
@RequiredArgsConstructor
public class BicycleSettingService {

    private final BicycleSettingComponent bicycleSettingComponent;

    /**
     * 添加三轮车配置信息
     * @param settingVo
     * @return
     */
    public Long addBicycleSetting(BicycleSettingVo settingVo) {
        return bicycleSettingComponent.addBicycleSetting(settingVo);
    }

    /**
     * 修改三轮车配置信息
     * @param settingVo
     */
    public void updateBicycleSetting(BicycleSettingVo settingVo) {
        bicycleSettingComponent.updateBicycleSetting(settingVo);
    }

    /**
     * 根据id获取三轮车配置信息
     * @param id
     * @return
     */
    public BicycleSettingVo findById(Long id) {
        return bicycleSettingComponent.findById(id);
    }

    /**
     * 根据id删除三轮车配置信息
     * @param id
     */
    public void deleteById(Long id) {
        bicycleSettingComponent.deleteById(id);
    }

    /**
     * 三轮车配置信息列表
     * @return
     */
    public List<BicycleSettingVo> listBicycleSetting() {
        return bicycleSettingComponent.listBicycleSetting();
    }
}
