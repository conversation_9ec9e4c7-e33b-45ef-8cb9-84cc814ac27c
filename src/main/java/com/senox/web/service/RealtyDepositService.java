package com.senox.web.service;

import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderProductDetailVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.pm.vo.RefundOrderVo;
import com.senox.realty.vo.BillTollVo;
import com.senox.realty.vo.RealtyDepositSearchVo;
import com.senox.realty.vo.RealtyDepositVo;
import com.senox.realty.vo.RefundBillPageResult;
import com.senox.realty.vo.TollSerialVo;
import com.senox.web.component.RealtyComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.MixPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/4 9:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RealtyDepositService extends BillService {

    private final RealtyComponent realtyComponent;

    /**
     * 添加物业押金
     * @param deposit
     * @return
     */
    public Long addDeposit(RealtyDepositVo deposit) {
        return realtyComponent.addDeposit(deposit);
    }

    /**
     * 批量添加物业押金
     * @param depositList
     */
    public void batchAddDeposit(List<RealtyDepositVo> depositList) {
        if (CollectionUtils.isEmpty(depositList)) {
            return;
        }
        realtyComponent.batchAddDeposit(depositList);
    }

    /**
     * 更新物业押金
     * @param deposit
     */
    public void updateDeposit(RealtyDepositVo deposit) {
        if (!WrapperClassUtils.biggerThanLong(deposit.getId(), 0L)) {
            return;
        }
        realtyComponent.updateDeposit(deposit);
    }

    /**
     * 更新物业押金票据号
     * @param serial
     */
    public void updateDepositSerial(TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)
                || StringUtils.isBlank(serial.getSerial())) {
            return;
        }
        realtyComponent.updateDepositSerial(serial);
    }

    /**
     * 删除物业押金
     * @param id
     */
    public void deleteDeposit(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        RealtyDepositVo deposit = new RealtyDepositVo();
        deposit.setId(id);
        deposit.setDisabled(Boolean.TRUE);
        updateDeposit(deposit);
    }

    /**
     * 支付物业押金
     * @param id
     * @param toll
     */
    public void payDeposit(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        realtyComponent.payDeposit(id, toll);
    }

    /**
     * 混合支付
     * @param payRequest
     * @return
     */
    public List<OrderResultVo> mixPayDeposit(MixPayRequestVo payRequest) {
        // 账单
        List<RealtyDepositVo> depositList = listByIds(payRequest.getBillIds());

        // 校验待支付账单
        checkPayingBill(depositList);
        BigDecimal depositTotal = depositList.stream().map(RealtyDepositVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payTotal = payRequest.getDetails().stream().map(PayAmountVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!DecimalUtils.equals(depositTotal, payTotal)) {
            throw new BusinessException("支付金额不匹配");
        }

        // 支付
        List<OrderResultVo> resultList = new ArrayList<>(payRequest.getDetails().size());
        depositList.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_10M));
        try {
            // 订单
            List<OrderVo> orders = buildDepositOrders(depositList, payRequest.getDetails(), payRequest.getRequestIp());
            log.info("【押金支付】 混合支付 {}", JsonUtils.object2Json(orders));

            // 下单
            for (OrderVo order : orders) {
                OrderResultVo result = orderComponent.addOrder(order);
                if (result == null) {
                    throw new BusinessException("下单失败");
                }

                log.info("支付押金订单成功，返回 {}", JsonUtils.object2Json(result));
                resultList.add(result);
            }

            // 更新远程
            if (!resultList.isEmpty() && resultList.stream().allMatch(x -> WrapperClassUtils.biggerThanLong(x.getOrderId(), 0L))) {
                for (OrderResultVo orderResult : resultList) {
                    // 更新账单结果
                    notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), orderResult);
                }
            }
        } finally {
            removeBillPayingLock(depositList);
        }

        return resultList;
    }

    /**
     * 支付押金
     * @param payRequest
     * @return
     */
    public OrderResultVo payDeposit(BillPayRequestVo payRequest) {
        // 账单
        List<RealtyDepositVo> depositList = listByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(depositList);

        depositList.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_10M));
        OrderResultVo result = null;
        try {
            OrderVo order = newPayOrder(depositList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付物业押金成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            log.info("pay deposit {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));

        } finally {
            removeBillPayingLock(depositList);
        }

        log.info("finish pay deposit {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return null;
    }

    /**
     * 撤销支付物业押金
     * @param id
     */
    public void revokeDepositPayment(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        realtyComponent.revokeDepositPayment(id);
    }

    /**
     * 退档物业押金
     * @param id
     * @param refundOrder
     */
    public void refundDeposit(Long id, RefundOrderVo refundOrder) {
        RealtyDepositVo deposit = findById(id);
        if (deposit == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.fromValue(deposit.getStatus()) != BillStatus.PAID) {
            throw new BusinessException("订单未支付");
        }

        // 不关联之前的支付订单
        refundNoOrderBill(deposit, refundOrder);
    }

    /**
     * 撤销退档物业押金
     * @param id
     */
    public void revokeDepositRefund(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        realtyComponent.revokeDepositRefund(id);
    }

    /**
     * 查找物业押金
     * @param id
     * @return
     */
    public RealtyDepositVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return realtyComponent.findDepositById(id);
    }

    /**
     * 根据id列表查找物业押金
     * @param ids
     * @return
     */
    public List<RealtyDepositVo> listByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : realtyComponent.listDepositByIds(ids);
    }

    /**
     * 合同物业押金
     * @param contractId
     * @return
     */
    public List<RealtyDepositVo> listContractDeposit(Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            return Collections.emptyList();
        }
        return realtyComponent.listContractDeposit(contractId);
    }

    /**
     * 物业押金列表
     * @param search
     * @return
     */
    public RefundBillPageResult<RealtyDepositVo> listDepositPage(RealtyDepositSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.listDepositPage(search);
    }

    /**
     * 账单状态校验
     * @param list
     */
    private void checkPayingBill(List<RealtyDepositVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 押金账单锁
     *
     * @param deposit
     * @return
     */
    private String buildPayLockKey(RealtyDepositVo deposit) {
        return String.format(SenoxConst.Cache.KEY_ONE_TIME_FEE_BILL_PAY, deposit.getRealtySerial(), deposit.getContractNo());
    }

    /**
     * 移除支付账单锁
     * @param bills
     */
    private void removeBillPayingLock(List<RealtyDepositVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 无支付订单退费
     * @param deposit
     * @param refundOrder
     * @return
     */
    private OrderResultVo refundNoOrderBill(RealtyDepositVo deposit, RefundOrderVo refundOrder) {
        lockRefundOrder(deposit.getId());
        OrderResultVo result = null;
        try {
            PayWay payWay = PayWay.fromName(refundOrder.getRefundWay().name());
            PayAmountVo payAmount = new PayAmountVo(payWay, DecimalUtils.subtract(BigDecimal.ZERO, deposit.getAmount()));
            OrderVo order = newPayOrder(Collections.singletonList(deposit), payAmount, refundOrder.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("退费下单失败");
            }
            log.info("物业押金退费成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(Collections.singletonList(deposit.getId()), refundOrder.getTollMan(), result);
            }
            log.info("refund deposit {} finish.", deposit.getId());
        } finally {
            removeRefundOrderLock(deposit.getId());
        }

        return null;
    }

    /**
     * 构建押金支付订单
     * @param depositList
     * @param amounts
     * @param ip
     * @return
     */
    private List<OrderVo> buildDepositOrders(List<RealtyDepositVo> depositList, List<PayAmountVo> amounts, String ip) {
        List<OrderVo> resultList = new ArrayList<>(depositList.size() + amounts.size());
        for (RealtyDepositVo deposit : depositList) {
            BigDecimal payingAmount = deposit.getAmount();

            for (PayAmountVo amount : amounts) {
                log.info("【押金支付】payingAmount: {}, amount: {}", payingAmount, JsonUtils.object2Json(amount));
                if (!DecimalUtils.isPositive(amount.getAmount()) || !DecimalUtils.isPositive(payingAmount)) {
                    continue;
                }

                if (amount.getAmount().compareTo(payingAmount) > 0) {
                    resultList.add(newPayOrder(Collections.singletonList(deposit), new PayAmountVo(amount.getPayWay(), payingAmount), ip));
                    amount.setAmount(DecimalUtils.subtract(amount.getAmount(), payingAmount));
                    payingAmount = BigDecimal.ZERO;
                } else {
                    resultList.add(newPayOrder(Collections.singletonList(deposit), new PayAmountVo(amount.getPayWay(), amount.getAmount()), ip));
                    payingAmount = DecimalUtils.subtract(payingAmount, amount.getAmount());
                    amount.setAmount(BigDecimal.ZERO);
                }
            }
        }
        return resultList;
    }

    /**
     * 构建支付订单
     * @param depositList
     * @param payAmount
     * @param ip
     * @return
     */
    private OrderVo newPayOrder(List<RealtyDepositVo> depositList, PayAmountVo payAmount, String ip) {
        if (payAmount.getAmount() == null) {
            payAmount.setAmount(depositList.stream().map(RealtyDepositVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        final boolean isRefund = DecimalUtils.isNegative(payAmount.getAmount());

        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.DEPOSIT);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(depositList.stream().map(x -> newPayOrderItem(x, payAmount.getAmount())).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(getOrderTitle(isRefund), LocalDate.now(), depositList.get(0).getFeeName()));
        }
        return result;
    }

    /**
     * 构建支付订单明细
     * @param deposit
     * @return
     */
    private OrderItemVo newPayOrderItem(RealtyDepositVo deposit, BigDecimal amount) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(deposit.getId());
        result.setProductName(
                String.format(getOrderTitle(DecimalUtils.isNegative(amount)), deposit.getRealtyName(), deposit.getRemark())
        );
        result.setQuantity(1);
        result.setPrice(amount.abs().compareTo(deposit.getAmount().abs()) > 0 ? deposit.getAmount() : amount);
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        result.setDetails(Collections.singletonList(newPayOrderItemDetail(deposit, amount)));
        return result;
    }

    /**
     * 构建支付订单明细详情
     * @param deposit
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(RealtyDepositVo deposit, BigDecimal amount) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(deposit.getFeeId());
        result.setFeeName(deposit.getFeeName());
        result.setQuantity(1);
        result.setPrice(amount.abs().compareTo(deposit.getAmount().abs()) > 0 ? deposit.getAmount() : amount);
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 通知更新账单结果
     * @param billIds
     * @param tollMan
     * @param orderResult
     */
    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo orderResult) {
        BillPaidVo result = new BillPaidVo();
        result.setBillIds(billIds);
        result.setOrderId(orderResult.getOrderId());
        result.setAmount(orderResult.getAmount());
        result.setPaid(orderResult.getStatus() == OrderStatus.PAID.getStatus());
        result.setPaidTime(orderResult.getOrderTime());
        result.setTollMan(tollMan);
        result.setRefund(orderResult.getAmount() == null || DecimalUtils.isNegative(orderResult.getAmount()));
        realtyComponent.updateDepositStatus(result);
    }

}
