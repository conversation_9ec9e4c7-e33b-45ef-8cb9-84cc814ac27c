package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingStoreInspectionBriefVo;
import com.senox.realty.vo.FirefightingStoreInspectionSearchVo;
import com.senox.realty.vo.FirefightingStoreInspectionVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/25 17:07
 */
@Service
@RequiredArgsConstructor
public class FirefightingStoreInspectionService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加商铺消防巡检记录
     * @param inspection
     * @return
     */
    public Long addStoreInspection(FirefightingStoreInspectionVo inspection) {
        return firefightingComponent.addStoreInspection(inspection);
    }

    /**
     * 更新商铺消防巡检记录
     * @param inspection
     */
    public void updateStoreInspection(FirefightingStoreInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateStoreInspection(inspection);
    }

    /**
     * 删除商铺消防巡检记录
     * @param id
     */
    public void deleteStoreInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteStoreInspection(id);
    }

    /**
     * 获取商铺消防巡检记录
     * @param id
     * @return
     */
    public FirefightingStoreInspectionVo findStoreInspectionById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findStoreInspectionById(id) : null;
    }

    /**
     * 商铺消防巡检记录数统计
     * @param search
     * @return
     */
    public int countStoreInspection(FirefightingStoreInspectionSearchVo search) {
        return firefightingComponent.countStoreInspection(search);
    }

    /**
     * 商铺消防巡检记录页
     * @param search
     * @return
     */
    public PageResult<FirefightingStoreInspectionBriefVo> listStoreInspectionPage(FirefightingStoreInspectionSearchVo search) {
        return firefightingComponent.listStoreInspectionPage(search);
    }


}
