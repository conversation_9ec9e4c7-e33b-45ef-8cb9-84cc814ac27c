package com.senox.web.service;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.vo.*;
import com.senox.web.component.LogisticLoaderComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
@RequiredArgsConstructor
@Service
public class LogisticLoaderService {
    private final LogisticLoaderComponent loaderComponent;

    /**
     * 添加搬运工结算
     *
     * @param loaderSettlementFormVo 搬运工结算表单
     */
    public void addLoaderSettlement(LogisticLoaderSettlementFormVo loaderSettlementFormVo) {
        loaderComponent.addLoaderSettlement(loaderSettlementFormVo);
    }

    /**
     * 更新搬运工结算
     *
     * @param loaderSettlementFormVo 搬运工结算表单
     */
    public void updateLoaderSettlement(LogisticLoaderSettlementFormVo loaderSettlementFormVo) {
        loaderComponent.updateLoaderSettlement(loaderSettlementFormVo);
    }

    /**
     * 根据id获取结算
     *
     * @param loaderSettlementId 搬运工结算id
     */
    public LogisticLoaderSettlementVo findLoaderSettlementById(Long loaderSettlementId) {
        if (!WrapperClassUtils.biggerThanLong(loaderSettlementId, 0)) {
            throw new InvalidParameterException();
        }
        return loaderComponent.findLoaderSettlementById(loaderSettlementId);
    }

    /**
     * 删除搬运工结算
     *
     * @param loaderSettlementId 搬运工结算id
     */
    public void deleteLoaderSettlementById(Long loaderSettlementId) {
        if (!WrapperClassUtils.biggerThanLong(loaderSettlementId, 0)) {
            throw new InvalidParameterException();
        }
        loaderComponent.deleteLoaderSettlementById(loaderSettlementId);
    }

    /**
     * 搬运工结算列表
     *
     * @param searchVo 查询
     * @return 返回搬运工结算列表
     */
    public List<LogisticLoaderSettlementVo> listLoaderSettlement(LogisticLoaderSettlementSearchVo searchVo) {
        searchVo.setPage(false);
        return loaderComponent.listLoaderSettlement(searchVo);
    }

    /**
     * 搬运工结算列表
     *
     * @param searchVo 查询
     * @return 返回搬运工结算列表
     */
    public BicycleTotalPageResult<LogisticLoaderSettlementVo> listPageLoaderSettlement(LogisticLoaderSettlementSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleTotalPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return loaderComponent.listPageLoaderSettlement(searchVo);
    }

    /**
     * 生成搬运工收益日报表
     *
     * @param dateVo 时间
     */
    public void generateLoaderIncomeDayReport(BicycleDateVo dateVo) {
        loaderComponent.generateLoaderIncomeDayReport(dateVo);
    }

    /**
     * 搬运工收益报表
     *
     * @param searchVo 查询
     * @return 返回搬运工收益报表
     */
    public BicycleTotalPageResult<LogisticLoaderIncomeVo> listLoaderIncomeStatistics(LogisticLoaderIncomeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleTotalPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return loaderComponent.listLoaderIncomeStatistics(searchVo);
    }

    /**
     * 搬运工收益统计报表
     *
     * @param searchVo 查询
     * @return 返回统计后的报表
     */
    public List<LogisticLoaderIncomeReportVo> listLoaderIncomeReportStatistics(LogisticLoaderIncomeSearchVo searchVo) {
       return loaderComponent.listLoaderIncomeReportStatistics(searchVo);
    }
}
