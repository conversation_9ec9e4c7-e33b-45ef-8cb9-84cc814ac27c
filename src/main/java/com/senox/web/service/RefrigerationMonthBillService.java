package com.senox.web.service;

import com.senox.cold.constant.RefrigerationFee;
import com.senox.cold.vo.RefrigerationBillMonthVo;
import com.senox.cold.vo.RefrigerationBillRemarkVo;
import com.senox.cold.vo.RefrigerationBillSendVo;
import com.senox.cold.vo.RefrigerationDayBillVo;
import com.senox.cold.vo.RefrigerationMonthBillSearchVo;
import com.senox.cold.vo.RefrigerationMonthBillVo;
import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.*;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.web.component.RefrigerationBillComponent;
import com.senox.web.component.WechatComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.convert.BillDiscountConvertor;
import com.senox.web.vo.BillDiscountVo;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/12/16 15:12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RefrigerationMonthBillService extends BillService {

    private final WechatComponent wechatComponent;
    private final RefrigerationBillComponent billComponent;
    private final BillDiscountConvertor discountConvertor;

    /**
     * 生成月账单
     * @param billMonth
     */
    public void generateMonthBill(RefrigerationBillMonthVo billMonth) {
        billComponent.generateMonthBill(billMonth);
    }

    /**
     * 更新月账单备注
     * @param billRemark
     */
    public void updateMonthBillRemark(RefrigerationBillRemarkVo billRemark) {
        if (!WrapperClassUtils.biggerThanLong(billRemark.getId(), 0L)) {
            return;
        }

        billComponent.updateMonthBillRemark(billRemark);
    }

    /**
     * 下发月账单
     * @param send
     */
    public void sendMonthBill(RefrigerationBillSendVo send) {
        billComponent.sendMonthBill(send);
    }

    /**
     * 保存票据号
     * @param tollSerial
     */
    public void saveBillSerial(TollSerialVo tollSerial) {
        if (!WrapperClassUtils.biggerThanLong(tollSerial.getBillId(), 0L)) {
            return;
        }
        billComponent.saveBillSerial(tollSerial);
    }

    /**
     * 删除月账单
     * @param id
     */
    public void deleteMonthBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        billComponent.deleteMonthBill(id);
    }

    /**
     * 支付冷藏账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 坏账核销
        updateMonthBillBadDebt(payRequest.getBills(), payRequest.getRemark());
        // 免滞纳金
        ignoreMonthBillPenalty(payRequest.getIgnorePenaltyIds());

        // 账单
        List<RefrigerationMonthBillVo> billList = listMonthBillByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(billList);

        billList.forEach(x ->  RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result = null;
        // 支付
        try {
            OrderVo order = newPayOrder(billList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            log.info("pay bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));
        } finally {
            removeBillPayingLock(billList);
        }
        log.info("finish pay bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 获取月账单
     * @param id
     * @return
     */
    public RefrigerationMonthBillVo findMonthBillById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? billComponent.findMonthBillById(id) : null;
    }

    /**
     * 根据id列表获取月账单列表
     * @param ids
     * @return
     */
    public List<RefrigerationMonthBillVo> listMonthBillByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return billComponent.listMonthBillByIds(ids);
    }

    /**
     * 月账单明细
     * @param id
     * @return
     */
    public List<RefrigerationDayBillVo> listMonthDayBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return Collections.emptyList();
        }
        return billComponent.listMonthDayBill(id);
    }

    /**
     * 合计月账单
     * @param search
     * @return
     */
    public RefrigerationMonthBillVo sumMonthBill(RefrigerationMonthBillSearchVo search) {
        return billComponent.sumMonthBill(search);
    }

    /**
     * 月账单列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationMonthBillVo> listMonthBill(RefrigerationMonthBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return billComponent.listMonthBill(search);
    }

    /**
     * 冷藏发票账单
     * @param search
     * @return
     */
    public PageStatisticsResult<RefrigerationMonthBillVo ,RefrigerationMonthBillVo> listReceiptBill(RefrigerationMonthBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return billComponent.listReceiptBill(search);
    }

    /**
     * 冷藏月账单微信通知
     * @param year
     * @param month
     */
    public void notifyMonthBill(Integer year, Integer month) {
        wechatComponent.notifyRefrigerationBill(year, month);
    }

    /**
     * 免滞纳金
     * @param billIds
     */
    private void ignoreMonthBillPenalty(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return;
        }

        BillPenaltyIgnoreVo penaltyIgnore = new BillPenaltyIgnoreVo();
        penaltyIgnore.setBillIds(billIds);
        penaltyIgnore.setPenaltyIgnore(Boolean.TRUE);
        billComponent.ignoreMonthBillPenalty(penaltyIgnore);
    }

    /**
     * 坏账核销
     * @param list
     * @param remark
     */
    private void updateMonthBillBadDebt(List<BillDiscountVo> list, String remark) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<BillBadDebtVo> badDebtList = new ArrayList<>(list.size());
        for (BillDiscountVo item : list) {
            BillBadDebtVo bdItem = discountConvertor.discount2BadDebt(item);
            bdItem.setRemark(remark);
            badDebtList.add(bdItem);
        }
        billComponent.updateMonthBillBadDebt(badDebtList);
    }


    /**
     * 账单
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    protected OrderVo newPayOrder(List<RefrigerationMonthBillVo> bills, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.REFRIGERATION);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_REFRIGERATION_BILL, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     * @param bill
     * @return
     */
    private OrderItemVo newPayOrderItem(RefrigerationMonthBillVo bill) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(buildRefrigerationBillTitle(bill));
        result.setQuantity(1);
        result.setPrice(DecimalUtils.subtract(bill.getTotalCharge(), bill.getDiscountAmount(), bill.getBadDebtAmount()));
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);

        BigDecimal badDebtAmount = bill.getBadDebtAmount();

        RefrigerationFee[] fees = RefrigerationFee.values();
        List<OrderItemDetailVo> list = new ArrayList<>(fees.length);
        for (int i = fees.length; i > 0; i--) {
            RefrigerationFee fee = fees[i - 1];

            OrderItemDetailVo item = newPayOrderItemDetail(bill, fee);
            if (fee == RefrigerationFee.PENALTY) {
                item.setTotalAmount(DecimalUtils.subtract(item.getTotalAmount(), bill.getDiscountAmount()));
                if (!DecimalUtils.isPositive(item.getTotalAmount())) {
                    continue;
                }

                item.setFree(bill.checkPenaltyIgnore() && !DecimalUtils.isPositive(item.getTotalAmount()));
            }
            // 坏账核销
            if (!item.isFree() && DecimalUtils.isPositive(badDebtAmount)) {
                if (badDebtAmount.compareTo(item.getTotalAmount()) > 0) {
                    badDebtAmount = DecimalUtils.subtract(badDebtAmount, item.getTotalAmount());
                    item.setTotalAmount(BigDecimal.ZERO);
                } else {
                    item.setTotalAmount(DecimalUtils.subtract(item.getTotalAmount(), badDebtAmount));
                    badDebtAmount = BigDecimal.ZERO;
                }
            }
            list.add(item);
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 子账单明细
     * @param bill
     * @param fee
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(RefrigerationMonthBillVo bill, RefrigerationFee fee) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(fee.getFeeId());
        result.setFeeName(fee.getName());
        result.setQuantity(1);
        result.setPrice(bill.getCharge(fee));
        result.setTotalAmount(result.getPrice());
        return result;
    }


    /**
     * 待支付校验
     * @param list
     */
    private void checkPayingBill(List<RefrigerationMonthBillVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 移除账单锁
     *
     * @param bills
     */
    private void removeBillPayingLock(List<RefrigerationMonthBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 通知更新账单结果
     *
     * @param billIds
     * @param tollMan
     * @param order
     */
    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(billIds);
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(tollMan);
        billPaid.setRefund(order.getAmount() == null || BigDecimal.ZERO.compareTo(order.getAmount()) > 0);
        billComponent.updateBillStatus(billPaid);
    }

    /**
     * 支付锁
     * @param bill
     * @return
     */
    private String buildPayLockKey(RefrigerationMonthBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_REFRIGERATION_BILL_PAY, bill.getId());
    }

    /**
     * 冷藏账单标题
     * @param bill
     * @return
     */
    private String buildRefrigerationBillTitle(RefrigerationMonthBillVo bill) {
        return String.format(SenoxConst.TITLE_REFRIGERATION_BILL, bill.getCustomerName(), buildBillYearMonth(bill.getBillYear(), bill.getBillMonth()));
    }

    /**
     * 账单年月
     *
     * @param billYear
     * @param billMonth
     * @return
     */
    private String buildBillYearMonth(Integer billYear, Integer billMonth) {
        return billYear + StringUtils.fixLength(String.valueOf(billMonth), 2, '0');
    }

}
