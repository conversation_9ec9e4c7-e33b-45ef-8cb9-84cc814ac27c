package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.AuditVo;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.*;
import com.senox.web.component.MerchantComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@RequiredArgsConstructor
@Service
public class MerchantService {
    private final MerchantComponent merchantComponent;


    /**
     * 添加商户
     *
     * @param merchant 商户
     */
    public Long addMerchant(MerchantVo merchant) {
        return merchantComponent.addMerchant(merchant);
    }

    /**
     * 批量新增商户，返回已存在的商户列表
     * @param list
     * @return
     */
    public List<MerchantVo> batchAddMerchant(List<MerchantVo> list) {
        return merchantComponent.batchAddMerchant(list);
    }

    /**
     * 更新商户
     *
     * @param merchantVo 商户
     */
    public void update(MerchantVo merchantVo) {
        if (!WrapperClassUtils.biggerThanLong(merchantVo.getId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        merchantComponent.updateMerchant(merchantVo);
    }

    /**
     * 根据联系方式查询商户
     * @param contact
     * @return
     */
    public List<MerchantVo> findByContact(String contact) {
        return merchantComponent.findByContact(contact);
    }

    /**
     * 删除商户
     *
     * @param id
     */
    public void delete(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        merchantComponent.deleteMerchant(id);
    }

    /**
     * 根据id获取商户信息
     *
     * @param id
     * @return
     */
    public MerchantVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? merchantComponent.findMerchantById(id) : null;
    }

    /**
     * 商户列表
     *
     * @param searchVo 查询参数
     */
    public PageResult<MerchantVo> list(MerchantSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return merchantComponent.listMerchant(searchVo);
    }

    /**
     * 添加权限申请
     *
     * @param apply
     * @return
     */
    public Long addAuthApply(MerchantAuthApplyEditVo apply) {
        return merchantComponent.addAuthApply(apply);
    }

    /**
     * 更新权限申请信息
     *
     * @param apply
     */
    public void updateAuthApply(MerchantAuthApplyEditVo apply) {
        merchantComponent.updateAuthApply(apply);
    }

    /**
     * 删除权限申请
     *
     * @param id
     */
    public void deleteAuthApply(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        merchantComponent.deleteAuthApply(id);
    }

    /**
     * 申请审核
     *
     * @param audit
     * @return
     */
    public MerchantVo auditAuthApply(AuditVo audit) {
        return merchantComponent.auditAuthApply(audit);
    }

    /**
     * 根据id查找审批信息
     *
     * @param id
     * @return
     */
    public MerchantAuthApplyVo findAuthApplyById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? merchantComponent.findApplyById(id) : null;
    }


    /**
     * 申请列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MerchantAuthApplyListVo> applyList(MerchantAuthApplySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return merchantComponent.listApplyPage(search);
    }

    /**
     * 根据冷藏客户编号查找商户
     *
     * @param rcSerial 冷藏编号
     * @return
     */
    public MerchantVo findByRc(String rcSerial) {
        if (StringUtils.isBlank(rcSerial)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return merchantComponent.findByRc(rcSerial);
    }

    /**
     * 根据城际运输编号查找商户
     *
     * @param ltSerials 城际运输编号集
     * @return 返回查找到的商户列表
     */
    public List<MerchantVo> findByLtSerialList(List<String> ltSerials) {
        if (CollectionUtils.isEmpty(ltSerials)) {
            return Collections.emptyList();
        }
        return merchantComponent.findByLtSerialList(ltSerials);
    }

    /**
     * 根据商户名查找商户
     *
     * @param merchantName 商户名
     * @return 返回查找到的商户
     */
    public MerchantVo findByName(String merchantName) {
        if (StringUtils.isBlank(merchantName)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return merchantComponent.findByName(merchantName);
    }

    /**
     * 商户地址保存
     * @param addressVo
     */
    public void saveMerchantAddress(MerchantAddressVo addressVo) {
        merchantComponent.saveMerchantAddress(addressVo);
    }

    /**
     * 商户地址获取
     * @param id
     * @return
     */
    public MerchantAddressVo findMerchantAddress(Long id) {
        return merchantComponent.findMerchantAddress(id);
    }

    /**
     * 商户地址删除
     * @param id
     */
    public void deleteMerchantAddress(Long id) {
        merchantComponent.deleteMerchantAddress(id);
    }

    /**
     * 根据商户id获取地址
     * @param merchantId
     * @return
     */
    public List<MerchantAddressVo> listByMerchantId(Long merchantId) {
        return merchantComponent.listByMerchantId(merchantId);
    }

    /**
     * 商户地址分页
     * @param searchVo
     * @return
     */
    public PageResult<MerchantAddressVo> pageMerchantAddress(MerchantAddressSearchVo searchVo) {
        return merchantComponent.pageMerchantAddress(searchVo);
    }
}
