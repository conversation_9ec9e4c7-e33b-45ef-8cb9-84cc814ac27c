package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import com.senox.web.component.EnterpriseComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:33
 */
@Service
@RequiredArgsConstructor
public class EnterpriseService {

    private final EnterpriseComponent enterpriseComponent;

    /**
     * 保存经营户信息
     * @param enterprise
     * @return
     */
    public Long saveEnterprise(EnterpriseEditVo enterprise) {
        return enterpriseComponent.saveEnterprise(enterprise);
    }

    /**
     * 删除经营户信息
     * @param id
     */
    public void deleteEnterprise(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        enterpriseComponent.deleteEnterprise(id);
    }

    /**
     * 设定经营户消防重点场所
     * @param enterpriseIds
     */
    public void saveEnterpriseFirefightingEmphasis(List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return;
        }

        enterpriseComponent.saveEnterpriseFirefightingEmphasis(enterpriseIds);
    }

    /**
     * 取消经营户消防重点场所
     * @param enterpriseIds
     */
    public void cancelEnterpriseFirefightingEmphasis(List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return;
        }

        enterpriseComponent.cancelEnterpriseFirefightingEmphasis(enterpriseIds);
    }

    /**
     * 根据id查找经营户信息
     * @param id
     * @return
     */
    public EnterpriseViewVo findEnterpriseById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? enterpriseComponent.findEnterpriseById(id) : null;
    }

    /**
     * 经营户信息列表
     * @param search
     * @return
     */
    public List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search) {
        return enterpriseComponent.listEnterprise(search);
    }

    /**
     * 经营户信息列表页
     * @param search
     * @return
     */
    public PageResult<EnterpriseViewVo> listEnterprisePage(EnterpriseSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return enterpriseComponent.listEnterprisePage(search);
    }
}
