package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.tms.vo.*;
import com.senox.web.component.BicycleDeliveryOrderComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/21 13:59
 */
@Service
@RequiredArgsConstructor
public class BicycleDeliveryOrderService {

    private final BicycleDeliveryOrderComponent bicycleDeliveryOrderComponent;

    /**
     * 添加三轮车配送单
     * @param orderVo
     * @return
     */
    public Long addBicycleDeliveryOrder(BicycleDeliveryOrderVo orderVo) {
        return bicycleDeliveryOrderComponent.addBicycleDeliveryOrder(orderVo);
    }

    /**
     * 根据id获取配送单
     * @param id
     * @return
     */
    public BicycleDeliveryOrderVo findDeliveryOrderById(Long id) {
        return bicycleDeliveryOrderComponent.findDeliveryOrderById(id);
    }

    /**
     * 根据id获取配送详细单
     * @param id
     * @return
     */
    public BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(Long id) {
        return bicycleDeliveryOrderComponent.findDeliveryOrderDetailById(id);
    }

    /**
     * 根据订单流水号获取配送详细单
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(String orderSerialNo) {
        return bicycleDeliveryOrderComponent.findDeliveryOrderDetailByOrderSerialNo(orderSerialNo);
    }

    /**
     * 指定骑手配送三轮车配送单
     * @param orderVo
     */
    public void appointBicycleDeliveryOrderReider(BicycleDeliveryOrderVo orderVo) {
        bicycleDeliveryOrderComponent.appointBicycleDeliveryOrderReider(orderVo);
    }

    /**
     * 取消骑手配送
     * @param id
     */
    public void cancelRiderDeliveryOrderById(Long id) {
        bicycleDeliveryOrderComponent.cancelRiderDeliveryOrderById(id);
    }

    /**
     * 查询骑手情况
     * @param searchVo
     * @return
     */
    public BicycleDayCountRiderVo riderStatistics(BicycleStatisticsSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.riderStatistics(searchVo);
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<BicycleOrderCountVo> statistics(BicycleStatisticsSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.statistics(searchVo);
    }

    /**
     * 合并配送单
     * @param orderSerialNoList
     * @param deliveryOrderSerialNo
     */
    public void merged(List<String> orderSerialNoList, String deliveryOrderSerialNo) {
        bicycleDeliveryOrderComponent.merged(orderSerialNoList, deliveryOrderSerialNo);
    }

    /**
     * 取消合并
     * @param orderSerialNoList
     */
    public void cancelMerged(List<String> orderSerialNoList) {
        bicycleDeliveryOrderComponent.cancelMerged(orderSerialNoList);
    }

    /**
     * 今日骑手完成单量统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderCountList(BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.riderCountList(searchVo);
    }

    /**
     * 骑手当日统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderCount(BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.sumRiderCount(searchVo);
    }

    /**
     * 骑手配送历史统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderHistoryCountList(BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.riderHistoryCountList(searchVo);
    }

    /**
     * 骑手历史配送统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderHistoryCount(BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.sumRiderHistoryCount(searchVo);
    }

    /**
     * 添加费用
     * @param chargeVo
     */
    public void addBicycleOrderCharge(BicycleOrderChargeVo chargeVo) {
        bicycleDeliveryOrderComponent.addBicycleOrderCharge(chargeVo);
    }

    /**
     * 更新货物明细
     * @param goodsDetailVos
     */
    public void updateBicycleOrderDetail(List<BicycleOrderGoodsDetailVo> goodsDetailVos) {
        bicycleDeliveryOrderComponent.updateBicycleOrderDetail(goodsDetailVos);
    }

    /**
     * 配送单提货信息分页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(BicycleDeliveryInfoSearchVo searchVo) {
        return bicycleDeliveryOrderComponent.deliveryInfoPage(searchVo);
    }

    /**
     *
     * 根据订单编号查询配送订单详细信息
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(String orderSerialNo) {
        return bicycleDeliveryOrderComponent.deliveryDetailInfoByOrderSerialNo(orderSerialNo);
    }
}
