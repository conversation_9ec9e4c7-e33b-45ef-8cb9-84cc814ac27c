package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.tms.constant.BicycleFee;
import com.senox.tms.vo.*;
import com.senox.web.component.BicycleBillSettlementComponent;
import com.senox.web.component.WechatComponent;
import com.senox.web.vo.TollPrintItemVo;
import com.senox.web.vo.TollPrintVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@RequiredArgsConstructor
@Component
public class BicycleBillSettlementService {
    private final BicycleBillSettlementComponent billSettlementComponent;
    private final WechatComponent wechatComponent;
    private final BicycleBillService billService;
    private final AdminUserService adminUserService;

    /**
     * 根据id查询结算单
     *
     * @param id id
     * @return 返回结算单
     */
    public BicycleBillSettlementVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return billSettlementComponent.findById(id);
    }

    /**
     * 账单支付
     *
     * @param billOrderVo 订单
     * @return 返回支付结果
     */
    public OrderResultVo payBill(BicycleBillOrderVo billOrderVo) {
        return billSettlementComponent.payBill(billOrderVo);
    }

    /**
     * 结算单列表
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    public List<BicycleBillSettlementVo> list(BicycleBillSettlementSearchVo searchVo) {
        searchVo.setPage(false);
        return billSettlementComponent.list(searchVo);
    }

    /**
     * 结算单分页列表
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    public BicycleBillSettlementPageResult<BicycleBillSettlementVo> listPage(BicycleBillSettlementSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleBillSettlementPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        if (!StringUtils.isBlank(searchVo.getOpenid())) {
            searchVo.setMerchantIds(wechatComponent.listMerchantIdByOpenid(searchVo.getOpenid()));
            searchVo.setOpenid(StringUtils.EMPTY);
        }
        return billSettlementComponent.listPage(searchVo);

    }

    /**
     * 根据结算单id查询详情
     *
     * @param settlementId 结算单id
     * @return 返回查询到的详情
     */
    public BicycleBillSettlementDetailVo detailBySettlement(Long settlementId) {
        if (!WrapperClassUtils.biggerThanLong(settlementId, 0)) {
            return null;
        }
        return billSettlementComponent.detailBySettlement(settlementId);
    }

    /**
     * 结算单下发
     *
     * @param sendVo 下发参数
     */
    public void send(BicycleBillSettlementSendVo sendVo) {
        if ((null == sendVo.getStartDate() || null == sendVo.getEndDate())
                && CollectionUtils.isEmpty(sendVo.getSettlementIds())
        ) {
            throw new InvalidParameterException();
        }
        billSettlementComponent.send(sendVo);
    }

    /**
     * 结算单下发通知
     *
     * @param sendVo 下发参数
     */
    public void notifySend(BicycleBillSettlementSendVo sendVo) {
        if (null == sendVo.getStartDate() || null == sendVo.getEndDate()) {
            throw new InvalidParameterException();
        }
        billSettlementComponent.notifySend(sendVo);
    }

    /**
     * 结算单生成
     *
     * @param sendVo 参数
     */
    public void generate(BicycleBillSettlementSendVo sendVo) {
        billSettlementComponent.generate(sendVo);
    }

    /**
     * 打印结算单
     *
     * @param id            结算单id
     * @param refreshSerial 刷新单号
     * @return 返回结算单打印参数
     */
    public TollPrintVo printBill(Long id, Boolean refreshSerial) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        BicycleBillSettlementVo billSettlement = findById(id);
        if (null == billSettlement) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BooleanUtils.isFalse(billSettlement.getStatus())) {
            throw new BusinessException(String.format("账单[%s]未支付", billSettlement.getId()));
        }
        BicycleBillSearchVo searchVo = new BicycleBillSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10000);
        searchVo.setSettlementIds(Collections.singletonList(id));
        List<BicycleBillVo> billList = billService.listBillPage(searchVo).getDataList();
        if (CollectionUtils.isEmpty(billList)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        TollPrintVo result = new TollPrintVo();
        result.setPayer(billSettlement.getMerchantName());
        result.setPayerDesc(billSettlement.getRcSerial());
        result.setTollMan(billSettlement.getTollManName());
        result.setTollTime(billSettlement.getPaidTime());
        result.setTotalAmount(billSettlement.getPaidAmount());
        result.setDetails(buildTollPrintItem(billList,billSettlement));
        result.setBillSerial(adminUserService.getAndIncAdminTollSerial());
        return result;
    }

    /**
     * 构建打印项
     *
     * @param billList 账单列表
     * @param billSettlement 结算单
     * @return 返回构建好的项列表
     */
    private List<TollPrintItemVo> buildTollPrintItem(List<BicycleBillVo> billList,BicycleBillSettlementVo billSettlement) {
        List<TollPrintItemVo> list = new ArrayList<>();
        TollPrintItemVo item1 = new TollPrintItemVo();
        item1.setAmount(billList.stream().map(BicycleBillVo::getDeliveryAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        item1.setFee(BicycleFee.DELIVERY.getName());
        item1.setPrice(item1.getAmount());
        item1.setTime(billSettlement.getBillYearMonth());
        list.add(item1);
        TollPrintItemVo item2 = new TollPrintItemVo();
        item2.setAmount(billList.stream().map(BicycleBillVo::getOtherAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        item2.setFee(BicycleFee.OTHER.getName());
        item2.setPrice(item2.getAmount());
        item2.setTime(billSettlement.getBillYearMonth());
        list.add(item2);
        TollPrintItemVo item3 = new TollPrintItemVo();
        item3.setAmount(billList.stream().map(BicycleBillVo::getHandlingAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        item3.setFee(BicycleFee.BICYCLE_HANDLING.getName());
        item3.setPrice(item3.getAmount());
        item3.setTime(billSettlement.getBillYearMonth());
        list.add(item3);
        TollPrintItemVo item4 = new TollPrintItemVo();
        item4.setAmount(billList.stream().map(BicycleBillVo::getUpstairsAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        item4.setFee(BicycleFee.BICYCLE_UPSTAIRS.getName());
        item4.setPrice(item4.getAmount());
        item4.setTime(billSettlement.getBillYearMonth());
        list.add(item4);
        return list;
    }
}
