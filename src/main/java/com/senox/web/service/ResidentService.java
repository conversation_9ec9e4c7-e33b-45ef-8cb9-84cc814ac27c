package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.*;
import com.senox.web.component.ResidentComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 15:23
 */
@Service
public class ResidentService {

    @Autowired
    private ResidentComponent residentComponent;

    /**
     * 添加住户
     *
     * @param residentVo
     * @return
     */
    public String addResident(ResidentVo residentVo) {
        return residentComponent.addResident(residentVo);
    }

    /**
     * 只修改住户，不修改人脸
     *
     * @param residentVo
     */
    public void updateResident(ResidentVo residentVo) {
        if (!WrapperClassUtils.biggerThanLong(residentVo.getId(), 0L)) {
            return;
        }
        residentComponent.updateResident(residentVo);
    }

    /**
     * 只修改人脸
     *
     * @param residentFaceUrlVo
     */
    public void updateFaceUrl(ResidentFaceUrlVo residentFaceUrlVo) {
        residentComponent.updateFaceUrl(residentFaceUrlVo);
    }

    /**
     * 删除住户
     *
     * @param id
     */
    public void deleteResident(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        residentComponent.deleteResident(id);
    }

    /**
     * 获取住户
     *
     * @param id
     * @return
     */
    public ResidentVo findResidentById(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return residentComponent.findResidentById(id);
    }

    /**
     * 根据住户编号查找住户
     *
     * @param residentNo
     * @return
     */
    public ResidentVo findResidentByResidentNo(String residentNo){
        if (StringUtils.isBlank(residentNo)){
            return null;
        }
        return residentComponent.findResidentByResidentNo(residentNo);
    }

    /**
     * 根据身份证查找住户
     *
     * @param idNum
     * @return
     */
    public ResidentVo findResidentByIdNum(String idNum){
        if (StringUtils.isBlank(idNum)){
            return null;
        }
        return residentComponent.findResidentByIdNum(idNum);
    }

    /**
     * 住户列表
     *
     * @param search
     * @return
     */
    public PageResult<ResidentVo> residentList(ResidentSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return residentComponent.residentList(search);
    }

    /**
     * 添加住户权限
     *
     * @param residentAccessVo
     */
    public void addResidentAccess(ResidentAccessVo residentAccessVo){
        residentComponent.addResidentAccess(residentAccessVo);
    }

    /**
     * 删除住户权限
     *
     * @param ids
     */
    public void deleteResidentAccess(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        residentComponent.deleteResidentAccess(ids);
    }

    /**
     * 根据住户编号查询住户权限
     *
     * @param residentNo
     * @return
     */
    public ResidentAccessResultVo residentAccessResultByNo(String residentNo){
        if (StringUtils.isBlank(residentNo)){
            return null;
        }
        return residentComponent.residentAccessResultByNo(residentNo);
    }

    /**
     * 门禁权限同步
     * @param deviceId
     * @param targetDeviceId
     */
    public void residentAccessSync(Long deviceId, Long targetDeviceId) {
        residentComponent.residentAccessSync(deviceId, targetDeviceId);
    }
}
