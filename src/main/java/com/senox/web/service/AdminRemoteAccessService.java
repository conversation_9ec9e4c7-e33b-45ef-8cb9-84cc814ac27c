package com.senox.web.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import com.senox.web.component.AdminRemoteAccessComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/26 15:11
 */
@Service
public class AdminRemoteAccessService {

    @Autowired
    private AdminRemoteAccessComponent adminRemoteAccessComponent;

    /**
     * 添加远程权限
     *
     * @param remoteAccessVo
     * @return
     */
    public Long addRemoteAccess(AdminRemoteAccessVo remoteAccessVo) {
        return adminRemoteAccessComponent.addRemoteAccess(remoteAccessVo);
    }

    /**
     * 删除远程权限
     *
     * @param id
     */
    public void deleteRemoteAccess(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        adminRemoteAccessComponent.deleteRemoteAccess(id);
    }

    /**
     * 获取远程权限
     *
     * @param id
     * @return
     */
    public AdminRemoteAccessVo findRemoteAccessById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return adminRemoteAccessComponent.findRemoteAccessById(id);
    }

    /**
     * 远程权限列表
     *
     * @param search
     * @return
     */
    public PageResult<AdminRemoteAccessVo> remoteAccessList(AdminRemoteAccessSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return adminRemoteAccessComponent.remoteAccessList(search);
    }
}
