package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.component.AdminUserComponent;
import com.senox.user.vo.*;
import com.senox.web.component.AdminComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.senox.web.constant.SenoxConst.CACHE_KEY_LOGIN_USER;

/**
 * <AUTHOR>
 * @Date 2021/1/6 16:26
 */
@Service
public class AdminUserService {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserService.class);

    @Value("${senox.user.multiLogin:true}")
    private Boolean userMultiLogin;
    @Autowired
    private AdminComponent adminComponent;
    @Autowired
    private AdminUserComponent adminUserComponent;

    /**
     * 登录校验登录密码
     * @param loginVo
     * @return
     */
    public AdminUserDto login(AdminUserLoginVo loginVo) {
        AdminUserVo adminUser = adminComponent.checkPassword(loginVo);
        if (adminUser == null) {
            throw new BusinessException(ResultConst.USER_NOT_AUTHENTICATED, "账号/密码错误");
        }
        if (!BooleanUtils.isTrue(adminUser.getLoginable())) {
            throw new BusinessException("用户没有权限登录该系统");
        }
        AdminUserDto result = adminUserVo2AdminUserDto(adminUser);

        if (result.isValid()) {
            // 获取权限
            AdminContext.setUser(result);
            try {
                result.setCosList(adminComponent.listUserCos(result.getUserId()));
            } finally {
                AdminContext.clear();
            }

            if (!WrapperClassUtils.isTrue(userMultiLogin)) {
                // 不允许用户同时登录
                RedisUtils.set(String.format(CACHE_KEY_LOGIN_USER, result.getUserId()), result.getToken());
            }
        }
        return result;
    }

    /**
     * 退出登录
     * @param token
     */
    public void logout(String token) {
        if (!StringUtils.isBlank(token)) {
            AdminUserDto loginUser = adminUserComponent.getUserFromToken(token);
            if (loginUser != null) {
                RedisUtils.del(String.format(CACHE_KEY_LOGIN_USER, loginUser.getUserId()));
            }

            adminUserComponent.invalidToken(token);
        }
    }

    /**
     * 修改用户密码
     * @param changePwd
     */
    public void modifyPassword(AdminUserChangePwdVo changePwd) {
        adminComponent.changePassword(changePwd);
    }

    /**
     * 获取收费票据信息
     * @return
     */
    public TollManSerialVo findAdminToll() {
        TollManSerialVo result = adminComponent.findAdminToll();
        logger.info("Admin toll info: {}", JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 修改票据编号
     * @param adminToll
     */
    public void modifyAdminToll(TollManSerialVo adminToll) {
        adminComponent.modifyAdminToll(adminToll);
    }

    /**
     * 获取并递增票据号
     * @return
     */
    public String getAndIncAdminTollSerial() {
        // 获取票据号
        TollManSerialVo toll = findAdminToll();
        if (toll == null || StringUtils.isBlank(toll.getBillSerial())) {
            throw new BusinessException("无效的票据号");
        }

        String result = toll.getBillSerial();

        TollManSerialVo incrToll = new TollManSerialVo();
        incrToll.setBillSerial(String.valueOf(Integer.parseInt(result) + 1));
        modifyAdminToll(incrToll);
        return result;
    }

    /**
     * 添加用户
     * @param user
     * @return
     */
    public Long addAdminUser(AdminUserVo user) {
        if (StringUtils.isBlank(user.getUsername())) {
            return 0L;
        }
        return adminComponent.addAdminUser(user);
    }

    /**
     * 更新用户
     * @param user
     */
    public void updateAdminUser(AdminUserVo user) {
        if (!WrapperClassUtils.biggerThanLong(user.getId(), 0L)) {
            return;
        }
        adminComponent.updateAdminUser(user);
    }

    /**
     * 删除用户
     * @param id
     */
    public void deleteAdminUser(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        AdminUserVo user = new AdminUserVo();
        user.setId(id);
        user.setDisabled(Boolean.TRUE);
        updateAdminUser(user);
    }

    /**
     * 根据id查找用户
     * @param id
     * @return
     */
    public AdminUserVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return adminComponent.findById(id);
    }

    /**
     * 用户列表
     * @param searchVo
     * @return
     */
    public PageResult<AdminUserVo> listAdminUserPage(AdminUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return adminComponent.listAdminUserPage(searchVo);
    }

    /**
     * 用户部门列表
     * @param id
     * @return
     */
    public List<Long> listUserDepartment(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return Collections.emptyList();
        }
        return adminComponent.listUserDepartment(id);
    }

    /**
     * 用户权限列表
     * @param id
     * @return
     */
    public List<String> listUserCos(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return Collections.emptyList();
        }
        return adminComponent.listUserCos(id);
    }


    private AdminUserDto adminUserVo2AdminUserDto(AdminUserVo user) {
        AdminUserDto result = new AdminUserDto();
        result.setUserId(user.getId());
        result.setUsername(user.getUsername());
        result.setRealName(user.getRealName());
        result.setDepartmentId(user.getDepartmentId());
        result.setDepartmentName(user.getDepartmentName());
        result.setTollMan(BooleanUtils.isTrue(user.getTollMan()));
        result.setMaintainType(user.getMaintainManType());
        result.setLoginPath(user.getLoginPath());
        result.setToken(user.getToken());
        result.setRoleCodes(user.getRoleCodes());
        return result;
    }
}
