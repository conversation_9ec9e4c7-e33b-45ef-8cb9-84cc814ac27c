package com.senox.web.service;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.AccessControlAlarmTimeoutVo;
import com.senox.dm.vo.AccessControlCommandSearchVo;
import com.senox.dm.vo.AccessControlCommandVo;
import com.senox.dm.vo.AccessControlMaintainVo;
import com.senox.dm.vo.AccessControlSearchVo;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.AuthorizedGateSearchVo;
import com.senox.dm.vo.AuthorizedGateVo;
import com.senox.web.component.AccessControlComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 13:54
 */
@Service
@RequiredArgsConstructor
public class AccessControlService {

    private final AccessControlComponent accessControlComponent;

    /**
     * 新增海康门禁设备
     * @param accessControlVoList
     */
    public void addBatchAccessControl(List<AccessControlVo> accessControlVoList){
        if (CollectionUtils.isEmpty(accessControlVoList)){
            throw new InvalidParameterException();
        }
        accessControlComponent.addBatchAccessControl(accessControlVoList);
    }

    /**
     * 更新海康门禁设备
     * @param accessControlVo
     */
    public void updateAccessControl(AccessControlVo accessControlVo){
        if (!WrapperClassUtils.biggerThanLong(accessControlVo.getId(), 0L)) {
            return;
        }
        accessControlComponent.updateAccessControl(accessControlVo);
    }

    /**
     * 维修海康门禁设备
     * @param maintain
     */
    public void maintainAccessControl(AccessControlMaintainVo maintain) {
        if (StringUtils.isBlank(maintain.getDeviceIp())) {
            return;
        }

        accessControlComponent.maintainAccessControl(maintain);
    }

    /**
     * 延时报警时长保存
     * @param acTimeout
     */
    public void saveAlarmTimeout(AccessControlAlarmTimeoutVo acTimeout) {
        if (StringUtils.isBlank(acTimeout.getDeviceIp())) {
            return;
        }
        accessControlComponent.saveAlarmTimeout(acTimeout);
    }

    /**
     * 海康门禁设备布防
     * @param ip
     */
    public void deployAccessControl(String ip) {
        if (StringUtils.isBlank(ip)) {
            return;
        }

        accessControlComponent.deployAccessControl(ip);
    }

    /**
     * 海康门禁设备取消布防
     * @param ip
     */
    public void undeployAccessControl(String ip) {
        if (StringUtils.isBlank(ip)) {
            return;
        }

        accessControlComponent.undeployAccessControl(ip);
    }

    /**
     * 删除海康门禁设备
     * @param id
     */
    public void deleteAccessControl(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        accessControlComponent.deleteAccessControl(id);
    }

    /**
     * 根据id获取海康门禁设备
     * @param id
     * @return
     */
    public AccessControlVo findAccessControlById(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return accessControlComponent.findAccessControlById(id);
    }

    /**
     * 海康门禁设备列表
     * @param search
     * @return
     */
    public PageResult<AccessControlVo> listAccessControl(AccessControlSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return accessControlComponent.listAccessControl(search);
    }

    /**
     * 根据物业编号获取设备
     * @param realtySerial
     * @return
     */
    public List<AccessControlVo> getAccessControlByRealtySerial(String realtySerial){
        if (StringUtils.isBlank(realtySerial)){
            return Collections.emptyList();
        }
        return accessControlComponent.getByRealtySerial(realtySerial);
    }

    /**
     * 授权门禁列表
     * @param search
     * @return
     */
    public List<AuthorizedGateVo> listAuthorizedGate(AuthorizedGateSearchVo search) {
        return accessControlComponent.listAuthorizedGate(search);
    }

    /**
     * 远程开门
     * @param deviceIp
     */
    public void remoteControlGate(String deviceIp){
        if (StringUtils.isBlank(deviceIp)){
            return;
        }
        accessControlComponent.remoteControlGate(deviceIp);
    }

    /**
     * 执行命令
     * @param id
     */
    public void executeCommand(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        accessControlComponent.executeCommand(id);
    }

    /**
     * 恢复命令
     * @param id
     */
    public void enableCommand(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        accessControlComponent.enableCommand(id);
    }

    /**
     * 作废命令
     * @param id
     */
    public void disableCommand(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        accessControlComponent.disableCommand(id);
    }

    /**
     * 命令列表
     * @param search
     * @return
     */
    public PageResult<AccessControlCommandVo> listCommand(AccessControlCommandSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return accessControlComponent.listCommand(search);
    }
}
