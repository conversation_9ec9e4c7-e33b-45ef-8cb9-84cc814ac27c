package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.WxReplyMessageTemplateSearchVo;
import com.senox.user.vo.WxReplyMessageTemplateVo;
import com.senox.web.component.WechatMpReplyMessageTemplateComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-21
 **/
@Service
@RequiredArgsConstructor
public class WechatMpReplyMessageTemplateService {
    private final WechatMpReplyMessageTemplateComponent templateComponent;

    /**
     * 添加模板
     * @param template 模板
     */
    public void add(WxReplyMessageTemplateVo template) {
        templateComponent.add(template);
    }

    /**
     * 更新
     * @param template 模板
     */
    public void update(WxReplyMessageTemplateVo template) {
        if (!WrapperClassUtils.biggerThanLong(template.getId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        templateComponent.update(template);
    }

    /**
     * 更新状态
     * @param template 模板
     */
    public void updateStatus(WxReplyMessageTemplateVo template) {
        if (!WrapperClassUtils.biggerThanLong(template.getId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        templateComponent.updateStatus(template);
    }

    /**
     * 根据模板id删除
     * @param templateId 模板id
     */
    public void deleteById(Long templateId) {
        if (!WrapperClassUtils.biggerThanLong(templateId, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        templateComponent.deleteById(templateId);
    }

    /**
     * 根据id查找
     * @param templateId 模板id
     * @return 返回模板视图
     */
    public WxReplyMessageTemplateVo findById(Long templateId) {
        if (!WrapperClassUtils.biggerThanLong(templateId, 0)) {
            return null;
        }
        return templateComponent.findById(templateId);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public PageResult<WxReplyMessageTemplateVo> pageList(WxReplyMessageTemplateSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return templateComponent.pageList(search);
    }
}
