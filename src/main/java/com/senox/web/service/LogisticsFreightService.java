package com.senox.web.service;

import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.tms.constant.LogisticFee;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import com.senox.web.component.LogisticsFreightComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LogisticsFreightService extends BillService{
    private final LogisticsFreightComponent freightComponent;


    /**
     * 批量添加
     *
     * @param freightVos 货运列表
     */
    public void addBatch(List<LogisticsFreightVo> freightVos) {
        freightComponent.addBatch(freightVos);
    }

    /**
     * 根据id查找
     *
     * @param id id
     * @return 返回查找到的
     */
    public LogisticsFreightVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return freightComponent.findById(id);
    }

    /**
     * 更新
     *
     * @param freightVo 货运
     */
    public void update(LogisticsFreightVo freightVo) {
        if (!WrapperClassUtils.biggerThanLong(freightVo.getId(), 0)) {
            throw new InvalidParameterException();
        }
        freightComponent.update(freightVo);
    }

    /**
     * 删除
     *
     * @param id 货运id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        freightComponent.deleteById(id);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    public List<LogisticsFreightVo> list(LogisticsFreightSearchVo searchVo) {
        searchVo.setPage(false);
        return freightComponent.list(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页列表
     */
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(LogisticsFreightSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return freightComponent.listPage(searchVo);
    }

    /**
     * 支付珠三角账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {

        // 珠三角账单
        List<LogisticsFreightVo> freightVos = freightComponent.listByIds(payRequest.getBillIds());
        // 珠三角账单支付校验
        checkPayingBill(freightVos);

        freightVos.forEach(x ->  RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result = null;
        // 珠三角账单支付
        try {
            OrderVo order = newPayOrder(freightVos, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
                //更新备注
                updateBillRemark(payRequest);
            }
            log.info("pay logistic freight {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));
        } finally {
            removePayingLock(freightVos);
        }
        log.info("finish logistic freight {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 更新账单备注
     * @param payRequest
     */
    private void updateBillRemark(BillPayRequestVo payRequest) {
        LogisticFreightRemarkVo remarkVo = new LogisticFreightRemarkVo();
        remarkVo.setIds(payRequest.getBillIds());
        remarkVo.setRemark(payRequest.getRemark());
        freightComponent.updateBillRemark(remarkVo);
    }

    /**
     * 账单
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    protected OrderVo newPayOrder(List<LogisticsFreightVo> bills, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.LOGISTICS_FREIGHT);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_LOGISTIC_FREIGHT_BILL, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     *
     * @param freightVo
     * @return
     */
    private OrderItemVo newPayOrderItem(LogisticsFreightVo freightVo) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(freightVo.getId());
        result.setProductName(String.format(SenoxConst.TITLE_LOGISTIC_FREIGHT, StringUtils.EMPTY, StringUtils.EMPTY));
        result.setQuantity(1);
        result.setPrice(freightVo.getProfitAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        result.setDetails(Collections.singletonList(newPayOrderItemDetail(freightVo)));
        return result;
    }

    /**
     * 子账单明细
     * @param freightVo
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(LogisticsFreightVo freightVo) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(LogisticFee.LOGISTIC_FREIGHT.getFeeId());
        result.setFeeName(LogisticFee.LOGISTIC_FREIGHT.getName());
        result.setQuantity(1);
        result.setPrice(freightVo.getProfitAmount());
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 支付锁
     * @param freightVo
     * @return
     */
    private String buildPayLockKey(LogisticsFreightVo freightVo) {
        return String.format(SenoxConst.Cache.KEY_LOGISTIC_FREIGHT_PAY, freightVo.getId());
    }

    /**
     * 移除账单锁
     *
     * @param list
     */
    private void removePayingLock(List<LogisticsFreightVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 待支付校验
     * @param list
     */
    private void checkPayingBill(List<LogisticsFreightVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(billIds);
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(tollMan);
        billPaid.setRefund(order.getAmount() == null || BigDecimal.ZERO.compareTo(order.getAmount()) > 0);
        freightComponent.updateBillStatus(billPaid);
    }

    /**
     * 导入数据
     *
     * @param list 数据集
     */
    public void importData(List<LogisticsFreightVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        addBatch(idempotentData(list));
    }

    /**
     * 数据幂等
     * @param freights 数据集
     * @return 返回唯一数据集
     */
    private List<LogisticsFreightVo> idempotentData(List<LogisticsFreightVo> freights) {
        if (CollectionUtils.isEmpty(freights)) {
            return Collections.emptyList();
        }
        LocalDate minDate = freights.stream().map(LogisticsFreightVo::getReceivingDate).min(LocalDate::compareTo).orElse(null);
        LocalDate maxDate = freights.stream().map(LogisticsFreightVo::getReceivingDate).max(LocalDate::compareTo).orElse(null);
        LogisticsFreightSearchVo search = new LogisticsFreightSearchVo();
        search.setPage(false);
        search.setReceivingStartDate(minDate);
        search.setReceivingEndDate(maxDate);
        List<LogisticsFreightVo> dbFreights = list(search);
        if (CollectionUtils.isEmpty(dbFreights)) {
            return freights;
        }
        List<LogisticsFreightVo> effectiveFreights = new ArrayList<>(freights.size());
        Map<String, LogisticsFreightVo> dbFreightMap = dbFreights.stream().collect(Collectors.toMap(LogisticsFreightVo::getReceivingNo, Function.identity()));
        for (LogisticsFreightVo freight : freights) {
            LogisticsFreightVo dbFreight = dbFreightMap.get(freight.getReceivingNo());
            if (dbFreight != null) {
                log.warn("【Logistics freight】重复项: {}", JsonUtils.object2Json(freight));
                continue;
            }
            effectiveFreights.add(freight);
        }
        return effectiveFreights;
    }
}
