package com.senox.web.service;


import com.senox.car.dto.ParkingReceiptManagerDto;
import com.senox.car.vo.ReceiptOrderSearchVo;
import com.senox.car.vo.ReceiptOrderVo;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.ParkingReceiptComponent;
import com.senox.web.vo.ReceiptAuditVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-8-31
 */
@RequiredArgsConstructor
@Service
public class ParkingReceiptService {
    private final ParkingReceiptComponent receiptComponent;

    /**
     * 停车发票申请
     *
     * @param receiptManagerDto 发票管理
     * @return 单据编号
     */
    public String receiptApply(ParkingReceiptManagerDto receiptManagerDto) {
        if (null == receiptManagerDto) {
            return StringUtils.EMPTY;
        }
        return receiptComponent.receiptApply(receiptManagerDto);
    }

    /**
     * 处理审批开票申请
     *
     * @param audit
     */
    public void audit(ReceiptAuditVo audit) {
        if (!WrapperClassUtils.biggerThanLong(audit.getId(), 0L)) {
            return;
        }

        ReceiptOrderVo order = new ReceiptOrderVo();
        order.setId(audit.getId());
        order.setStatus(audit.getStatus().getValue());
        order.setReceiptRemark(audit.getRemark());
        receiptComponent.updateReceiptOrder(order);
    }

    /**
     * 重置开票申请姐u共
     *
     * @param id
     */
    public void resetAudit(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        receiptComponent.resetReceiptOrder(id);
    }

    /**
     * 根据id查找开票申请
     *
     * @param id
     * @return
     */
    public ReceiptOrderVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? receiptComponent.findOrderById(id) : null;
    }

    /**
     * 开票申请列表
     *
     * @param search
     * @return
     */
    public PageResult<ReceiptOrderVo> listOrder(ReceiptOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return receiptComponent.listOrder(search);
    }

}
