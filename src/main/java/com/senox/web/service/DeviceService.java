package com.senox.web.service;


import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.*;
import com.senox.pm.vo.PaymentDeviceSearchVo;
import com.senox.pm.vo.PaymentDeviceVo;
import com.senox.web.component.DeviceComponent;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-24
 */
@Service
public class DeviceService {

    private final DeviceComponent deviceComponent;

    public DeviceService(DeviceComponent deviceComponent) {
        this.deviceComponent = deviceComponent;
    }

    /**
     * 添加设备
     * @param device
     */
    public Long addAccessDevice(AccessDeviceVo device) {
        return deviceComponent.addAccessDevice(device);
    }

    /**
     * 更新设备
     * @param device
     */
    public void updateAccessDevice(AccessDeviceVo device) {
        if (!WrapperClassUtils.biggerThanLong(device.getId(), 0L)) {
            return;
        }
        deviceComponent.updateAccessDevice(device);
    }

    /**
     * 删除防疫门禁设备
     * @param id
     */
    public void deleteAccessDevice(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        AccessDeviceVo device = new AccessDeviceVo();
        device.setId(id);
        device.setDisabled(Boolean.TRUE);
        updateAccessDevice(device);
    }

    /**
     * 获取防疫设备
     * @param id
     * @return
     */
    public AccessDeviceVo findAccessDeviceById(Long id) {
       return WrapperClassUtils.biggerThanLong(id, 0L) ? deviceComponent.findById(id) : null;
    }

    /**
     * 门禁设备列表
     * @param search
     * @return
     */
    public PageResult<AccessDeviceVo> listAccessDevice(AccessDeviceSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return deviceComponent.listAccessDevice(search);
    }

    /**
     * 设置门禁设备上传状态
     * @param switcher
     * @return
     */
    public void switchAccessDeviceDataReport(Covid19DeviceReportSwitcherVo switcher) {
        deviceComponent.switchAccessDeviceDataReport(switcher);
    }

    /**
     * 上报防疫设备数据
     * @param data
     * @return
     */
    public Covid19AccessResponse reportCovid19AccessData(Covid19AccessDeviceDataVo data) {
        return deviceComponent.reportCovid19AccessData(data);
    }

    /**
     * 添加支付终端设备
     * @param device
     * @return
     */
    public Long addPaymentDevice(PaymentDeviceVo device) {
        return deviceComponent.addPaymentDevice(device);
    }

    /**
     * 更新支付终端设备
     * @param device
     */
    public void updatePaymentDevice(PaymentDeviceVo device) {
        if (!WrapperClassUtils.biggerThanLong(device.getId(), 0L)) {
            return;
        }
        deviceComponent.updatePaymentDevice(device);
    }

    /**
     * 删除支付终端
     * @param id
     */
    public void delPaymentDevice(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deviceComponent.delPaymentDevice(id);
    }

    /**
     * 根据id查找支付终端
     * @param id
     * @return
     */
    public PaymentDeviceVo findPaymentDeviceById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return deviceComponent.findPaymentDeviceById(id);
    }

    /**
     * 支付终端列表
     * @param search
     * @return
     */
    public List<PaymentDeviceVo> listPaymentDevice(PaymentDeviceSearchVo search) {
        return deviceComponent.listPaymentDevice(search);
    }

}
