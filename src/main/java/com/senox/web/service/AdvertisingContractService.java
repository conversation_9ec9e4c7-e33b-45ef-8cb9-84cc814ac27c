package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.AdvertisingContractEditVo;
import com.senox.realty.vo.AdvertisingContractListVo;
import com.senox.realty.vo.AdvertisingContractSearchVo;
import com.senox.realty.vo.AdvertisingContractVo;
import com.senox.realty.vo.AdvertisingCostVo;
import com.senox.realty.vo.AdvertisingIncomeVo;
import com.senox.realty.vo.ContractSuspendDto;
import com.senox.web.component.AdvertisingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/28 14:48
 */
@Service
@RequiredArgsConstructor
public class AdvertisingContractService {

    private final AdvertisingComponent advertisingComponent;

    /**
     * 添加广告合同
     * @param contract
     * @return
     */
    public Long addContract(AdvertisingContractEditVo contract) {
        return advertisingComponent.addContract(contract);
    }

    /**
     * 更新广告合同
     * @param contract
     */
    public void updateContract(AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return;
        }

        advertisingComponent.updateContract(contract);
    }

    /**
     * 更新已缴费广告合同
     * @param contract
     */
    public void updatePaidContract(AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return;
        }

        advertisingComponent.updatePaidContract(contract);
    }

    /**
     * 更新广告合同成本金额
     * @param cost
     */
    public void updateContractCost(AdvertisingCostVo cost) {
        if (!WrapperClassUtils.biggerThanLong(cost.getContractId(), 0L)) {
            return;
        }

        advertisingComponent.updateContractCost(cost);
    }

    /**
     * 停用广告合同
     * @param suspendDto
     */
    public void suspendContract(ContractSuspendDto suspendDto) {
        if (StringUtils.isBlank(suspendDto.getContractNo())) {
            return;
        }
        if (suspendDto.getSuspendDate() == null) {
            suspendDto.setSuspendDate(LocalDate.now());
        }

        advertisingComponent.suspendContract(suspendDto);
    }

    /**
     * 删除广告合同
     * @param id
     */
    public void deleteContract(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        advertisingComponent.deleteContract(id);
    }

    /**
     * 获取广告合同详情
     * @param id
     * @return
     */
    public AdvertisingContractVo findContractById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? advertisingComponent.findContractById(id) : null;
    }

    /**
     * 统计广告合同
     * @param search
     * @return
     */
    public int countContract(AdvertisingContractSearchVo search) {
        return advertisingComponent.countContract(search);
    }

    /**
     * 广告列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingContractListVo> listContractPage(AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return advertisingComponent.listContractPage(search);
    }

    /**
     * 广告收益列表
     * @param search
     * @return
     */
    public List<AdvertisingIncomeVo> listAdvertisingIncome(AdvertisingContractSearchVo search) {
        search.setWithPayoff(Boolean.TRUE);
        return advertisingComponent.listAdvertisingIncome(search);
    }

    /**
     * 广告收益合计
     * @param search
     * @return
     */
    public AdvertisingIncomeVo sumAdvertisingIncome(AdvertisingContractSearchVo search) {
        return advertisingComponent.sumAdvertisingIncome(search);
    }

    /**
     * 广告收益列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingIncomeVo> listAdvertisingIncomePage(AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return advertisingComponent.listAdvertisingIncomePage(search);
    }
}
