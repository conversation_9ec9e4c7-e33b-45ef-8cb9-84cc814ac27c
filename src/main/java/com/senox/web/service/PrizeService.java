package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.user.vo.PrizeRecordsSearchVo;
import com.senox.user.vo.PrizeRecordsVo;
import com.senox.user.vo.PrizeSearchVo;
import com.senox.user.vo.PrizeVo;
import com.senox.web.component.PrizeComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/15 15:39
 */
@Component
@RequiredArgsConstructor
public class PrizeService {

    private final PrizeComponent prizeComponent;

    /**
     * 添加抽奖奖品
     * @param prizeVo
     * @return
     */
    public Long addPrize(PrizeVo prizeVo) {
        return prizeComponent.addPrize(prizeVo);
    }

    /**
     * 更新抽奖奖品
     * @param prizeVo
     */
    public void updatePrize(PrizeVo prizeVo) {
        prizeComponent.updatePrize(prizeVo);
    }

    /**
     * 根据id获取抽奖奖品
     * @param id
     * @return
     */
    public PrizeVo findById(Long id) {
        return prizeComponent.findById(id);
    }

    /**
     * 根据id删除抽奖奖品
     * @param id
     */
    public void deleteById(Long id) {
        prizeComponent.deleteById(id);
    }

    /**
     * 抽奖奖品分页
     * @param searchVo
     * @return
     */
    public PageResult<PrizeVo> pagePrize(PrizeSearchVo searchVo) {
        return prizeComponent.pagePrize(searchVo);
    }

    /**
     * 根据id获取抽奖记录
     * @param id
     * @return
     */
    public PrizeRecordsVo findRecordsById(Long id) {
        return prizeComponent.findRecordsById(id);
    }

    /**
     * 抽奖记录分页
     * @param searchVo
     * @return
     */
    public PageResult<PrizeRecordsVo> pageRecords(PrizeRecordsSearchVo searchVo) {
        return prizeComponent.pageRecords(searchVo);
    }

    /**
     * 根据uuid兑奖
     * @param uuid
     */
    public void verifyPrize(String uuid) {
        prizeComponent.verifyPrize(uuid);
    }
}
