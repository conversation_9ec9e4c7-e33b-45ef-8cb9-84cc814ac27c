package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.vo.StreetVo;
import com.senox.web.component.RealtyDictionaryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 11:18
 */
@Service
public class StreetService {

    @Autowired
    private RealtyDictionaryComponent realtyDictionaryComponent;

    /**
     * 添加街道
     * @param street
     * @return
     */
    public Long addStreet(StreetVo street) {
        if (StringUtils.isBlank(street.getName())) {
            return 0L;
        }
        return realtyDictionaryComponent.addStreet(street);
    }

    /**
     * 更新街道
     * @param street
     */
    public void updateStreet(StreetVo street) {
        if (!WrapperClassUtils.biggerThanLong(street.getId(), 0L)) {
            return;
        }
        realtyDictionaryComponent.updateStreet(street);
    }

    /**
     * 删除街道
     * @param id
     */
    public void deleteStreet(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        StreetVo street = new StreetVo();
        street.setId(id);
        street.setDisabled(Boolean.TRUE);
        updateStreet(street);
    }

    /**
     * 根据id获取街道
     * @param id
     * @return
     */
    public StreetVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return realtyDictionaryComponent.findStreetById(id);
    }

    /**
     * 区域街道列表
     * @param regionId
     * @return
     */
    public List<StreetVo> listRegionStreet(Long regionId) {
        if (!WrapperClassUtils.biggerThanLong(regionId, 0L)) {
            return Collections.emptyList();
        }
        return realtyDictionaryComponent.listRegionStreet(regionId);
    }

    /**
     * 街道列表
     * @return
     */
    public List<StreetVo> listAll() {
        return realtyDictionaryComponent.listStreet();
    }
}
