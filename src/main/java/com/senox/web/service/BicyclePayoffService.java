package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.tms.vo.*;
import com.senox.web.component.BicyclePayoffComponent;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:55
 */
@Service
@RequiredArgsConstructor
public class BicyclePayoffService {

    private final BicyclePayoffComponent bicyclePayoffComponent;

    /**
     * 更新应付账单状态
     * @param billPaid
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return;
        }

        bicyclePayoffComponent.updatePayoffStatus(billPaid, payway);
    }

    /**
     * 更新应付金额
     *
     * @param id     应付id
     * @param amount 金额
     */
    public void updateAmountFromReport(Long id, BigDecimal amount) {
        if (!WrapperClassUtils.biggerThanLong(id,0)){
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        bicyclePayoffComponent.updateAmountFromReport(id, amount);
    }

    /**
     * 三轮车应付账单合计
     * @param search
     * @return
     */
    public BicyclePayoffVo sumPayoff(BicyclePayoffSearchVo search) {
        return bicyclePayoffComponent.sumPayoff(search);
    }

    /**
     * 三轮车应付账单页
     * @param search
     * @return
     */
    public PageResult<BicyclePayoffVo> listPayoff(BicyclePayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return bicyclePayoffComponent.listPayoff(search);
    }

    /**
     * 生成日报表
     *
     * @param dateVo 日期
     */
    public void generateReportByDay(BicycleDateVo dateVo){
        bicyclePayoffComponent.generateReportByDay(dateVo);
    }

    /**
     * 生成月报表
     * @param dateVo 日期
     */
    public void generateReportByMonth(BicycleDateVo dateVo){
        bicyclePayoffComponent.generateReportByMonth(dateVo);
    }

    /**
     * 日报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportDayList(BicyclePayoffReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new BicycleTotalPageResult<>(PageResult.emptyPage());
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicyclePayoffComponent.reportDayList(searchVo);
    }

    /**
     * 月报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportMonthList(BicyclePayoffReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new BicycleTotalPageResult<>(PageResult.emptyPage());
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicyclePayoffComponent.reportMonthList(searchVo);
    }
}
