package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.web.component.MaterialFlowComponent;
import com.senox.wms.constant.MaterialRequisitionStatus;
import com.senox.wms.vo.MaterialFlowInfoVo;
import com.senox.wms.vo.requisition.FlowRequest;
import com.senox.wms.vo.requisition.MaterialRequisitionSubmitVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-09-13
 **/
@RequiredArgsConstructor
@Service
public class MaterialFlowService {

    private final MaterialFlowComponent materialFlowComponent;

    /**
     * 提交流程
     *
     * @param submit 提交参数
     * @return 返回状态
     */
    public MaterialRequisitionStatus submitFlow(MaterialRequisitionSubmitVo submit) {
        return materialFlowComponent.submitFlow(submit);
    }

    /**
     * 获取流程
     *
     * @param flowRequest 流程请求参数
     * @return 返回流程信息
     */
    public MaterialFlowInfoVo getFlow(FlowRequest flowRequest) {
        if (!WrapperClassUtils.biggerThanLong(flowRequest.getInstanceId(), 0) && !WrapperClassUtils.biggerThanLong(flowRequest.getTaskId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return materialFlowComponent.getFlow(flowRequest);
    }
}
