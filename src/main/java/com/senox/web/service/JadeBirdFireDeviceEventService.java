package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.dm.vo.JadeBirdFireDeviceEventSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceEventVo;
import com.senox.tms.vo.BicycleTotalPageResult;
import com.senox.web.component.JadeBirdFireDeviceEventComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class JadeBirdFireDeviceEventService {
    private final JadeBirdFireDeviceEventComponent fireDeviceEventComponent;

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public List<JadeBirdFireDeviceEventVo> list(JadeBirdFireDeviceEventSearchVo search) {
        search.setPage(false);
        return fireDeviceEventComponent.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<JadeBirdFireDeviceEventVo> pageList(JadeBirdFireDeviceEventSearchVo search) {
        if (search.getPageSize() < 1) {
            return BicycleTotalPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return fireDeviceEventComponent.pageList(search);
    }
}
