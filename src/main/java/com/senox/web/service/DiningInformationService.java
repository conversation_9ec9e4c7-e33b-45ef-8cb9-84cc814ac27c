package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import com.senox.web.component.DiningInformationComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@RequiredArgsConstructor
@Service
public class DiningInformationService {

    private final DiningInformationComponent diningInformationComponent;

    /**
     * 批量添加报餐
     * @param diningInformationVoList
     * @return
     */
    public void addBatchDiningInformation(List<DiningInformationVo> diningInformationVoList) {
        if (CollectionUtils.isEmpty(diningInformationVoList)) {
            return ;
        }
         diningInformationComponent.addBatchDiningInformation(diningInformationVoList);
    }

    /**
     * 添加报餐记录
     * @param diningInformationVo
     * @return
     */
    public void addDiningInformation(DiningInformationVo diningInformationVo) {
        diningInformationComponent.addDiningInformation(diningInformationVo);
    }

    /**
     * 修改报餐数据
     * @param diningInformationVo
     */
    public void updateDiningInformation(DiningInformationVo diningInformationVo){
        diningInformationComponent.updateDiningInformation(diningInformationVo);
    }

    public DiningInformationVo getDiningInformation(Long id){
        return diningInformationComponent.getDiningInformation(id);
    }


    /**
     * 删除报餐数据
     * @param id
     */
    public void deleteDiningInformation(Long id){
        diningInformationComponent.deleteDiningInformation(id);
    }


    /**
     * 查询报餐数据
     * @param search
     * @return
     */
    public PageResult<DiningInformationVo> listDiningInformation(DiningInformationSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return diningInformationComponent.listDiningInformation(search);
    }
}
