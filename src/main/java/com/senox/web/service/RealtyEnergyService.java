package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyEnergyComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-5-12
 */
@Service
@RequiredArgsConstructor
public class RealtyEnergyService {
    private final RealtyEnergyComponent realtyEnergyComponent;

    /**
     * 计量点设备绑定尾页
     *
     * @param realtyBindPoint 绑定vo
     */
    public void meteringPointBindRealty(RealtyBindEnergyMeteringPointVo realtyBindPoint) {
        if (StringUtils.isBlank(realtyBindPoint.getPointCode())) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        realtyEnergyComponent.meteringPointBindRealty(realtyBindPoint);
    }

    /**
     * 物业计量点统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public Integer meteringPointCountList(RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyComponent.meteringPointCountList(search);
    }

    /**
     * 物业计量点列表
     *
     * @param search 查询参数
     * @return 物业计量点分页
     */
    public PageResult<RealtyToEnergyMeteringPointVo> meteringPoint(RealtyToEnergyMeteringPointSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyEnergyComponent.meteringPoint(search);
    }

    /**
     * 物业计量点表码列表
     *
     * @param search 查询参数
     * @return 物业计量点表码分页
     */
    public PageResult<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsList(RealtyToEnergyMeteringPointReadingsSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyEnergyComponent.meteringPointReadingsList(search);
    }

    /**
     * 同步计量点读数
     *
     * @param realtyWeBatch 参数
     */
    public void syncMeteringPoint(RealtyWeBatchVo realtyWeBatch) {
        if (null == realtyWeBatch
                || !WrapperClassUtils.biggerThanInt(realtyWeBatch.getYear(), 0)
                || !WrapperClassUtils.biggerThanInt(realtyWeBatch.getMonth(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        realtyEnergyComponent.syncMeteringPoint(realtyWeBatch);
    }

    /**
     * 自动绑定计量设备
     *
     * @param search 查询参数
     * @return 返回处理结果
     */
    public PointBindRealtyResult automaticMeteringPointBindRealty(RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyComponent.automaticMeteringPointBindRealty(search);
    }

    /**
     * 计量点作废
     * @param meteringPointCode 计量点编号
     */
    public void meteringPointCancel(String meteringPointCode) {
        if (StringUtils.isBlank(meteringPointCode)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        realtyEnergyComponent.meteringPointCancel(meteringPointCode);
    }

    /**
     * 计量点刷新
     * @param meteringPointCode 计量点编码
     * @return 返回刷新结果
     */
    public EnergyPointRefreshResult meteringPointRefresh(String meteringPointCode) {
        return realtyEnergyComponent.meteringPointRefresh(meteringPointCode);
    }
}
