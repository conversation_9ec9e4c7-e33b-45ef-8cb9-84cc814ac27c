package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.dm.vo.GateOperationDayReportVo;
import com.senox.dm.vo.GateOperationReportGenerateVo;
import com.senox.dm.vo.GateOperationReportSearchVo;
import com.senox.dm.vo.GateOperationReportSumVo;
import com.senox.web.component.AccessControlComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:09
 */
@Service
@RequiredArgsConstructor
public class GateOperationReportService {

    private final AccessControlComponent accessControlComponent;

    /**
     * 重新生成日报
     * @param generate
     */
    public void generateDayReport(GateOperationReportGenerateVo generate) {
        accessControlComponent.generateDayReport(generate);
    }

    /**
     * 日报合计
     * @param search
     * @return
     */
    public GateOperationReportSumVo sumDayReport(GateOperationReportSearchVo search) {
        return accessControlComponent.sumDayReport(search);
    }

    /**
     * 日报列表页
     * @param search
     * @return
     */
    public PageResult<GateOperationDayReportVo> listDayReportPage(GateOperationReportSearchVo search) {
        return accessControlComponent.listDayReportPage(search);
    }

    /**
     * 日报列表
     * @param search
     * @return
     */
    public List<GateOperationDayReportVo> listDayReport(GateOperationReportSearchVo search) {
        return accessControlComponent.listDayReport(search);
    }
}
