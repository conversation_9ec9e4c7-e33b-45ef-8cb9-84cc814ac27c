package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.WmsComponent;
import com.senox.wms.dto.KuaimaiPrintDto;
import com.senox.wms.dto.MaterialDictFixedAssetsScrapBillDto;
import com.senox.wms.dto.MaterialDictFixedAssetsTransferBillDto;
import com.senox.wms.dto.MaterialDto;
import com.senox.wms.vo.*;
import com.senox.wms.vo.dict.DictMaterialCodeVo;
import com.senox.wms.vo.dict.DictTypeVo;
import com.senox.wms.vo.dict.DictUnitSearchVo;
import com.senox.wms.vo.dict.DictUnitVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-1-10
 */
@Service
public class WmsService {
    private final WmsComponent wmsComponent;

    public WmsService(WmsComponent wmsComponent) {
        this.wmsComponent = wmsComponent;
    }

    /**
     * 新增物料编码
     *
     * @param materialCode 物料编码Vo
     */
    public void addMaterialCode(DictMaterialCodeVo materialCode) {
        wmsComponent.addMaterialCode(materialCode);
    }

    /**
     * 删除物料编码
     *
     * @param ids 物料编码数组
     */
    public void deleteMaterialCode(Long[] ids) {
        if (ids ==  null || ids.length < 1) {
            return;
        }
        wmsComponent.deleteMaterialCode(ids);
    }


    /**
     * 修改物料编码
     *
     * @param materialCode 物料编码vo
     */
    public void updateMaterialCode(DictMaterialCodeVo materialCode) {
        wmsComponent.updateMaterialCode(materialCode);
    }

    /**
     * 根据当前用户查询编码列表
     *
     * @param materialSearch 查询参数
     * @return PageResult<DictMaterialCodeVo>
     */
    public PageResult<DictMaterialCodeVo> getMaterialCode(DictMaterialSearchVo materialSearch) {
        if (materialSearch.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (materialSearch.getPageNo() < 1) {
            materialSearch.setPageNo(1);
        }
        return wmsComponent.getMaterialCode(materialSearch);
    }

    /**
     * 新增物料类型
     *
     * @param dictType 物料类型Vo
     */
    public void addDictType(DictTypeVo dictType) {
        wmsComponent.addDictType(dictType);
    }

    /**
     * 删除物料类型
     *
     * @param ids 物料类型id数组
     */
    public void deleteDictType(Long[] ids) {
        if (ids == null || ids.length < 1) {
            return;
        }
        wmsComponent.deleteDictType(ids);
    }

    /**
     * 更新物料类型
     *
     * @param dictType 物料类型
     */
    public void updateDictType(DictTypeVo dictType) {
        wmsComponent.updateDictType(dictType);
    }

    /**
     * 获取当前用户物料类型列表
     *
     * @param warehouseId 仓id
     * @return List<DictType>
     */
    public List<DictTypeVo> listDictTypeAndWarehouse(Long warehouseId) {
        return wmsComponent.listDictTypeAndWarehouse(warehouseId);
    }

    /**
     * 新增单位
     *
     * @param unit 单位
     */
    public void addDictUnit(DictUnitVo unit) {
        wmsComponent.addDictUnit(unit);
    }

    /**
     * 删除单位
     *
     * @param unitIds 单位id数组
     */
    public void deleteDictUnit(Long[] unitIds) {
        if (unitIds == null || unitIds.length < 1) {
            return;
        }
        wmsComponent.deleteDictUnit(unitIds);
    }


    /**
     * 修改单位
     *
     * @param unit 单位
     */
    public void updateDictUnit(DictUnitVo unit) {
        wmsComponent.updateDictUnit(unit);
    }

    /**
     * 获取单位列表
     *
     * @param unitSearch 单位查询参数
     * @return PageResult<DictUnitVo>
     */
    public PageResult<DictUnitVo> getDictUnit(DictUnitSearchVo unitSearch) {
        if (unitSearch.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (unitSearch.getPageNo() < 1) {
            unitSearch.setPageNo(1);
        }
        return wmsComponent.getDictUnit(unitSearch);
    }

    /**
     * 获取对应用户仓列表
     *
     * @return Result<List < WarehouseTreeNode>>
     */
    public List<WarehouseTreeNode> warehouseUserList() {
        return wmsComponent.warehouseUserList();
    }

    /**
     * 根据角色id查询该角色对仓的权限列表
     *
     * @param roleId 用户id
     * @return List<WarehouseTreeNode>
     */
    public List<WarehouseTreeNode> roleWarehouseList(Long roleId) {
        if (!WrapperClassUtils.biggerThanLong(roleId, 0L)) {
            throw new BusinessException("角色id无效");
        }
        return wmsComponent.roleWarehouseList(roleId);
    }

    /**
     * 更新角色仓权限
     *
     * @param roleId        角色id
     * @param warehouseList 仓id列表
     */
    public void warehouseRoleAdd(Long roleId, List<Long> warehouseList) {
        if (!WrapperClassUtils.biggerThanLong(roleId, 0L)) {
            throw new BusinessException("角色id无效");
        }
        wmsComponent.warehouseRoleAdd(roleId, warehouseList);
    }


    /**
     * 创建入仓单
     *
     * @param form 表单
     * @return List<MaterialCodeFixedCheckBillVo>
     */
    public EnterWarehouseCollectVo addEnterBill(MaterialEnterBillFormVo form) {
        return wmsComponent.addEnterBill(form);
    }

    /**
     * 获取入仓单列表
     *
     * @param search 入仓单查询参数
     * @return PageResult<MaterialEnterBillVo>
     */
    public PageResult<MaterialEnterBillVo> enterBillList(MaterialEnterBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.enterBillList(search);
    }

    /**
     * 入仓单号获取物料记录
     *
     * @param bill 入仓单号
     * @return Result<List < MaterialVo>>
     */
    public List<MaterialVo> materialListByEnterBill(Long bill) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("入仓单号无效");
        }
        return wmsComponent.materialListByEnterBill(bill);
    }

    /**
     * 根据入仓单号返回入仓单集
     *
     * @param bill 单号
     * @return EnterWarehouseCollectVo
     */
    public EnterWarehouseCollectVo getMaterialEnterBill(Long bill,Boolean merge) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        return wmsComponent.getMaterialEnterBill(bill, merge);
    }

    /**
     * 入仓单数据列表
     *
     * @param search 查询参数
     */
    public EnterWarehouseCollectVo enterDataList(MaterialEnterBillSearchVo search) {
        return wmsComponent.enterDataList(search);
    }


    /**
     * 根据单号撤回入仓
     *
     * @param bill 单号
     */
    public void enterBillCancel(Long bill) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        wmsComponent.enterBillCancel(bill);
    }


    /**
     * 删除入仓单中的物料
     *
     * @param bill        单号
     * @param materialIds 物料集
     */
    public void enterBillRecordDelete(Long bill, Long[] materialIds) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (ObjectUtils.isEmpty(materialIds)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.enterBillRecordDelete(bill, materialIds);

    }

    /**
     * 入仓单追加物料
     *
     * @param bill         单号
     * @param materialList 物料集
     */
    public void enterBillRecordAdded(Long bill, List<MaterialVo> materialList) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (CollectionUtils.isEmpty(materialList)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.enterBillRecordAdded(bill, materialList);
    }

    /**
     * 修改入仓单的物料
     *
     * @param bill        单号
     * @param materialDto 物料dto
     */
    public void enterBillRecordUpdate(Long bill, MaterialDto materialDto) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (Objects.isNull(materialDto)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.enterBillRecordUpdate(bill, materialDto);

    }

    /**
     * 入仓记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialEnterRecordInfoVo>
     */
    public PageResult<MaterialEnterRecordInfoVo> enterRecordList(MaterialEnterRecordSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.enterRecordList(search);

    }


    /**
     * 创建出仓单
     *
     * @param form 表单
     * @return MaterialOutConfirmFormVo
     */
    public MaterialOutConfirmFormVo createOutBill(MaterialOutRecordFormVo form) {
        return wmsComponent.createOutBill(form);
    }

    /**
     * 确认出仓
     *
     * @param form 表单
     * @return OutWarehouseCollectVo
     */
    public OutWarehouseCollectVo confirmOutBill(MaterialOutConfirmFormVo form) {
        return wmsComponent.confirmOutBill(form);
    }

    /**
     * 获取出仓单列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialOutBillVo>
     */
    public PageResult<MaterialOutBillVo> billOutList(MaterialOutBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.billOutList(search);
    }


    /**
     * 根据出仓单号查询出仓记录
     *
     * @param bill 单号
     * @return List<MaterialOutRecordVo>
     */
    public List<MaterialOutRecordVo> recordListByBill(Long bill) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("出仓单号无效");
        }
        return wmsComponent.recordListByBill(bill);
    }

    /**
     * 根据出仓单号返回出仓单
     *
     * @param bill 单号
     * @param merge 合并
     * @return OutWarehouseCollectVo
     */
    public OutWarehouseCollectVo getOutBill(Long bill,Boolean merge) {
        if (!WrapperClassUtils.biggerThanLong(bill, 0L)) {
            throw new BusinessException("单号无效");
        }
        return wmsComponent.getOutBill(bill,merge);
    }

    /**
     * 出仓单数据列表
     *
     * @param search 查询参数
     */
    public OutWarehouseCollectVo outDataList(MaterialOutBillSearchVo search) {
        return wmsComponent.outDataList(search);
    }

    /**
     * 撤销出仓
     *
     * @param billId 单号
     */
    public void outBillCancel(Long billId) {
        wmsComponent.outBillCancel(billId);
    }


    /**
     * 删除出仓单中的物料
     *
     * @param billId               单号
     * @param materialOutRecordIds 出仓记录id集
     */
    public void deleteBillOutRecord(Long billId, Long[] materialOutRecordIds) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (ObjectUtils.isEmpty(materialOutRecordIds)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.deleteBillOutRecord(billId, materialOutRecordIds);
    }

    /**
     * 更新出仓物料数量
     *
     * @param billId      单号
     * @param outRecordId 出仓记录id
     * @param number      数量
     */
    public void updateBillOutRecord(Long billId, Long outRecordId, Long number) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (!WrapperClassUtils.biggerThanLong(outRecordId, 0L)) {
            throw new BusinessException("出仓记录无效");
        }
        if (!WrapperClassUtils.biggerThanLong(number, 0L)) {
            throw new BusinessException("数量无效");
        }
        wmsComponent.updateBillOutRecord(billId, outRecordId, number);
    }

    /**
     * 出仓单追加物料
     *
     * @param billId                单号
     * @param materialOutRecordList 出仓id集
     */
    public void outBillRecordAdded(Long billId, List<MaterialOutRecordVo> materialOutRecordList) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new BusinessException("单号无效");
        }
        if (CollectionUtils.isEmpty(materialOutRecordList)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.outBillRecordAdded(billId, materialOutRecordList);

    }

    /**
     * 出仓记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialOutRecordInfoVo>
     */
    public MaterialOutRecordPageResult<MaterialOutRecordInfoVo> outRecordList(MaterialOutRecordSearchVo search) {
        if (search.getPageSize() < 1) {
            return new MaterialOutRecordPageResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.outRecordList(search);
    }

    /**
     * 物料盘点
     *
     * @param search 查询参数
     * @return PageResult<MaterialVo>
     */
    public MaterialPageResult<MaterialVo> materialInventoryList(MaterialInventorySearchVo search) {
        if (search.getPageSize() < 1) {
            return new MaterialPageResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.materialInventoryList(search);
    }


    /**
     * 物料编码物料库存列表
     *
     * @param search 查询参数
     * @return PageResult<DictMaterialCodeVo>
     */
    public PageResult<DictMaterialCodeVo> materialCodeStockList(DictMaterialSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.materialCodeStockList(search);
    }


    /**
     * 创建调拨单
     *
     * @param materialAllocationSingleVo 物料调拨单
     */
    public void materialAllocationAdd(MaterialAllocationSingleVo materialAllocationSingleVo) {
        wmsComponent.materialAllocationAdd(materialAllocationSingleVo);
    }

    /**
     * 调拨单列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialAllocationSingleVo>
     */
    public PageResult<MaterialAllocationSingleVo> materialAllocationSingleList(MaterialAllocationSingleSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.materialAllocationSingleList(search);
    }

    /**
     * 审核调拨单
     *
     * @param id    id
     * @param state 状态
     */
    public void materialAllocationExamine(Long id, Integer state) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new BusinessException("调拨单id无效");
        }
        wmsComponent.materialAllocationExamine(id, state);
    }

    /**
     * 根据id获取单个固定资产使用记录
     *
     * @param fixedAssetsUseId 固定资产使用记录id
     * @return MaterialFixedAssetsUseVo
     */
    public MaterialFixedAssetsUseVo getFixedAssetsUse(Long fixedAssetsUseId) {
        if (!WrapperClassUtils.biggerThanLong(fixedAssetsUseId, 0L)) {
            throw new BusinessException("使用记录id无效");
        }
        return wmsComponent.getFixedAssetsUse(fixedAssetsUseId);
    }

    /**
     * 固定资产使用记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialFixedAssetsUseVo>
     */
    public PageResult<MaterialFixedAssetsUseVo> assetsUseList(MaterialFixedAssetsUseSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.assetsUseList(search);

    }

    /**
     * 固定资产报废
     *
     * @param materialDictFixedAssetsScrapBillDto 固定资产报废单
     * @return MaterialDictFixedAssetsScrapBillVo
     */
    public MaterialDictFixedAssetsScrapBillVo assetsScrapAdd(MaterialDictFixedAssetsScrapBillDto materialDictFixedAssetsScrapBillDto) {
        return wmsComponent.assetsScrapAdd(materialDictFixedAssetsScrapBillDto);
    }

    /**
     * 固定资产报废列表
     *
     * @return List<MaterialDictFixedAssetsScrapBillVo>
     */
    public List<MaterialDictFixedAssetsScrapBillVo> assetsScrapList() {
        return wmsComponent.assetsScrapList();
    }

    /**
     * 创建固定资产转移单
     *
     * @param transferBillDtoList 固定资产转移单集
     * @return 返回转移单集
     */
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferAdd(List<MaterialDictFixedAssetsTransferBillDto> transferBillDtoList) {
        return wmsComponent.assetsTransferAdd(transferBillDtoList);
    }

    /**
     * 固定资产转移列表
     *
     * @return List<MaterialDictFixedAssetsTransferBillVo>
     */
    public List<MaterialDictFixedAssetsTransferBillVo> assetsTransferList() {
        return wmsComponent.assetsTransferList();
    }

    /**
     * 生成仓报表
     */
    public void createWarehouseReport(Integer year, Integer month, Integer reportType) {
        wmsComponent.createWarehouseReport(year, month, reportType);
    }

    /**
     * 获取仓报表记录
     *
     * @return List<WarehouseReportRecordVo>
     */
    public List<WarehouseReportRecordVo> reportRecordList() {
        return wmsComponent.reportRecordList();
    }

    /**
     * 获取仓报表记录文件
     *
     * @param time 日期
     * @return List<WarehouseReportFileVo>
     */
    public List<WarehouseReportFileVo> reportFileList(String time) {
        if (Objects.isNull(time)) {
            throw new BusinessException("日期无效");
        }
        return wmsComponent.reportFileList(time);
    }


    /**
     * 打印物料
     *
     * @param kuaimaiPrintDtos 打印dto
     * @param sn               设备sn
     */
    public void kuaimaiPrint(String sn, List<KuaimaiPrintDto> kuaimaiPrintDtos) {
        if (CollectionUtils.isEmpty(kuaimaiPrintDtos)) {
            throw new BusinessException("物料编码无效");
        }
        if (StringUtils.isBlank(sn)) {
            throw new BusinessException("参数无效");
        }
        wmsComponent.kuaimaiPrint(sn, kuaimaiPrintDtos);
    }

    /**
     * 打印机列表
     *
     * @return List<KuaimaiPrintVo>
     */
    public List<KuaimaiPrintVo> kuaimaiPrintList() {
        return wmsComponent.kuaimaiPrintList();
    }

    /**
     * 盘点单记录列表
     *
     * @param search 查询参数
     * @return PageResult<MaterialInventoryBillRecordVo>
     */
    public PageResult<MaterialInventoryBillRecordVo> materialInventoryRecordList(@RequestBody MaterialInventoryBillRecordSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return wmsComponent.materialInventoryRecordList(search);
    }

    /**
     * 快速盘点
     *
     * @param search 查询参数
     */
    public void recordQuick(@RequestBody MaterialInventoryBillRecordSearchVo search) {
        if (!WrapperClassUtils.biggerThanInt(search.getYear(), 0) || !WrapperClassUtils.biggerThanInt(search.getMonth(), 0)) {
            throw new BusinessException("参数无效");
        }
        wmsComponent.recordQuick(search);
    }

    /**
     * 物料绑定gtin
     *
     * @param materialCode 物料编码
     * @param gtin         gtin
     */
    public void materialBindGtin(Long materialCode, String gtin) {
        if (!WrapperClassUtils.biggerThanLong(materialCode, 0) || StringUtils.isBlank(gtin)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.materialBindGtin(materialCode, gtin);
    }

    /**
     * 物料出仓记录打印
     *
     * @param outBill 出仓单
     * @param merge 合并
     */
    public void materialOutRecordPrint(Long outBill,Boolean merge) {
        if (!WrapperClassUtils.biggerThanLong(outBill, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        wmsComponent.materialOutRecordPrint(outBill,merge);
    }

}
