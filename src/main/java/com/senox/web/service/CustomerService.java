package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import com.senox.web.component.CustomerComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/15 11:59
 */
@Service
public class CustomerService {

    @Autowired
    private CustomerComponent customerComponent;

    /**
     * 添加客户
     * @param customer
     * @return
     */
    public Long addCustomer(CustomerVo customer) {
        if (StringUtils.isBlank(customer.getName())) {
            return 0L;
        }
        return customerComponent.addCustomer(customer);
    }

    /**
     * 更新客户
     * @param customer
     */
    public void updateCustomer(CustomerVo customer) {
        if (!WrapperClassUtils.biggerThanLong(customer.getId(), 0L)) {
            return;
        }
        customerComponent.updateCustomer(customer);
    }

    /**
     * 删除客户
     * @param id
     */
    public void deleteCustomer(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        customerComponent.deleteCustomer(id);
    }

    /**
     * 根据id获取客户
     * @param id
     * @return
     */
    public CustomerVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return customerComponent.findById(id);
    }

    /**
     * 根据id获取客户信息
     * @param idcard
     * @return
     */
    public CustomerVo findByIdcard(String idcard) {
        if (StringUtils.isBlank(idcard)) {
            return null;
        }
        return customerComponent.findByIdcard(idcard);
    }

    /**
     * 客户列表
     * @param search
     * @return
     */
    public PageResult<CustomerVo> listCustomerPage(CustomerSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return customerComponent.listCustomerPage(search);
    }

    /**
     * 客户列表（五分夜）
     * @param searchVo
     * @return
     */
    public List<CustomerVo> listCustomer(CustomerSearchVo searchVo) {
        return customerComponent.listCustomer(searchVo);
    }
}
