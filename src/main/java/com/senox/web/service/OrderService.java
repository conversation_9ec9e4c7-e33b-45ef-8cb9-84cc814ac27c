package com.senox.web.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderType;
import com.senox.pm.vo.*;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.MaintainChargePage;
import com.senox.web.vo.RefundOrderRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/18 11:22
 */
@Service
@Slf4j
public class OrderService {

    @Autowired
    private OrderComponent orderComponent;

    /**
     * 更新订单票据号
     * @param serial
     */
    public void updateOrderSerial(OrderSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getOrderId(), 0L)
                || !WrapperClassUtils.biggerThanLong(serial.getProductId(), 0L)
                || StringUtils.isBlank(serial.getBillSerial())) {
            return;
        }
        orderComponent.updateOrderSerial(serial);
    }

    /**
     * 查询订单状态
     * @param tradeNo
     * @param realTime
     * @return
     */
    public OrderResultVo queryOrderStatus(String tradeNo, Boolean realTime) {
        if (StringUtils.isBlank(tradeNo)) {
            return null;
        }

        return orderComponent.queryOrderStatus(tradeNo, realTime);
    }

    /**
     * 退费订单
     * @param refundOrder
     * @return
     */
    public OrderResultVo refundOrder(RefundOrderRequestVo refundOrder) {
        lockRefundOrder(refundOrder.getTradeNo());
        refundOrder.setRefundAmount(DecimalUtils.subtract(BigDecimal.ZERO, refundOrder.getRefundAmount()));
        OrderResultVo result = null;
        try {
            OrderVo order = newPayOrder(refundOrder);
            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("退费下单失败");
            }
            log.info("订单退费成功，返回 {}", JsonUtils.object2Json(result));
        } finally {
            removeRefundOrderLock(refundOrder.getTradeNo());
        }
        return result;
    }

    /**
     * 构建支付订单
     * @param refundOrder
     * @return
     */
    private OrderVo newPayOrder(RefundOrderRequestVo refundOrder) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.OTHER);
        result.setPayWay(refundOrder.getPayWay());
        result.setCreateIp(refundOrder.getRequestIp());
        result.setRefundOrderTradeNo(refundOrder.getTradeNo());
        result.setTitle(refundOrder.getTitle());
        result.setRemark(refundOrder.getRemark());
        result.setItems(Collections.singletonList(newPayOrderItem(refundOrder)));
        return result;
    }

    /**
     * 构建支付订单明细
     * @param refundOrder
     * @return
     */
    private OrderItemVo newPayOrderItem(RefundOrderRequestVo refundOrder) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(1L);
        result.setProductName(refundOrder.getTitle());
        result.setQuantity(1);
        result.setPrice(refundOrder.getRefundAmount());
        result.setTotalAmount(refundOrder.getRefundAmount());
        return result;
    }

    /**
     * 获取订单明细
     * @param orderId
     * @return
     */
    public OrderVo findOrderWithDetail(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return null;
        }
        return orderComponent.findWithDetail(orderId);
    }

    /**
     * 获取订单项目明细
     * @param orderId
     * @param productId
     * @return
     */
    public OrderProductDetailVo getOrderProduct(Long orderId, Long productId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)
                || !WrapperClassUtils.biggerThanLong(productId, 0L)) {
            return null;
        }
        return orderComponent.getOrderProduct(orderId, productId);
    }

    /**
     * 账单费用明细
     * @param search
     * @return
     */
    public List<OrderItemDetailVo> listOrderItemDetail(OrderItemDetailSearchVo search) {
        if (!WrapperClassUtils.biggerThanLong(search.getBillId(), 0L)
                || OrderType.fromValue(search.getOrderType()) == null) {
            return Collections.emptyList();
        }
        return orderComponent.listOrderItemDetail(search);
    }

    /**
     * 订单列表
     * @param search
     * @return
     */
    public OrderPageResult<OrderListVo> listOrderPage(OrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return OrderPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listOrderPage(search);
    }

    /**
     * 收款明细列表
     * @param search
     * @return
     */
    public OrderPageResult<OrderTollVo> listOrderTollPage(OrderTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return OrderPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listOrderTollPage(search);
    }

    /**
     * 收款费项明细
     * @param search
     * @return
     */
    public PageResult<OrderTollDetailVo> listOrderTollDetail(OrderTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return OrderPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listOrderTollDetail(search);
    }

    /**
     * 冷藏缴费合计
     * @param search
     * @return
     */
    public RefrigerationTollVo sumRefrigerationToll(RefrigerationTollSearchVo search) {
        return orderComponent.sumRefrigerationToll(search);
    }

    /**
     * 冷藏缴费列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationTollVo> listRefrigerationToll(RefrigerationTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return OrderPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listRefrigerationToll(search);
    }

    /**
     * 物维缴费合计
     * @param search
     * @return
     */
    public MaintainChargeTollVo sumMaintainChargeToll(MaintainChargeTollSearchVo search) {
        return orderComponent.sumMaintainChargeToll(search);
    }

    /**
     * 物维缴费列表
     * @param search
     * @return
     */
    public PageResult<MaintainChargeTollVo> listMaintainChargeToll(MaintainChargeTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listMaintainChargeToll(search);
    }

    /**
     * 押金流水合计
     * @param search
     * @return
     */
    public DepositTollVo sumDepositToll(DepositTollSearchVo search) {
        return orderComponent.sumDepositToll(search);
    }

    /**
     * 押金流水列表
     * @param search
     * @return
     */
    public List<DepositTollVo> listDepositToll(DepositTollSearchVo search) {
        return orderComponent.listDepositToll(search);
    }

    /**
     * 押金流水列表页
     * @param search
     * @return
     */
    public PageResult<DepositTollVo> listDepositTollPage(DepositTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return orderComponent.listDepositTollPage(search);
    }

    /**
     * 广告结算账单合计
     * @param search
     * @return
     */
    public AdvertisingTollVo sumAdvertisingToll(AdvertisingTollSearchVo search) {
        return orderComponent.sumAdvertisingToll(search);
    }

    /**
     * 广告结算账单列表
     * @param search
     * @return
     */
    public PageResult<AdvertisingTollVo> listAdvertisingToll(AdvertisingTollSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePage.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return orderComponent.listAdvertisingToll(search);
    }

    /**
     * 根据id获取物维账单
     * @param id
     * @return
     */
    public MaintainChargeTollVo maintainChargeTollById(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return orderComponent.maintainChargeTollById(id);
    }

    /**
     * 退费上锁
     *
     * @param tradeNo
     */
    protected void lockRefundOrder(String tradeNo) {
        if (!RedisUtils.lock(buildOrderRefundLockKey(tradeNo), SenoxConst.Cache.TTL_10M)) {
            throw new BusinessException("退费操作太频繁，请稍后尝试");
        }
    }

    /**
     * 移除退费锁
     *
     * @param tradeNo
     */
    protected void removeRefundOrderLock(String tradeNo) {
        RedisUtils.del(buildOrderRefundLockKey(tradeNo));
    }

    /**
     * 退费分布式锁
     *
     * @param tradeNo
     * @return
     */
    private String buildOrderRefundLockKey(String tradeNo) {
        return String.format(SenoxConst.Cache.KEY_ORDER_REFUND, tradeNo);
    }
}
