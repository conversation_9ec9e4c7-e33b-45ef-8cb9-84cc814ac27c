package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.AdminUserVo;
import com.senox.web.component.WmsMaterialProcureOrderComponent;
import com.senox.wms.vo.EnterWarehouseCollectVo;
import com.senox.wms.vo.requisition.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-12
 **/
@RequiredArgsConstructor
@Service
public class WmsMaterialProcureOrderService {
    private final WmsMaterialProcureOrderComponent materialProcureOrderComponent;

    /**
     * 保存采购订单明细
     *
     * @param procureOrderId        采购订单id
     * @param procureOrderGoodsList 采购订单商品集
     */
    public void saveGoods(Long procureOrderId, List<MaterialProcureOrderGoodsVo> procureOrderGoodsList) {
        if (!WrapperClassUtils.biggerThanLong(procureOrderId, 0) || CollectionUtils.isEmpty(procureOrderGoodsList)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        materialProcureOrderComponent.saveGoods(procureOrderId, procureOrderGoodsList);
    }

    /**
     * 根据id查找采购订单
     *
     * @param procureOrderId 采购订单id
     * @return 返回查找到的采购订单
     */
    public MaterialProcureOrderVo findById(Long procureOrderId) {
        if (!WrapperClassUtils.biggerThanLong(procureOrderId, 0)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        return materialProcureOrderComponent.findById(procureOrderId);
    }

    /**
     * 根据流程id查找采购订单
     *
     * @param flowId 申购id
     * @return 返回查找到的采购订单
     */
    public MaterialProcureOrderVo findByFlowId(Long flowId) {
        if (!WrapperClassUtils.biggerThanLong(flowId, 0)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        return materialProcureOrderComponent.findByFlowId(flowId);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<MaterialProcureOrderVo> list(MaterialProcureOrderSearchVo search) {
        return materialProcureOrderComponent.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialProcureOrderVo> pageList(MaterialProcureOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return materialProcureOrderComponent.pageList(search);
    }

    /**
     * 采购商品分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<MaterialProcureOrderGoodsVo> goodsPageList(MaterialProcureOrderGoodsSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return materialProcureOrderComponent.goodsPageList(search);
    }

    /**
     * 采购人列表
     * @return 返回采购人列表
     */
    public List<AdminUserVo> purchaserList() {
        return materialProcureOrderComponent.purchaserList();
    }

    /**
     * 确认商品并提交
     * @param procureOrderForm 采购单表单集
     */
    public EnterWarehouseCollectVo confirmGoodsAndSubmit(MaterialProcureOrderFormVo procureOrderForm) {
        return materialProcureOrderComponent.confirmGoodsAndSubmit(procureOrderForm);
    }
}
