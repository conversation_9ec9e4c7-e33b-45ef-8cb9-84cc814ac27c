package com.senox.web.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyReceiptComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-4-3
 */
@Service
@RequiredArgsConstructor
public class RealtyReceiptService {
    private final RealtyReceiptComponent realtyReceiptComponent;

    /**
     * 物业发票申请
     *
     * @param receiptManger 物业发票管理
     */
    public void apply(RealtyReceiptMangerVo receiptManger) {
        if (null == receiptManger) {
            return;
        }
        if (StringUtils.isBlank(receiptManger.getOpenid())) {
            receiptManger.setOpenid(StringUtils.EMPTY);
        }
        realtyReceiptComponent.apply(receiptManger);
    }

    /**
     * 物业发票申请列表
     *
     * @param search 查询参数
     * @return 分页申请列表
     */
    public PageResult<RealtyReceiptVo> applyList(RealtyReceiptSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyReceiptComponent.applyList(search);
    }

    /**
     * 物业发票申请账单信息列表
     *
     * @param id 物业发票申请id
     * @return 申请账单信息列表
     */
    public List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return Collections.emptyList();
        }
        return realtyReceiptComponent.applyBillInfoList(id);
    }


    /**
     * 发票申请列表
     *
     * @param id     物业发票申请id
     * @param detail 是否详细
     * @return 发票申请列表
     */
    public List<ReceiptApplyVo> applyInfoList(Long id, Boolean detail) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return Collections.emptyList();
        }
        return realtyReceiptComponent.applyInfoList(id, detail);
    }

    /**
     * 物业发票申请审核
     *
     * @param receiptApplyAudit     物业发票审核
     */
    public void applyAudit(ReceiptApplyAuditVo receiptApplyAudit) {
        if (!WrapperClassUtils.biggerThanLong(receiptApplyAudit.getId(), 0)) {
            throw new BusinessException("物业发票申请id不合法");
        }
        realtyReceiptComponent.applyAudit(receiptApplyAudit);

    }

}
