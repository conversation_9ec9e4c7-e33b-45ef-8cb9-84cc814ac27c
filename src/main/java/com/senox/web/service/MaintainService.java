package com.senox.web.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.realty.vo.*;
import com.senox.web.component.MaintainComponent;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/3/31 8:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaintainService extends BillService{

    private final MaintainComponent maintainComponent;

    private final OrderComponent orderComponent;


    /**
     * 添加维修单
     *
     * @param maintainOrderVo
     */
    public void addMaintainOrder(MaintainOrderVo maintainOrderVo) {
        maintainComponent.addMaintainOrder(maintainOrderVo);
    }

    /**
     * 修改维修单
     *
     * @param maintainOrderVo
     */
    public void updateMaintainOrder(MaintainOrderVo maintainOrderVo) {
        maintainComponent.updateMaintainOrder(maintainOrderVo);
    }

    /**
     * 查询维修单
     *
     * @param id
     * @return
     */
    public MaintainOrderVo findMaintainOrder(Long id, Boolean media) {
        return maintainComponent.findMaintainOrder(id, media);
    }

    /**
     * 维修单列表
     *
     * @param search
     * @return
     */
    public PageResult<MaintainOrderVo> listMaintainOrder(MaintainOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search,
                () -> maintainComponent.countMaintainOrder(search),
                () -> maintainComponent.exportListMaintainOrder(search));
    }

    /**
     * 删除维修单
     * @param id
     */
    public void deleteMaintainOrder(Long id) {
        maintainComponent.deleteMaintainOrder(id);
    }

    /**
     * 维修单列表导出所有处理节点
     * @param search
     * @return
     */
    public List<MaintainOrderVo> exportListMaintainOrder(MaintainOrderSearchVo search) {
        return maintainComponent.exportListMaintainOrder(search);
    }

    /**
     * 维修单费用统计分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderStatisticVo> pageOrderStatistic(MaintainOrderSearchVo searchVo) {
        return maintainComponent.pageOrderStatistic(searchVo);
    }

    /**
     * 维修单费用合计
     * @param searchVo
     * @return
     */
    public MaintainOrderStatisticVo sumOrderStatistic(MaintainOrderSearchVo searchVo) {
        return maintainComponent.sumOrderStatistic(searchVo);
    }

    /**
     * 维修单评价
     * @param evaluateVo
     */
    public void evaluateOrder(MaintainOrderEvaluateVo evaluateVo) {
        maintainComponent.evaluateOrder(evaluateVo);
    }

    /**
     * 重置维修单评价
     * @param orderId
     */
    public void resetEvaluate(Long orderId) {
        maintainComponent.resetEvaluate(orderId);
    }

    /**
     * 维修单评价分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderVo> evaluateOrderPage(MaintainOrderEvaluateSearchVo searchVo) {
        return maintainComponent.evaluateOrderPage(searchVo);
    }

    /**
     * 添加派工单
     *
     * @param maintainJobVo
     */
    public void addMaintainJob(MaintainJobVo maintainJobVo) {
        maintainComponent.addMaintainJob(maintainJobVo);
    }

    /**
     * 修改派工单
     * @param maintainJobVo
     */
    public void updateMaintainJob(MaintainJobVo maintainJobVo) {
        maintainComponent.updateMaintainJob(maintainJobVo);
    }

    /**
     * 修改派工人员信息
     *
     * @param maintainJobItemVo
     */
    public void updateMaintainJobItem(MaintainJobItemVo maintainJobItemVo) {
        maintainComponent.updateMaintainJobItem(maintainJobItemVo);
    }

    /**
     * 获取派工单
     *
     * @param id
     * @return
     */
    public MaintainJobVo findMaintainJob(Long id) {
        return maintainComponent.findMaintainJob(id);
    }

    /**
     * 查询派工单
     *
     * @param orderId
     * @return
     */
    public List<MaintainJobVo> listDispatchJobByOrderId(Long orderId) {
        return maintainComponent.listDispatchJobByOrderId(orderId);
    }

    /**
     * 查询派工单列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<MaintainDispatchJobVo> listDispatchJob(MaintainJobSearchVo searchVo) {
        return maintainComponent.listDispatchJob(searchVo);
    }

    /**
     * 根据派工子单查询
     * @param itemId
     * @return
     */
    public MaintainDispatchJobVo findDispatchByJobItemId(Long itemId) {
        return maintainComponent.findDispatchByJobItemId(itemId);
    }

    /**
     * 删除派工单
     * @param jobId
     */
    public void deleteMaintainJob(Long jobId) {
        maintainComponent.deleteMaintainJob(jobId);
    }

    /**
     * 添加维修所需物料
     *
     * @param materialVo
     */
    public void saveMaintainMaterial(MaintainMaterialVo materialVo) {
        maintainComponent.saveMaintainMaterial(materialVo);
    }


    /**
     * 删除维修物料
     *
     * @param id
     */
    public void deleteMaintainMaterial(Long id) {
        maintainComponent.deleteMaintainMaterial(id);
    }

    /**
     * 批量删除物料及明细
     * @param ids
     */
    public void batchDeleteMaterial(List<Long> ids) {
        maintainComponent.batchDeleteMaterial(ids);
    }

    /**
     * 物料列表
     *
     * @param search
     * @return
     */
    public PageResult<MaintainMaterialDataVo> listMaintainMaterial(MaintainMaterialSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return maintainComponent.listMaintainMaterial(search);
    }

    /**
     * 更新出库单号
     *
     * @param outNoVo
     */
    public void saveOutNo(MaintainMaterialOutNoVo outNoVo) {
        maintainComponent.saveOutNo(outNoVo);
    }

    /**
     * 撤销出库
     *
     * @param outNo
     */
    public void cancelOutBound(String outNo) {
        maintainComponent.cancelOutBound(outNo);
    }

    /**
     * 获取维修所需物料
     *
     * @param orderId
     * @param jobId
     * @return
     */
    public List<MaintainMaterialItemVo> listMaterialByOrderIdAndJobId(Long orderId, Long jobId) {
        return maintainComponent.listMaterialByOrderIdAndJobId(orderId, jobId);
    }

    /**
     * 更新维修物料单
     * @param materialVo
     */
    public void updateMaintainMaterial(MaintainMaterialVo materialVo) {
        maintainComponent.updateMaintainMaterial(materialVo);
    }

    /**
     * 批量添加维修所需物料
     * @param materialItemVos
     */
    public void batchSaveMaterial(List<MaintainMaterialItemVo> materialItemVos) {
        maintainComponent.batchSaveMaterial(materialItemVos);
    }

    /**
     * 添加维修收费帐单
     *
     * @param chargeVo
     */
    public void addMaintainCharge(MaintainChargeVo chargeVo) {
        if (CollectionUtils.isEmpty(chargeVo.getChargeItemVos())) {
            throw new BusinessException("维修收费账单不能为空");
        }
        maintainComponent.addMaintainCharge(chargeVo);
    }

    /**
     * 维修收费账单列表
     *
     * @param searchVo
     * @return
     */
    public MaintainChargePageResult<MaintainChargeDataVo> listMaintainCharge(MaintainChargeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return MaintainChargePageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return maintainComponent.listMaintainCharge(searchVo);
    }

    /**
     * 删除维修账单
     *
     * @param id
     */
    public void deleteMaintainCharge(Long id) {
        maintainComponent.deleteMaintainCharge(id);
    }

    /**
     * 删除维修账单明细
     *
     * @param id
     */
    public void deleteMaintainChargeItem(Long id) {
        maintainComponent.deleteMaintainChargeItem(id);
    }

    /**
     * 维修收费账单详情
     *
     * @param id
     * @return
     */
    public MaintainChargeDataVo chargeDataVoById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainComponent.chargeDataVoById(id);
    }

    /**
     * 根据订单号查询维修收费账单详情
     * @param orderId
     * @return
     */
    public List<MaintainChargeDataVo> listChargeDataVoByOrderId(Long orderId) {
        return maintainComponent.listChargeDataVoByOrderId(orderId);
    }

    /**
     * 根据收费单id查询物维收费单费项
     *
     * @param chargeId
     * @return
     */
    public List<MaintainChargeItemVo> chargeItemList(Long chargeId) {
        return maintainComponent.chargeItemList(chargeId);
    }

    /**
     * 根据派工id查询物维收费单集合
     *
     * @param jobId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByJobId(Long jobId) {
        return maintainComponent.listChargeItemByJobId(jobId);
    }

    /**
     * 根据订单id查询物维收费单集合
     * @param orderId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByOrderId(Long orderId) {
        return maintainComponent.listChargeItemByOrderId(orderId);
    }

    /**
     * 根据id物维收费单集合
     *
     * @param ids
     * @return
     */
    public List<MaintainChargeVo> listMaintainChargeByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        return maintainComponent.listMaintainChargeByIds(ids);
    }

    /**
     * 保存票据号
     *
     * @param chargeSerial
     */
    public void saveChargeSerial(MaintainChargeSerialVo chargeSerial) {
        if (!WrapperClassUtils.biggerThanLong(chargeSerial.getChargeId(), 0L)) {
            return;
        }
        maintainComponent.saveChargeSerial(chargeSerial);
    }

    /**
     * 物维单日报表分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> dayListPage(MaintainOrderDayReportSearchVo searchVo) {
        return maintainComponent.dayListPage(searchVo);
    }

    /**
     * 物维单月报表分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> monthListPage(MaintainOrderMonthReportSearchVo searchVo) {
        return maintainComponent.monthListPage(searchVo);
    }

    /**
     * 支付物维收费账单
     *
     * @param payRequest
     * @return
     */
    public OrderResultVo payCharge(BillPayRequestVo payRequest) {

        //收费账单
        List<MaintainChargeVo> chargeList = listMaintainChargeByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingCharge(chargeList);

        chargeList.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result = null;
        // 支付
        try {
            OrderVo order = newPayOrder(chargeList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest, result);
                // 更新备注
                batchUpdateRemark(payRequest.getBillIds(), payRequest.getRemark());
            }
            log.info("pay bill {} finish.", JsonUtils.object2Json(payRequest.getBills()));
        } finally {
            removeChargePayingLock(chargeList);
        }
        log.info("finish pay bill {}, result {}", JsonUtils.object2Json(payRequest.getBills()), JsonUtils.object2Json(result));
        return result;
    }

    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {

    }

    private void notifyBillStatus(BillPayRequestVo payRequest, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(payRequest.getBillIds());
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPayWay(payRequest.getPayWay().getValue());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(payRequest.getTollMan());
        maintainComponent.updateChargeStatus(billPaid);
    }

    /**
     * 账单
     *
     * @param charges
     * @param payAmount
     * @param ip
     * @return
     */
    private OrderVo newPayOrder(List<MaintainChargeVo> charges, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.MAINTAIN);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(charges.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_MAINTAIN_CHARGE, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     *
     * @param charge
     * @return
     */
    private OrderItemVo newPayOrderItem(MaintainChargeVo charge) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(charge.getId());
        result.setProductName(String.format(SenoxConst.TITLE_MAINTAIN_CHARGE, StringUtils.EMPTY, StringUtils.EMPTY));
        result.setQuantity(1);
        result.setPrice(charge.getTotalAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);

        Map<Long, List<MaintainChargeItemVo>> collect = charge.getChargeItemVos().stream().collect(Collectors.groupingBy(MaintainChargeItemVo::getFeeId));

        result.setDetails(collect.values().stream().map(this::newPayOrderItemDetail).collect(Collectors.toList()));
        return result;
    }

    /**
     * 子账单明细
     *
     * @param chargeItemVoList
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(List<MaintainChargeItemVo> chargeItemVoList) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(chargeItemVoList.get(0).getFeeId());
        result.setFeeName(chargeItemVoList.get(0).getFeeTitle());
        result.setQuantity(1);
        result.setPrice(chargeItemVoList.stream().map(MaintainChargeItemVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 批量更新备注
     * @param billIds
     * @param remark
     */
    private void batchUpdateRemark(List<Long> billIds, String remark) {
        if (CollectionUtils.isEmpty(billIds)) {
            return;
        }

        List<MaintainChargeRemarkVo> remarkVoList = new ArrayList<>(billIds.size());
        billIds.forEach(billId -> {
            MaintainChargeRemarkVo remarkVo = new MaintainChargeRemarkVo();
            remarkVo.setId(billId);
            remarkVo.setRemark(remark);
            remarkVoList.add(remarkVo);
        });
        maintainComponent.updateChargeRemark(remarkVoList);
    }

    /**
     * 批量添加维修所需账单
     * @param chargeItemVos
     */
    public void batchSaveCharge(List<MaintainChargeItemVo> chargeItemVos) {
        maintainComponent.batchSaveCharge(chargeItemVos);
    }

    /**
     * 待支付校验
     *
     * @param list
     */
    private void checkPayingCharge(List<MaintainChargeVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> x.getStatus() == 1)) {
            throw new BusinessException("存在已缴费账单");
        }
    }


    /**
     * 支付锁
     *
     * @param charge
     * @return
     */
    private String buildPayLockKey(MaintainChargeVo charge) {
        return String.format(SenoxConst.Cache.KEY_MAINTAIN_CHARGE_PAY, charge.getId());
    }

    /**
     * 移除账单锁
     *
     * @param charges
     */
    private void removeChargePayingLock(List<MaintainChargeVo> charges) {
        if (CollectionUtils.isEmpty(charges)) {
            return;
        }
        charges.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

}
