package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.vo.EnergyConsumeUnitVo;
import com.senox.web.component.RealtyEnergyComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:16
 */
@RequiredArgsConstructor
@Service
public class EnergyConsumeUnitService {

    private final RealtyEnergyComponent realtyEnergyComponent;

    /**
     * 添加能源消费单元
     * @param unit
     * @return
     */
    public Long addConsumeUnit(EnergyConsumeUnitVo unit) {
        return realtyEnergyComponent.addConsumeUnit(unit);
    }

    /**
     * 更新能源消费单元
     * @param unit
     */
    public void updateConsumeUnit(EnergyConsumeUnitVo unit) {
        if (!WrapperClassUtils.biggerThanLong(unit.getId(), 0L)) {
            return;
        }

        realtyEnergyComponent.updateConsumeUnit(unit);
    }

    /**
     * 获取能源消费单元详情
     * @param id
     * @return
     */
    public EnergyConsumeUnitVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? realtyEnergyComponent.findConsumeUnitById(id) : null;
    }

    /**
     * 能源消费单元列表
     * @param type
     * @return
     */
    public List<EnergyConsumeUnitVo> list(Integer type) {
        return realtyEnergyComponent.listConsumeUnit(type);
    }




}
