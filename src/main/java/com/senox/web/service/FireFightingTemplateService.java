package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FireFightingTemplateSearchVo;
import com.senox.realty.vo.FirefightingFormTemplateVo;
import com.senox.realty.vo.FirefightingTemplateVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 16:59
 */
@Service
@RequiredArgsConstructor
public class FireFightingTemplateService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加店铺消防安全告知单模板
     * @param template
     * @param newVersion
     * @return
     */
    public Long addTemplate(FirefightingTemplateVo template, Boolean newVersion) {
        return firefightingComponent.addTemplate(template, newVersion);
    }

    /**
     * 更新店铺消防安全告知单模板
     * @param template
     */
    public void updateTemplate(FirefightingTemplateVo template) {
        if (!WrapperClassUtils.biggerThanLong(template.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateTemplate(template);
    }

    /**
     * 启用店铺消防安全告知单模板
     * @param id
     */
    public void enableTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.enableTemplate(id);
    }

    /**
     * 停用店铺消防安全告知单模板
     * @param id
     */
    public void disableTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.disableTemplate(id);
    }

    /**
     * 删除店铺消防安全告知单模板
     * @param id
     */
    public void deleteTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteTemplate(id);
    }

    /**
     * 获取店铺消防安全告知单模板
     * @param id
     * @return
     */
    public FirefightingTemplateVo findTemplateById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findTemplateById(id) : null;
    }

    /**
     * 获取最新店铺消防安全告知单模板
     * @param search
     * @return
     */
    public FirefightingTemplateVo findLatestTemplateByCode(FireFightingTemplateSearchVo search) {
        if (StringUtils.isBlank(search.getCode())) {
            return null;
        }

        return firefightingComponent.findLatestTemplateByCode(search);
    }

    /**
     * 店铺消防安全告知单模板页
     * @param search
     * @return
     */
    public PageResult<FirefightingTemplateVo> listTemplatePage(FireFightingTemplateSearchVo search) {
        return firefightingComponent.listTemplatePage(search);
    }

    public Long addFormTemplate(FirefightingFormTemplateVo formTemplate) {
        return firefightingComponent.addFormTemplate(formTemplate);
    }

    public void updateFormTemplate(FirefightingFormTemplateVo formTemplate) {
        if (!WrapperClassUtils.biggerThanLong(formTemplate.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateFormTemplate(formTemplate);
    }

    public void deleteFormTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteFormTemplate(id);
    }

    public FirefightingFormTemplateVo findFormTemplateById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findFormTemplateById(id) : null;
    }

    public List<FirefightingFormTemplateVo> listFormTemplate(String form) {
        if (StringUtils.isBlank(form)) {
            return Collections.emptyList();
        }

        return firefightingComponent.listFormTemplate(form);
    }
}
