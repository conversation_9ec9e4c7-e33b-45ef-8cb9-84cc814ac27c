package com.senox.web.service;

import com.senox.cold.constant.CloudWarehousingFee;
import com.senox.cold.vo.*;
import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.*;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.web.component.CloudWarehousingBillComponent;
import com.senox.web.component.WechatComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/14 10:43
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CloudWarehousingBillService extends BillService{

    private final CloudWarehousingBillComponent warehousingBillComponent;
    private final WechatComponent wechatComponent;

    /**
     * 云仓月账单微信通知
     * @param year
     * @param month
     */
    public void notifyBill(Integer year, Integer month) {
        wechatComponent.notifyCloudWarehousingBill(year, month);
    }

    /**
     * 添加云仓账单
     * @param billVo
     * @return
     */
    public Long addCloudWarehousingBill(CloudWarehousingBillVo billVo) {
        return warehousingBillComponent.addCloudWarehousingBill(billVo);
    }

    /**
     * 批量添加云仓账单
     * @param list
     */
    public void batchAdd(List<CloudWarehousingBillVo> list) {
        warehousingBillComponent.batchAdd(list);
    }

    /**
     * 更新云仓账单
     * @param billVo
     */
    public void updateCloudWarehousingBill(CloudWarehousingBillVo billVo) {
        warehousingBillComponent.updateCloudWarehousingBill(billVo);
    }

    /**
     * 根据id获取云仓账单
     * @param id
     * @return
     */
    public CloudWarehousingBillVo findById(Long id) {
        return warehousingBillComponent.findById(id);
    }

    /**
     * 批量删除云仓账单
     * @param ids
     */
    public void deleteByIds(List<Long> ids) {
        warehousingBillComponent.deleteByIds(ids);
    }

    /**
     * 更新账单备注
     * @param remarkVo
     */
    public void updateBillRemark(RefrigerationBillRemarkVo remarkVo) {
        warehousingBillComponent.updateBillRemark(Collections.singletonList(remarkVo));
    }

    /**
     * 更新票据号
     * @param tollSerial
     */
    public void updateBillSerial(TollSerialVo tollSerial) {
        warehousingBillComponent.updateBillSerial(tollSerial);
    }

    /**
     * 下发云仓账单
     * @param send
     */
    public void sendBill(RefrigerationBillSendVo send) {
        warehousingBillComponent.sendBill(send);
    }

    /**
     * 免滞纳金
     * @param billIds
     */
    public void ignoreBillPenalty(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return;
        }

        BillPenaltyIgnoreVo penaltyIgnore = new BillPenaltyIgnoreVo();
        penaltyIgnore.setBillIds(billIds);
        penaltyIgnore.setPenaltyIgnore(Boolean.TRUE);
        warehousingBillComponent.ignoreBillPenalty(penaltyIgnore);
    }

    /**
     * 云仓账单合计
     * @param search
     * @return
     */
    public CloudWarehousingBillVo sumBill(CloudWarehousingBillSearchVo search) {
        return warehousingBillComponent.sumBill(search);
    }

    /**
     * 云仓账单列表
     * @param search
     * @return
     */
    public PageResult<CloudWarehousingBillVo> listBill(CloudWarehousingBillSearchVo search) {
        return warehousingBillComponent.listBill(search);
    }

    /**
     * 获取账单列表
     * @param ids
     * @return
     */
    public List<CloudWarehousingBillVo> listBillByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return warehousingBillComponent.listBillByIds(ids);
    }

    /**
     * 批量更新备注
     * @param billIds
     * @param remark
     */
    private void batchUpdateRemark(List<Long> billIds, String remark) {
        if (CollectionUtils.isEmpty(billIds)) {
            return;
        }

        List<RefrigerationBillRemarkVo> remarkVoList = new ArrayList<>(billIds.size());
        billIds.forEach(billId -> {
            RefrigerationBillRemarkVo remarkVo = new RefrigerationBillRemarkVo();
            remarkVo.setId(billId);
            remarkVo.setRemark(remark);
            remarkVoList.add(remarkVo);
        });
        warehousingBillComponent.updateBillRemark(remarkVoList);
    }

    /**
     * 支付冷藏账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 免滞纳金
        ignoreBillPenalty(payRequest.getIgnorePenaltyIds());

        // 账单
        List<CloudWarehousingBillVo> billList = listBillByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(billList);

        billList.forEach(x ->  RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result = null;
        // 支付
        try {
            OrderVo order = newPayOrder(billList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付云仓账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest, result);
                // 更新备注
                batchUpdateRemark(payRequest.getBillIds(), payRequest.getRemark());
            }
            log.info("pay bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));
        } finally {
            removeBillPayingLock(billList);
        }
        log.info("finish pay bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 待支付校验
     * @param list
     */
    private void checkPayingBill(List<CloudWarehousingBillVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 支付锁
     * @param bill
     * @return
     */
    private String buildPayLockKey(CloudWarehousingBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_CLOUD_WAREHOUSING_BILL_PAY, bill.getId());
    }

    /**
     * 云仓账单标题
     * @param bill
     * @return
     */
    private String buildCloudWarehousingBillTitle(CloudWarehousingBillVo bill) {
        return String.format(SenoxConst.TITLE_CLOUD_WAREHOUSING_BILL, bill.getName(), buildBillYearMonth(bill.getBillYear(), bill.getBillMonth()));
    }

    /**
     * 账单年月
     *
     * @param billYear
     * @param billMonth
     * @return
     */
    private String buildBillYearMonth(Integer billYear, Integer billMonth) {
        return billYear + StringUtils.fixLength(String.valueOf(billMonth), 2, '0');
    }


    /**
     * 移除账单锁
     *
     * @param bills
     */
    private void removeBillPayingLock(List<CloudWarehousingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 账单
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    protected OrderVo newPayOrder(List<CloudWarehousingBillVo> bills, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.CLOUD_WAREHOUSING);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_CLOUD_WAREHOUSING_BILL, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     * @param bill
     * @return
     */
    private OrderItemVo newPayOrderItem(CloudWarehousingBillVo bill) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(buildCloudWarehousingBillTitle(bill));
        result.setQuantity(1);
        result.setPrice(bill.getTotalCharge());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);


        CloudWarehousingFee[] fees = CloudWarehousingFee.values();
        List<OrderItemDetailVo> list = new ArrayList<>(fees.length);
        for (int i = fees.length; i > 0; i--) {
            CloudWarehousingFee fee = fees[i - 1];

            OrderItemDetailVo item = newPayOrderItemDetail(bill, fee);
            if (fee == CloudWarehousingFee.PENALTY) {
                if (!DecimalUtils.isPositive(item.getTotalAmount())) {
                    continue;
                }

                item.setFree(bill.checkPenaltyIgnore());
            }
            list.add(item);
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 子账单明细
     * @param bill
     * @param fee
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(CloudWarehousingBillVo bill, CloudWarehousingFee fee) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(fee.getFeeId());
        result.setFeeName(fee.getName());
        result.setQuantity(1);
        result.setPrice(bill.getCharge(fee));
        result.setTotalAmount(result.getPrice());
        return result;
    }

    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {

    }

    private void notifyBillStatus(BillPayRequestVo payRequest, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(payRequest.getBillIds());
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPayWay(payRequest.getPayWay().getValue());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(payRequest.getTollMan());
        warehousingBillComponent.updateBillStatus(billPaid);
    }
}
