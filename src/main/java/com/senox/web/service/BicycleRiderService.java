package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.tms.vo.*;
import com.senox.web.component.BicycleRiderComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 15:39
 */
@Service
@RequiredArgsConstructor
public class BicycleRiderService {

    private final BicycleRiderComponent bicycleRiderComponent;
    private final WxUserService wxUserService;

    /**
     * 添加三轮车配送骑手
     * @param riderVo
     * @return
     */
    public Long addBicycleRider(BicycleRiderVo riderVo) {
        return bicycleRiderComponent.addBicycleRider(riderVo);
    }

    /**
     * 修改三轮车配送骑手
     * @param riderVo
     */
    public void updateBicycleRider(BicycleRiderVo riderVo) {
        bicycleRiderComponent.updateBicycleRider(riderVo);
    }

    /**
     * 根据id获取三轮车骑手
     * @param id
     * @return
     */
    public BicycleRiderVo findById(Long id) {
        return bicycleRiderComponent.findById(id);
    }

    /**
     * 删除三轮车配送骑手
     * @param id
     */
    public void deleteBicycleRider(Long id) {
        bicycleRiderComponent.deleteBicycleRider(id);
        wxUserService.unbindRider(id);
    }

    /**
     * 三轮车配送骑手列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderVo> listRider(BicycleRiderSearchVo searchVo) {
        return bicycleRiderComponent.listRider(searchVo);
    }

    /**
     * 根据id获取三轮车骑手考勤信息
     * @param id
     * @return
     */
    public BicycleRiderAttendanceVo findAttendanceById(Long id) {
        return bicycleRiderComponent.findAttendanceById(id);
    }

    /**
     * 三轮车骑手考勤信息列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderAttendanceVo> listRiderAttendance(BicycleRiderAttendanceSearchVo searchVo) {
        return bicycleRiderComponent.listRiderAttendance(searchVo);
    }

    /**
     * 三轮车配送骑手信息列表
     * @return
     */
    public List<BicycleRiderInfoVo> listRiderInfo() {
        return bicycleRiderComponent.listRiderInfo();
    }
}
