package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.spring.ApplicationContextHolder;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.pm.vo.RefundOrderVo;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/28 15:50
 */

public abstract class BillService {

    private final Logger logger = LoggerFactory.getLogger(BillService.class);

    @Lazy
    @Autowired
    protected OrderComponent orderComponent;

    /**
     * 支付请求
     * @param payRequest
     * @return
     */
    protected PayAmountVo newPayAmountRequest(BillPayRequestVo payRequest) {
        PayAmountVo result = new PayAmountVo(payRequest.getPayWay(), null);
        result.setAuthCode(payRequest.getAuthCode());
        result.setDeviceSn(payRequest.getDeviceSn());
        return result;
    }

    /**
     * 订单退款
     * @param refundOrder
     * @return
     */
    public OrderResultVo refundOrder(RefundOrderVo refundOrder) {
        lockRefundOrder(refundOrder.getOrderId());

        OrderResultVo result = null;
        try {
            // 退费下单
            result = orderComponent.refundOrder(refundOrder);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            logger.info("订单退费下单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                OrderVo order = orderComponent.findWithDetail(result.getOrderId());
                if (order == null || CollectionUtils.isEmpty(order.getItems())) {
                    throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到退费订单明细");
                }
                notifyBillStatus(order.getItems().stream().map(OrderItemVo::getProductId).collect(Collectors.toList()), refundOrder.getTollMan(), result);
            }

        } finally {
            removeRefundOrderLock(refundOrder.getOrderId());
        }
        return result;
    }

    /**
     * 通知订单结果
     * @param billIds
     * @param tollMan
     * @param order
     */
    protected abstract void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order);


    /**
     * 退费上锁
     *
     * @param orderId
     */
    protected void lockRefundOrder(Long orderId) {
        if (!RedisUtils.lock(buildOrderRefundLockKey(orderId), SenoxConst.Cache.TTL_10M)) {
            throw new BusinessException("退费操作太频繁，请稍后尝试");
        }
    }

    /**
     * 移除退费锁
     *
     * @param orderId
     */
    protected void removeRefundOrderLock(Long orderId) {
        RedisUtils.del(buildOrderRefundLockKey(orderId));
    }

    /**
     * 获取通用订单标题
     * @param isRefund
     * @return
     */
    protected String getOrderTitle(boolean isRefund) {
        return isRefund ? SenoxConst.TITLE_BILL_REFUND : SenoxConst.TITLE_BILL_ORDER;
    }

    /**
     * 退费分布式锁
     *
     * @param orderId
     * @return
     */
    private String buildOrderRefundLockKey(Long orderId) {
        return String.format(SenoxConst.Cache.KEY_ORDER_REFUND, orderId);
    }

}
