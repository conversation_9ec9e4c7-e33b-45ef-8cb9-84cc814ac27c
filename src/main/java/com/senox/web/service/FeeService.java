package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.vo.FeeVo;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import com.senox.web.component.RealtyDictionaryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/18 15:52
 */
@Service
public class FeeService {

    @Autowired
    private RealtyDictionaryComponent realtyDictionaryComponent;

    /**
     * 添加费项
     * @param fee
     * @return
     */
    public Long addFee(FeeVo fee) {
        if (StringUtils.isBlank(fee.getName())) {
            return 0L;
        }
        return realtyDictionaryComponent.addFee(fee);
    }

    /**
     * 更新费项
     * @param fee
     */
    public void updateFee(FeeVo fee) {
        if (!WrapperClassUtils.biggerThanLong(fee.getId(), 0L)) {
            return;
        }
        realtyDictionaryComponent.updateFee(fee);
    }

    /**
     * 删除费项
     * @param id
     */
    public void deleteFee(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        FeeVo fee = new FeeVo();
        fee.setId(id);
        fee.setDisabled(Boolean.TRUE);
        updateFee(fee);
    }

    /**
     * 根据id获取费项
     * @param id
     * @return
     */
    public FeeVo findFeeById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return realtyDictionaryComponent.findFeeById(id);
    }

    /**
     * 费项列表
     * @return
     */
    public List<FeeVo> listFee(Integer category) {
        return realtyDictionaryComponent.listFee(category);
    }

    /**
     * 添加水/电价类别列表
     * @param priceType
     * @return
     */
    public Long addPriceType(WaterElectricPriceTypeVo priceType) {
        if (StringUtils.isBlank(priceType.getName())
                || priceType.getPrice() == null || priceType.getPrice().compareTo(BigDecimal.ZERO) < 1) {
            return 0L;
        }
        return realtyDictionaryComponent.addPriceType(priceType);
    }

    /**
     * 更新水/电价类别
     * @param priceType
     */
    public void updatePriceType(WaterElectricPriceTypeVo priceType) {
        if (!WrapperClassUtils.biggerThanLong(priceType.getId(), 0L)) {
            return;
        }
        realtyDictionaryComponent.updatePriceType(priceType);
    }

    /**
     * 删除水/电价类别
     * @param id
     */
    public void deletePriceType(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        WaterElectricPriceTypeVo priceType = new WaterElectricPriceTypeVo();
        priceType.setId(id);
        priceType.setDisabled(Boolean.TRUE);
        updatePriceType(priceType);
    }

    /**
     * 根据id获取水/电价类别
     * @param id
     * @return
     */
    public WaterElectricPriceTypeVo findPriceTypeById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return realtyDictionaryComponent.findPriceTypeById(id);
    }

    /**
     * 水/电价类别列表
     * @return
     */
    public List<WaterElectricPriceTypeVo> listPriceType(Integer type) {
        return realtyDictionaryComponent.listPriceType(type);
    }

}
