package com.senox.web.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleOrderGoodsType;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.dto.BicycleRiderImportDto;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import com.senox.web.component.BicycleOrderComponent;
import com.senox.web.vo.BicycleOrderImportExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/19 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BicycleOrderService {

    private final BicycleOrderComponent bicycleOrderComponent;
    private final BicyclePointService pointService;
    private final MerchantService merchantService;

    /**
     * 添加三轮车配送订单
     * @param orderVo
     * @return
     */
    public Long addBicycleOrder(BicycleOrderVo orderVo) {
        return bicycleOrderComponent.addBicycleOrder(orderVo);
    }

    /**
     * 根据id获取配送订单
     * @param id
     * @param containStatus
     * @return
     */
    public BicycleOrderVo findOrderVoById(Long id, Boolean containStatus) {
        return bicycleOrderComponent.findOrderVoById(id, containStatus);
    }

    /**
     * 配送订单合计
     * @param searchVo
     * @return
     */
    public BicycleOrderVo sumOrder(BicycleOrderSearchVo searchVo) {
        return bicycleOrderComponent.sumOrder(searchVo);
    }

    /**
     * 配送订单分页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderVo> page(BicycleOrderSearchVo searchVo) {
        return bicycleOrderComponent.page(searchVo);
    }

    /**
     * 三轮车配送订单合计
     * @param search
     * @return
     */
    public BicycleOrderVo sumDeliveryOrder(BicycleOrderSearchVo search) {
        return bicycleOrderComponent.sumDeliveryOrder(search);
    }

    /**
     * 三轮车配送订单页
     * @param search
     * @return
     */
    public PageResult<BicycleOrderVo> listDeliveryPage(BicycleOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return bicycleOrderComponent.listDeliveryPage(search);
    }

    /**
     * 导出订单列表
     * @param searchVo
     * @return
     */
    public List<BicycleOrderVo> exportOrderList(BicycleOrderSearchVo searchVo) {
        return bicycleOrderComponent.exportOrderList(searchVo);
    }

    /**
     * 计算配送费用
     * @param orderVo
     * @param useChargesId
     * @return
     */
    public BicycleOrderCalculateEstimateVo calculateCharges(BicycleOrderVo orderVo, Boolean useChargesId) {
        return bicycleOrderComponent.calculateCharges(orderVo, useChargesId);
    }

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    public BicycleOperateAnalysisVo operateAnalysisStatistics(BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderComponent.operateAnalysisStatistics(searchVo);
    }

    /**
     * 配送地点使用统计
     * @param isStart
     * @return
     */
    public List<BicyclePointCountVo> listPointCount(Boolean isStart) {
        return bicycleOrderComponent.listPointCount(isStart);
    }

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    public BicycleCountRankingVo rankingStatistics(BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderComponent.rankingStatistics(searchVo);
    }

    /**
     * 未处理的订单数量
     * @return
     */
    public Integer undoOrderCount() {
        return bicycleOrderComponent.undoOrderCount();
    }

    /**
     * 历史运营分析记录列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        return bicycleOrderComponent.listOperateAnalysis(searchVo);
    }

    /**
     * 历史运营分析记录合计
     * @param searchVo
     * @return
     */
    public BicycleOperateAnalysisVo sumOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        return bicycleOrderComponent.sumOperateAnalysis(searchVo);
    }

    /**
     * 今日下单客户统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleCustomerCountVo> customerCountList(BicycleCustomerCountSearchVo searchVo) {
        return bicycleOrderComponent.customerCountList(searchVo);
    }

    /**
     * 客户下单统计合计
     * @param searchVo
     * @return
     */
    public BicycleCustomerCountVo sumCustomerCount(BicycleCustomerCountSearchVo searchVo) {
        return bicycleOrderComponent.sumCustomerCount(searchVo);
    }

    /**
     * 查询订单统计数量情况
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCount(BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderComponent.orderCount(searchVo);
    }

    /**
     * 删除未生成结算单的订单
     * @param id
     */
    public void cancelBicycleOrderById(Long id) {
        bicycleOrderComponent.cancelBicycleOrderById(id);
    }

    /**
     * 批量删除未生成结算单的订单
     * @param ids
     */
    public void cancelBicycleOrderByIds(List<Long> ids) {
        bicycleOrderComponent.cancelBicycleOrderByIds(ids);
    }

    /**
     * 取消订单
     * @param cancelVo
     */
    public void cancelBicycleOrder(BicycleOrderCancelVo cancelVo) {
        bicycleOrderComponent.cancelBicycleOrder(cancelVo);
    }

    /**
     * 根据id删除未配送的订单
     * @param id
     */
    public void deleteUndeliveredBicycleOrder(Long id) {
        bicycleOrderComponent.deleteUndeliveredBicycleOrder(id);
    }

    /**
     * 订单导入
     *
     * @param orderImportDtoList 订单导入列表
     */
    public void orderImport(List<BicycleOrderImportDto> orderImportDtoList) {
        bicycleOrderComponent.orderImport(orderImportDtoList);
    }

    /**
     * 订单导入
     *
     * @param orderExcelDataMap 订单excel数据集
     */
    public void orderImport(Long merchantId, MultiValueMap<String, BicycleOrderImportExcel> orderExcelDataMap) {
        List<BicycleOrderImportDto> dtoList = bicycleOrderBuilder(merchantId, orderExcelDataMap);
        orderImport(dtoList);
    }

    /**
     * 客户每月下单统计
     * @param searchVo
     * @return
     */
    public List<BicycleCustomerMonthInfoVo> customerMonthInfoList(BicycleCustomerMonthInfoSearchVo searchVo) {
        return bicycleOrderComponent.customerMonthInfoList(searchVo);
    }

    /**
     * 三轮车配送订单分页V2
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryOrderV2Vo> pageOrderV2(BicycleOrderV2SearchVo searchVo) {
        return bicycleOrderComponent.pageOrderV2(searchVo);
    }

    /**
     * 根据订单id查询货物信息
     * @param orderId
     * @return
     */
    public List<BicycleOrderGoodsDetailVo> goodsDetailByOrderId(Long orderId) {
        return bicycleOrderComponent.goodsDetailByOrderId(orderId);
    }

    /**
     * 查询订单统计数量情况V2
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCountV2(BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderComponent.orderCountV2(searchVo);
    }

    /**
     * 三轮车配送订单合计V2
     * @param searchVo
     * @return
     */
    public BicycleOrderV2Vo sumOrderV2(BicycleOrderV2SearchVo searchVo) {
        return bicycleOrderComponent.sumOrderV2(searchVo);
    }

    /**
     * 构建三轮车订单
     * @param orderExcelDataMap 订单excel数据集
     * @return
     */
    private List<BicycleOrderImportDto> bicycleOrderBuilder(Long merchantId, MultiValueMap<String, BicycleOrderImportExcel> orderExcelDataMap) {
        List<BicycleOrderImportDto> orderDtoList = new ArrayList<>();
        List<BicyclePointVo> points = pointService.listBicyclePoint();
        AtomicReference<MerchantVo> merchant = new AtomicReference<>(WrapperClassUtils.biggerThanLong(merchantId, 0) ? merchantService.findById(merchantId) : null);
        orderExcelDataMap.forEach((merchantName, data) -> {
            MultiValueMap<String, BicycleOrderImportExcel> dataMap = new LinkedMultiValueMap<>(data.size());
            data.stream().filter(e -> !StringUtils.isBlank(e.getMark())).forEach(e -> dataMap.add(e.getMark(), e));
            Map<String, Map<String, BicycleOrderImportExcel>> goodsMap = new HashMap<>(dataMap.size());
            dataMap.forEach((mark, item) -> goodsMap.put(mark, item.stream().collect(Collectors.toMap(BicycleOrderImportExcel::getGoodsName, Function.identity(), (e1, e2) -> {
                e1.setPieces(e1.getPieces().add(e2.getPieces()));
                return e1;
            }))));
            data.forEach(orderImportExcel -> {
                if (StringUtils.isBlank(orderImportExcel.getParticipant())) {
                    return;
                }
                try {
                    orderDtoList.add(orderHandle(orderImportExcel, merchantName, merchant, points, goodsMap));
                } catch (Exception e) {
                    log.error("excel数据处理异常：{}-{}", merchantName, JsonUtils.object2Json(orderImportExcel), e);
                }
            });
        });
        return orderDtoList;
    }

    private BicycleOrderImportDto orderHandle(BicycleOrderImportExcel orderImportExcel
            , String merchantName, AtomicReference<MerchantVo> merchant
            , List<BicyclePointVo> points,Map<String, Map<String, BicycleOrderImportExcel>> goodsMap) {
        BicycleOrderImportDto orderDto = new BicycleOrderImportDto();
        setDate(orderImportExcel, orderDto);
        if (null == merchant.get()) {
            MerchantSearchVo searchVo = new MerchantSearchVo();
            searchVo.setPageNo(1);
            searchVo.setPageSize(1);
            searchVo.setName(merchantName);
            List<MerchantVo> merchantList = merchantService.list(searchVo).getDataList();
            merchant.set(CollectionUtils.isEmpty(merchantList) ? null : merchantList.get(0));
        }
        orderDto.setSender(merchantName);
        setSender(merchant.get(), orderDto);
        setRider(orderImportExcel,goodsMap, orderDto);
        setPoint(orderImportExcel, orderDto, points);
        orderDto.setPieces(orderDto.getRiders().stream()
                .map(e-> e.getGoodsDetails().stream()
                        .map(BicycleOrderGoodsDetailVo::getPieces).reduce(BigDecimal.ZERO,BigDecimal::add))
                .reduce(BigDecimal.ZERO,BigDecimal::add));
        orderDto.setRemark(orderImportExcel.getRemark());
        orderDto.setMergeNumber(orderImportExcel.getMergeNumber());
        return orderDto;
    }

    private void setSender(MerchantVo merchant, BicycleOrderImportDto orderDto) {
        if (null == merchant) {
            throw new BusinessException(String.format("商户[%s]未找到", orderDto.getSender()));
        }
        orderDto.setSenderId(merchant.getId());
        orderDto.setSenderSerialNo(merchant.getRcSerial());
        orderDto.setSender(merchant.getName());
        orderDto.setSenderContact(merchant.getContact());
    }

    private void setRider(BicycleOrderImportExcel orderImportExcel, Map<String, Map<String, BicycleOrderImportExcel>> goodsMap, BicycleOrderImportDto orderDto) {
        if (StringUtils.isBlank(orderImportExcel.getParticipant())){
            return;
        }
        Pattern pattern = Pattern.compile("[a-zA-Z0-9]+");
        Matcher matcher = pattern.matcher(orderImportExcel.getParticipant());
        List<String> group = new ArrayList<>();
        List<BicycleRiderImportDto> riders = new ArrayList<>();
        while (matcher.find()) {
            group.add(matcher.group());
            if (group.size() == 2) {
                BicycleRiderImportDto rider = new BicycleRiderImportDto();
                String riderNumber = group.get(0);
                rider.setCompletePieces(new BigDecimal(group.get(1)));
                if (riderNumber.length() == 1) {
                    riderNumber = "0".concat(riderNumber);
                }
                rider.setRiderNo(riderNumber);
                final BigDecimal[] completePieces = {new BigDecimal(String.valueOf(rider.getCompletePieces()))};
                List<BicycleOrderGoodsDetailVo> goodsDetails = new ArrayList<>();
                Map<String, BicycleOrderImportExcel> stringBicycleOrderImportExcelMap = goodsMap.get(orderImportExcel.getMark());
                stringBicycleOrderImportExcelMap.forEach((goodsName, goods) -> {
                    BigDecimal pieces = goods.getPieces();
                    BicycleOrderGoodsDetailVo goodsDetailVo = new BicycleOrderGoodsDetailVo();
                    goodsDetailVo.setGoodsName(goodsName);
                    goodsDetailVo.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
                    int piecesContrast = pieces.compareTo(completePieces[0]);
                    if (piecesContrast > 0) {
                        goodsDetailVo.setPieces(completePieces[0]);
                        goods.setPieces(pieces.subtract(completePieces[0]));
                        completePieces[0] = BigDecimal.ZERO;
                    } else if (piecesContrast == 0) {
                        goodsDetailVo.setPieces(completePieces[0]);
                        goods.setPieces(BigDecimal.ZERO);
                        completePieces[0] = BigDecimal.ZERO;
                    } else {
                        goodsDetailVo.setPieces(pieces);
                        completePieces[0] = completePieces[0].subtract(pieces);
                        goods.setPieces(BigDecimal.ZERO);
                    }
                    if (goodsDetailVo.getPieces().compareTo(BigDecimal.ONE) < 0) {
                        return;
                    }
                    goodsDetailVo.setWeight(goods.getWeight());
                    goodsDetailVo.setSize(BigDecimal.ZERO);
                    goodsDetails.add(goodsDetailVo);
                });
                rider.setGoodsDetails(goodsDetails);
                riders.add(rider);
                group.clear();
            }
        }
        orderDto.setRiders(riders);
    }

    public void setDate(BicycleOrderImportExcel orderImportExcel, BicycleOrderImportDto orderDto) {
        orderDto.setDate(orderImportExcel.getDate());
        orderDto.setTime(orderImportExcel.getTime());
        orderDto.setMinutesConsumed(orderImportExcel.getConsumedTime());
    }
    public void setPoint(BicycleOrderImportExcel orderImportExcel, BicycleOrderImportDto orderVo,List<BicyclePointVo> points){
        Map<String, BicyclePointVo> pointMap = points.stream().collect(Collectors.toMap(BicyclePointVo::getName, Function.identity()));
        Set<String> pointNameSet = pointMap.keySet();
        orderVo.setStartPointAddress(pointMatch(orderImportExcel.getStartPoint(), pointNameSet));
        orderVo.setEndPointAddress(pointMatch(orderImportExcel.getEndPoint(), pointNameSet));
        orderVo.setStartPointId(pointMap.get(orderVo.getStartPointAddress()).getId());
        orderVo.setEndPointId(pointMap.get(orderVo.getEndPointAddress()).getId());
        orderVo.setStartPointDetailAddress(StringUtils.EMPTY);
        orderVo.setEndPointDetailAddress(StringUtils.EMPTY);
    }

    /**
     * 地址匹配
     * @param point 需要匹配的地址
     * @param points 地址列表
     * @return
     */
    private String pointMatch(String point, Set<String> points) {
        point = point.replace("一", "1");
        point = point.replace("二", "2");
        point = point.replace("三", "3");
        point = point.replace("档口", "采购区");
        point = point.replace("，", "|");
        Pattern pattern = Pattern.compile(String.format("(%s)", point));
        return points.stream().filter(p -> {
            Matcher matcher = pattern.matcher(p);
            return matcher.find();
        }).findFirst().orElse(points.stream().filter(p -> p.contains("采购区")).findFirst().orElse(""));
    }
}
