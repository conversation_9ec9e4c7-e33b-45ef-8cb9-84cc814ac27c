package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyPayoffSearchVo;
import com.senox.realty.vo.RealtyPayoffVo;
import com.senox.web.component.RealtyComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/25 11:17
 */
@RequiredArgsConstructor
@Service
public class RealtyPayoffService {

    private final RealtyComponent realtyComponent;

    /**
     * 生成应付账单
     * @param month
     */
    public void generatePayoff(BillMonthVo month) {
        realtyComponent.generatePayoff(month);
    }

    /**
     * 更新应付账单
     * @param payoff
     */
    public void updatePayoff(RealtyPayoffVo payoff) {
        realtyComponent.updatePayoff(payoff);
    }

    /**
     * 删除应付账单
     * @param id
     */
    public void deletePayoff(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        realtyComponent.deletePayoff(id);
    }

    /**
     * 查找应付账单明细
     * @param id
     * @return
     */
    public RealtyPayoffVo getPayoff(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? realtyComponent.getPayoff(id) : null;
    }

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    public RealtyPayoffVo sumPayoff(RealtyPayoffSearchVo search) {
        return realtyComponent.sumPayoff(search);
    }

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    public List<RealtyPayoffVo> listPayoff(RealtyPayoffSearchVo search) {
        return realtyComponent.listPayoff(search);
    }

    /**
     * 应付账单列表页
     * @param search
     * @return
     */
    public PageResult<RealtyPayoffVo> listPayoffPage(RealtyPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return realtyComponent.listPayoffPage(search);
    }
}
