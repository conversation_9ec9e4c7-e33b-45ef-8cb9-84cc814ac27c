package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingFileBriefVo;
import com.senox.realty.vo.FirefightingFileSearchVo;
import com.senox.realty.vo.FirefightingFileVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/22 15:37
 */
@Service
@RequiredArgsConstructor
public class FirefightingFileService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加店铺消防安全档案
     * @param file
     * @return
     */
    public Long addFile(FirefightingFileVo file) {
        return firefightingComponent.addFile(file);
    }

    /**
     * 更新店铺消防安全档案
     * @param file
     */
    public void updateFile(FirefightingFileVo file) {
        if (!WrapperClassUtils.biggerThanLong(file.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateFile(file);
    }

    /**
     * 删除店铺消防安全档案
     * @param id
     */
    public void deleteFile(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteFile(id);
    }

    /**
     * 获取店铺消防安全档案详情
     * @param id
     * @return
     */
    public FirefightingFileVo findFileById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findFileById(id) : null;
    }

    /**
     * 店铺消防安全档案合计
     * @param search
     * @return
     */
    public int countFile(FirefightingFileSearchVo search) {
        return firefightingComponent.countFile(search);
    }

    /**
     * 店铺消防安全档案列表
     * @param search
     * @return
     */
    public PageResult<FirefightingFileBriefVo> listFileBriefPage(FirefightingFileSearchVo search) {
        return firefightingComponent.listFileBriefPage(search);
    }


}
