package com.senox.web.service;

import com.senox.car.vo.AlarmInformationSearchVo;
import com.senox.car.vo.AlarmInformationVo;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.AlarmInformationComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/9 10:50
 */
@Service
public class AlarmInformationService {

    @Autowired
    private AlarmInformationComponent alarmInformationComponent;

    /**
     * 更新告警信息
     * @param alarmInformationVo
     */
    public void updateAlarmInformation(AlarmInformationVo alarmInformationVo) {
        if (!WrapperClassUtils.biggerThanLong(alarmInformationVo.getId(), 0L)) {
            return;
        }
        alarmInformationComponent.updateAlarmInformation(alarmInformationVo);
    }

    /**
     * 获取告警信息
     * @param id
     * @return
     */
    public AlarmInformationVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return alarmInformationComponent.findById(id);
    }

    /**
     * 告警列表
     * @param search
     * @return
     */
    public PageResult<AlarmInformationVo> list(AlarmInformationSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return alarmInformationComponent.list(search);
    }
}
