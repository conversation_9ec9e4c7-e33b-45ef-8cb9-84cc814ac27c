package com.senox.web.service;

import com.senox.cold.vo.*;
import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.web.component.NormalTemperatureWarehousingBillComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-21
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class NormalTemperatureWarehousingBillService extends BillService {
    private final NormalTemperatureWarehousingBillComponent billComponent;

    /**
     * 添加账单
     *
     * @param year       年
     * @param month      月
     * @param isDuplicate
     * @param isOverride
     * @param addDtoList 账单集
     */
    public void add(Integer year, Integer month, Boolean isDuplicate, Boolean isOverride, List<NormalTemperatureWarehousingBillAddDto> addDtoList) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0) || CollectionUtils.isEmpty(addDtoList)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        billComponent.add(year, month, isDuplicate, isOverride, addDtoList);
    }

    /**
     * 删除账单
     *
     * @param ids 账单id集
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        billComponent.deleteByIds(ids);
    }

    /**
     * 更新账单
     *
     * @param bill 账单
     */
    public void update(NormalTemperatureWarehousingBillVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        billComponent.update(bill);
    }

    /**
     * 检查重复
     * @param bill 账单
     * @return 返回检查结果
     */
    public Boolean checkDuplicate(NormalTemperatureWarehousingBillAddDto bill) {
        return billComponent.checkDuplicate(bill);
    }

    /**
     * 更新账单备注
     *
     * @param bills 账单集
     */
    public void updateRemark(List<NormalTemperatureWarehousingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        billComponent.updateRemark(bills);
    }

    /**
     * 列表
     *
     * @param search 查询
     * @return 返回查询到的列表
     */
    public List<NormalTemperatureWarehousingBillVo> list(NormalTemperatureWarehousingBillSearchVo search) {
        return billComponent.list(search);
    }

    /**
     * 分页列表
     *
     * @param search 查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<NormalTemperatureWarehousingBillVo, NormalTemperatureWarehousingStatisticsVo> pageList(NormalTemperatureWarehousingBillSearchVo search) {
        return billComponent.pageList(search);
    }

    /**
     * 账单下发
     *
     * @param send 下发参数
     */
    public void send(RefrigerationBillSendVo send) {
        billComponent.send(send);
    }

    /**
     * 账单免除滞纳金
     *
     * @param billIds 账单id集
     */
    public void ignoreBillPenalty(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return;
        }

        BillPenaltyIgnoreVo penaltyIgnore = new BillPenaltyIgnoreVo();
        penaltyIgnore.setBillIds(billIds);
        penaltyIgnore.setPenaltyIgnore(Boolean.TRUE);
        billComponent.ignoreBillPenalty(penaltyIgnore);
    }

    /**
     * 更新账单状态
     *
     * @param billPaid 账单支付信息
     */
    public void updateBillStatus(@Validated BillPaidVo billPaid) {
        billComponent.updateBillStatus(billPaid);
    }

    /**
     * 更新票据号
     *
     * @param tollSerial 收费票据
     */
    public void updateSerial(TollSerialVo tollSerial) {
        if (!WrapperClassUtils.biggerThanLong(tollSerial.getBillId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        billComponent.updateSerial(tollSerial);
    }


    /**
     * 根据id获取账单
     *
     * @param billId 账单id
     * @return 返回获取到的账单
     */
    public NormalTemperatureWarehousingBillVo findById(Long billId) {
        return billComponent.findById(billId);
    }

    /**
     * 账单支付
     *
     * @param payRequest 支付参数
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 免滞纳金
        ignoreBillPenalty(payRequest.getIgnorePenaltyIds());
        NormalTemperatureWarehousingBillSearchVo search = new NormalTemperatureWarehousingBillSearchVo();
        search.setIds(payRequest.getBillIds());
        List<NormalTemperatureWarehousingBillVo> bills = list(search);
        // 支付校验
        checkPayingBill(bills);
        //支付锁
        bills.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result;
        try {
            OrderVo order = newPayOrder(bills, newPayAmountRequest(payRequest), payRequest.getRequestIp());
            // 下单
            result = orderComponent.addOrder(order);
            if (null == result) {
                throw new BusinessException("下单失败");
            }
            log.info("支付干仓账单成功，返回 {}", JsonUtils.object2Json(result));
            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest, result);
                // 更新备注
                updateRemark(payRequest.getBillIds(), payRequest.getRemark());
            }

        } finally {
            removePayingLock(bills);
        }
        return result;
    }

    /**
     * 更新备注
     *
     * @param billIds 账单集
     * @param remark  备注
     */
    public void updateRemark(List<Long> billIds, String remark) {
        if (CollectionUtils.isEmpty(billIds) || StringUtils.isBlank(remark)) {
            return;
        }

        List<NormalTemperatureWarehousingBillVo> bills = new ArrayList<>();
        for (Long billId : billIds) {
            NormalTemperatureWarehousingBillVo bill = new NormalTemperatureWarehousingBillVo();
            bill.setId(billId);
            bill.setRemark(remark);
            bills.add(bill);
        }
        updateRemark(bills);
    }

    /**
     * 支付锁
     *
     * @param bill 账单
     * @return
     */
    private String buildPayLockKey(NormalTemperatureWarehousingBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_COLD_NORMAL_TEMPERATURE_WAREHOUSING_BILL_PAY, bill.getId());
    }

    /**
     * 移除支付锁
     *
     * @param bills 账单集
     */
    private void removePayingLock(List<NormalTemperatureWarehousingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 账单支付校验
     *
     * @param bills 账单集
     */
    private void checkPayingBill(List<NormalTemperatureWarehousingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            throw new BusinessException("账单列表为空");
        }

        if (bills.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 账单
     *
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    protected OrderVo newPayOrder(List<NormalTemperatureWarehousingBillVo> bills, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.NORMAL_TEMPERATURE_WAREHOUSING);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_NORMAL_TEMPERATURE_WAREHOUSING_BILL, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     *
     * @param bill 账单
     * @return
     */
    private OrderItemVo newPayOrderItem(NormalTemperatureWarehousingBillVo bill) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(buildBillTitle(bill));
        result.setQuantity(1);
        result.setPrice(bill.getPaidStillAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        List<NormalTemperatureWarehousingBillItemVo> items = bill.getItems();
        List<OrderItemDetailVo> list = new ArrayList<>(items.size());
        for (NormalTemperatureWarehousingBillItemVo item : items) {
            OrderItemDetailVo itemDetail = newPayOrderItemDetail(item);
            list.add(itemDetail);
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 子账单明细
     *
     * @param item 子账单
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(NormalTemperatureWarehousingBillItemVo item) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(item.getFeeId());
        result.setFeeName(item.getFeeName());
        result.setQuantity(1);
        result.setPrice(item.getAmount());
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 账单标题
     *
     * @param bill 账单
     * @return
     */
    private String buildBillTitle(NormalTemperatureWarehousingBillVo bill) {
        return String.format(SenoxConst.TITLE_NORMAL_TEMPERATURE_WAREHOUSING_BILL, bill.getMerchantName(), buildBillYearMonth(bill.getBillYear(), bill.getBillMonth()));
    }

    /**
     * 账单年月
     *
     * @param billYear  账单年
     * @param billMonth 账单月
     * @return
     */
    private String buildBillYearMonth(Integer billYear, Integer billMonth) {
        return billYear + StringUtils.fixLength(String.valueOf(billMonth), 2, '0');
    }

    /**
     * 干仓月度账单通知
     *
     * @param year  年
     * @param month 月
     */
    public void notifyBill(Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0) || !WrapperClassUtils.biggerThanInt(month, 0)) {
            return;
        }
        billComponent.notifyBill(year, month);
    }

    private void notifyBillStatus(BillPayRequestVo payRequest, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(payRequest.getBillIds());
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPayWay(payRequest.getPayWay().getValue());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(payRequest.getTollMan());
        billComponent.updateBillStatus(billPaid);
    }

    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {

    }
}
