package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import com.senox.web.component.LogisticComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 14:39
 */
@Service
@RequiredArgsConstructor
public class LogisticOrderService {

    private final LogisticComponent logisticComponent;

    /**
     * 添加物流单
     * @param order
     * @return
     */
    public Long addLogisticOrder(LogisticOrderVo order) {
        return logisticComponent.addLogisticOrder(order);
    }

    /**
     * 更新物流单
     * @param order
     */
    public void updateLogisticOrder(LogisticOrderVo order) {
        if (!WrapperClassUtils.biggerThanLong(order.getProductOrderId(), 0L)) {
            return;
        }

        logisticComponent.updateLogisticOrder(order);
    }

    /**
     * 批量保存物流单
     * @param orderList
     * @param overwrite
     */
    public void saveLogisticOrderBatch(List<LogisticOrderVo> orderList, Boolean overwrite) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        logisticComponent.saveLogisticOrderBatch(orderList, overwrite);
    }

    /**
     * 批量更新物流单信息
     * @param editBatch
     */
    public void editLogisticOrderBatch(LogisticOrderEditBatchVo editBatch) {
        if (CollectionUtils.isEmpty(editBatch.getProductOrderIds())) {
            return;
        }

        logisticComponent.editLogisticOrderBatch(editBatch);
    }

    /**
     * 删除物流订单
     * @param orderIds
     */
    public void deleteLogisticOrder(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        logisticComponent.deleteLogisticOrder(orderIds);
    }

    /**
     * 计算发货订单折扣率
     * @param search
     * @return
     */
    public BigDecimal calShipOrderDiscount(ShipOrderDiscountSearchVo search) {
        if (search.getShipDate() == null || StringUtils.isBlank(search.getOrderNo())) {
            return BigDecimal.ONE;
        }

        return logisticComponent.calShipDiscount(search);
    }

    /**
     * 获取物流单详情
     * @param id
     * @return
     */
    public LogisticOrderVo findLogisticOrderById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? logisticComponent.findLogisticOrderById(id) : null;
    }

    /**
     * 物流单合计
     * @param search
     * @return
     */
    public LogisticOrderVo sumLogisticOrder(LogisticOrderSearchVo search) {
        return logisticComponent.sumLogisticOrder(search);
    }

    /**
     * 物流单列表（无分页）
     * @param search
     * @return
     */
    public List<LogisticOrderVo> listLogisticOrder(LogisticOrderSearchVo search) {
        return logisticComponent.listLogisticOrder(search);
    }

    /**
     * 物流单列表
     * @param search
     * @return
     */
    public PageResult<LogisticOrderVo> listLogisticOrderPage(LogisticOrderSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return logisticComponent.listLogisticOrderPage(search);
    }
}
