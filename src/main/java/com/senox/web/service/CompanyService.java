package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.CompanyVo;
import com.senox.web.component.CompanyComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:51
 */
@Service
public class CompanyService {

    @Autowired
    private CompanyComponent companyComponent;

    /**
     * 添加企业
     * @param company
     * @return
     */
    public Long addCompany(CompanyVo company) {
        if (StringUtils.isBlank(company.getCompanyName())) {
            return 0L;
        }
        return companyComponent.addCompany(company);
    }

    /**
     * 更新企业
     * @param company
     */
    public void updateCompany(CompanyVo company) {
        if (!WrapperClassUtils.biggerThanLong(company.getId(), 0L)) {
            return;
        }

        companyComponent.updateCompany(company);
    }

    /**
     * 删除企业
     * @param id
     */
    public void deleteCompany(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        CompanyVo company = new CompanyVo();
        company.setId(id);
        company.setDisabled(Boolean.TRUE);
        updateCompany(company);
    }

    /**
     * 根据id查找企业
     * @param id
     * @return
     */
    public CompanyVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return companyComponent.findById(id);
    }

    /**
     * 企业列表
     * @return
     */
    public List<CompanyVo> listCompany() {
        return companyComponent.listCompany();
    }
}
