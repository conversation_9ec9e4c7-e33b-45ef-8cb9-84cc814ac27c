package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyReadingsComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 9:19
 */
@RequiredArgsConstructor
@Service
public class RealtyReadingsService {

    private final RealtyReadingsComponent readingsComponent;


    /**
     * 批量添加水电数据
     * @param weBatchReadings
     */
    public void batchAddWeReadings(RealtyWeBatchVo weBatchReadings) {
        if (CollectionUtils.isEmpty(weBatchReadings.getData())) {
            return;
        }
        readingsComponent.batchAddWeReadings(weBatchReadings);
    }

    /**
     * 更新水电数据
     * @param we
     */
    public void updateWeReadings(RealtyWeVo we) {
        if (!WrapperClassUtils.biggerThanLong(we.getId(), 0L)) {
            return;
        }
        readingsComponent.updateWeReadings(we);
    }

    /**
     * 删除水电数据
     * @param ids
     */
    public void deleteWeReadings(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        readingsComponent.deleteWeReadings(ids);
    }

    /**
     * 重置月水电数据
     * @param month
     */
    public void resetWeReadings(BillMonthVo month) {
        readingsComponent.resetWeReadings(month);
    }

    /**
     * 水电数据是否导入
     * @param month
     * @return
     */
    public boolean checkWeReadings(BillMonthVo month) {
        return readingsComponent.checkWeReadings(month);
    }

    /**
     * 根据 id 查找水电数据
     * @param id
     * @return
     */
    public RealtyWeVo findWeReadingsById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? readingsComponent.findWeReadingsById(id) : null;
    }

    /**
     * 水电数据列表
     * @param search
     * @return
     */
    public RealtyWePageResult<RealtyWeVo> listReadingsPage(RealtyWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyWePageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return readingsComponent.listWeReadingsPage(search);
    }
}
