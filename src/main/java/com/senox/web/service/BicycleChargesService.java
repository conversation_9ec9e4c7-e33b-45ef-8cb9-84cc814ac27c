package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import com.senox.web.component.BicycleChargesComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-19
 */
@RequiredArgsConstructor
@Service
public class BicycleChargesService {
    private final BicycleChargesComponent chargesComponent;

    /**
     * 添加收费标准
     *
     * @param chargesVo 收费标准
     */
    public void addCharges(BicycleChargesVo chargesVo) {
        chargesComponent.addCharges(chargesVo);
    }

    /**
     * 根据id查询收费标准
     *
     * @param chargesId 收费标准id
     */
    public BicycleChargesVo getChargesById(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return null;
        }
        return chargesComponent.getChargesById(chargesId);
    }

    /**
     * 删除收费标准
     *
     * @param chargesId 收费标准id
     */
    public void deleteCharges(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return;
        }
        chargesComponent.deleteCharges(chargesId);
    }


    /**
     * 修改收费标准
     *
     * @param chargesVo 收费标准
     */
    public void updateCharges(BicycleChargesVo chargesVo) {
        chargesComponent.updateCharges(chargesVo);
    }

    /**
     * 收费标准分页列表
     *
     * @param searchVo 查询参数
     * @return 分页结果
     */
    public PageResult<BicycleChargesVo> chargesListPage(BicycleChargesSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return chargesComponent.chargesListPage(searchVo);
    }

    /**
     * 添加收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    public void addChargesDetail(BicycleChargesDetailVo chargesDetailVo) {
        chargesComponent.addChargesDetail(chargesDetailVo);
    }

    /**
     * 修改收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    public void updateChargesDetail(BicycleChargesDetailVo chargesDetailVo) {
        chargesComponent.updateChargesDetail(chargesDetailVo);
    }

    /**
     * 删除收费标准明细
     *
     * @param chargesDetailIds 收费标准明细id列表
     */
    public void deleteChargesDetail(List<Long> chargesDetailIds) {
        if (CollectionUtils.isEmpty(chargesDetailIds)) {
            return;
        }
        chargesComponent.deleteChargesDetail(chargesDetailIds);
    }

    /**
     * 根据收费标准查询明细
     *
     * @param chargesId 收费标准id
     * @return 查询到的明细列表
     */
    public List<BicycleChargesDetailVo> listDetailByCharges(Long chargesId) {
        return chargesComponent.listDetailByCharges(chargesId);
    }

}
