package com.senox.web.service;


import com.senox.car.vo.LotteryParkingExitSupplementVo;
import com.senox.car.vo.LotteryParkingRecordVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.component.LotteryParkingExitComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LotteryParkingExitService {
    private final LotteryParkingExitComponent lotteryParkingExitComponent;

    /**
     * 离场不支付
     * @param id
     * @return
     */
    public LotteryParkingRecordVo exitNotPay(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException("车位号id不能为空");
        }
        return lotteryParkingExitComponent.exitNotPay(id);
    }

    /**
     * 离场补录
     *
     * @param lotteryParkingExitSupplementVo 补录参数
     */
    public void exitSupplement(LotteryParkingExitSupplementVo lotteryParkingExitSupplementVo) {
        if (null == lotteryParkingExitSupplementVo
                || !WrapperClassUtils.biggerThanLong(lotteryParkingExitSupplementVo.getRecordId(), 0)
                || null == lotteryParkingExitSupplementVo.getExitTime()) {
            throw new InvalidParameterException();
        }
        lotteryParkingExitComponent.exitSupplement(lotteryParkingExitSupplementVo);
    }

    /**
     * 离场支付
     *
     * @param parkingRecordVo 车位记录
     * @return 订单下单结果
     */
    public OrderResultVo exitPay(LotteryParkingRecordVo parkingRecordVo) {
        if (null == parkingRecordVo || StringUtils.isBlank(parkingRecordVo.getCarNo())) {
            throw new InvalidParameterException("无效的记录");
        }
        return lotteryParkingExitComponent.exitPay(parkingRecordVo);
    }

    /**
     * 离场并支付
     * @param recordVo
     * @return
     */
    public LotteryParkingRecordVo exitAndPay(LotteryParkingRecordVo recordVo) {
        if (recordVo == null || !WrapperClassUtils.biggerThanLong(recordVo.getParkingId(), 0)) {
            throw new InvalidParameterException();
        }
        return lotteryParkingExitComponent.exitAndPay(recordVo);
    }
}
