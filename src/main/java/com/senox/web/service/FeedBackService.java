package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import com.senox.web.component.FeedBackComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/6/1 14:00
 */
@Service
public class FeedBackService {

    @Autowired
    private FeedBackComponent feedBackComponent;


    /**
     * 获取建议反馈及回复信息
     * @param id
     * @param isDetail
     * @return
     */
    public FeedBackVo findFeedBackById(Long id, Boolean isDetail){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return feedBackComponent.findFeedBackById(id, isDetail);
    }

    /**
     * 建议反馈列表
     * @param search
     * @return
     */
    public PageResult<FeedBackVo> listFeedBack(FeedBackSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return feedBackComponent.listFeedBack(search);
    }

    /**
     * 添加建议回复
     * @param feedBackReplyVo
     * @return
     */
    public Long addFeedBackReply(FeedBackReplyVo feedBackReplyVo){
        return feedBackComponent.addFeedBackReply(feedBackReplyVo);
    }

    /**
     * 获取建议回复
     * @param id
     * @return
     */
    public FeedBackReplyVo findFeedBackReplyById(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return feedBackComponent.findFeedBackReplyById(id);
    }

}
