package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.pm.vo.RefundOrderFeeVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.vo.*;
import com.senox.web.component.RealtyComponent;
import com.senox.web.component.RealtyDictionaryComponent;
import com.senox.web.component.WechatComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.MixPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/6/11 15:34
 */
@Service
@RequiredArgsConstructor
public class RealtyBillService extends BillService {

    private static final Logger logger = LoggerFactory.getLogger(RealtyBillService.class);

    private final RealtyComponent realtyComponent;
    private final RealtyDictionaryComponent dictionaryComponent;
    private final WechatComponent wechatComponent;

    @Value("${senox.fee.penaltyId:8}")
    private Long penaltyFeeId;

    /**
     * 生成应收账单
     * @param month
     */
    public void generateBill(BillMonthVo month) {
        realtyComponent.generateBill(month);
    }

    /**
     * 重新生成应收账单
     * @param month
     */
    public void regenerateBill(BillMonthVo month) {
        realtyComponent.regenerateBill(month);
    }

    /**
     * 同步账单水电数据
     * @param billMonth
     */
    public void syncBillWeData(BillMonthVo billMonth) {
        realtyComponent.syncBillWeData(billMonth);
    }

    /**
     * 更新应收账单
     *
     * @param bill
     */
    public void updateBill(RealtyBillVo bill) {
        if (WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            realtyComponent.updateRealtyBill(bill);
        }
    }

    /**
     * 撤销免滞纳金
     * @param billIds
     */
    private void revokeBillPenaltyIgnore(List<Long> billIds) {
        ignoreBillPenalty(billIds, null, false);
    }

    /**
     * 减免滞纳金
     * @param billIds
     * @param ignoreAmount
     */
    private void ignoreBillPenalty(List<Long> billIds, BigDecimal ignoreAmount) {
        ignoreBillPenalty(billIds, ignoreAmount, true);
    }

    /**
     * 免滞纳金
     * @param billIds
     * @param ignoreAmount
     */
    public void ignoreBillPenalty(List<Long> billIds, BigDecimal ignoreAmount, boolean isIgnore) {
        if (CollectionUtils.isEmpty(billIds) || (isIgnore && !DecimalUtils.isPositive(ignoreAmount))) {
            return;
        }

        BillPenaltyIgnoreVo penaltyIgnore = new BillPenaltyIgnoreVo();
        penaltyIgnore.setBillIds(billIds);
        penaltyIgnore.setPenaltyIgnoreAmount(ignoreAmount);
        penaltyIgnore.setPenaltyIgnore(isIgnore);
        realtyComponent.ignoreBillPenalty(penaltyIgnore);
    }

    /**
     * 发票
     * @param batchBills
     */
    public void receiptBill(RealtyBillBatchVo batchBills) {
        if (!WrapperClassUtils.biggerThanLong(batchBills.getId(), 0L) && CollectionUtils.isEmpty(batchBills.getIds())) {
            return;
        }
        if (batchBills.getReceipt() == null) {
            return;
        }

        realtyComponent.receiptBill(batchBills);
    }

    /**
     * 保存应收账单备注
     *
     * @param remark
     */
    public void saveBillRemark(RealtyBillRemarkVo remark) {
        if (WrapperClassUtils.biggerThanLong(remark.getId(), 0L)) {
            realtyComponent.saveRealtyBillRemark(remark);
        }
    }

    /**
     * 保存应收账单票据号
     *
     * @param serial
     */
    public void saveBillSerial(RealtyBillSerialVo serial) {
        if (WrapperClassUtils.biggerThanLong(serial.getBillId(), 0L)) {
            realtyComponent.saveRealtyBillSerial(serial);
        }
    }

    /**
     * 下发应收账单
     *
     * @param sendVo
     */
    public void sendBill(RealtyBillSendVo sendVo) {
        boolean isEmptyBillYearMonth = sendVo.getBillYear() == null || sendVo.getBillMonth() == null;
        if (isEmptyBillYearMonth && CollectionUtils.isEmpty(sendVo.getBillIds())) {
            logger.warn("未明下发的应收账单");
            return;
        }
        realtyComponent.sendBill(sendVo);
    }

    /**
     * 撤销账单
     * @param cancel
     */
    public void cancelBill(BillCancelVo cancel) {
        realtyComponent.cancelBill(cancel);
    }


    /**
     * 删除应收账单
     * @param billId
     */
    public void deleteBill(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            return;
        }
        realtyComponent.deleteBill(billId);
    }

    /**
     * 查找物业月账单列表
     * @param monthVo
     * @return
     */
    public List<RealtyBillVo> monthlyBillList(BillMonthVo monthVo) {
        return realtyComponent.monthlyBillList(monthVo);
    }

    /**
     * 新增物业账单
     * @param monthVo
     */
    public void saveBill(BillMonthVo monthVo) {
        realtyComponent.saveBill(monthVo);
    }

    /**
     * 支付应收账单
     *
     * @param payRequest
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 免滞纳金
        ignoreBillPenalty(payRequest.getIgnorePenaltyIds(), payRequest.getIgnorePenaltyAmount());

        // 应收账单
        List<RealtyBillVo> billList = realtyComponent.listBillById(payRequest.getBillIds(), true);
        // 校验待支付账单
        checkPayingBill(billList);

        List<FeeVo> fees = dictionaryComponent.listFee(null);
        OrderResultVo result = null;
        try {
            // 账单标题
            OrderVo order = newPayOrder(billList, newPayAmountRequest(payRequest), fees, payRequest.getRequestIp());
            if (billList.size() == 1) {
                RealtyBillVo bill = billList.get(0);
                order.setTitle(buildBillTitle(bill.getBillYear(), bill.getBillMonth(), bill.getRealtyName()));
            } else {
                order.setTitle(String.format(SenoxConst.TITLE_REALTY_BILL_ORDER_OFFLINE, LocalDate.now()));
            }

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            logger.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));
            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            logger.info("pay bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));
        } catch (Exception e) {
            // 手工回滚免滞纳金
            if (!CollectionUtils.isEmpty(payRequest.getBillIds())) {
                revokeBillPenaltyIgnore(payRequest.getBillIds());
            }

            logger.warn("支付失败 " + JsonUtils.object2Json(payRequest), e);
            throw new BusinessException("支付失败");
        } finally {
            removeBillPayingLock(billList);
        }
        logger.info("finish pay bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 多种支付方式混合支付
     *
     * @param payRequest
     * @return
     */
    public List<OrderResultVo> mixPayBill(MixPayRequestVo payRequest) {
        // 免滞纳金
        if (DecimalUtils.isPositive(payRequest.getPenaltyIgnoreAmount())) {
            ignoreBillPenalty(Collections.singletonList(payRequest.getBillId()), payRequest.getPenaltyIgnoreAmount());
        }

        // 应收账单
        RealtyBillVo bill = realtyComponent.findBillById(payRequest.getBillId());
        // 校验待支付账单
        checkPayingBill(bill);
        // 支付枷锁
        tryLockPayingBill(bill);

        // 支付金额判断
        BigDecimal payTotal = payRequest.getDetails().stream().map(PayAmountVo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!DecimalUtils.equals(payTotal, bill.getPaidStillAmount())) {
            throw new BusinessException("支付金额不匹配");
        }

        List<OrderResultVo> resultList = new ArrayList<>(payRequest.getDetails().size());
        final List<FeeVo> fees = dictionaryComponent.listFee(null);
        try {
            // 订单
            List<OrderVo> orders = payRequest.getDetails().stream()
                    .map(x -> newPayOrder(bill, x, fees, payRequest.getRequestIp()))
                    .collect(Collectors.toList());

            // 下单
            for (OrderVo order : orders) {
                OrderResultVo result = orderComponent.addOrder(order);
                if (result == null) {
                    throw new BusinessException("下单失败");
                }

                logger.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));
                resultList.add(result);
            }

            // 更新远程
            if (!resultList.isEmpty() && resultList.stream().allMatch(x -> WrapperClassUtils.biggerThanLong(x.getOrderId(), 0L))) {
                for (OrderResultVo orderResult : resultList) {
                    // 更新账单结果
                    notifyBillStatus(Collections.singletonList(bill.getId()), payRequest.getTollMan(), orderResult);
                }
            }
        } catch (Exception e) {
            // 手工回滚免滞纳金
            if (DecimalUtils.isPositive(payRequest.getPenaltyIgnoreAmount())) {
                revokeBillPenaltyIgnore(Collections.singletonList(payRequest.getBillId()));
            }

            logger.warn("混合支付失败 " + JsonUtils.object2Json(payRequest), e);
            throw new BusinessException("混合支付失败");
        } finally {
            removeBillPayingLock(Collections.singletonList(bill));
        }

        return resultList;
    }


    /**
     * 订单部分退费
     * @param refundOrderFee
     * @return
     */
    public OrderResultVo refundBillOrderPart(RefundOrderFeeVo refundOrderFee) {
        lockRefundOrder(refundOrderFee.getOrderId());

        OrderResultVo result = null;
        try {
            // 退费下单
            result = orderComponent.refundOrderPart(refundOrderFee);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            logger.info("订单部分退费下单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                OrderVo order = orderComponent.findWithDetail(result.getOrderId());
                if (order == null || CollectionUtils.isEmpty(order.getItems())) {
                    throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到退费订单明细");
                }
                notifyBillStatus(order.getItems().stream().map(OrderItemVo::getProductId).collect(Collectors.toList()), refundOrderFee.getTollMan(), result);
            }

        } finally {
            removeRefundOrderLock(refundOrderFee.getOrderId());
        }
        return result;
    }

    /**
     * 根据id获取应收账单明细
     *
     * @param billId
     * @return
     */
    public RealtyBillVo findBillById(Long billId) {
        return !WrapperClassUtils.biggerThanLong(billId, 0L) ? null : realtyComponent.findBillById(billId);
    }

    /**
     * 应收账单合计
     * @param search
     * @return
     */
    public RealtyBillVo sumBillDetail(RealtyBillSearchVo search) {
        return realtyComponent.sumBillDetail(search);
    }

    public PageResult<RealtyBillVo> listBillDetailPage(RealtyBillSearchVo search) {
        return realtyComponent.listBillDetailPage(search);
    }

    /**
     * 应收账单列表
     *
     * @param search
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listBillPage(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.listBillPage(search);
    }

    /**
     * 物业发票账单列表
     * @param search
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listReceiptBill(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.listReceiptBill(search);
    }

    /**
     * 应收账单明细列表
     *
     * @param searchVo
     * @return
     */
    public RealtyBillPageResult<RealtyBillVo> listBillPageWithDetail(RealtyBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return realtyComponent.listBillPageWitDetail(searchVo);
    }

    /**
     * 物业月度账单微信通知
     *
     * @param year
     * @param month
     */
    public void notifyWechatUserMonthlyBill(Integer year, Integer month) {
        wechatComponent.notifyRealtyMonthlyBill(year, month);
    }

    /**
     * 应收账单水电明细列表
     *
     * @param billId
     * @return
     */
    public List<RealtyBillWeVo> listRealtyBillWeDetail(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new BusinessException("参数无效");
        }
        return realtyComponent.listRealtyBillWeDetail(billId);
    }


    /**
     * 应收账单水电列表
     *
     * @return
     */
    public PageResult<RealtyBillWeVo> listRealtyWeBill(RealtyBillWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return realtyComponent.listRealtyWeBill(search);
    }

    /**
     * 待支付账单校验
     *
     * @param billList
     */
    private void checkPayingBill(List<RealtyBillVo> billList) {
        if (CollectionUtils.isEmpty(billList)) {
            throw new BusinessException("找不到的账单");
        }

        // 账单状态判断
        billList.forEach(this::checkPayingBill);

        // 支付加锁
        List<String> lockKeys = new ArrayList<>(billList.size());
        billList.forEach(x -> {
            if (!lockKeys.contains(buildBillPayingLockKey(x))) {
                lockKeys.add(tryLockPayingBill(x));
            }
        });
    }

    /**
     * 校验账单
     *
     * @param bill
     */
    private void checkPayingBill(RealtyBillVo bill) {
        if (bill.getStatus() == BillStatus.PAID.getStatus()) {
            throw new BusinessException("存在已缴费的账单");
        }

        if (BooleanUtils.isTrue(bill.getOffer()) && !BooleanUtils.isTrue(bill.getBack())) {
            throw new BusinessException("存在已报盘的账单");
        }
    }

    /**
     * 支付锁
     *
     * @param bill
     */
    private String tryLockPayingBill(RealtyBillVo bill) {
        String lockKey = buildBillPayingLockKey(bill);
        if (!RedisUtils.lock(lockKey, SenoxConst.Cache.TTL_60S)) {
            throw new BusinessException("账单缴费操作太频繁，请稍后尝试");
        }
        return lockKey;
    }

    /**
     * 支付订单
     *
     * @param bill
     * @param payAmount
     * @param fees
     * @param ip
     * @return
     */
    private OrderVo newPayOrder(RealtyBillVo bill, PayAmountVo payAmount, List<FeeVo> fees, String ip) {
        return newPayOrder(Collections.singletonList(bill), payAmount, fees, ip);
    }

    /**
     * 支付订单
     *
     * @param bills
     * @param payAmount
     * @param fees
     * @param ip
     * @return
     */
    private OrderVo newPayOrder(List<RealtyBillVo> bills, PayAmountVo payAmount, List<FeeVo> fees, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.REALTY);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);
        if (bills.size() == 1) {
            result.setTitle(buildBillTitle(bills.get(0).getBillYear(), bills.get(0).getBillMonth(), bills.get(0).getRealtyName()));
        }

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(x -> this.newPayOrderItem(x, payAmount, fees)).collect(Collectors.toList()));
        return result;
    }

    /**
     * 账单明细
     *
     * @param bill
     * @param amount
     * @param fees
     * @return
     */
    private OrderItemVo newPayOrderItem(RealtyBillVo bill, PayAmountVo amount, List<FeeVo> fees) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(buildBillTitle(bill.getBillYear(), bill.getBillMonth(), bill.getRealtyName()));
        result.setQuantity(1);
        result.setPrice(amount.getAmount() == null ? bill.getPaidStillAmount() : amount.getAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);

        // 支付明细
        List<OrderItemDetailVo> list = new ArrayList<>(5);
        list.add(newPayOrderItemDetail(bill, fees, RealtyFee.MANAGE, amount));
        list.add(newPayOrderItemDetail(bill, fees, RealtyFee.RENT, amount));
        list.add(newPayOrderItemDetail(bill, fees, RealtyFee.WATER, amount));
        list.add(newPayOrderItemDetail(bill, fees, RealtyFee.ELECTRIC, amount));

        // 滞纳金
        OrderItemDetailVo penaltyItem = newPayOrderItemPenalty(bill, amount);
        if (penaltyItem != null) {
            list.add(penaltyItem);
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 应收账单转账单费项明细
     *
     * @param bill
     * @param fees
     * @param fee
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(RealtyBillVo bill, List<FeeVo> fees, RealtyFee fee, PayAmountVo amount) {
        FeeVo item = getRealtyFee(fees, fee);
        if (item == null) {
            logger.warn("找不到费项 {}", fee);
            throw new BusinessException("找不到费项");
        }

        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(item.getId());
        result.setFeeName(fee.getName());
        result.setQuantity(1);

        // 费用金额
        BigDecimal feeAmount = bill.getFeeAmount(fee);
        feeAmount = feeAmount == null ? BigDecimal.ZERO : feeAmount;
        if (amount.getAmount() == null || amount.getAmount().compareTo(feeAmount) >= 0) {
            result.setPrice(feeAmount);
            bill.setFeeAmount(fee, BigDecimal.ZERO);
            if (amount.getAmount() != null) {
                amount.setAmount(DecimalUtils.subtract(amount.getAmount(), feeAmount));
            }

        } else {
            result.setPrice(amount.getAmount());
            bill.setFeeAmount(fee, DecimalUtils.subtract(feeAmount, amount.getAmount()));
            amount.setAmount(BigDecimal.ZERO);
        }

        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 滞纳金费项
     *
     * @param bill
     * @param amount
     * @return
     */
    private OrderItemDetailVo newPayOrderItemPenalty(RealtyBillVo bill, PayAmountVo amount) {
        if (!DecimalUtils.isPositive(bill.getPenaltyAmount())) {
            return null;
        }

        // 实收滞纳金
        BigDecimal penaltyShouldPaid = DecimalUtils.subtract(bill.getPenaltyAmount(), bill.getPenaltyIgnoreAmount());
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(penaltyFeeId);
        result.setFeeName(RealtyFee.PENALTY.getName());
        result.setFree(DecimalUtils.equals(penaltyShouldPaid, BigDecimal.ZERO));
        result.setQuantity(1);

        // 费用
        if (amount.getAmount() == null || result.isFree() || amount.getAmount().compareTo(bill.getPenaltyAmount()) > 0) {
            result.setPrice(penaltyShouldPaid);
            bill.setPenaltyAmount(BigDecimal.ZERO);
            if (amount.getAmount() != null && !result.isFree()) {
                amount.setAmount(DecimalUtils.subtract(amount.getAmount(), penaltyShouldPaid));
            }
        } else {
            result.setPrice(amount.getAmount());
            bill.setPenaltyAmount(DecimalUtils.subtract(penaltyShouldPaid, amount.getAmount()));
            amount.setAmount(BigDecimal.ZERO);
        }
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 账单标题
     *
     * @param year
     * @param month
     * @param realtyName
     * @return
     */
    private String buildBillTitle(Integer year, Integer month, String realtyName) {
        return String.format(SenoxConst.TITLE_BILL_ORDER, realtyName, buildBillYearMonth(year, month));
    }

    /**
     * 账单年月
     *
     * @param billYear
     * @param billMonth
     * @return
     */
    private String buildBillYearMonth(Integer billYear, Integer billMonth) {
        return billYear + StringUtils.fixLength(String.valueOf(billMonth), 2, '0');
    }

    /**
     * 移除账单锁
     *
     * @param bills
     */
    private void removeBillPayingLock(List<RealtyBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildBillPayingLockKey(x)));
    }

    /**
     * 构建账单支付分布式锁
     *
     * @param bill
     * @return
     */
    private String buildBillPayingLockKey(RealtyBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_REALTY_BILL_PAY, bill.getContractNo(),
                bill.getBillYear() + "-" + bill.getBillMonth());
    }

    /**
     * 获取费项
     *
     * @param fees
     * @param fee
     * @return
     */
    private FeeVo getRealtyFee(List<FeeVo> fees, RealtyFee fee) {
        Optional<FeeVo> op = fees.stream().filter(x -> Objects.equals(x.getAlias(), fee.name())).findFirst();
        return op.orElse(null);
    }

    /**
     * 通知更新账单结果
     *
     * @param billIds
     * @param tollMan
     * @param order
     */
    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(billIds);
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(tollMan);
        billPaid.setRefund(order.getAmount() == null || BigDecimal.ZERO.compareTo(order.getAmount()) > 0);
        realtyComponent.updateBillStatus(billPaid);
    }

}
