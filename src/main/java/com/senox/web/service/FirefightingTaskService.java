package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingInspectPropertyTaskVo;
import com.senox.realty.vo.FirefightingInspectTaskItemSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import com.senox.realty.vo.FirefightingTaskItemDropVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/15 15:41
 */
@Service
@RequiredArgsConstructor
public class FirefightingTaskService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加巡检任务
     * @param task
     * @return
     */
    public Long addInspectTask(FirefightingInspectTaskVo task) {
        return firefightingComponent.addInspectTask(task);
    }

    /**
     * 更新巡检任务
     * @param task
     */
    public void updateInspectTask(FirefightingInspectTaskVo task) {
        if (!WrapperClassUtils.biggerThanLong(task.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateInspectTask(task);
    }

    /**
     * 获取巡检任务详情
     * @param id
     * @return
     */
    public FirefightingInspectTaskVo findInspectTaskById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findInspectTaskById(id) : null;
    }

    /**
     * 巡检任务列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingInspectTaskVo> listInspectTaskPage(FirefightingInspectTaskSearchVo search) {
        return firefightingComponent.listInspectTaskPage(search);
    }

    /**
     * 删除巡检子任务
     * @param drop
     */
    public void deleteInspectTaskItem(FirefightingTaskItemDropVo drop) {
        firefightingComponent.deleteInspectTaskItem(drop);
    }

    /**
     * 巡检子任务页
     * @param search
     * @return
     */
    public PageResult<FirefightingInspectPropertyTaskVo> listTaskItemPage(FirefightingInspectTaskItemSearchVo search) {
        return firefightingComponent.listTaskItemPage(search);
    }
}
