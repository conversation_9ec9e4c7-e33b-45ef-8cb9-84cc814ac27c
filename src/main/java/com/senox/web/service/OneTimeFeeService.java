package com.senox.web.service;

import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.pm.vo.RefundOrderVo;
import com.senox.realty.vo.*;
import com.senox.web.component.AdminComponent;
import com.senox.web.component.OneTimeFeeComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/12 15:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OneTimeFeeService extends BillService {

    private final AdminComponent adminComponent;
    private final OneTimeFeeComponent oneTimeFeeComponent;

    @Value("${senox.user.role.oneTimeFeeAdmin:1}")
    private Long oneTimeFeeAdminRole;

    /**
     * 添加一次性收入费项
     * @param fee
     * @return
     */
    public Long addFee(OneTimeFeeVo fee) {
        if (StringUtils.isBlank(fee.getName())) {
            throw new InvalidParameterException("无效的一次性收入费项名");
        }
        return oneTimeFeeComponent.addOneTimeFee(fee);
    }

    /**
     * 更新一次性收入费项
     * @param fee
     */
    public void updateFee(OneTimeFeeVo fee) {
        if (!WrapperClassUtils.biggerThanLong(fee.getId(), 0L)) {
            throw new InvalidParameterException("无效的一次性收入费项名");
        }
        oneTimeFeeComponent.updateOneTimeFee(fee);
    }

    /**
     * 删除一次性收入费项
     * @param id
     */
    public void deleteFee(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
           return;
        }

        OneTimeFeeVo fee = new OneTimeFeeVo();
        fee.setId(id);
        fee.setDisabled(Boolean.TRUE);
        updateFee(fee);
    }

    /**
     * 根据id查找一次性收入费项
     * @param id
     * @return
     */
    public OneTimeFeeVo findFeeById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return oneTimeFeeComponent.findOneTimeFeeById(id);
    }

    /**
     * 一次性收费项目列表
     * @param search
     * @return
     */
    public PageResult<OneTimeFeeVo> listFee(OneTimeFeeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return oneTimeFeeComponent.listOneTimeFee(search);
    }

    /**
     * 用户授权的一次性收入项目
     * @param adminUser
     * @return
     */
    public List<OneTimeFeeVo> listUserAuthorizedFee(AdminUserDto adminUser) {
        if (adminUser == null) {
            return Collections.emptyList();
        }
        if (!isUserOneTimeFeeAdmin(adminUser.getUserId())
                && !WrapperClassUtils.biggerThanLong(adminUser.getDepartmentId(), 0L)) {
            return Collections.emptyList();
        }
        List<Long> departments = adminComponent.listUserDepartment(adminUser.getUserId());
        return oneTimeFeeComponent.listDepartmentOneTimeFee(isUserOneTimeFeeAdmin(adminUser.getUserId()) ? null : departments);
    }

    /**
     * 添加一次性收费账单
     * @param bill
     * @return
     */
    public Long addFeeBill(OneTimeFeeBillVo bill) {
        return oneTimeFeeComponent.addOneTimeFeeBill(bill);
    }

    /**
     * 更新一次性收费账单
     * @param bill
     */
    public void updateFeeBill(OneTimeFeeBillVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }
        oneTimeFeeComponent.updateOneTimeFeeBill(bill);
    }

    /**
     * 更新一次性收费账单票据号
     * @param serial
     */
    public void updateFeeBillSerial(TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L) || StringUtils.isBlank(serial.getSerial())) {
            return;
        }
        oneTimeFeeComponent.updateOneTimeFeeBillSerial(serial);
    }

    /**
     * 删除一次性收费账单
     * @param id
     */
    public void deleteFeeBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        OneTimeFeeBillVo bill = new OneTimeFeeBillVo();
        bill.setId(id);
        bill.setDisabled(Boolean.TRUE);
        updateFeeBill(bill);
    }

    /**
     * 一次性收费账单收费+
     * @param id
     * @param toll
     */
    public void payFeeBill(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        oneTimeFeeComponent.payOneTimeFeeBill(id, toll);
    }

    /**
     * 支付一次性收费账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 账单
        List<OneTimeFeeBillVo> billList = listByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(billList);

        billList.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_10M));
        OrderResultVo result = null;
        try {
            OrderVo order = newPayOrder(billList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付一次性账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            log.info("pay oneTimeFee bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));

        } finally {
            removeBillPayingLock(billList);
        }

        log.info("finish pay oneTimeFee bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 撤销一次性收费账单收费
     * @param id
     */
    public void revokeFeeBillPayment(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        oneTimeFeeComponent.revokeOneTimeFeeBillPayment(id);
    }

    /**
     * 一次性收费退费
     * @param id
     * @param refundOrder
     */
    public void refundFeeBill(Long id, RefundOrderVo refundOrder) {
        OneTimeFeeBillVo bill = findById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.fromValue(bill.getStatus()) != BillStatus.PAID) {
            throw new BusinessException("订单未支付");
        }

        if (WrapperClassUtils.biggerThanLong(bill.getRemoteOrderId(), 0L)) {
            // 有收费订单时，走退费逻辑
            refundOrder.setOrderId(bill.getRemoteOrderId());
            refundOrder.setProductIds(Collections.singletonList(id));
            refundOrder(refundOrder);
        } else {
            // 无收费订单，走新增订单逻辑
            log.info("一次性收费无入款订单id，不走新增退费订单逻辑 {}", JsonUtils.object2Json(bill));
            refundNoOrderBill(bill, refundOrder);
        }
    }

    /**
     * 撤销一次性收费账单退费
     * @param id
     */
    public void revokeFeeBillRefund(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        oneTimeFeeComponent.revokeOneTimeFeeBillRefund(id);
    }

    /**
     * 根据id获取一次性收费账单信息
     * @param id
     * @return
     */
    public OneTimeFeeBillVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return oneTimeFeeComponent.findOneTimeFeeBillById(id);
    }

    /**
     * 根据id列表获取一次性收费账单信息列表
     * @param ids
     * @return
     */
    private List<OneTimeFeeBillVo> listByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : oneTimeFeeComponent.listOneTimeFeeBillByIds(ids);
    }

    /**
     * 根据id获取一次性收费账单信息及缴费退费信息
     * @param id
     * @return
     */
    public OneTimeFeeBillTradeVo findDetailById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return oneTimeFeeComponent.findOneTimeFeeBillDetailById(id);
    }

    /**
     * 一次性收费账单页
     * @param search
     * @return
     */
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listFeeBill(OneTimeFeeBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return oneTimeFeeComponent.listOneTimeFeeBill(search);
    }

    /**
     * 一次性收费交易列表
     * @param search
     * @return
     */
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listFeeBillTrade(OneTimeFeeBillTradeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return oneTimeFeeComponent.listOneTimeFeeBillTrade(search);
    }

    /**
     * 一次性收费押金
     * @param search
     * @return
     */
    public List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(OneTimeFeeDepositSearchVo search) {
        if (!WrapperClassUtils.biggerThanLong(search.getDepositFee(), 0L)
                || CollectionUtils.isEmpty(search.getCustomers())) {
            return Collections.emptyList();
        }

        return oneTimeFeeComponent.listOneTimeFeeDeposit(search);
    }

    /**
     * 账单状态校验
     * @param list
     */
    private void checkPayingBill(List<OneTimeFeeBillVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 一次性费用账单锁
     *
     * @param bill
     * @return
     */
    private String buildPayLockKey(OneTimeFeeBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_ONE_TIME_FEE_BILL_PAY, bill.getBillNo(),
                bill.getBillYear() + "-" + bill.getBillMonth());
    }

    /**
     * 移除支付账单锁
     * @param bills
     */
    private void removeBillPayingLock(List<OneTimeFeeBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 无支付订单退费
     * @param bill
     * @param refundOrder
     * @return
     */
    private OrderResultVo refundNoOrderBill(OneTimeFeeBillVo bill, RefundOrderVo refundOrder) {
        lockRefundOrder(bill.getId());
        OrderResultVo result = null;
        try {
            PayAmountVo payAmount = new PayAmountVo(PayWay.CASH, DecimalUtils.subtract(BigDecimal.ZERO, bill.getAmount()));
            OrderVo order = newPayOrder(Collections.singletonList(bill), payAmount, refundOrder.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("退费下单失败");
            }
            log.info("一次性账单退费成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(Collections.singletonList(bill.getId()), refundOrder.getTollMan(), result);
            }
            log.info("refund oneTimeFee bill {} finish.", bill.getId());
        } finally {
            removeRefundOrderLock(bill.getId());
        }

        return null;
    }

    /**
     * 构建支付订单
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    private OrderVo newPayOrder(List<OneTimeFeeBillVo> bills, PayAmountVo payAmount, String ip) {
        final boolean isRefund = DecimalUtils.isNegative(payAmount.getAmount());

        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.ONE_TIME_FEE);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(x -> newPayOrderItem(x, isRefund)).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(getOrderTitle(isRefund), LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 构建支付订单明细
     * @param bill
     * @return
     */
    private OrderItemVo newPayOrderItem(OneTimeFeeBillVo bill, boolean isRefund) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(
                String.format(getOrderTitle(isRefund), bill.getFeeName(), StringUtils.buildYearMonthStr(bill.getBillYear(), bill.getBillMonth()))
        );
        result.setQuantity(1);
        result.setPrice(isRefund ? DecimalUtils.subtract(BigDecimal.ZERO, bill.getAmount()) : bill.getAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        result.setDetails(Collections.singletonList(newPayOrderItemDetail(bill, isRefund)));
        return result;
    }

    /**
     * 构建支付订单明细详情
     * @param bill
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(OneTimeFeeBillVo bill, boolean isRefund) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(bill.getFeeId());
        result.setFeeName(bill.getFeeName());
        result.setQuantity(1);
        result.setPrice(isRefund ? DecimalUtils.subtract(BigDecimal.ZERO, bill.getAmount()) : bill.getAmount());
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 通知更新账单结果
     * @param billIds
     * @param tollMan
     * @param orderResult
     */
    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo orderResult) {
        BillPaidVo result = new BillPaidVo();
        result.setBillIds(billIds);
        result.setOrderId(orderResult.getOrderId());
        result.setAmount(orderResult.getAmount());
        result.setPaid(orderResult.getStatus() == OrderStatus.PAID.getStatus());
        result.setPaidTime(orderResult.getOrderTime());
        result.setTollMan(tollMan);
        result.setRefund(orderResult.getAmount() == null || DecimalUtils.isNegative(orderResult.getAmount()));
        oneTimeFeeComponent.updateOneTimeFeeBillStatus(result);
    }

    /**
     * 是否一次性收入管理员
     * @param adminUserId
     * @return
     */
    private boolean isUserOneTimeFeeAdmin(Long adminUserId) {
        if (!WrapperClassUtils.biggerThanLong(adminUserId, 0L)) {
            return false;
        }
        return adminComponent.listUserRole(adminUserId).contains(oneTimeFeeAdminRole);
    }

}
