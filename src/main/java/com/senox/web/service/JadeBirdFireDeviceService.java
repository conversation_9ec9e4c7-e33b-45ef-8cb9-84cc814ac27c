package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.JadeBirdFireDeviceControlRequest;
import com.senox.dm.vo.JadeBirdFireDeviceSearchVo;
import com.senox.dm.vo.JadeBirdFireDeviceVo;
import com.senox.tms.vo.BicycleTotalPageResult;
import com.senox.web.component.JadeBirdFireDeviceComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class JadeBirdFireDeviceService {
    private final JadeBirdFireDeviceComponent fireDeviceComponent;

    /**
     * 添加设备
     *
     * @param fireDevice 消防设备
     */
    public void add(JadeBirdFireDeviceVo fireDevice) {
        fireDeviceComponent.add(fireDevice);
    }

    /**
     * 更新设备
     *
     * @param fireDevice 消防设备
     */
    public void update(JadeBirdFireDeviceVo fireDevice) {
        if (!WrapperClassUtils.biggerThanLong(fireDevice.getId(), 0L)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        fireDeviceComponent.update(fireDevice);
    }

    /**
     * 根据id删除设备
     *
     * @param fireDeviceId 消防设备id
     */
    public void deleteById(Long fireDeviceId) {
        if (!WrapperClassUtils.biggerThanLong(fireDeviceId, 0L)) {
            return;
        }
        fireDeviceComponent.deleteById(fireDeviceId);
    }

    /**
     * 根据psn查找设备
     *
     * @param psn psn
     * @return 设备信息
     */
    public JadeBirdFireDeviceVo findByPsn(String psn) {
        if (StringUtils.isBlank(psn)) {
            return null;
        }
        return fireDeviceComponent.findByPsn(psn);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<JadeBirdFireDeviceVo> pageList(JadeBirdFireDeviceSearchVo search) {
        if (search.getPageSize() < 1) {
            return BicycleTotalPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return fireDeviceComponent.pageList(search);
    }

    /**
     * 设备复位
     *
     * @param fireDeviceControlRequest 消防设备控制请求参数
     */
    public void reset(JadeBirdFireDeviceControlRequest fireDeviceControlRequest) {
        fireDeviceComponent.reset(fireDeviceControlRequest);
    }
}
