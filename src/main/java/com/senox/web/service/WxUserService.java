package com.senox.web.service;

import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import com.senox.web.component.WxUserComponent;
import com.senox.web.constant.SenoxConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7 16:09
 */
@Service
public class WxUserService {

    @Autowired
    private WxUserComponent wxUserComponent;

    /**
     * 设置灰度用户
     * @param userId
     */
    public void setGrayUser(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            return;
        }
        wxUserComponent.setGrayUser(userId);
    }

    /**
     * 取消灰度用户
     * @param userId
     */
    public void cancelGrayUser(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            return;
        }
        wxUserComponent.cancelGrayUser(userId);
    }

    /**
     * 微信客户列表
     * @param searchVo
     * @return
     */
    public PageResult<WxUserVo> listWxUserPage(WxUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return wxUserComponent.listWxUserPage(searchVo);
    }

    /**
     * 绑定物业
     * @param userRealty
     */
    public void bindWxUserRealty(WxUserRealtyVo userRealty) {
        wxUserComponent.bindWxUserRealty(userRealty);
    }

    /**
     * 解绑物业
     * @param userRealty
     */
    public void unbindWxUserRealty(WxUserRealtyVo userRealty) {
        wxUserComponent.unbindWxUserRealty(userRealty);
    }

    /**
     * 微信客户物业
     * @param wxUserId
     * @return
     */
    public List<WxUserRealtyVo> listWxUserRealty(Long wxUserId) {
        return !WrapperClassUtils.biggerThanLong(wxUserId, 0L)
                ? Collections.emptyList() : wxUserComponent.listWxUserRealty(wxUserId);
    }

    /**
     * 微信用户备注更新
     * @param remarkVo
     */
    public void updateWxUserRemark(WxUserRemarkVo remarkVo) {
        wxUserComponent.updateWxUserRemark(remarkVo);
        //删除缓存
        String cacheKey = String.format(SenoxConst.Cache.KEY_WXUSER, remarkVo.getOpenid());
        RedisUtils.del(cacheKey);
    }

    /**
     * 解绑骑手
     * @param riderId
     */
    public void unbindRider(Long riderId) {
        wxUserComponent.unbindRider(riderId);
    }
}
