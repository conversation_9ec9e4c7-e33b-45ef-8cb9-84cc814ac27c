package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.flow.vo.WorkflowTaskItemSearchVo;
import com.senox.flow.vo.WorkflowTaskItemVo;
import com.senox.flow.vo.WorkflowTaskVo;
import com.senox.flow.vo.WorkflowTreeNode;
import com.senox.web.component.WorkFlowTaskComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 14:25
 */
@Service
@RequiredArgsConstructor
public class WorkFlowTaskService {

    private final WorkFlowTaskComponent workFlowTaskComponent;

    /**
     * 根据实例id查询任务历史
     * @param instanceId 实例id
     * @return 返回结果
     */
    public List<WorkflowTaskVo> taskHistoryByInstanceId(Long instanceId) {
        if (!WrapperClassUtils.biggerThanLong(instanceId, 0)) {
            return Collections.emptyList();
        }
        return workFlowTaskComponent.taskHistoryByInstanceId(instanceId);
    }

    /**
     * 根据任务id查询任务历史
     *
     * @param taskId 任务id
     * @return 返回结果
     */
    public WorkflowTreeNode<WorkflowTaskVo> taskHistory(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0)) {
            return null;
        }
        return workFlowTaskComponent.taskHistory(taskId);
    }

    /**
     * 根据实例id查询当前处理人任务
     *
     * @param instanceId
     * @return
     */
    public WorkflowTaskVo currentAssigneeFindByInstanceId(Long instanceId) {
        return workFlowTaskComponent.currentAssigneeFindByInstanceId(instanceId);
    }

    /**
     * 根据任务id查询当前处理人任务
     *
     * @param taskId 任务id
     * @return 返回结果
     */
    public WorkflowTaskVo currentAssigneeFindByTaskId(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return workFlowTaskComponent.currentAssigneeFindByTaskId(taskId);
    }


    /**
     * 待办列表
     *
     * @param search 查询参数
     * @return 返回待办列表
     */
    public PageResult<WorkflowTaskItemVo> todoList(WorkflowTaskItemSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return workFlowTaskComponent.todoList(search);
    }

    /**
     * 待办任务数
     * @param search 查询参数
     * @return 返回任务数
     */
    public Integer countTodoList(WorkflowTaskItemSearchVo search) {
        return workFlowTaskComponent.countTodoList(search);
    }

    /**
     * 已办列表
     *
     * @param search 查询参数
     * @return 返回已办列表
     */
    public PageResult<WorkflowTaskItemVo> doneList(WorkflowTaskItemSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return workFlowTaskComponent.doneList(search);
    }
}
