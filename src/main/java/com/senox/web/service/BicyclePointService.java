package com.senox.web.service;

import com.senox.tms.vo.BicyclePointVo;
import com.senox.web.component.BicyclePointComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:31
 */
@Service
@RequiredArgsConstructor
public class BicyclePointService {

    private final BicyclePointComponent bicyclePointComponent;

    /**
     * 添加三轮车配送地点
     * @param pointVo
     * @return
     */
    public Long addBicyclePoint(BicyclePointVo pointVo) {
        return bicyclePointComponent.addBicyclePoint(pointVo);
    }

    /**
     * 修改三轮车配送地点
     * @param pointVo
     */
    public void updateBicyclePoint(BicyclePointVo pointVo) {
        bicyclePointComponent.updateBicyclePoint(pointVo);
    }

    /**
     * 删除三轮车配送地点
     * @param id
     */
    public void deleteBicyclePoint(Long id) {
        bicyclePointComponent.deleteBicyclePoint(id);
    }

    /**
     * 根据id获取三轮车配送地点
     * @param id
     * @return
     */
    public BicyclePointVo findById(Long id) {
        return bicyclePointComponent.findById(id);
    }

    /**
     * 三轮车配送地点列表
     * @return
     */
    public List<BicyclePointVo> listBicyclePoint() {
        return bicyclePointComponent.listBicyclePoint();
    }
}
