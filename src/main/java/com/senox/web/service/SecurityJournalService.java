package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.SecurityEvent;
import com.senox.realty.vo.SecurityJournalSearchVo;
import com.senox.realty.vo.SecurityJournalVo;
import com.senox.web.component.SecurityComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/27 14:49
 */
@RequiredArgsConstructor
@Service
public class SecurityJournalService {

    private final SecurityComponent securityComponent;

    /**
     * 安保日志类别清单
     * @return
     */
    public SecurityEvent[] listEventTypes() {
        return securityComponent.listSecurityEventTypes();
    }

    /**
     * 添加安保日志
     * @param journal
     * @return
     */
    public Long addJournal(SecurityJournalVo journal) {
        return securityComponent.addSecurityJournal(journal);
    }

    /**
     * 更新安保日志
     * @param journal
     */
    public void updateJournal(SecurityJournalVo journal) {
        if (!WrapperClassUtils.biggerThanLong(journal.getId(), 0L)) {
            return;
        }

        securityComponent.updateSecurityJournal(journal);
    }

    /**
     * 删除安保日志
     * @param id
     */
    public void deleteJournal(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        securityComponent.deleteSecurityJournal(id);
    }

    /**
     * 获取安保日志
     * @param id
     * @return
     */
    public SecurityJournalVo findJournalById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? securityComponent.findSecurityJournalById(id) : null;
    }

    /**
     * 安保日志列表页
     * @param search
     * @return
     */
    public PageResult<SecurityJournalVo> listJournalPage(SecurityJournalSearchVo search) {
        return securityComponent.listSecurityJournalPage(search);
    }
}
