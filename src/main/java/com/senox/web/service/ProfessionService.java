package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.ProfessionVo;
import com.senox.web.component.DictionaryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/15 10:09
 */
@Service
public class ProfessionService {

    @Autowired
    private DictionaryComponent dictionaryComponent;

    /**
     * 添加行业
     * @param profession
     * @return
     */
    public Long addProfession(ProfessionVo profession) {
        if (StringUtils.isBlank(profession.getName())) {
            return 0L;
        }
        return dictionaryComponent.addProfession(profession);
    }

    /**
     * 更新行业信息
     * @param profession
     */
    public void updateProfession(ProfessionVo profession) {
        if (!WrapperClassUtils.biggerThanLong(profession.getId(), 0L)) {
            return;
        }
        dictionaryComponent.updateProfession(profession);
    }

    /**
     * 删除行业
     * @param id
     */
    public void deleteProfession(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        ProfessionVo profession = new ProfessionVo();
        profession.setId(id);
        profession.setDisabled(Boolean.TRUE);
        updateProfession(profession);
    }

    /**
     * 根据id获取行业
     * @param id
     * @return
     */
    public ProfessionVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return dictionaryComponent.findProfessionById(id);
    }

    /**
     * 行业列表
     * @return
     */
    public List<ProfessionVo> listAll() {
        return dictionaryComponent.listProfession();
    }
}
