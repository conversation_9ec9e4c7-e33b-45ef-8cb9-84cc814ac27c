package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingAccommodateInspectionBriefVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/25 17:07
 */
@Service
@RequiredArgsConstructor
public class FirefightingAccommodateInspectionService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加违规住人消防巡检记录
     * @param inspection
     * @return
     */
    public Long addAccommodateInspection(FirefightingAccommodateInspectionVo inspection) {
        return firefightingComponent.addAccommodateInspection(inspection);
    }

    /**
     * 更新违规住人消防巡检记录
     * @param inspection
     */
    public void updateAccommodateInspection(FirefightingAccommodateInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateAccommodateInspection(inspection);
    }

    /**
     * 删除违规住人消防巡检记录
     * @param id
     */
    public void deleteAccommodateInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteAccommodateInspection(id);
    }

    /**
     * 获取违规住人消防巡检记录
     * @param id
     * @return
     */
    public FirefightingAccommodateInspectionVo findAccommodateInspectionById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findAccommodateInspectionById(id) : null;
    }

    /**
     * 违规住人消防巡检记录数统计
     * @param search
     * @return
     */
    public int countAccommodateInspection(FirefightingAccommodateInspectionSearchVo search) {
        return firefightingComponent.countAccommodateInspection(search);
    }

    /**
     * 违规住人消防巡检记录列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingAccommodateInspectionBriefVo> listAccommodateInspectionPage(FirefightingAccommodateInspectionSearchVo search) {
        return firefightingComponent.listAccommodateInspectionPage(search);
    }


}
