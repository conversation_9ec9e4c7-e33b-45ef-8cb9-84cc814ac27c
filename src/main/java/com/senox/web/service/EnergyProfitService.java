package com.senox.web.service;

import com.senox.common.constant.device.EnergyType;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.EnergyProfitEditVo;
import com.senox.realty.vo.EnergyProfitItemVo;
import com.senox.realty.vo.EnergyProfitSearchVo;
import com.senox.realty.vo.EnergyProfitVo;
import com.senox.web.component.RealtyEnergyComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/26 14:31
 */
@RequiredArgsConstructor
@Service
public class EnergyProfitService {

    private final RealtyEnergyComponent energyComponent;

    /**
     * 能源损益生成
     * @param billTime
     * @param type
     * @return
     */
    public EnergyProfitVo generateProfits(BillTimeVo billTime, EnergyType type) {
        return energyComponent.generateProfit(billTime, type);
    }

    /**
     * 能源损益保存
     * @param profitEdit
     * @return
     */
    public EnergyProfitVo saveProfit(EnergyProfitEditVo profitEdit) {
        return energyComponent.saveProfit(profitEdit);
    }

    /**
     * 能源损益获取
     * @param id
     * @return
     */
    public EnergyProfitVo findProfitById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? energyComponent.findProfitById(id) : null;
    }

    /**
     * 能源损益页
     * @param search
     * @return
     */
    public PageResult<EnergyProfitVo> listProfitPage(EnergyProfitSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return energyComponent.listProfitPage(search);
    }

    /**
     * 能源损益明细
     * @param profitId
     * @return
     */
    public List<EnergyProfitItemVo> listProfitItem(Long profitId) {
        if (!WrapperClassUtils.biggerThanLong(profitId, 0L)) {
            return Collections.emptyList();
        }

        return energyComponent.listProfitItem(profitId);
    }


}
