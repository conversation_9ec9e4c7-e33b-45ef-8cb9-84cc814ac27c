package com.senox.web.service;

import com.senox.car.vo.ParkingRegulatorySearchVo;
import com.senox.car.vo.ParkingRegulatoryVo;
import com.senox.common.vo.PageResult;
import com.senox.web.component.ParkingRegulatoryComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:57
 */
@Service
@RequiredArgsConstructor
public class ParkingRegulatoryService {

    private final ParkingRegulatoryComponent parkingRegulatoryComponent;

    /**
     * 添加监管名单
     * @param parkingRegulatoryVo
     * @return
     */
    public Long addParkingRegulatory(ParkingRegulatoryVo parkingRegulatoryVo) {
        return parkingRegulatoryComponent.addParkingRegulatory(parkingRegulatoryVo);
    }

    /**
     * 更新监管名单
     * @param parkingRegulatoryVo
     */
    public void updateParkingRegulatory(ParkingRegulatoryVo parkingRegulatoryVo) {
        parkingRegulatoryComponent.updateParkingRegulatory(parkingRegulatoryVo);
    }

    /**
     * 获取监管名单
     * @param id
     * @return
     */
    public ParkingRegulatoryVo findById(Long id) {
        return parkingRegulatoryComponent.findById(id);
    }

    /**
     * 监管名单分页
     * @param searchVo
     * @return
     */
    public PageResult<ParkingRegulatoryVo> page(ParkingRegulatorySearchVo searchVo) {
        return parkingRegulatoryComponent.page(searchVo);
    }
}
