package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.*;
import com.senox.web.component.ReceiptComponent;
import com.senox.web.vo.ReceiptAuditVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/18 17:19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ReceiptService {

    private final ReceiptComponent receiptComponent;


    /**
     * 添加发票抬头
     *
     * @param header
     * @return
     */
    public Long addHeader(TaxHeaderVo header) {
        return receiptComponent.addTaxHeader(header);
    }

    /**
     * 更新发票抬头
     *
     * @param header
     */
    public void updateHeader(TaxHeaderVo header) {
        if (!WrapperClassUtils.biggerThanLong(header.getId(), 0L)) {
            return;
        }

        TaxHeaderVo h = findHeaderById(header.getId());
        if (h != null && !Objects.equals(header.getOpenid(), h.getOpenid())) {
            log.warn("只允许更新自己的发票抬头 {} - {}", header.getOpenid(), h.getOpenid());
            return;
        }

        receiptComponent.updateTaxHeader(header);
    }

    /**
     * 删除发票抬头
     *
     * @param headerBatch
     */
    public void delHeader(TaxHeaderBatchVo headerBatch) {
        if (CollectionUtils.isEmpty(headerBatch.getIds())) {
            return;
        }
        if (StringUtils.isBlank(headerBatch.getOpenid())) {
            headerBatch.setOpenid(StringUtils.EMPTY);
        }

        receiptComponent.delTaxHeader(headerBatch);
    }

    /**
     * 根据id查找发票抬头
     *
     * @param id
     * @return
     */
    public TaxHeaderVo findHeaderById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return receiptComponent.findTaxHeaderById(id);
    }

    /**
     * 发票抬头列表
     *
     * @param search
     * @return
     */
    public List<TaxHeaderVo> listHeader(TaxHeaderSearchVo search) {
        if (StringUtils.isBlank(search.getOpenid())) {
            search.setOpenid(StringUtils.EMPTY);
        }

        return receiptComponent.listTaxHeader(search);
    }
}
