package com.senox.web.service;

import com.senox.cold.vo.*;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.web.component.RefrigerationReceiptComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/2/20 13:46
 */
@RequiredArgsConstructor
@Service
public class RefrigerationReceiptService {

    private final RefrigerationReceiptComponent receiptComponent;

    /**
     * 冷藏发票申请
     * @param managerVo
     */
    public void apply(RefrigerationReceiptManagerVo managerVo) {
        receiptComponent.apply(managerVo);
    }

    /**
     * 冷藏发票状态重置
     * @param id
     */
    public void refreshRefrigerationReceipt(Long id) {
        receiptComponent.refreshRefrigerationReceipt(id);
    }

    /**
     * 冷藏发票分页
     * @param searchVo
     * @return
     */
    public PageResult<RefrigerationReceiptVo> page(RefrigerationReceiptSearchVo searchVo) {
        return receiptComponent.page(searchVo);
    }

    /**
     * 冷藏账单发票信息详细列表
     * @param id
     * @return
     */
    public List<RefrigerationMonthBillReceiptInfoVo> applyBillInfoList(Long id) {
        return receiptComponent.applyBillInfoList(id);
    }

    /**
     * 冷藏发票列表
     * @param id
     * @param isDetail
     * @return
     */
    public List<ReceiptApplyVo> applyInfoList(Long id, Boolean isDetail) {
        return receiptComponent.applyInfoList(id, isDetail);
    }

    /**
     * 根据客户编号查询最近开票抬头
     * @param customerSerial
     * @return
     */
    public TaxHeaderVo lastTaxByCustomerSerial(String customerSerial) {
        return receiptComponent.lastTaxByCustomerSerial(customerSerial);
    }
}
