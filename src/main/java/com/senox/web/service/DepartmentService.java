package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.DepartmentNode;
import com.senox.user.vo.DepartmentVo;
import com.senox.web.component.DepartmentComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 14:24
 */
@Service
public class DepartmentService {

    @Autowired
    private DepartmentComponent departmentComponent;

    /**
     * 添加部门
     * @param department
     * @return
     */
    public Long addDepartment(DepartmentVo department) {
        if (StringUtils.isBlank(department.getName())) {
            return 0L;
        }

        return departmentComponent.addDepartment(department);
    }

    /**
     * 更新部门
     * @param department
     */
    public void updateDepartment(DepartmentVo department) {
        if (!WrapperClassUtils.biggerThanLong(department.getId(), 0L)) {
            return;
        }

        departmentComponent.updateDepartment(department);
    }

    /**
     * 删除部门
     * @param id
     */
    public void deleteDepartment(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        DepartmentVo department = new DepartmentVo();
        department.setId(id);
        department.setDisabled(Boolean.TRUE);
        updateDepartment(department);
    }

    /**
     * 获取部门详情
     * @param id
     * @return
     */
    public DepartmentVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return departmentComponent.findById(id);
    }

    /**
     * 部门列表
     * @param parentId
     * @return
     */
    public List<DepartmentVo> listDepartment(Long parentId) {
        if (parentId == null || parentId < 0L) {
            return Collections.emptyList();
        }

        return departmentComponent.listDepartment(parentId);
    }

    /**
     * 部门树
     * @return
     */
    public List<DepartmentNode> listDepartmentTree() {
        return departmentComponent.listDepartmentTree();
    }
}
