package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.*;
import com.senox.web.component.EmployeeComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:02
 */
@Service
public class EmployeeService {

    @Autowired
    private EmployeeComponent employeeComponent;

    /**
     * 新增员工
     * @param employee
     * @return
     */
    public Long addEmployee(EmployeeVo employee) {
        if (StringUtils.isBlank(employee.getCompanyName())) {
            return 0L;
        }
        return employeeComponent.addEmployee(employee);
    }

    /**
     * 更新员工
     * @param employee
     */
    public void updateEmployee(EmployeeVo employee) {
        if (!WrapperClassUtils.biggerThanLong(employee.getId(), 0L)) {
            return;
        }
        employeeComponent.updateEmployee(employee);
    }

    /**
     * 删除员工
     * @param id
     */
    public void deleteEmployee(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        EmployeeVo employee = new EmployeeVo();
        employee.setId(id);
        employee.setDisabled(Boolean.TRUE);
        updateEmployee(employee);
    }

    /**
     * 根据id查找员工
     * @param id
     * @return
     */
    public EmployeeVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return employeeComponent.findById(id);
    }

    /**
     * 员工列表页
     * @param searchVo
     * @return
     */
    public PageResult<EmployeeVo> listEmployeePage(EmployeeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return employeeComponent.listEmployeePage(searchVo);
    }

    /**
     * 报餐列表页
     * @param searchVo
     * @return
     */
    public PageResult<BookingMealVo> listBookingPage(BookingMealSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return employeeComponent.listBookingPage(searchVo);
    }

    /**
     * 报餐日报列表
     * @param searchVo
     * @return
     */
    public PageResult<BookingMealCompanyDayReportVo> listBookingCompanyDayReportPage(BookingMealDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return employeeComponent.listBookingCompanyDayReportPage(searchVo);
    }

    /**
     * 报餐日报详情
     * @param mealDate
     * @return
     */
    public List<BookingMealCompanyDayReportVo> listBookingCompanyDayReportDetail(String mealDate) {
        if (mealDate == null) {
            return Collections.emptyList();
        }
        return employeeComponent.listBookingCompanyDayReportDetail(mealDate);
    }
}
