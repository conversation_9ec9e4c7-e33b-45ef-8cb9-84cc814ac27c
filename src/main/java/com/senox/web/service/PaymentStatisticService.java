package com.senox.web.service;

import com.senox.car.vo.*;
import com.senox.common.utils.*;
import com.senox.common.utils.BeanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.PayWayDailyStatisticVo;
import com.senox.pm.vo.TollManDailyStatisticVo;
import com.senox.pm.vo.TollPayWayExtVo;
import com.senox.pm.vo.TollPayWayStatisticVo;
import com.senox.pm.vo.TollStatisticSearchVo;
import com.senox.web.component.OrderComponent;
import com.senox.web.component.ParkingComponent;
import com.senox.web.vo.PaymentDailyWayStatisticVo;
import com.senox.web.vo.PaymentTollManDailyStatisticVo;
import com.senox.web.vo.PaymentWayStatisticVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/29 15:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentStatisticService {

    private final OrderComponent orderComponent;
    private final ParkingComponent parkingComponent;

    /**
     * 收费员日收费统计合计
     * @param search
     * @return
     */
    public PaymentTollManDailyStatisticVo sumTollManDailyStatistic(TollStatisticSearchVo search) {
        TollManDailyStatisticVo statisticVo = orderComponent.sumTollManDailyStatistic(search);
        ParkingTollManPaymentVo parkingTollManPaymentVo = marketSumCashParkingTollManPayment(search);
        BigDecimal marketMonthParkingCashAmount = DecimalUtils.nullToZero(parkingTollManPaymentVo == null ? null : parkingTollManPaymentVo.getMonthTotalAmount());
        return buildTollManDailyStatistic(null, statisticVo, marketMonthParkingCashAmount);
    }

    /**
     * 收费员日收费统计列表
     * @param search
     * @return
     */
    public List<TollManDailyStatisticVo> listTollManDailyStatistic(TollStatisticSearchVo search) {
        return orderComponent.listTollManDailyStatistic(search);
    }

    /**
     * 收费员日收费统计页
     * @param search
     * @return
     */
    public PageResult<PaymentTollManDailyStatisticVo> listTollManDailyStatisticPage(TollStatisticSearchVo search) {
        search.setTollDateEnd(DateUtils.getMinDate(search.getTollDateEnd(), LocalDate.now()));
        List<PaymentTollManDailyStatisticVo> statisticVos = buildListPaymentTollManStatistic(search);
        int totalSize = orderComponent.listTollManDailyStatisticPage(search).getTotalSize();
        return PageUtils.resultPage(search, totalSize, statisticVos);
    }

    /**
     * 收费员日收费统计页(含停车系统月卡现金)
     * @param search
     * @return
     */
    public List<PaymentTollManDailyStatisticVo> buildListPaymentTollManStatistic(TollStatisticSearchVo search) {
        if (search.getTollDateEnd().isBefore(search.getTollDateStart())) {
            return Collections.emptyList();
        }

        // 园区收费数据
        List<TollManDailyStatisticVo> marketStatistic = orderComponent.listTollManDailyStatisticPage(search).getDataList();
        // 停车收费数据
        List<ParkingTollManPaymentVo> parkingStatistic = marketListCashParkingTollManPayment(search);

        List<PaymentTollManDailyStatisticVo> resultList = new ArrayList<>((int) DateUtils.getDaysBetween(search.getTollDateStart(), search.getTollDateEnd()) + 1);
        for (TollManDailyStatisticVo statisticVo : marketStatistic) {
            BigDecimal marketMonthParkingCashAmount = parkingStatistic.stream().filter(x -> Objects.equals(x.getTollName(), statisticVo.getTollMan()) && Objects.equals(statisticVo.getTollDate(), x.getTollDate())).map(ParkingTollManPaymentVo::getMonthTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            resultList.add(buildTollManDailyStatistic(statisticVo.getTollDate(), statisticVo, marketMonthParkingCashAmount));
        }
        return resultList;
    }

    private PaymentTollManDailyStatisticVo buildTollManDailyStatistic(LocalDate tollDate,
                                                                      TollManDailyStatisticVo statisticVo,
                                                                      BigDecimal marketMonthParkingCashAmount) {
        PaymentTollManDailyStatisticVo result = PaymentTollManDailyStatisticVo.builder()
                .tollDate(tollDate)
                .tollMan(StringUtils.trimToEmpty(statisticVo == null ? null : statisticVo.getTollMan()))
                .parkingAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getParkingAmount()))
                .realtyAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getRealtyAmount()))
                .oneTimeFeeAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getOneTimeFeeAmount()))
                .oneTimeFeeRefund(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getOneTimeFeeRefund()))
                .refrigerationAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getRefrigerationAmount()))
                .maintainAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getMaintainAmount()))
                .advertisingAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getAdvertisingAmount()))
                .bicycleAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getBicycleAmount()))
                .depositAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDepositAmount()))
                .cloudWarehousingAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getCloudWarehousingAmount()))
                .normalTemperatureWarehousingAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getNormalTemperatureWarehousingAmount()))
                .depositRefund(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDepositRefund()))
                .cycleAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getCycleAmount()))
                .logisticFreightAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getLogisticFreightAmount()))
                .logisticTransportAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getLogisticTransportAmount()))
                .marketMonthParkingCashAmount(marketMonthParkingCashAmount)
                .build();
        result.setTotalAmount(DecimalUtils.add(statisticVo == null ? null : statisticVo.getTotalAmount(), marketMonthParkingCashAmount));
        return result;
    }

    /**
     * 停车系统收费员现金合计
     * @param search
     * @return
     */
    private ParkingTollManPaymentVo marketSumCashParkingTollManPayment(TollStatisticSearchVo search) {
        ParkingTollManPaymentVo parkingStatistic = null;
        if (CollectionUtils.isEmpty(search.getPayWays())
                || search.getPayWays().contains(PayWay.CASH.getValue())) {
            ParkingPaymentSearchVo parkingSearch = new ParkingPaymentSearchVo();
            parkingSearch.setTollDateStart(search.getTollDateStart());
            parkingSearch.setTollDateEnd(search.getTollDateEnd());
            parkingSearch.setTollManId(search.getTollManId());
            parkingStatistic = sumParkingTollManPayment(parkingSearch);
        }
        return parkingStatistic;
    }

    /**
     * 获取收费员收费记录合计数据
     * @param search
     * @return
     */
    private List<ParkingTollManPaymentVo> marketListCashParkingTollManPayment(TollStatisticSearchVo search) {
        List<ParkingTollManPaymentVo> parkingStatistic = Collections.emptyList();
        if (CollectionUtils.isEmpty(search.getPayWays())
                || search.getPayWays().contains(PayWay.CASH.getValue())) {
            ParkingPaymentSearchVo parkingSearch = new ParkingPaymentSearchVo();
            parkingSearch.setTollDateStart(search.getTollDateStart());
            parkingSearch.setTollDateEnd(search.getTollDateEnd());
            parkingSearch.setTollManId(search.getTollManId());
            parkingStatistic = listParkingTollManPayment(parkingSearch);
        }
        return parkingStatistic;
    }

    /**
     * 收费员收费记录合计
     * @param searchVo
     * @return
     */
    public ParkingTollManPaymentVo sumParkingTollManPayment(ParkingPaymentSearchVo searchVo) {
        return parkingComponent.sumParkingTollManPayment(searchVo);
    }

    /**
     * 收费员收费记录列表
     * @param searchVo
     * @return
     */
    public List<ParkingTollManPaymentVo> listParkingTollManPayment(ParkingPaymentSearchVo searchVo) {
        return parkingComponent.listParkingTollManPayment(searchVo);
    }

    /**
     * 支付通道日统计合计
     * @param search
     * @return
     */
    public PaymentDailyWayStatisticVo sumPayWayDailyStatistic(TollStatisticSearchVo search) {
        PayWayDailyStatisticVo statisticVo = orderComponent.sumPayWayDailyStatistic(search);
        ParkingPaymentStatisticVo parkingStatistic = marketParkingPaymentStatistic(search);
        return buildPayWayDailyStatistic(null, statisticVo, parkingStatistic);
    }


    private PaymentDailyWayStatisticVo buildPayWayDailyStatistic(LocalDate tollDate,
                                                                    PayWayDailyStatisticVo statisticVo,
                                                                    ParkingPaymentStatisticVo parkingStatistic) {
        BigDecimal monthCashAmount = DecimalUtils.nullToZero(parkingStatistic == null ? null : parkingStatistic.getMonthCashAmount());
        PaymentDailyWayStatisticVo result = PaymentDailyWayStatisticVo.builder()
                .tollDate(tollDate)
                .drcAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDrcAmount()))
                .drc015Amount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDrc015Amount()))
                .drcTransferAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDrcTransferAmount()))
                .drcWithholdAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getDrcWithholdAmount()))
                .bodAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getBodAmount()))
                .bodTransferAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getBodTransferAmount()))
                .bodScanAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getBodScanAmount()))
                .cashAmount(DecimalUtils.add(statisticVo == null ? null : statisticVo.getCashAmount(), monthCashAmount))
                .cardAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getCardAmount()))
                .confiscateAmount(DecimalUtils.nullToZero(statisticVo == null ? null : statisticVo.getConfiscateAmount()))
                .remark(StringUtils.trimToEmpty(statisticVo == null ? null : statisticVo.getRemark()))
                .build();
        result.setTotalAmount(DecimalUtils.add(statisticVo == null ? null : statisticVo.getTotalAmount(), monthCashAmount));
        return result;
    }

    /**
     * 支付通道日统计列表
     * @param search
     * @return
     */
    public List<PayWayDailyStatisticVo> listPayWayDailyStatistic(TollStatisticSearchVo search) {
        List<PayWayDailyStatisticVo> resultList = orderComponent.listPayWayDailyStatistic(search);

        if (!CollectionUtils.isEmpty(resultList)) {
            preparePaymentStatisticExt(resultList);
        }
        return resultList;
    }

    /**
     * 支付通道日统计页
     * @param search
     * @return
     */
    public PageResult<PaymentDailyWayStatisticVo> listPayWayDailyStatisticPage(TollStatisticSearchVo search) {
        search.setTollDateEnd(DateUtils.getMinDate(search.getTollDateEnd(), LocalDate.now()));
        PageResult<PaymentDailyWayStatisticVo> resultPage = new PageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize((int) DateUtils.getDaysBetween(search.getTollDateStart(), search.getTollDateEnd()) + 1);
        resultPage.initTotalPage();

        TollStatisticSearchVo newSearch = prepareSearch(search);
        log.info("payWay daily statistic search {}.", JsonUtils.object2Json(newSearch));
        log.info("payWay daily statistic result page {}.", JsonUtils.object2Json(resultPage));

        resultPage.setDataList(buildListPayWayDailyStatistic(newSearch));

        return resultPage;
    }

    /**
     * 支付通道日统计页（含停车系统）
     * @param search
     * @return
     */
    public List<PaymentDailyWayStatisticVo> buildListPayWayDailyStatistic(TollStatisticSearchVo search) {
        if (search.getTollDateEnd().isBefore(search.getTollDateStart())) {
            return Collections.emptyList();
        }

        // 园区收费数据
        List<PayWayDailyStatisticVo> marketStatistic = listPayWayDailyStatistic(search);
        // 停车收费数据
        List<ParkingPaymentStatisticVo> parkingStatistic = getParkingPaymentStatisticVos(search);

        Map<LocalDate, PayWayDailyStatisticVo> marketMap = marketStatistic.stream().collect(Collectors.toMap(PayWayDailyStatisticVo::getTollDate, Function.identity()));
        Map<LocalDate, ParkingPaymentStatisticVo> parkingMap = parkingStatistic.stream().collect(Collectors.toMap(ParkingPaymentStatisticVo::getTollDate, Function.identity()));

        List<PaymentDailyWayStatisticVo> resultList = new ArrayList<>((int) DateUtils.getDaysBetween(search.getTollDateStart(), search.getTollDateEnd()) + 1);
        for (LocalDate date = search.getTollDateEnd(); !date.isBefore(search.getTollDateStart()); date = date.minusDays(1L)) {
            PayWayDailyStatisticVo marketItem = marketMap.get(date);
            ParkingPaymentStatisticVo parkingItem = parkingMap.get(date);
            resultList.add(buildPayWayDailyStatistic(date, marketItem, parkingItem));
        }
        return resultList;
    }

    /**
     * 支付通道日明细合计
     * @param search
     * @return
     */
    public TollPayWayStatisticVo sumTollPayWayStatistic(TollStatisticSearchVo search) {
        return orderComponent.sumTollPayWayStatistic(search);
    }

    /**
     * 支付通道日明细列表
     * @param search
     * @return
     */
    public List<TollPayWayStatisticVo> listTollPayWayStatistic(TollStatisticSearchVo search) {
        return orderComponent.listTollPayWayStatistic(search);
    }

    /**
     * 支付通道日明细页
     * @param search
     * @return
     */
    public PageResult<TollPayWayStatisticVo> listTollPayWayStatisticPage(TollStatisticSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return orderComponent.listTollPayWayStatisticPage(search);
    }

    /**
     * 收费通道统计（含停车系统）合计
     * @param search
     * @return
     */
    public PaymentWayStatisticVo sumPaymentWayStatistic(TollStatisticSearchVo search) {
        TollPayWayStatisticVo marketStatistic = sumTollPayWayStatistic(search);
        ParkingPaymentStatisticVo parkingStatistic = marketParkingPaymentStatistic(search);

        return buildPaymentWayStatistic(null, marketStatistic, calMarketParkingAmount(parkingStatistic, search.getPayWays()), calMarketMonthParkingAmount(parkingStatistic, search.getPayWays()));
    }

    /**
     * 停车系统合计
     * @param search
     * @return
     */
    private ParkingPaymentStatisticVo marketParkingPaymentStatistic(TollStatisticSearchVo search) {
        ParkingPaymentStatisticVo parkingStatistic = null;
        if (CollectionUtils.isEmpty(search.getPayWays())
                || search.getPayWays().contains(PayWay.DRC.getValue())
                || search.getPayWays().contains(PayWay.CASH.getValue())
                || search.getPayWays().contains(PayWay.BOD_G.getValue())) {
            ParkingPaymentSearchVo parkingSearch = new ParkingPaymentSearchVo();
            if (!CollectionUtils.isEmpty(search.getPayWays())) {
                parkingSearch.setPayWay(search.getPayWays().contains(PayWay.BOD_G.getValue()) ? PayWay.BOD_G.getValue() : PayWay.DRC.getValue());
            }
            parkingSearch.setTollDateStart(search.getTollDateStart());
            parkingSearch.setTollDateEnd(search.getTollDateEnd());
            parkingStatistic = sumParkingPaymentStatistic(parkingSearch);
        }
        return parkingStatistic;
    }

    /**
     * 收费通道统计（含停车系统）
     * @param search
     * @return
     */
    public List<PaymentWayStatisticVo> listPaymentWayStatistic(TollStatisticSearchVo search) {
        if (search.getTollDateEnd().isBefore(search.getTollDateStart())) {
            return Collections.emptyList();
        }

        // 园区收费数据
        List<TollPayWayStatisticVo> marketStatistic = listTollPayWayStatistic(search);
        // 停车收费数据
        List<ParkingPaymentStatisticVo> parkingStatistic = getParkingPaymentStatisticVos(search);

        Map<LocalDate, TollPayWayStatisticVo> marketMap = marketStatistic.stream().collect(Collectors.toMap(TollPayWayStatisticVo::getTollDate, Function.identity()));
        Map<LocalDate, ParkingPaymentStatisticVo> parkingMap = parkingStatistic.stream().collect(Collectors.toMap(ParkingPaymentStatisticVo::getTollDate, Function.identity()));

        List<PaymentWayStatisticVo> resultList = new ArrayList<>((int) DateUtils.getDaysBetween(search.getTollDateStart(), search.getTollDateEnd()) + 1);
        for (LocalDate date = search.getTollDateEnd(); !date.isBefore(search.getTollDateStart()); date = date.minusDays(1L)) {
            TollPayWayStatisticVo marketItem = marketMap.get(date);
            ParkingPaymentStatisticVo parkingItem = parkingMap.get(date);
            resultList.add(buildPaymentWayStatistic(date, marketItem, calMarketParkingAmount(parkingItem, search.getPayWays()), calMarketMonthParkingAmount(parkingItem, search.getPayWays())));
        }
        return resultList;
    }

    /**
     * 获取停车收费数据
     * @param search
     * @return
     */
    private List<ParkingPaymentStatisticVo> getParkingPaymentStatisticVos(TollStatisticSearchVo search) {
        List<ParkingPaymentStatisticVo> parkingStatistic = Collections.emptyList();
        if (CollectionUtils.isEmpty(search.getPayWays())
                || search.getPayWays().contains(PayWay.DRC.getValue())
                || search.getPayWays().contains(PayWay.CASH.getValue())
                || search.getPayWays().contains(PayWay.BOD_G.getValue())) {
            ParkingPaymentSearchVo parkingSearch = new ParkingPaymentSearchVo();
            parkingSearch.setTollDateStart(search.getTollDateStart());
            parkingSearch.setTollDateEnd(search.getTollDateEnd());
            if (!CollectionUtils.isEmpty(search.getPayWays())) {
                parkingSearch.setPayWay(search.getPayWays().contains(PayWay.BOD_G.getValue()) ? PayWay.BOD_G.getValue() : PayWay.DRC.getValue());
            }
            parkingStatistic = listParkingPaymentStatistic(parkingSearch);
        }
        return parkingStatistic;
    }

    /**
     * 收费通道统计页（含停车系统）
     * @param search
     * @return
     */
    public PageResult<PaymentWayStatisticVo> listPaymentWayStatisticPage(TollStatisticSearchVo search) {
        search.setTollDateEnd(DateUtils.getMinDate(search.getTollDateEnd(), LocalDate.now()));
        PageResult<PaymentWayStatisticVo> resultPage = new PageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize((int) DateUtils.getDaysBetween(search.getTollDateStart(), search.getTollDateEnd()) + 1);
        resultPage.initTotalPage();

        TollStatisticSearchVo newSearch = prepareSearch(search);
        log.info("Payment way statistic search {}.", JsonUtils.object2Json(newSearch));
        log.info("Payment way statistic result page {}.", JsonUtils.object2Json(resultPage));

        resultPage.setDataList(listPaymentWayStatistic(newSearch));
        return resultPage;
    }

    /**
     * 查询参数初始化
     * @param search
     * @return
     */
    private TollStatisticSearchVo prepareSearch(TollStatisticSearchVo search) {
        // 内存分页处理
        search.prepare();
        int offset = search.getOffset();
        TollStatisticSearchVo newSearch = new TollStatisticSearchVo();
        BeanUtils.copyProperties(search, newSearch);
        // 默认按时间倒序
        newSearch.setTollDateEnd(search.getTollDateEnd().minusDays(offset));
        newSearch.setTollDateStart(DateUtils.getMaxDate(newSearch.getTollDateEnd().minusDays(search.getPageSize() - 1L), search.getTollDateStart()));
        return newSearch;
    }

    /**
     * 支付通道日明细备注
     * @param ext
     */
    public void remarkTollPayWay(TollPayWayExtVo ext) {
        orderComponent.remarkTollPayWay(ext);
    }

    /**
     * 停车缴费统计备注
     * @param remark
     */
    public void remarkParkingPaymentStatistic(ParkingPaymentRemarkVo remark) {
        parkingComponent.remarkParkingPaymentStatistic(remark);
    }

    /**
     * 停车缴费统计合计
     * @param search
     * @return
     */
    public ParkingPaymentStatisticVo sumParkingPaymentStatistic(ParkingPaymentSearchVo search) {
        return parkingComponent.sumParkingPaymentStatistic(search);
    }

    /**
     * 停车缴费统计列表
     * @param search
     * @return
     */
    public List<ParkingPaymentStatisticVo> listParkingPaymentStatistic(ParkingPaymentSearchVo search) {
        return parkingComponent.listParkingPaymentStatistic(search);
    }

    /**
     * 停车缴费统计页
     * @param search
     * @return
     */
    public PageResult<ParkingPaymentStatisticVo> listParkingPaymentStatisticPage(ParkingPaymentSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return parkingComponent.listParkingPaymentStatisticPage(search);
    }

    /**
     * 停车系统统计
     * @param search
     * @return
     */
    public ParkingStatisticsVo parkingPaymentStatistic(ParkingPaymentSearchVo search) {
        return parkingComponent.parkingPaymentStatistic(search);
    }

    /**
     * 停车系统大屏统计
     * @param search
     * @return
     */
    public List<ParkingStatisticsVo> parkingPaymentStatisticScreen(ParkingPaymentStatisticSearchVo search) {
        return parkingComponent.parkingPaymentStatisticScreen(search);
    }

    /**
     * 入场登记车辆大屏统计
     * @param search
     * @return
     */
    public List<ParkingVehicleDayReportVo> parkingVehicleStatisticScreen(ParkingVehicleStatisticsSearchVo search) {
        return parkingComponent.parkingVehicleStatisticScreen(search);
    }

    private PaymentWayStatisticVo buildPaymentWayStatistic(LocalDate tollDate,
                                                           TollPayWayStatisticVo marketStatistic,
                                                           BigDecimal parkingAmount,
                                                           BigDecimal parkingMonthAmount) {
        PaymentWayStatisticVo result = PaymentWayStatisticVo.builder()
                .tollDate(tollDate)
                .parkingAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getParkingAmount()))
                .realtyAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getRealtyAmount()))
                .oneTimeFeeAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getOneTimeFeeAmount()))
                .oneTimeFeeRefund(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getOneTimeFeeRefund()))
                .refrigerationAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getRefrigerationAmount()))
                .maintainAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getMaintainAmount()))
                .advertisingAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getAdvertisingAmount()))
                .bicycleAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getBicycleAmount()))
                .depositAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getDepositAmount()))
                .depositRefund(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getDepositRefund()))
                .cloudWarehousingAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getCloudWarehousingAmount()))
                .normalTemperatureWarehousingAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getNormalTemperatureWarehousingAmount()))
                .cycleAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getCycleAmount()))
                .depositRefund(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getDepositRefund()))
                .eggDepositAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getEggDepositAmount()))
                .eggDepositRefundAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getEggDepositRefundAmount()))
                .logisticFreightAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getLogisticFreightAmount()))
                .logisticTransportAmount(DecimalUtils.nullToZero(marketStatistic == null ? null : marketStatistic.getLogisticTransportAmount()))
                .marketParkingAmount(DecimalUtils.nullToZero(parkingAmount))
                .marketMonthParkingAmount(DecimalUtils.nullToZero(parkingMonthAmount))
                .remark(StringUtils.trimToEmpty(marketStatistic == null ? null : marketStatistic.getRemark()))
                .build();
        result.setTotalAmount(DecimalUtils.add(marketStatistic == null ? null : marketStatistic.getTotalAmount(), parkingAmount, parkingMonthAmount));
        return result;
    }

    /**
     * 根据收费方式计算园区临卡停车收费金额
     * @param statistic
     * @param payWays
     * @return
     */
    private BigDecimal calMarketParkingAmount(ParkingPaymentStatisticVo statistic, List<Integer> payWays) {
        if (statistic == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = BigDecimal.ZERO;
        // 农商行
        if (payWays == null || payWays.contains(PayWay.DRC.getValue())
                || payWays.contains(PayWay.BOD_G.getValue())) {
            result = DecimalUtils.add(result
                    , statistic.getParkingScanAmount()
                    , statistic.getParkingAppAmount()
            );
        }

        if (payWays == null || payWays.contains(PayWay.CASH.getValue())) {
            result = DecimalUtils.add(result
                    , statistic.getParkingCashAmount()
            );
        }
        return result;
    }

    /**
     * 根据收费方式计算园区停车月卡收费金额
     * @param statistic
     * @param payWays
     * @return
     */
    private BigDecimal calMarketMonthParkingAmount(ParkingPaymentStatisticVo statistic, List<Integer> payWays) {
        if (statistic == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = BigDecimal.ZERO;
        // 农商行
        if (payWays == null || payWays.contains(PayWay.DRC.getValue())
                || payWays.contains(PayWay.BOD_G.getValue())) {
            result = DecimalUtils.add(result
                    , statistic.getMonthDrcAmount()
                    , statistic.getMonthScanAmount()
                    , statistic.getMonthDrcHandlingAmount()
            );
        }

        if (payWays == null || payWays.contains(PayWay.CASH.getValue())) {
            result = DecimalUtils.add(result
                    , statistic.getMonthCashAmount()
            );
        }
        return result;
    }

    /**
     * 支付统计扩展信息补充
     * @param list
     */
    private void preparePaymentStatisticExt(List<PayWayDailyStatisticVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<LocalDate> tollDates = list.stream().map(PayWayDailyStatisticVo::getTollDate).distinct().collect(Collectors.toList());
        List<TollPayWayExtVo> extList = orderComponent.listTollPayWayExt(PayWay.UNDEFINED.getValue(), tollDates);
        if (CollectionUtils.isEmpty(extList)) {
            return;
        }

        Map<LocalDate, TollPayWayExtVo> extMap = extList.stream().collect(Collectors.toMap(TollPayWayExtVo::getTollDate, Function.identity()));
        for (PayWayDailyStatisticVo item : list) {
            TollPayWayExtVo ext = extMap.get(item.getTollDate());
            if (ext != null) {
                item.setRemark(ext.getRemark());
            }
        }
    }

}
