package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.BusinessCategoryVo;
import com.senox.web.component.EnterpriseComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8 16:09
 */
@Service
@RequiredArgsConstructor
public class BusinessCategoryService {

    private final EnterpriseComponent enterpriseComponent;

    /**
     * 添加经营范围字典
     * @param category
     * @return
     */
    public Long addBusinessCategory(BusinessCategoryVo category) {
        return enterpriseComponent.addBusinessCategory(category);
    }

    /**
     * 更新经营范围字典
     * @param category
     */
    public void updateBusinessCategory(BusinessCategoryVo category) {
        enterpriseComponent.updateBusinessCategory(category);
    }

    /**
     * 删除经营范围字典
     * @param id
     */
    public void deleteBusinessCategory(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        enterpriseComponent.deleteBusinessCategory(id);
    }

    /**
     * 经营范围字典列表
     * @return
     */
    public List<BusinessCategoryVo> listBusinessCategory() {
        return enterpriseComponent.listBusinessCategory();
    }
}
