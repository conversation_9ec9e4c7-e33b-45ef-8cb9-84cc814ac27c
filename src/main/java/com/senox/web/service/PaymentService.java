package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.*;
import com.senox.web.component.PaymentComponent;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/17 9:11
 */
@AllArgsConstructor
@Service
public class PaymentService {

    private final PaymentComponent paymentComponent;

    /**
     * 东莞银行莞付平台密钥
     * @return
     */
    public BodSecretKeyVo getBodSecretKey() {
        return paymentComponent.getBodSecretKey();
    }
    /**
     * 新增/修改日期支付方式
     *
     * @param payWaySettingVoList 日期支付方式列表
     */
    public void payWaySettingSave(List<PayWaySettingVo> payWaySettingVoList) {
        if (CollectionUtils.isEmpty(payWaySettingVoList)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        paymentComponent.payWaySettingSave(payWaySettingVoList);
    }

    /**
     * 删除支付方式设置
     *
     * @param payWayDeleteVoList 删除参数列表
     */
    public void payWaySettingDelete(List<PayWayDeleteVo> payWayDeleteVoList) {
        if (CollectionUtils.isEmpty(payWayDeleteVoList)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        paymentComponent.payWaySettingDelete(payWayDeleteVoList);
    }

    /**
     * 获取日期支付方式设置列表
     *
     * @param search 查询参数
     * @return List<PayWaySettingVo>
     */
    public List<PayWaySettingVo> payWaySettingList(PayWaySettingSearchVo search) {
        if (Objects.isNull(search.getStartTime()) || Objects.isNull(search.getEndTime())) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return paymentComponent.payWaySettingList(search);
    }

    /**
     * 获取支付方式
     * @param orderValue 订单类型value
     * @param date 日期
     * @return PayWay
     */
    public PayWay payWaySettingGet(Integer orderValue,LocalDate date) {
        return paymentComponent.payWaySettingGet(orderValue,date);
    }

    /**
     * 获取莞银通token
     *
     * @param appId appId
     * @return 返回token
     */
    public BodTokenVo getBodToken(String appId){
        if (StringUtils.isBlank(appId)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return paymentComponent.getBodToken(appId);
    }
}
