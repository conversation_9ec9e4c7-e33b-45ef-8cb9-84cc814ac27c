package com.senox.web.service;

import com.senox.car.constant.CarFee;
import com.senox.car.constant.MonthlyCarSignType;
import com.senox.car.constant.MonthlyCarStatus;
import com.senox.car.vo.*;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.*;
import com.senox.web.component.MonthlyCarComponent;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/12/1 10:02
 */
@Service
public class MonthlyCarService {

    private static final Logger logger = LoggerFactory.getLogger(MonthlyCarService.class);

    @Autowired
    private OrderComponent orderComponent;
    @Autowired
    private MonthlyCarComponent monthlyCarComponent;

    /**
     * 添加车型
     * @param carType
     * @return
     */
    public Long addCarType(CarTypeVo carType) {
        return monthlyCarComponent.addCarType(carType);
    }

    /**
     * 更新车型
     * @param carType
     */
    public void updateCarType(CarTypeVo carType) {
        if (!WrapperClassUtils.biggerThanLong(carType.getId(), 0L)) {
            return;
        }
        monthlyCarComponent.updateCarType(carType);
    }

    /**
     * 删除车型
     * @param id
     */
    public void deleteCarType(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        CarTypeVo carType = new CarTypeVo();
        carType.setId(id);
        carType.setDisabled(Boolean.TRUE);
        updateCarType(carType);
    }

    /**
     * 根据id查找车型
     * @param id
     * @return
     */
    public CarTypeVo findCarTypeById(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return monthlyCarComponent.findCarTypeById(id);
    }

    /**
     * 车型列表
     * @param search
     * @return
     */
    public PageResult<CarTypeVo> listCarType(CarTypeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return monthlyCarComponent.listCarType(search);
    }

    /**
     * 添加月卡车
     * @param car
     * @return
     */
    public Long addMonthlyCar(MonthlyCarEditVo car) {
        return monthlyCarComponent.addMonthlyCar(car);
    }

    /**
     * 更新月卡车
     * @param car
     */
    public void updateMonthlyCar(MonthlyCarEditVo car) {
        if (!WrapperClassUtils.biggerThanLong(car.getId(), 0L)) {
            return;
        }
        monthlyCarComponent.updateMonthlyCar(car);
    }

    /**
     * 月卡车作废
     * @param cancel
     */
    public void cancelMonthlyCar(MonthlyCarCancelVo cancel) {
        if (!WrapperClassUtils.biggerThanLong(cancel.getId(), 0L)) {
            return;
        }
        monthlyCarComponent.cancelMonthlyCar(cancel);
    }

    /**
     * 删除月卡车
     * @param id
     */
    public void deleteMonthlyCar(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        MonthlyCarEditVo car = new MonthlyCarEditVo();
        car.setId(id);
        car.setDisabled(Boolean.TRUE);
        updateMonthlyCar(car);
    }

    /**
     * 支付月卡车账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payMonthlyCar(BillPayRequestVo payRequest) {
        boolean isRefund = BooleanUtils.isTrue(payRequest.getRefund());
        // 月卡车账单
        List<MonthlyCarVo> carList = monthlyCarComponent.listMonthlyCarById(payRequest.getBillIds());
        // 校验待支付账单
        checkPayingBill(carList, isRefund);

        // 车牌
        List<String> carNoList = carList.stream().map(MonthlyCarVo::getCarNo).distinct().collect(Collectors.toList());
        // 历史账单
        List<Long> preOrders = isRefund ? carList.stream().map(MonthlyCarVo::getRefundOrderId).distinct().collect(Collectors.toList())
                : carList.stream().map(MonthlyCarVo::getTollOrderId).distinct().collect(Collectors.toList());

        OrderResultVo result = null;
        try {
            OrderVo order = new OrderVo();
            // 账单标题
            String title = isRefund ? SenoxConst.TITLE_MONTHLY_CAR_REFUND : SenoxConst.TITLE_MONTHLY_CAR_ORDER;
            order.setTitle(String.format(title, DateUtils.formatYearMonth(LocalDate.now()), StringUtils.list2String(carNoList, ',')));
            order.setOrderType(OrderType.PARKING);
            order.setPayWay(payRequest.getPayWay());
            // 扫码付款
            if (order.getPayWay() == PayWay.DRC) {
                order.setTradeType(TradeType.NATIVE.name());
                order.setAuthCode(payRequest.getAuthCode());
            }
            order.setCreateIp(payRequest.getRequestIp());
            order.setItems(carList.stream().map(x -> this.monthlyCar2OrderItem(x, isRefund)).collect(Collectors.toList()));


            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            logger.info("支付月卡车账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyCarBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), isRefund, result);
                // 关闭历史账单
                if (!CollectionUtils.isEmpty(preOrders)) {
                    logger.info("Close car preOrder {}", JsonUtils.object2Json(preOrders));
                    closeCarRemoteOrders(preOrders, "已重新下单，订单id" + result.getOrderId());
                }
            }
        } finally {
            removeCarPayingLock(carList);
        }

        return result;
    }

    /**
     * 撤销月卡车账单收费
     * @param id
     */
    public void revokeMonthlyCarPaid(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        MonthlyCarVo car = monthlyCarComponent.findMonthlyCarById(id);
        if (car == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到记录");
        }
        if (car.getStatus() == MonthlyCarStatus.INIT.getValue()) {
            throw new BusinessException("订单未支付");
        }
        if (car.getStatus() == MonthlyCarStatus.REFUND.getValue()) {
            throw new BusinessException("订单已退费");
        }
        if (BooleanUtils.isTrue(car.getCancel())) {
            throw new BusinessException("订单已作废");
        }
        monthlyCarComponent.revokeMonthlyCarPaid(id);
        closeCarRemoteOrders(Collections.singletonList(car.getTollOrderId()), "取消收费...");
    }

    /**
     * 撤销月卡车账单退费
     * @param id
     */
    public void revokeMonthlyCarRefund(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        MonthlyCarVo car = monthlyCarComponent.findMonthlyCarById(id);
        if (car == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到记录");
        }
        if (car.getStatus() != MonthlyCarStatus.REFUND.getValue()) {
            throw new BusinessException("订单未退费");
        }
        if (BooleanUtils.isTrue(car.getCancel())) {
            throw new BusinessException("订单已作废");
        }
        monthlyCarComponent.revokeMonthlyCarRefund(id);
        closeCarRemoteOrders(Collections.singletonList(car.getRefundOrderId()), "取消退费...");
    }



    /**
     * 根据id查找月卡车
     * @param id
     * @return
     */
    public MonthlyCarVo findMonthlyCarById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return monthlyCarComponent.findMonthlyCarById(id);
    }

    /**
     * 根据id查找月卡车支付信息
     * @param id
     * @return
     */
    public MonthlyCarVo findMonthlyCarPayById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return monthlyCarComponent.findMonthlyCarPayById(id);
    }

    /**
     * 根据车牌查找最近的月卡车记录
     * @param carNo
     * @return
     */
    public MonthlyCarVo findRecentMonthlyCarByCarNo(String carNo) {
        if (StringUtils.isBlank(carNo)) {
            return null;
        }
        return monthlyCarComponent.findRecentMonthlyCarByCarNo(carNo);
    }

    /**
     * 月卡车列表
     * @param search
     * @return
     */
    public PageResult<MonthlyCarVo> listMonthlyCar(MonthlyCarSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return monthlyCarComponent.listMonthlyCar(search);
    }

    /**
     * 月卡车账单列表
     * @param search
     * @return
     */
    public MonthlyCarPageResult<MonthlyCarVo> listMonthlyCarBill(MonthlyCarSearchVo search) {
        if (search.getPageSize() < 1) {
            return MonthlyCarPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return monthlyCarComponent.listMonthlyCarBill(search);
    }

    /**
     * 通知更新月卡车账单结果
     * @param billIds
     * @param tollMan
     * @param isRefund
     * @param orderResult
     */
    private void notifyCarBillStatus(List<Long> billIds, Long tollMan, boolean isRefund, OrderResultVo orderResult) {
        MonthlyCarPaidVo carPaid = new MonthlyCarPaidVo();
        carPaid.setBillIds(billIds);
        carPaid.setOrderId(orderResult.getOrderId());
        carPaid.setAmount(orderResult.getAmount());
        carPaid.setPaid(orderResult.getStatus() == OrderStatus.PAID.getStatus());
        carPaid.setPaidTime(orderResult.getOrderTime());
        carPaid.setTollMan(tollMan);
        carPaid.setRefund(isRefund);
        monthlyCarComponent.updateMonthlyCarPaid(carPaid);
    }

    /**
     * 关闭i订单
     * @param orderIds
     * @param remark
     */
    private void closeCarRemoteOrders(List<Long> orderIds, String remark) {
        OrderCloseVo orderClose = new OrderCloseVo();
        orderClose.setOrderIds(orderIds);
        orderClose.setRemark(remark);
        orderComponent.closeOrders(orderClose);
    }

    /**
     * 待支付月卡车账单校验
     * @param carList
     */
    private void checkPayingBill(List<MonthlyCarVo> carList, boolean isRefund) {
        if (CollectionUtils.isEmpty(carList)) {
            throw new BusinessException("找不到月卡车记录");
        }

        // 账单状态判断
        if (isRefund) {
            if (carList.stream().anyMatch(x -> !isCarBillRefundAble(x))) {
                throw new BusinessException("存在未缴费或已退费的月卡车记录");
            }
        } else {
            if (carList.stream().anyMatch(x -> !isCarBillPayable(x))) {
                throw new BusinessException("存在已缴费的月卡车记录");
            }
        }

        // 支付加锁，防止重复支付
        carList.forEach(x -> {
            if (!RedisUtils.lock(buildCarBillPayingLockKey(x.getCarNo()), SenoxConst.Cache.TTL_60S)) {
                throw new BusinessException("月卡车账单缴费/退费操作太频繁，请稍后尝试");
            }
        });
    }

    /**
     * 账单是否可以支付
     * @param car
     * @return
     */
    private boolean isCarBillPayable(MonthlyCarVo car) {
        return !BooleanUtils.isTrue(car.getCancel()) && car.getStatus() == MonthlyCarStatus.INIT.getValue();
    }

    /**
     * 账单是否可以退款
     * @param car
     * @return
     */
    private boolean isCarBillRefundAble(MonthlyCarVo car) {
        return !BooleanUtils.isTrue(car.getCancel()) && car.getStatus() == MonthlyCarStatus.PAID.getValue();
    }

    /**
     * 删除月卡车支付锁
     * @param carList
     */
    private void removeCarPayingLock(List<MonthlyCarVo> carList) {
        if (CollectionUtils.isEmpty(carList)) {
            return;
        }

        carList.forEach(x -> RedisUtils.del(buildCarBillPayingLockKey(x.getCarNo())));
    }

    /**
     * 账单支付锁
     * @param carNo
     * @return
     */
    private String buildCarBillPayingLockKey(String carNo) {
        return String.format(SenoxConst.Cache.KEY_CAR_BILL_PAY, carNo);
    }

    /**
     * 月卡订单详情
     * @param car
     * @return
     */
    private OrderItemVo monthlyCar2OrderItem(MonthlyCarVo car, boolean isRefund) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(car.getId());
        String title = isRefund ? SenoxConst.TITLE_MONTHLY_CAR_REFUND : SenoxConst.TITLE_MONTHLY_CAR_ORDER;
        result.setProductName(String.format(title, car.getCarNo(),
               "(".concat(car.getStartDate().toString()).concat("-").concat(car.getEndDate().toString()).concat(")")));
        result.setQuantity(1);
        if (isRefund) {
            result.setPrice(DecimalUtils.subtract(BigDecimal.ZERO, car.getAmount()));
            result.setPrice(DecimalUtils.add(result.getPrice(), car.getDiscountAmount()));
        } else {
            result.setPrice(car.getTotalAmount());
        }
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);

        List<OrderItemDetailVo> list = new ArrayList<>(2);
        // 租金
        if (!DecimalUtils.equals(car.getAmount(), BigDecimal.ZERO)) {
            BigDecimal amount  = isRefund ? result.getTotalAmount() : DecimalUtils.subtract(car.getAmount(), car.getDiscountAmount());
            list.add(newOrderDetailItem(CarFee.MONTHLY_RENT, amount, car.getStartDate().toString(), car.getEndDate().toString()));
        }
        // 手续费
        if (!isRefund && car.getServiceCharged().compareTo(BigDecimal.ZERO) > 0) {
            MonthlyCarSignType signType = MonthlyCarSignType.fromValue(car.getSignType());
            CarFee fee = signType == MonthlyCarSignType.TRANSFER ? CarFee.MONTHLY_TRANS_CHARGED : CarFee.MONTHLY_CHARGED;
            list.add(newOrderDetailItem(fee, car.getServiceCharged()));
        }
        result.setDetails(list);
        return result;
    }

    /**
     * 账单明细
     * @param fee
     * @param amount
     * @return
     */
    private OrderItemDetailVo newOrderDetailItem(CarFee fee, BigDecimal amount) {
        return newOrderDetailItem(fee, amount, null, null);
    }

    /**
     * 账单明细
     * @param fee
     * @param amount
     * @param attr1
     * @param attr2
     * @return
     */
    private OrderItemDetailVo newOrderDetailItem(CarFee fee, BigDecimal amount, String attr1, String attr2) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(fee.getId().longValue());
        result.setFeeName(fee.getName());
        result.setQuantity(1);
        result.setPrice(amount);
        result.setTotalAmount(amount);
        result.setAttr1(attr1);
        result.setAttr1(attr2);
        return result;
    }
}
