package com.senox.web.service;

import com.senox.car.vo.LotteryParkingEntryQueueSearchVo;
import com.senox.car.vo.LotteryParkingEntryQueueVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.OrderResultVo;
import com.senox.web.component.LotteryParkingEntryQueueComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:41
 */
@Service
@RequiredArgsConstructor
public class LotteryParkingEntryQueueService {

    private final LotteryParkingEntryQueueComponent lotteryParkingEntryQueueComponent;

    /**
     * 排队支付
     * @param entryQueueVo
     * @return
     */
    public OrderResultVo entryQueuePay(LotteryParkingEntryQueueVo entryQueueVo) {
        return lotteryParkingEntryQueueComponent.entryQueuePay(entryQueueVo);
    }

    /**
     * 更新入场队列信息,返回车位id
     * @param id
     * @param status
     * @return
     */
    public Long updateEntryQueueStatus(Long id, Integer status) {
        return lotteryParkingEntryQueueComponent.updateEntryQueueStatus(id, status);
    }

    /**
     * 进入临时车位
     * @param id
     * @param parkingId
     */
    public void fillEntryQueue(Long id, Long parkingId) {
        lotteryParkingEntryQueueComponent.fillEntryQueue(id, parkingId);
    }

    /**
     * 入场队列列表
     * @param searchVo
     * @return
     */
    public PageResult<LotteryParkingEntryQueueVo> entryQueuePage(LotteryParkingEntryQueueSearchVo searchVo) {
        return lotteryParkingEntryQueueComponent.entryQueuePage(searchVo);
    }

    /**
     * 入场队列合计
     * @param searchVo
     * @return
     */
    public LotteryParkingEntryQueueVo sumEntryQueue(LotteryParkingEntryQueueSearchVo searchVo) {
        return lotteryParkingEntryQueueComponent.sumEntryQueue(searchVo);
    }

    /**
     * 重排
     * @param id
     */
    public void rearrangement(Long id) {
        lotteryParkingEntryQueueComponent.rearrangement(id);
    }
}
