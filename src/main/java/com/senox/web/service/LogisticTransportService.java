package com.senox.web.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import com.senox.web.component.LogisticTransportComponent;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.LogisticTransportOrderImportExcel;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticTransportService {
    private final LogisticTransportComponent transportComponent;
    private final OrderComponent orderComponent;
    private final MerchantService merchantService;
    private final DictLogisticService logisticService;

    /**
     * 添加订单
     *
     * @param orders 订单集
     */
    public void addOrder(List<LogisticTransportOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        transportComponent.addOrder(orders);
    }

    /**
     * 更新订单
     *
     * @param orders 订单集
     */
    public void updateOrder(List<LogisticTransportOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        transportComponent.updateOrder(orders);
    }

    /**
     * 订单审核
     *
     * @param orderAudit 订单审核参数
     */
    public void auditOrder(LogisticTransportOrderAuditVo orderAudit) {
        transportComponent.auditOrder(orderAudit);
    }

    /**
     * 根据id查找订单
     *
     * @param orderId 订单id
     * @return 返回订单
     */
    public LogisticTransportOrderVo findOrderById(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0)) {
            return null;
        }
        return transportComponent.findOrderById(orderId);
    }

    /**
     * 根据编号查找订单
     *
     * @param orderSerialNo 订单编号
     * @return 返回订单
     */
    public LogisticTransportOrderVo findOrderBySerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return null;
        }
        return transportComponent.findOrderBySerialNo(orderSerialNo);
    }

    /**
     * 根据编号删除订单
     *
     * @param orderSerialNo 订单编号
     */
    public void deleteOrderBySerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return;
        }
        transportComponent.deleteOrderBySerialNo(orderSerialNo);
    }

    /**
     * 订单列表
     *
     * @param search 查询参数
     * @return 返回分页列表
     */
    public List<LogisticTransportOrderVo> orderList(LogisticTransportOrderSearchVo search) {
        search.setPage(false);
        return transportComponent.orderList(search);
    }

    /**
     * 订单分页列表
     *
     * @param search 查询参数
     * @return 返回分页列表
     */
    public PageStatisticsResult<LogisticTransportOrderVo, LogisticTransportOrderStatisticsVo> orderPageList(LogisticTransportOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return new PageStatisticsResult<>(transportComponent.orderPageList(search), orderStatistics(search));
    }

    /**
     * 订单统计
     *
     * @param search 查询参数
     * @return 返回订单统计
     */
    public LogisticTransportOrderStatisticsVo orderStatistics(LogisticTransportOrderSearchVo search) {
        return transportComponent.orderStatistics(search);
    }

    /**
     * 生成结算单
     *
     * @param billGenerate – 账单生成参数
     */
    public List<Long> generateBillSettlement(LogisticTransportBillGenerateVo billGenerate) {
        return transportComponent.generateBillSettlement(billGenerate);
    }

    /**
     * 账单列表
     *
     * @param search 查询参数
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billList(LogisticTransportBillSearchVo search) {
        search.setPage(false);
        return transportComponent.billList(search);
    }

    /**
     * 账单分页列表
     *
     * @param search 查询参数
     * @return 返回账单分页列表
     */
    public PageResult<LogisticTransportBillVo> billPageList(LogisticTransportBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return transportComponent.billPageList(search);
    }

    /**
     * 根据账单id查询列表
     *
     * @param billIds 账单id
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billListById(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        return transportComponent.billListById(billIds);
    }

    /**
     * 根据订单编号查询账单
     *
     * @param orderSerialNo 订单编号
     * @return 返回账单
     */
    public LogisticTransportBillVo findByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return null;
        }
        return transportComponent.findByOrderSerialNo(orderSerialNo);
    }

    /**
     * 根据账单id删除账单
     *
     * @param billId 账单id
     */
    public void billDeleteById(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0)) {
            return;
        }
        transportComponent.billDeleteById(billId);
    }

    /**
     * 根据账单id查找账单
     *
     * @param billId 账单id
     * @return 返回账单
     */
    public LogisticTransportBillVo findBillById(Long billId) {
        return billListById(Collections.singletonList(billId)).stream().findFirst().orElse(null);
    }

    /**
     * 账单结算列表
     *
     * @param search 查询参数
     * @return 返回账单结算列表
     */
    public List<LogisticTransportBillSettlementVo> billSettlementList(LogisticTransportBillSettlementSearchVo search) {
        return transportComponent.billSettlementList(search);
    }


    /**
     * 账单结算列表
     *
     * @param ids id集
     * @return 返回账单结算列表
     */
    public List<LogisticTransportBillSettlementVo> billSettlementListById(List<Long> ids) {
        LogisticTransportBillSettlementSearchVo search = new LogisticTransportBillSettlementSearchVo();
        search.setPage(false);
        search.setIds(ids);
        return billSettlementList(search);
    }

    /**
     * 账单结算分页列表
     *
     * @param search 查询参数
     * @return 返回账单结算分页列表
     */
    public PageStatisticsResult<LogisticTransportBillSettlementVo, LogisticTransportBillSettlementStatisticsVo> billSettlementPageList(LogisticTransportBillSettlementSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return new PageStatisticsResult<>(transportComponent.billSettlementPageList(search), transportComponent.statisticsSettlement(search));
    }

    /**
     * 结算单下发
     *
     * @param send 下发参数
     */
    public void billSettlementSend(LogisticTransportBillSettlementSendVo send) {
        transportComponent.billSettlementSend(send);
    }

    /**
     * 结算单删除
     *
     * @param ids id集
     */
    public void billSettlementDeleteById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        transportComponent.billSettlementDeleteById(ids);
    }

    /**
     * 结算单详情
     *
     * @param settlementId 结算单id
     * @return 返回结算单详情
     */
    public LogisticTransportBillSettlementDetailVo billSettlementDetail(Long settlementId) {
        return transportComponent.billSettlementDetail(settlementId);
    }

    /**
     * 根据id查找结算单
     *
     * @param settlementId 结算单id
     * @return 返回查找到的结算单
     */
    public LogisticTransportBillSettlementVo billSettlementFindById(Long settlementId) {
        if (!WrapperClassUtils.biggerThanLong(settlementId, 0)) {
            return null;
        }
        return transportComponent.billSettlementFindById(settlementId);
    }

    /**
     * 账单支付
     *
     * @param payRequest 支付参数
     * @return 返回支付结果
     */
    public OrderResultVo billPay(BillPayRequestVo payRequest) {
        List<LogisticTransportBillSettlementVo> bills = billSettlementListById(payRequest.getBillIds());
        checkPayingBill(bills);
        OrderResultVo result;
        try {
            PayAmountVo payAmount = new PayAmountVo(payRequest.getPayWay(), null);
            payAmount.setAuthCode(payRequest.getAuthCode());
            payAmount.setDeviceSn(payRequest.getDeviceSn());
            OrderVo order = newPayOrder(bills, payAmount, payRequest);
            result = orderComponent.addOrder(order);
            if (null == result) {
                throw new BusinessException("下单失败");
            }
            log.info("【Logistic transport】支付账单成功，返回 {}", JsonUtils.object2Json(result));
            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), payRequest.getPayWay(), result);
            }
        } finally {
            removeBillPayLock(bills);
        }
        return result;
    }

    /**
     * 构建支付订单
     *
     * @param bills      账单集
     * @param payAmount  支付金额
     * @param payRequest 支付请求
     * @return 返回支付订单
     */
    private OrderVo newPayOrder(List<LogisticTransportBillSettlementVo> bills, PayAmountVo payAmount, BillPayRequestVo payRequest) {
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.LOGISTIC_TRANSPORT);
        result.setPayWay(payRequest.getPayWay());
        result.setCreateIp(payRequest.getRequestIp());
        result.setItems(bills.stream().map(this::buildOrderItem).collect(Collectors.toList()));
        result.setTitle(result.getItems().get(0).getProductName());
        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        return result;
    }

    /**
     * 构建订单项
     *
     * @param bill 账单
     * @return 返回订单项
     */
    private OrderItemVo buildOrderItem(LogisticTransportBillSettlementVo bill) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(String.format(SenoxConst.LOGISTIC_TRANSPORT_BILL, bill.getMerchantName(), bill.getBillDate()));
        result.setQuantity(1);
        result.setPrice(bill.getAmount());
        result.setTotalAmount(bill.getAmount());
        result.setFree(Boolean.FALSE);
        return result;
    }

    /**
     * 待支付账单校验
     *
     * @param bills 账单集
     */
    private void checkPayingBill(List<LogisticTransportBillSettlementVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            throw new BusinessException("找不到的账单");
        }
        if (bills.stream().anyMatch(x -> BooleanUtils.isTrue(x.getStatus()))) {
            throw new BusinessException("存在已缴费的账单");
        }
        bills.forEach(x -> {
            if (!RedisUtils.lock(getBillPayLockKey(x), SenoxConst.Cache.TTL_10S)) {
                throw new BusinessException("操作太频繁，请稍后尝试");
            }
        });

    }

    /**
     * 获取账单支付锁
     *
     * @param bill 账单
     * @return 返回账单支付锁
     */
    public String getBillPayLockKey(LogisticTransportBillSettlementVo bill) {
        return String.format(SenoxConst.Cache.KEY_LOGISTIC_TRANSPORT_BILL_PAY_LOCK, bill.getId());
    }

    /**
     * 移除账单支付锁
     *
     * @param bills 账单集
     */
    public void removeBillPayLock(List<LogisticTransportBillSettlementVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(getBillPayLockKey(x)));
    }

    /**
     * 通知账单状态
     *
     * @param billIds 账单id集
     * @param tollMan 收费员id
     * @param payWay  支付方式
     * @param order   支付结果
     */
    private void notifyBillStatus(List<Long> billIds, Long tollMan, PayWay payWay, OrderResultVo order) {
        BillPaidVo paid = new BillPaidVo();
        paid.setBillIds(billIds);
        paid.setOrderId(order.getOrderId());
        paid.setPaid(order.getStatus().equals(OrderStatus.PAID.getStatus()));
        paid.setAmount(order.getAmount());
        paid.setPaidTime(order.getOrderTime());
        paid.setTollMan(tollMan);
        paid.setPayWay(payWay.getValue());
        transportComponent.billSettlementStatus(paid);
    }

    /**
     * 订单导入
     *
     * @param excelDataMap excel数据map
     */
    public String orderImport(MultiValueMap<String, LogisticTransportOrderImportExcel> excelDataMap) {
        if (CollectionUtils.isEmpty(excelDataMap)) {
            return StringUtils.EMPTY;
        }
        List<LogisticTransportOrderVo> orders = new ArrayList<>(excelDataMap.size());
        StringBuilder orderImportResult = new StringBuilder();
        orderBuilder(excelDataMap, orders, orderImportResult);
        addOrder(idempotentOrder(orders, orderImportResult));
        return orderImportResult.toString();
    }

    /**
     * 订单幂等
     *
     * @param orders            订单集
     * @param orderImportResult 返回信息
     * @return 返回有效订单
     */
    private List<LogisticTransportOrderVo> idempotentOrder(List<LogisticTransportOrderVo> orders, StringBuilder orderImportResult) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<String> consignorCodes = new ArrayList<>(orders.size());
        List<String> licensePlateNumbers = new ArrayList<>(orders.size());
        List<BigDecimal> freightCharges = new ArrayList<>(orders.size());
        orders.forEach(x -> {
            consignorCodes.add(x.getConsignorCode());
            licensePlateNumbers.add(x.getLicensePlateNumber());
            freightCharges.add(x.getFreightCharge());
        });
        LogisticTransportOrderSearchVo search = new LogisticTransportOrderSearchVo();
        search.setConsignorCodes(consignorCodes);
        search.setLicensePlateNumbers(licensePlateNumbers);
        search.setFreightCharges(freightCharges);
        List<LogisticTransportOrderVo> dbOrders = orderList(search);
        if (CollectionUtils.isEmpty(dbOrders)) {
            return orders;
        }
        List<LogisticTransportOrderVo> effectiveOrders = new ArrayList<>(orders.size());
        Map<String, List<LogisticTransportOrderVo>> dbOrderMap = dbOrders.stream().collect(Collectors.groupingBy(x -> x.getConsignorCode().concat(x.getLicensePlateNumber()).concat(String.valueOf(x.getFreightCharge()))));
        for (LogisticTransportOrderVo order : orders) {
            String key = order.getConsignorCode().concat(order.getLicensePlateNumber()).concat(String.valueOf(order.getFreightCharge().setScale(2, RoundingMode.HALF_UP)));
            if (!CollectionUtils.isEmpty(dbOrderMap.get(key))) {
                log.warn("【Logistic transport】订单重复: {}", JsonUtils.object2Json(order));
                orderImportResult.append(String.format("订单重复，发货人编码: %s 车牌: %s 运费: %s", order.getConsignorCode(), order.getLicensePlateNumber(), order.getFreightCharge())).append("\r\n");
                continue;
            }
            effectiveOrders.add(order);
        }
        return effectiveOrders;
    }

    /**
     * 构建订单
     *
     * @param excelDataMap excel数据map
     */
    private void orderBuilder(MultiValueMap<String, LogisticTransportOrderImportExcel> excelDataMap, List<LogisticTransportOrderVo> orders, StringBuilder orderImportResult) {
        List<DictLogisticVo> drivers = logisticService.listDictLogisticByDriver(10000);
        if (CollectionUtils.isEmpty(drivers)) {
            throw new BusinessException("司机信息未找到");
        }
        Map<String, DictLogisticVo> driverMap = drivers.stream().collect(Collectors.toMap(x -> x.getName().concat(x.getKey()), Function.identity()));
        excelDataMap.forEach((sheetName, excelDataList) -> {
            Map<String, List<LogisticTransportOrderImportExcel>> excelDataGroup = excelDataList.stream().collect(Collectors.groupingBy(LogisticTransportOrderImportExcel::getConsignorCode));
            List<MerchantVo> merchants = merchantService.findByLtSerialList(new ArrayList<>(excelDataGroup.keySet()));
            if (CollectionUtils.isEmpty(merchants)) {
                log.error("【Logistic transport】未找到商户信息");
                orderImportResult.append("未找到商户信息");
                return;
            }
            Map<String, MerchantVo> ltMerchantMap = merchants.stream().collect(Collectors.toMap(MerchantVo::getLtSerial, Function.identity()));
            excelDataGroup.forEach((merchantSerial, orderImportExcels) -> {
                MerchantVo merchantVo = ltMerchantMap.get(merchantSerial);
                if (null == merchantVo) {
                    log.error("【Logistic transport】未找到编号为[{}]的城际运输商户信息", merchantSerial);
                    orderImportResult.append(String.format("未找到编号为[%s]的城际运输商户信息", merchantSerial)).append("\r\n");
                    return;
                }
                orderImportExcels.forEach(x -> {
                    x.setConsignorId(merchantVo.getId());
                    x.setConsignorName(merchantVo.getName());
                    x.setConsignorPhone(merchantVo.getContact());
                    LogisticTransportOrderVo order = excelDataToVo(x);
                    DictLogisticVo driverInfo = driverMap.get(order.getDriverName().concat(order.getLicensePlateNumber()));
                    if (null == driverInfo || StringUtils.isBlank(driverInfo.getAttr2())) {
                        orderImportResult.append(String.format("未找到司机: %s   %s", order.getDriverName(), order.getLicensePlateNumber())).append("\r\n");
                        return;
                    }
                    order.setDriverPhone(driverInfo.getAttr2());
                    orders.add(order);
                });
            });
        });
    }

    /**
     * excel数据转换为订单数据
     *
     * @param excelData excel数据
     * @return 返回订单数据
     */
    public LogisticTransportOrderVo excelDataToVo(LogisticTransportOrderImportExcel excelData) {
        LogisticTransportOrderVo order = new LogisticTransportOrderVo();
        order.setConsignorId(excelData.getConsignorId());
        order.setConsignorName(excelData.getConsignorName());
        order.setConsignorPhone(excelData.getConsignorPhone());
        order.setConsignorCode(excelData.getConsignorCode());
        order.setConsigneeName(excelData.getConsigneeName());
        order.setConsigneePhone(excelData.getConsigneePhone());
        order.setCategory(excelData.getCategory());
        order.setDriverName(excelData.getDriverName());
        order.setLicensePlateNumber(excelData.getLicensePlateNumber());
        order.setCharter(excelData.getCharter());
        order.setDepartureStation(excelData.getDepartureStation());
        order.setDestinationStation(excelData.getDestinationStation());
        order.setPieces(excelData.getPieces());
        order.setLoadingWeight(excelData.getLoadingWeight());
        order.setFreightCharge(excelData.getFreightCharge());
        order.setOtherCharge(excelData.getOtherCharge());
        order.setReceivableFreightCharge(excelData.getReceivableFreightCharge());
        order.setPayer(excelData.getPayer());
        order.setRemark(excelData.getRemark());
        return order;
    }
}
