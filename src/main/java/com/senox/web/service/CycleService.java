package com.senox.web.service;

import com.senox.car.vo.*;
import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.web.component.CycleComponent;
import com.senox.web.component.OrderComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CycleService extends BillService{

    private final CycleComponent cycleComponent;
    private final OrderComponent orderComponent;

    /**
     * 添加三轮车
     * @param cycle
     * @return
     */
    public Long addCycle(CycleVo cycle) {
        return cycleComponent.addCycle(cycle);
    }

    /**
     * 更新三轮车
     * @param cycle
     */
    public void updateCycle(CycleVo cycle) {
        if (!WrapperClassUtils.biggerThanLong(cycle.getId(), 0L)) {
            return;
        }
        cycleComponent.updateCycle(cycle);
    }

    /**
     * 更新票据号
     * @param tollSerial
     */
    public void updateCycleTollSerial(TollSerialVo tollSerial) {
        if (!WrapperClassUtils.biggerThanLong(tollSerial.getBillId(), 0L)) {
            return;
        }
        cycleComponent.updateCycleTollSerial(tollSerial);
    }

    /**
     * 删除三轮车
     * @param cycle
     */
    public void deleteCycle(CycleDeleteVo cycle) {
        if (!WrapperClassUtils.biggerThanLong(cycle.getId(), 0L)) {
            return;
        }
        cycleComponent.deleteCycle(cycle);
    }

    /**
     * 支付三轮车费用
     * @param cyclePay
     */
    public void payCycleFee(CyclePayVo cyclePay) {
        if (!WrapperClassUtils.biggerThanLong(cyclePay.getId(), 0L)) {
            return;
        }
        cycleComponent.payCycleFee(cyclePay);
    }

    public void revokeCyclePay(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        cycleComponent.revokeCyclePay(id);
    }

    /**
     * 查找三轮车
     * @param id
     * @return
     */
    public CycleVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return cycleComponent.findById(id);
    }

    /**
     * 三轮车列表
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<CycleVo, CycleVo> listCyclePage(CycleSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return cycleComponent.listCyclePage(searchVo);
    }

    /**
     * 三轮车账单列表
     * @param searchVo
     * @return
     */
    public CycleBillPageResult<CycleBillVo> listCycleBillPage(CycleBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return CycleBillPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return cycleComponent.listCycleBillPage(searchVo);
    }

    /**
     * 生成号牌
     * @param prefix
     * @return
     */
    public String genLicense(String prefix) {
        if (StringUtils.isBlank(prefix) || prefix.length() < 4) {
            throw new InvalidParameterException("无效的号牌前缀");
        }
        return cycleComponent.genLicense(prefix);
    }

    /**
     * 号牌是否存在
     * @param license
     * @param id
     * @return
     */
    public boolean checkLicenseExists(String license, Long id) {
        if (StringUtils.isBlank(license) || license.length() < 8) {
            throw new InvalidParameterException("无效的号牌");
        }
        return cycleComponent.checkLicenseExit(license, id);
    }

    /**
     * 换新车牌
     * @param cycleVo
     * @return
     */
    public Long renewCycle(CycleVo cycleVo) {
        return cycleComponent.renewCycle(cycleVo);
    }

    /**
     * 更新用账单支付结果
     * @param billPaidVo
     */
    public void updateCycleBillStatus(BillPaidVo billPaidVo) {
        cycleComponent.updateCycleBillStatus(billPaidVo);
    }

    /**
     * 撤销支付账单
     * @param id
     */
    public void revokeCycleBillPayment(Long id) {
        cycleComponent.revokeCycleBillPayment(id);
    }

    /**
     * 获取三轮车信息列表
     * @param ids
     * @return
     */
    public List<CycleVo> listByIds(List<Long> ids) {
        return cycleComponent.listByIds(ids);
    }

    /**
     * 账单状态校验
     * @param list
     */
    private void checkPayingBill(List<CycleVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 支付三轮车收费账单
     * @param payRequest
     * @param isFree
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest, Boolean isFree) {
        // 账单
        List<CycleVo> cycleVoList = listByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(cycleVoList);

        cycleVoList.forEach(x -> RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_10M));
        OrderResultVo result = null;
        try {
            OrderVo order = newPayOrder(cycleVoList, newPayAmountRequest(payRequest), payRequest.getRequestIp(), isFree);

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付三轮车车牌账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            log.info("pay cycle bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));

        } finally {
            removeBillPayingLock(cycleVoList);
        }

        log.info("finish pay cycle bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 构建支付订单
     * @param cycleVoList
     * @param payAmount
     * @param ip
     * @param isFree
     * @return
     */
    private OrderVo newPayOrder(List<CycleVo> cycleVoList, PayAmountVo payAmount, String ip, Boolean isFree) {
        final boolean isRefund = DecimalUtils.isNegative(payAmount.getAmount());

        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.CYCLE);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(cycleVoList.stream().map(x -> newPayOrderItem(x, isFree)).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(getOrderTitle(isRefund), LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 构建支付订单明细
     * @param cycleVo
     * @return
     */
    private OrderItemVo newPayOrderItem(CycleVo cycleVo, Boolean isFree) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(cycleVo.getId());
        result.setProductName(buildCycleBillTitle(cycleVo));
        result.setQuantity(1);
        result.setPrice(BooleanUtils.isTrue(isFree) ? BigDecimal.ZERO : BigDecimal.valueOf(60));
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        result.setDetails(Collections.singletonList(newPayOrderItemDetail(isFree)));
        return result;
    }

    /**
     * 构建支付订单明细详情
     * @param isFree
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(Boolean isFree) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(19L);
        result.setFeeName("三轮车车牌账单");
        result.setQuantity(1);
        result.setPrice(BooleanUtils.isTrue(isFree) ? BigDecimal.ZERO : BigDecimal.valueOf(60));
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 三轮车车牌账单标题
     * @param cycleVo
     * @return
     */
    private String buildCycleBillTitle(CycleVo cycleVo) {
        return String.format(SenoxConst.TITLE_CYCLE_BILL, cycleVo.getOwner(), cycleVo.getLicense());
    }

    /**
     * 三轮车车牌费用账单锁
     *
     * @param cycleVo
     * @return
     */
    private String buildPayLockKey(CycleVo cycleVo) {
        return String.format(SenoxConst.Cache.KEY_CYCLE_BILL_PAY, cycleVo.getLicense(),
                cycleVo.getOwner() + "-" + cycleVo.getId());
    }

    /**
     * 移除支付账单锁
     * @param cycleVos
     */
    private void removeBillPayingLock(List<CycleVo> cycleVos) {
        if (CollectionUtils.isEmpty(cycleVos)) {
            return;
        }
        cycleVos.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo orderResult) {
        BillPaidVo result = new BillPaidVo();
        result.setBillIds(billIds);
        result.setOrderId(orderResult.getOrderId());
        result.setAmount(orderResult.getAmount());
        result.setPaid(orderResult.getStatus() == OrderStatus.PAID.getStatus());
        result.setPaidTime(orderResult.getOrderTime());
        result.setTollMan(tollMan);
        result.setRefund(orderResult.getAmount() == null || DecimalUtils.isNegative(orderResult.getAmount()));
        updateCycleBillStatus(result);
    }
}
