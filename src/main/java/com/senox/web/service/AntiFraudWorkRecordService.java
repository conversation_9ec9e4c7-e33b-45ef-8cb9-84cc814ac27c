package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.vo.AntiFraudWorkRecordSearchVo;
import com.senox.realty.vo.AntiFraudWorkRecordStatistics;
import com.senox.realty.vo.AntiFraudWorkRecordVo;
import com.senox.web.component.AntiFraudWorkRecordComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-02
 **/
@RequiredArgsConstructor
@Service
public class AntiFraudWorkRecordService {
    private final AntiFraudWorkRecordComponent workRecordComponent;

    /**
     * 添加
     * @param workRecord 工作记录参数集
     */
    public void add(AntiFraudWorkRecordVo workRecord) {
        workRecordComponent.add(workRecord);
    }

    /**
     * 更新
     * @param workRecord 工作记录参数
     */
    public void update(AntiFraudWorkRecordVo workRecord) {
        workRecordComponent.update(workRecord);
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的数据
     */
    public AntiFraudWorkRecordVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return workRecordComponent.findById(id);
    }

    /**
     * 根据id批量删除
     * @param ids id集
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        workRecordComponent.deleteByIds(ids);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(AntiFraudWorkRecordSearchVo search) {
        return workRecordComponent.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<AntiFraudWorkRecordVo> list(AntiFraudWorkRecordSearchVo search) {
        return workRecordComponent.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<AntiFraudWorkRecordVo, AntiFraudWorkRecordStatistics> pageList(AntiFraudWorkRecordSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return workRecordComponent.pageList(search);
    }
}
