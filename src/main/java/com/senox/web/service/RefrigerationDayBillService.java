package com.senox.web.service;

import com.senox.cold.vo.RefrigerationDayBillDto;
import com.senox.cold.vo.RefrigerationDayBillSearchVo;
import com.senox.cold.vo.RefrigerationDayBillVo;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.RefrigerationBillComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16 10:24
 */
@RequiredArgsConstructor
@Service
public class RefrigerationDayBillService {

    private final RefrigerationBillComponent billComponent;

    /**
     * 添加日账单
     * @param dto
     * @return
     */
    public Long addDayBill(RefrigerationDayBillDto dto) {
        return billComponent.addDayBill(dto);
    }

    /**
     * 更新日账单
     * @param dto
     */
    public void updateDayBill(RefrigerationDayBillDto dto) {
        if (!WrapperClassUtils.biggerThanLong(dto.getId(), 0L)) {
            return;
        }

        billComponent.updateDayBill(dto);
    }

    /**
     * 批量保存日账单
     * @param list
     */
    public String saveDayBillBatch(List<RefrigerationDayBillDto> list) {
        String result = null;
        if (!CollectionUtils.isEmpty(list)) {
            result = billComponent.saveDayBillBatch(list);
        }

        return StringUtils.trimToEmpty(result);
    }

    /**
     * 删除日账单
     * @param ids
     */
    public void deleteDayBill(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        billComponent.deleteDayBill(ids);
    }

    /**
     * 获取日账单
     * @param id
     * @return
     */
    public RefrigerationDayBillVo findDayBillById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? billComponent.findDayBillById(id) : null;
    }

    /**
     * 合计日账单
     * @param search
     * @return
     */
    public RefrigerationDayBillVo sumDayBill(RefrigerationDayBillSearchVo search) {
        return billComponent.sumDayBill(search);
    }

    /**
     * 日账单列表
     * @param search
     * @return
     */
    public PageResult<RefrigerationDayBillVo> listDayBill(RefrigerationDayBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return billComponent.listDayBill(search);
    }

}
