package com.senox.web.service;

import com.senox.cold.vo.*;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.DeliveryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/27 14:43
 */
@Service
public class DeliveryService {

    @Autowired
    private DeliveryComponent deliveryComponent;

    /**
     * 新增仓库
     *
     * @param warehouseVo
     * @return
     */
    public Long addDeliveryWarehouse(DeliveryWarehouseVo warehouseVo) {
        if (StringUtils.isBlank(warehouseVo.getName())) {
            return 0L;
        }
        return deliveryComponent.addDeliveryWarehouse(warehouseVo);
    }

    /**
     * 批量新增仓库
     *
     * @param list
     */
    public void batchAddDeliveryWarehouse(List<DeliveryWarehouseVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        deliveryComponent.batchAddDeliveryWarehouse(list);
    }

    /**
     * 更新仓库
     *
     * @param warehouseVo
     */
    public void updateDeliveryWarehouse(DeliveryWarehouseVo warehouseVo) {
        if (!WrapperClassUtils.biggerThanLong(warehouseVo.getId(), 0L)) {
            return;
        }
        deliveryComponent.updateDeliveryWarehouse(warehouseVo);
    }

    /**
     * 删除仓库
     *
     * @param id
     */
    public void deleteDeliveryWarehouse(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.deleteDeliveryWarehouse(id);
    }

    /**
     * 根据id获取仓库
     *
     * @param id
     * @return
     */
    public DeliveryWarehouseVo findDeliveryWarehouseById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return deliveryComponent.findDeliveryWarehouseById(id);
    }

    /**
     * 仓库列表
     *
     * @param search
     * @return
     */
    public PageResult<DeliveryWarehouseVo> listDeliveryWarehouse(DeliveryWarehouseSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return deliveryComponent.listDeliveryWarehouse(search);
    }

    /**
     * 新增商品
     *
     * @param skuVo
     * @return
     */
    public Long addSku(SkuVo skuVo) {
        return deliveryComponent.addSku(skuVo);
    }

    /**
     * 批量新增商品
     *
     * @param list
     */
    public void batchAddSku(List<SkuVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        deliveryComponent.batchAddSku(list);
    }

    /**
     * 更新商品
     *
     * @param skuVo
     */
    public void updateSku(SkuVo skuVo) {
        if (!WrapperClassUtils.biggerThanLong(skuVo.getId(), 0L)) {
            return;
        }
        deliveryComponent.updateSku(skuVo);
    }

    /**
     * 删除商品
     *
     * @param id
     */
    public void deleteSku(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.deleteSku(id);
    }

    /**
     * 根据id获取商品
     *
     * @param id
     * @return
     */
    public SkuVo findSkuById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return deliveryComponent.findSkuById(id);
    }

    /**
     * 商品列表
     *
     * @param search
     * @return
     */
    public PageResult<SkuVo> listSku(SkuSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return deliveryComponent.listSku(search);
    }

    /**
     * 所有商品
     * @return
     */
    public List<SkuVo> listSkuAll() {
        return deliveryComponent.listSkuAll();
    }

    /**
     * 添加物流订单
     *
     * @param orderVo
     * @return
     */
    public Long addDeliveryOrder(DeliveryOrderVo orderVo) {
        return deliveryComponent.addDeliveryOrder(orderVo);
    }

    /**
     * 发货
     *
     * @param orderId
     */
    public void sendDeliveryOrder(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return;
        }
        deliveryComponent.sendDeliveryOrder(orderId);
    }

    /**
     * 删除物流订单
     *
     * @param id
     */
    public void deleteDeliveryOrder(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.deleteDeliveryOrder(id);
    }

    /**
     * 用户物流订单列表
     *
     * @param search
     * @return
     */
    public List<DeliveryOrderVo> orderInfoList(DeliveryOrderSearchVo search) {
        return deliveryComponent.orderInfoList(search);
    }

    /**
     * 根据id获取物流订单
     *
     * @param orderId
     * @return
     */
    public DeliveryOrderVo deliveryOrderById(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return null;
        }
        return deliveryComponent.deliveryOrderById(orderId);
    }

    /**
     * 根据订单id集合获取物流订单
     *
     * @param ids
     * @return
     */
    public List<DeliveryOrderVo> deliveryOrderByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return deliveryComponent.deliveryOrderByIds(ids);
    }

    /**
     * 物流订单列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<DeliveryOrderVo> orderList(DeliveryOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return deliveryComponent.orderList(searchVo);
    }

    /**
     * 最近一天未发货的订单数量
     *
     * @return
     */
    public Integer countStatusByUnSend() {
        return deliveryComponent.countStatusByUnSend();
    }

    /**
     * 添加物流员工
     *
     * @param employeeVo
     * @return
     */
    public Long addDeliveryEmployee(DeliveryEmployeeVo employeeVo) {
        return deliveryComponent.addDeliveryEmployee(employeeVo);
    }

    /**
     * 更新物流员工
     *
     * @param employeeVo
     */
    public void updateDeliveryEmployee(DeliveryEmployeeVo employeeVo) {
        if (!WrapperClassUtils.biggerThanLong(employeeVo.getId(), 0L)) {
            return;
        }
        deliveryComponent.updateDeliveryEmployee(employeeVo);
    }

    /**
     * 更新物流员工密码
     *
     * @param changePwdVo
     */
    public void changePassword(DeliveryEmployeeChangePwdVo changePwdVo) {
        deliveryComponent.changePassword(changePwdVo);
    }

    /**
     * 删除物流员工
     *
     * @param id
     */
    public void deleteDeliveryEmployee(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.deleteDeliveryEmployee(id);
    }

    /**
     * 根据id获取物流员工
     *
     * @param id
     * @return
     */
    public DeliveryEmployeeVo deliveryEmployeeById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return deliveryComponent.deliveryEmployeeById(id);
    }

    /**
     * 物流员工列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<DeliveryEmployeeVo> deliveryEmployeeList(DeliveryEmployeeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return deliveryComponent.deliveryEmployeeList(searchVo);
    }

    /**
     * 添加物流退货单(草稿状态)
     *
     * @param returnVo
     * @return
     */
    public Long addDeliveryReturn(DeliveryReturnVo returnVo) {
        return deliveryComponent.addDeliveryReturn(returnVo);
    }

    /**
     * 更新物流退货单
     *
     * @param returnVo
     */
    public void updateDeliveryReturn(DeliveryReturnVo returnVo) {
        if (!WrapperClassUtils.biggerThanLong(returnVo.getId(), 0L)) {
            return;
        }
        deliveryComponent.updateDeliveryReturn(returnVo);
    }

    /**
     * 更新物流退货单状态为正常
     *
     * @param id
     */
    public void updateDeliveryReturnState(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.updateDeliveryReturnState(id);
    }

    /**
     * 删除物流退货单
     *
     * @param id
     */
    public void deleteDeliveryReturn(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        deliveryComponent.deleteDeliveryReturn(id);
    }

    /**
     * 根据id获取物流退货单
     *
     * @param id
     * @return
     */
    public DeliveryReturnVo deliveryReturnById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return deliveryComponent.deliveryReturnById(id);
    }

    /**
     * 根据退货单id集合获取退货单
     *
     * @param ids
     * @return
     */
    public List<DeliveryReturnVo> deliveryReturnByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return deliveryComponent.deliveryReturnByIds(ids);
    }

    /**
     * 物流退货单列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<DeliveryReturnVo> deliveryReturnList(DeliveryReturnSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return deliveryComponent.deliveryReturnList(searchVo);
    }

    /**
     * 最近一天未提交的退货单数量
     *
     * @return
     */
    public Integer countStatusByDraft() {
        return deliveryComponent.countStatusByDraft();
    }

    /**
     * 物流退货单详细列表
     *
     * @param searchVo
     * @return
     */
    public List<DeliveryReturnVo> returnInfoList(DeliveryReturnSearchVo searchVo) {
        return deliveryComponent.returnInfoList(searchVo);
    }

    /**
     * 批量添加退货计划单
     * @param list
     */
    public void batchAddDeliveryReturnPlan(List<DeliveryReturnPlanVo> list) {
        deliveryComponent.batchAddDeliveryReturnPlan(list);
    }

    /**
     * 添加退货计划单
     * @param planVo
     */
    public void addDeliveryReturnPlan(DeliveryReturnPlanVo planVo) {
        deliveryComponent.addDeliveryReturnPlan(planVo);
    }

    /**
     * 更新退货计划单
     * @param planVo
     */
    public void updateDeliveryReturnPlan(DeliveryReturnPlanVo planVo) {
        deliveryComponent.updateDeliveryReturnPlan(planVo);
    }

    /**
     * 根据id获取计划单
     * @param id
     * @return
     */
    public DeliveryReturnPlanVo deliveryReturnPlanById(Long id) {
        return deliveryComponent.deliveryReturnPlanById(id);
    }

    /**
     * 根据id删除计划单
     * @param id
     */
    public void deleteReturnPlan(Long id) {
        deliveryComponent.deleteReturnPlan(id);
    }

    /**
     * 批量删除计划单
     * @param ids
     */
    public void deleteBatchByIds(List<Long> ids) {
        deliveryComponent.deleteBatchByIds(ids);
    }

    /**
     * 退货计划单日期列表
     * @param searchVo
     * @return
     */
    public PageResult<DeliveryReturnPlanDateVo> localDatePlanList(DeliveryReturnPlanSearchVo searchVo) {
        return deliveryComponent.localDatePlanList(searchVo);
    }

    /**
     * 退货计划单列表
     * @param searchVo
     * @return
     */
    public List<DeliveryReturnPlanVo> listReturnPlan(DeliveryReturnPlanSearchVo searchVo) {
        return deliveryComponent.listReturnPlan(searchVo);
    }

    /**
     * 检验是否导入退货计划单
     * @param planDate
     */
    public void checkDayPlan(LocalDate planDate) {
        deliveryComponent.checkDayPlan(planDate);
    }
}
