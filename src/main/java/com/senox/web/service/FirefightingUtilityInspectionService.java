package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingUtilityInspectionSearchVo;
import com.senox.realty.vo.FirefightingUtilityInspectionVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/14 10:03
 */
@Service
@RequiredArgsConstructor
public class FirefightingUtilityInspectionService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加公共消防设施巡检记录
     * @param inspection
     * @return
     */
    public Long addUtilityInspection(FirefightingUtilityInspectionVo inspection) {
        return firefightingComponent.addUtilityInspection(inspection);
    }

    /**
     * 更新公共消防设施巡检记录
     * @param inspection
     */
    public void updateUtilityInspection(FirefightingUtilityInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateUtilityInspection(inspection);
    }

    /**
     * 删除公共消防设施巡检记录
     * @param id
     */
    public void deleteUtilityInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteUtilityInspection(id);
    }

    /**
     * 获取公共消防设施巡检记录
     * @param id
     * @return
     */
    public FirefightingUtilityInspectionVo findUtilityInspectionById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findUtilityInspectionById(id) : null;
    }

    /**
     * 公共消防设施巡检记录页
     * @param search
     * @return
     */
    public PageResult<FirefightingUtilityInspectionVo> listUtilityInspectionPage(FirefightingUtilityInspectionSearchVo search) {
        return firefightingComponent.listUtilityInspectionPage(search);
    }
}
