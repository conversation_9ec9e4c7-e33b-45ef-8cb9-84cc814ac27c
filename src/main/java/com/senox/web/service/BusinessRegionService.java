package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.vo.BusinessRegionVo;
import com.senox.web.component.RealtyDictionaryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 11:18
 */
@Service
public class BusinessRegionService {

    @Autowired
    private RealtyDictionaryComponent realtyDictionaryComponent;

    /**
     * 添加经营区域
     * @param region
     * @return
     */
    public Long addRegion(BusinessRegionVo region) {
        if (StringUtils.isBlank(region.getName())) {
            return 0L;
        }
        return realtyDictionaryComponent.addRegion(region);
    }

    /**
     * 更新经营区域
     * @param region
     */
    public void updateRegion(BusinessRegionVo region) {
        if (!WrapperClassUtils.biggerThanLong(region.getId(), 0L)) {
            return;
        }
        realtyDictionaryComponent.updateRegion(region);
    }

    /**
     * 删除经营区域
     * @param id
     */
    public void deleteRegion(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        BusinessRegionVo region = new BusinessRegionVo();
        region.setId(id);
        region.setDisabled(Boolean.TRUE);
        updateRegion(region);
    }

    /**
     * 根据id获取经营区域
     * @param id
     * @return
     */
    public BusinessRegionVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return realtyDictionaryComponent.findRegionById(id);
    }

    /**
     * 经营区域列表
     * @return
     */
    public List<BusinessRegionVo> listAll() {
        return realtyDictionaryComponent.listRegion();
    }
}
