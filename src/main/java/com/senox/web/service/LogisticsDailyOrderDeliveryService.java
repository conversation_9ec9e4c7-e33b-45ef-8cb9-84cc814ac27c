package com.senox.web.service;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import com.senox.web.component.LogisticsDailyOrderDeliveryComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@RequiredArgsConstructor
@Service
public class LogisticsDailyOrderDeliveryService {
    private final LogisticsDailyOrderDeliveryComponent dailyOrderDeliveryComponent;


    /**
     * 批量添加
     *
     * @param dailyOrderDeliveryVos 物流日订单配送列表
     */
    public void addBatch(List<LogisticsDailyOrderDeliveryVo> dailyOrderDeliveryVos) {
        dailyOrderDeliveryComponent.addBatch(dailyOrderDeliveryVos);
    }

    /**
     * 根据id查找配送
     *
     * @param id id
     * @return 返回查找到的配送
     */
    public LogisticsDailyOrderDeliveryVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return dailyOrderDeliveryComponent.findById(id);
    }

    /**
     * 修改
     *
     * @param dailyOrderDeliveryVo 物流日订单配送
     */
    public void update(LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo) {
        dailyOrderDeliveryComponent.update(dailyOrderDeliveryVo);
    }

    /**
     * 删除
     *
     * @param id 物流日订单配送id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        dailyOrderDeliveryComponent.deleteById(id);
    }

    /**
     * 列表查询
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    public List<LogisticsDailyOrderDeliveryVo> list(LogisticsDailyOrderDeliverySearchVo searchVo) {
        searchVo.setPage(false);
        return dailyOrderDeliveryComponent.list(searchVo);
    }

    /**
     * 列表分页查询
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<LogisticsDailyOrderDeliveryVo, LogisticsDailyOrderDeliveryTotalAmountVo> listPage(LogisticsDailyOrderDeliverySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return dailyOrderDeliveryComponent.listPage(searchVo);
    }

}
