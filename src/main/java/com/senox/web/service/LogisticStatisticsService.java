package com.senox.web.service;

import com.senox.tms.vo.*;
import com.senox.web.component.LogisticStatisticsComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 16:23
 */
@Service
@RequiredArgsConstructor
public class LogisticStatisticsService {

    private final LogisticStatisticsComponent logisticStatisticsComponent;


    /**
     * 批量添加货物统计报表
     * @param reportVoList
     */
    public void batchAddLogisticStatisticsDayReport(List<LogisticStatisticsDayReportVo> reportVoList) {
        logisticStatisticsComponent.batchAddLogisticStatisticsDayReport(reportVoList);
    }

    /**
     * 添加货物统计报表
     * @param logisticStatisticsDayReportVo
     * @return
     */
    public Long addLogisticStatisticsDayReport(LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        return logisticStatisticsComponent.addLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
    }

    /**
     * 更新货物统计报表
     * @param logisticStatisticsDayReportVo
     */
    public void updateLogisticStatisticsDayReport(LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        logisticStatisticsComponent.updateLogisticStatisticsDayReport(logisticStatisticsDayReportVo);
    }

    /**
     * 根据Id获取货物统计报表
     * @param id
     * @return
     */
    public LogisticStatisticsDayReportVo findLogisticStatisticsDayReportById(Long id) {
        return logisticStatisticsComponent.findLogisticStatisticsDayReportById(id);
    }

    /**
     * 根据Id删除货物统计报表
     * @param id
     */
    public void deleteLogisticStatisticsDayReportById(Long id) {
        logisticStatisticsComponent.deleteLogisticStatisticsDayReportById(id);
    }

    /**
     * 货物统计报表分页
     * @param searchVo
     * @return
     */
    public LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(LogisticStatisticsDayReportSearchVo searchVo) {
        return logisticStatisticsComponent.page(searchVo);
    }

    /**
     * 货物统计批量收款
     * @param batchUpdateVo
     */
    public void batchUpdate(LogisticStatisticsDayReportBatchUpdateVo batchUpdateVo) {
        logisticStatisticsComponent.batchUpdate(batchUpdateVo);
    }
}
