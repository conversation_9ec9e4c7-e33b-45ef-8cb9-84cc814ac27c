package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.dm.vo.FileVo;
import com.senox.web.component.FileComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/12/10 15:22
 */
@Service
public class FileService {

    @Autowired
    private FileComponent fileComponent;

    /**
     * 上传文件
     * @param type
     * @param prefix
     * @param file
     * @return
     */
    public FileVo uploadFile(String type, String prefix, MultipartFile file) {
        if (StringUtils.isBlank(type) || file.isEmpty()) {
            return null;
        }
        return fileComponent.uploadFile(type, prefix, file);
    }
}
