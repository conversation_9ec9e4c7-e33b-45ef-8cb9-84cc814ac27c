package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import com.senox.realty.vo.RealtyRegionDictVo;
import com.senox.web.component.RealtyRegionDictComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-28
 **/
@Service
@RequiredArgsConstructor
public class RealtyRegionDictService {
    private final RealtyRegionDictComponent regionDictComponent;

    /**
     * 添加字典
     * @param regionDict 字典信息
     */
    public void add(RealtyRegionDictVo regionDict) {
        regionDictComponent.add(regionDict);
    }

    /**
     * 根据id删除字典
     * @param id 字典id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return;
        }
        regionDictComponent.deleteById(id);
    }

    /**
     * 根据id更新字典
     * @param regionDict 字典信息
     */
    public void updateById(RealtyRegionDictVo regionDict) {
        if (null == regionDict || !WrapperClassUtils.biggerThanLong(regionDict.getId(), 0)) {
            return;
        }
        regionDictComponent.updateById(regionDict);
    }

    /**
     * 分页查询字典
     * @param search 查询条件
     * @return 分页结果
     */
    public PageResult<RealtyRegionDictVo> pageList(RealtyRegionDictSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return regionDictComponent.pageList(search);
    }
}
