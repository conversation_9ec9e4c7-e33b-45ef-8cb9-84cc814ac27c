package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.CosVo;
import com.senox.user.vo.RoleVo;
import com.senox.web.component.RoleCosComponent;
import com.senox.web.vo.CosTreeNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/28 11:25
 */
@Service
public class RoleCosService {

    @Autowired
    private RoleCosComponent roleCosComponent;

    /**
     * 添加角色
     * @param role
     * @return
     */
    public Long addRole(RoleVo role) {
        if (StringUtils.isBlank(role.getName())) {
            return 0L;
        }
        return roleCosComponent.addRole(role);
    }

    /**
     * 更新角色
     * @param role
     */
    public void updateRole(RoleVo role) {
        if (!WrapperClassUtils.biggerThanLong(role.getId(), 0L)) {
            return;
        }
        roleCosComponent.updateRole(role);
    }

    /**
     * 删除角色
     * @param id
     */
    public void deleteRole(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        roleCosComponent.deleteRole(id);
    }

    /**
     * 获取角色
     * @param id
     * @return
     */
    public RoleVo findRoleById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return roleCosComponent.findRoleById(id);
    }

    /**
     * 角色列表
     * @return
     */
    public List<RoleVo> listRole() {
        return roleCosComponent.listRole();
    }

    /**
     * 添加权限项
     * @param cos
     * @return
     */
    public Long addCos(CosVo cos) {
        if (StringUtils.isBlank(cos.getName())) {
            return 0L;
        }
        return roleCosComponent.addCos(cos);
    }

    /**
     * 更新权限项
     * @param cos
     */
    public void updateCos(CosVo cos) {
        if (!WrapperClassUtils.biggerThanLong(cos.getId(), 0L)) {
            return;
        }
        roleCosComponent.updateCos(cos);
    }

    public void deleteCos(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        roleCosComponent.deleteCos(id);
    }

    /**
     * 根据id查找权限项
     * @param id
     * @return
     */
    public CosVo findCosById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return roleCosComponent.findCosById(id);
    }

    /**
     * 权限树
     * @return
     */
    public List<CosTreeNode> listCosTree() {
        List<CosVo> cosList = roleCosComponent.listCos();
        if (CollectionUtils.isEmpty(cosList)) {
            return Collections.emptyList();
        }

        // 权限树
        cosList = cosList.stream().sorted(Comparator.comparing(CosVo::getOrderNo)).collect(Collectors.toList());
        return buildCosTree(cosList, 0L);
    }

    /**
     * 权限树
     * @param cosList
     * @return
     */
    private List<CosTreeNode> prepareCosTree(List<CosVo> cosList) {
        if (CollectionUtils.isEmpty(cosList)) {
            return Collections.emptyList();
        }

        return buildCosTree(cosList, 0L);
    }

    /**
     * 构建权限树
     * @param list
     * @param parentId
     * @return
     */
    private List<CosTreeNode> buildCosTree(List<CosVo> list, Long parentId) {
        List<CosTreeNode> resultList = new ArrayList<>(list.size());
        for (CosVo item : list) {
            if (Objects.equals(item.getParentId(), parentId)) {
                CosTreeNode node = cos2TreeNode(item);
                node.setChildNodes(buildCosTree(list, node.getId()));
                resultList.add(node);
            }
        }
        return resultList;
    }

    /**
     * 权限实体转节点对象
     * @param cos
     * @return
     */
    private CosTreeNode cos2TreeNode(CosVo cos) {
        CosTreeNode result = new CosTreeNode();
        result.setId(cos.getId());
        result.setName(cos.getName());
        result.setDisplayName(cos.getDisplayName());
        return result;
    }
}
