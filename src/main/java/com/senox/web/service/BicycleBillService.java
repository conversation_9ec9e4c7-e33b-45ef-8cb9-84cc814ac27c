package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.*;
import com.senox.web.component.BicycleBillComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 16:10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BicycleBillService {

    private final BicycleBillComponent bicycleBillComponent;


    /**
     * 获取三轮车应收账单详情
     * @param id
     * @return
     */
    public BicycleBillVo findBillById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? bicycleBillComponent.findBillById(id) : null;
    }

    /**
     * 三轮车应收账单合计
     * @param searchVo
     * @return
     */
    public BicycleBillVo sumBill(BicycleBillSearchVo searchVo) {
        return bicycleBillComponent.sumBill(searchVo);
    }

    /**
     * 三轮车应收账单列表页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleBillVo> listBillPage(BicycleBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        return bicycleBillComponent.listBillPage(searchVo);
    }

    /**
     * 根据id列表获取三轮车应收账单详情
     * @param ids
     * @return
     */
    public List<BicycleBillVo> listBillByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return bicycleBillComponent.listBillByIds(ids);
    }

}
