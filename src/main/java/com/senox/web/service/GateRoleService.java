package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageRequest;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.GateRoleDeviceEditVo;
import com.senox.dm.vo.GateRoleSearchVo;
import com.senox.dm.vo.GateRoleUserEditVo;
import com.senox.dm.vo.GateRoleUserVo;
import com.senox.dm.vo.GateRoleVo;
import com.senox.web.component.AccessControlComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/13 15:01
 */
@Service
@RequiredArgsConstructor
public class GateRoleService {

    private final AccessControlComponent accessControlComponent;

    /**
     * 添加门禁角色
     * @param gateRole
     * @return
     */
    public Long addGateRole(GateRoleVo gateRole) {
        return accessControlComponent.addGateRole(gateRole);
    }

    /**
     * 更新门禁角色
     * @param gateRole
     */
    public void updateGateRole(GateRoleVo gateRole) {
        accessControlComponent.updateGateRole(gateRole);
    }

    /**
     * 删除门禁角色
     * @param id
     */
    public void deleteGateRole(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        accessControlComponent.deleteGateRole(id);
    }

    /**
     * 门禁角色列表页
     * @param search
     * @return
     */
    public PageResult<GateRoleVo> listGateRolePage(PageRequest search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return accessControlComponent.listGateRolePage(search);
    }

    /**
     * 添加门禁角色用户
     * @param roleUsers
     */
    public void addGateRoleUsers(GateRoleUserEditVo roleUsers) {
        accessControlComponent.addGateRoleUsers(roleUsers);
    }

    /**
     * 删除门禁角色用户
     * @param roleUsers
     */
    public void deleteGateRoleUsers(GateRoleUserEditVo roleUsers) {
        accessControlComponent.deleteGateRoleUsers(roleUsers);
    }

    /**
     * 门禁角色用户列表页
     * @param search
     * @return
     */
    public PageResult<GateRoleUserVo> listRoleUserPage(GateRoleSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return accessControlComponent.listGateRoleUserPage(search);
    }

    /**
     * 添加门禁角色设备
     * @param roleDevices
     */
    public void addGateRoleDevices(GateRoleDeviceEditVo roleDevices) {
        accessControlComponent.addGateRoleDevices(roleDevices);
    }

    /**
     * 删除门禁角色设备
     * @param roleDevices
     */
    public void deleteGateRoleDevices(GateRoleDeviceEditVo roleDevices) {
        accessControlComponent.deleteGateRoleDevices(roleDevices);
    }

    /**
     * 门禁角色设备列表页
     * @param search
     * @return
     */
    public PageResult<AccessControlVo> listGateRoleDevicePage(GateRoleSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        return accessControlComponent.listRoleDevicePage(search);
    }
}
