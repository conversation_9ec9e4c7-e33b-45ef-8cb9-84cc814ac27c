package com.senox.web.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.flow.vo.WorkflowNodeVo;
import com.senox.flow.vo.WorkflowSearchVo;
import com.senox.flow.vo.WorkflowVo;
import com.senox.web.component.WorkflowComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-09
 **/
@RequiredArgsConstructor
@Service
public class WorkflowService {
    private final WorkflowComponent workflowComponent;

    /**
     * 添加
     *
     * @param workflow 流程
     */
    public void add(WorkflowVo workflow) {
        workflowComponent.add(workflow);
    }

    /**
     * 删除
     *
     * @param flowId 流程id
     */
    public void delete(Long flowId) {
        if (!WrapperClassUtils.biggerThanLong(flowId, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        workflowComponent.delete(flowId);
    }

    /**
     * 更新流程
     *
     * @param workflow 流程
     */
    public void update(WorkflowVo workflow) {
        if (null == workflow || !WrapperClassUtils.biggerThanLong(workflow.getId(), 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        workflowComponent.update(workflow);
    }

    /**
     * 根据编码查找流程
     *
     * @param flowCode 流程编码
     * @return 返回查找到的流程
     */
    public WorkflowVo findByCode(String flowCode) {
        if (StringUtils.isBlank(flowCode)) {
            return null;
        }
        return workflowComponent.findByCode(flowCode);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<WorkflowVo> pageList(WorkflowSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return workflowComponent.pageList(search);
    }
}
