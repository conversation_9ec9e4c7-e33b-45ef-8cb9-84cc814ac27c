package com.senox.web.service;


import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.web.component.PaymentReceiptApplyComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-7-21
 */
@Service
@RequiredArgsConstructor
public class PaymentReceiptApplyService {
    private final PaymentReceiptApplyComponent receiptApplyComponent;

    /**
     * 发票申请列表
     *
     * @param search 查询参数
     * @return 返回分页后的发票申请列表
     */
    public PageResult<ReceiptApplyVo> list(ReceiptApplySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return receiptApplyComponent.list(search);
    }

    /**
     * 根据单据编号查询发票申请
     *
     * @param serialNo 单据编号
     * @return 发票申请信息
     */
    public ReceiptApplyVo findBySerialNo(String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            return null;
        }
        return receiptApplyComponent.findBySerialNo(serialNo);
    }

    /**
     * 查询最近发票申请
     *
     * @param taxHeader 发票抬头
     * @return 发票申请
     */
    public ReceiptApplyVo top(String taxHeader) {
        if (StringUtils.isBlank(taxHeader)) {
            return null;
        }
        return receiptApplyComponent.top(taxHeader);
    }

}
