package com.senox.web.service;

import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.constant.TradeType;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.vo.AdvertisingBillSearchVo;
import com.senox.realty.vo.AdvertisingBillVo;
import com.senox.web.component.AdvertisingComponent;
import com.senox.web.constant.SenoxConst;
import com.senox.web.vo.BillPayRequestVo;
import com.senox.web.vo.PayAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/28 15:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvertisingBillService extends BillService {

    private final AdvertisingComponent advertisingComponent;

    /**
     * 支付广告应收账单
     * @param payRequest
     * @return
     */
    public OrderResultVo payBill(BillPayRequestVo payRequest) {
        // 账单
        List<AdvertisingBillVo> billList = listBillByIds(payRequest.getBillIds());
        // 支付校验
        checkPayingBill(billList);

        billList.forEach(x ->  RedisUtils.lock(buildPayLockKey(x), SenoxConst.Cache.TTL_60S));
        OrderResultVo result = null;
        // 支付
        try {
            OrderVo order = newPayOrder(billList, newPayAmountRequest(payRequest), payRequest.getRequestIp());

            // 下单
            result = orderComponent.addOrder(order);
            if (result == null) {
                throw new BusinessException("下单失败");
            }
            log.info("支付账单成功，返回 {}", JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                notifyBillStatus(payRequest.getBillIds(), payRequest.getTollMan(), result);
            }
            log.info("pay bill {} finish.", JsonUtils.object2Json(payRequest.getBillIds()));
        } finally {
            removeBillPayingLock(billList);
        }
        log.info("finish pay bill {}, result {}", JsonUtils.object2Json(payRequest.getBillIds()), JsonUtils.object2Json(result));
        return result;
    }

    /**
     * 更新票据号
     * @param tollSerial
     */
    public void updateTollSerial(TollSerialVo tollSerial) {
        if (WrapperClassUtils.biggerThanLong(tollSerial.getBillId(), 0L)) {
            advertisingComponent.updateBillTollSerial(tollSerial);
        }
    }

    /**
     * 添加广告账单发票
     * @param receipt
     */
    public void addBillReceipt(BillReceiptBriefVo receipt) {
        if (CollectionUtils.isEmpty(receipt.getBillIds()) || StringUtils.isBlank(receipt.getTaxHeader())) {
            return;
        }
        advertisingComponent.addBillReceipt(receipt);
    }

    /**
     * 取消广告账单发票
     * @param ids
     */
    public void cancelBillReceipt(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        advertisingComponent.cancelBillReceipt(ids);
    }

    /**
     * 获取广告应收账单详情
     * @param id
     * @return
     */
    public AdvertisingBillVo findBillById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? advertisingComponent.findBillById(id) : null;
    }

    /**
     * 根据id列表获取广告应收账单详情
     * @param ids
     * @return
     */
    public List<AdvertisingBillVo> listBillByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return advertisingComponent.listBillByIds(ids);
    }

    /**
     * 广告应收账单合计
     * @param search
     * @return
     */
    public AdvertisingBillVo sumBill(AdvertisingBillSearchVo search) {
        return advertisingComponent.sumBill(search);
    }

    /**
     * 广告应收账单列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingBillVo> listBillPage(AdvertisingBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return advertisingComponent.listBillPage(search);
    }

    /**
     * 账单
     * @param bills
     * @param payAmount
     * @param ip
     * @return
     */
    protected OrderVo newPayOrder(List<AdvertisingBillVo> bills, PayAmountVo payAmount, String ip) {
        // 构建订单基本信息
        OrderVo result = new OrderVo();
        result.setOrderType(OrderType.ADVERTISING);
        result.setPayWay(payAmount.getPayWay());
        result.setCreateIp(ip);

        // 扫码付款
        if (payAmount.getPayWay() == PayWay.DRC) {
            result.setTradeType(TradeType.NATIVE.name());
            result.setAuthCode(payAmount.getAuthCode());
            result.setDeviceSn(payAmount.getDeviceSn());
        }
        result.setItems(bills.stream().map(this::newPayOrderItem).collect(Collectors.toList()));

        if (result.getItems().size() == 1) {
            result.setTitle(result.getItems().get(0).getProductName());
        } else {
            result.setTitle(String.format(SenoxConst.TITLE_ADVERTISING_BILL, LocalDate.now(), StringUtils.EMPTY));
        }
        return result;
    }

    /**
     * 账单明细
     * @param bill
     * @return
     */
    private OrderItemVo newPayOrderItem(AdvertisingBillVo bill) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getId());
        result.setProductName(buildBillTitle(bill));
        result.setQuantity(1);
        result.setPrice(bill.getAmount());
        result.setTotalAmount(result.getPrice());
        result.setFree(Boolean.FALSE);
        result.setDetails(Collections.singletonList(newPayOrderItemDetail(bill)));
        return result;
    }

    /**
     * 子账单明细
     * @param bill
     * @return
     */
    private OrderItemDetailVo newPayOrderItemDetail(AdvertisingBillVo bill) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId((long) RealtyFee.RENT.getFeeId());
        result.setFeeName(RealtyFee.RENT.getName());
        result.setQuantity(1);
        result.setPrice(bill.getAmount());
        result.setTotalAmount(result.getPrice());
        return result;
    }

    /**
     * 通知更新账单结果
     *
     * @param billIds
     * @param tollMan
     * @param order
     */
    @Override
    protected void notifyBillStatus(List<Long> billIds, Long tollMan, OrderResultVo order) {
        BillPaidVo billPaid = new BillPaidVo();
        billPaid.setBillIds(billIds);
        billPaid.setOrderId(order.getOrderId());
        billPaid.setAmount(order.getAmount());
        billPaid.setPaid(order.getStatus() == OrderStatus.PAID.getStatus());
        billPaid.setPaidTime(order.getOrderTime());
        billPaid.setTollMan(tollMan);
        billPaid.setRefund(order.getAmount() == null || BigDecimal.ZERO.compareTo(order.getAmount()) > 0);
        advertisingComponent.updateBillStatus(billPaid);
    }

    /**
     * 待支付校验
     * @param list
     */
    private void checkPayingBill(List<AdvertisingBillVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("账单列表为空");
        }

        if (list.stream().anyMatch(x -> BillStatus.fromValue(x.getStatus()) == BillStatus.PAID)) {
            throw new BusinessException("存在已缴费账单");
        }
    }

    /**
     * 账单支付锁
     * @param bill
     * @return
     */
    private String buildPayLockKey(AdvertisingBillVo bill) {
        return String.format(SenoxConst.Cache.KEY_ADVERTISING_BILL_PAY, bill.getId());
    }

    /**
     * 移除账单支付锁
     *
     * @param bills
     */
    private void removeBillPayingLock(List<AdvertisingBillVo> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> RedisUtils.del(buildPayLockKey(x)));
    }

    /**
     * 广告应收账单标题
     * @param bill
     * @return
     */
    private String buildBillTitle(AdvertisingBillVo bill) {
        return String.format(SenoxConst.TITLE_ADVERTISING_BILL, bill.getCustomerName(), bill.getContractNo());
    }

}
