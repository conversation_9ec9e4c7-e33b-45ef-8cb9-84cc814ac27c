package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.AdvertisingSpaceListVo;
import com.senox.realty.vo.AdvertisingSpaceSearchVo;
import com.senox.realty.vo.AdvertisingSpaceVo;
import com.senox.web.component.AdvertisingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/7/28 11:58
 */
@Service
@RequiredArgsConstructor
public class AdvertisingSpaceService {

    private final AdvertisingComponent advertisingComponent;

    /**
     * 添加广告位
     * @param space
     * @return
     */
    public Long addSpace(AdvertisingSpaceVo space) {
        return advertisingComponent.addSpace(space);
    }

    /**
     * 更新广告位
     * @param space
     */
    public void updateSpace(AdvertisingSpaceVo space) {
        if (!WrapperClassUtils.biggerThanLong(space.getId(), 0L)) {
            return;
        }

        advertisingComponent.updateSpace(space);
    }

    /**
     * 删除广告位
     * @param id
     */
    public void deleteSpace(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        advertisingComponent.deleteSpace(id);
    }

    /**
     * 获取广告位信息
     * @param id
     * @return
     */
    public AdvertisingSpaceVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L)
                ? advertisingComponent.findSpaceById(id) : null;
    }

    /**
     * 统计广告位
     * @param search
     * @return
     */
    public int countSpace(AdvertisingSpaceSearchVo search) {
        return advertisingComponent.countSpace(search);
    }

    /**
     * 广告位列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingSpaceListVo> listPage(AdvertisingSpaceSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return advertisingComponent.listSpacePage(search);
    }

}
