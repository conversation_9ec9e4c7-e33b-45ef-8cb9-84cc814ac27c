package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.SettleFileSearchVo;
import com.senox.pm.vo.SettleFileVo;
import com.senox.web.component.SettleFileComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/11/17 10:35
 */
@Service
public class SettleFileService {

    @Autowired
    private SettleFileComponent settleFileComponent;

    /**
     * 获取结算对账文件路径
     * @param settleDate
     * @param settleType
     * @return
     */
    public String getDrcSettleFilePath(String settleDate, String settleType, PayWay payWay) {
        if (StringUtils.isBlank(settleDate) || StringUtils.isBlank(settleType) || null == payWay) {
            return null;
        }
        return settleFileComponent.getDrcSettleFilePath(settleDate, settleType, payWay);
    }

    /**
     * 结算对账文件列表
     * @param search
     * @return
     */
    public PageResult<SettleFileVo> listDrcSettleFile(SettleFileSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return settleFileComponent.listDrcSettleFile(search);
    }
}
