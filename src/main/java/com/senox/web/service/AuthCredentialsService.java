package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import com.senox.web.component.AuthCredentialsComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-4
 */
@RequiredArgsConstructor
@Service
public class AuthCredentialsService {
    private final AuthCredentialsComponent authCredentialsComponent;

    /**
     * 添加凭证
     *
     * @param userId 用户id
     */
    public void add(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0)) {
            return;
        }
        authCredentialsComponent.add(userId);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<AuthCredentialsVo> list(AuthCredentialsSearchVo searchVo) {
        return authCredentialsComponent.list(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public PageResult<AuthCredentialsVo> listPage(AuthCredentialsSearchVo searchVo) {
        return authCredentialsComponent.listPage(searchVo);
    }
}
