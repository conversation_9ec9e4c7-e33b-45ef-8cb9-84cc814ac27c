package com.senox.web.service;

import com.senox.car.vo.ParkingActivityRecordSearchVo;
import com.senox.car.vo.ParkingActivityRecordVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.ParkingActivityRecordComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Service
public class ParkingActivityRecordService {
    private final ParkingActivityRecordComponent activityRecordComponent;


    /**
     * 添加活动记录
     *
     * @param activityRecordVo 活动记录
     * @return 返回id
     */
    public Long addActivityRecord(ParkingActivityRecordVo activityRecordVo) {
        return activityRecordComponent.addActivityRecord(activityRecordVo);
    }

    /**
     * 更新活动记录
     *
     * @param activityRecordVo 活动记录
     */
    public void updateActivityRecord(ParkingActivityRecordVo activityRecordVo) {
        if (!WrapperClassUtils.biggerThanLong(activityRecordVo.getId(), 0)) {
            throw new InvalidParameterException();
        }
        activityRecordComponent.updateActivityRecord(activityRecordVo);
    }

    /**
     * 根据id获取活动记录
     *
     * @param id 活动记录id
     * @return 返回获取到的活动记录
     */
    public ParkingActivityRecordVo findActivityRecordById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return activityRecordComponent.findActivityRecordById(id);
    }

    /**
     * 根据id删除活动记录
     *
     * @param id 活动记录id
     */
    public void deleteActivityRecordById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        activityRecordComponent.deleteActivityRecordById(id);
    }

    /**
     * 活动记录分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityRecordVo> activityRecordListPage(ParkingActivityRecordSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return activityRecordComponent.activityRecordListPage(searchVo);
    }
}
