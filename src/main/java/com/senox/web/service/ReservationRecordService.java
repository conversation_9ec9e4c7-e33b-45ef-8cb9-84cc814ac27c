package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import com.senox.web.component.ReservationRecordComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/27 18:44
 */
@Service
@RequiredArgsConstructor
public class ReservationRecordService {

    private final ReservationRecordComponent reservationRecordComponent;

    /**
     * 添加预约记录
     * @param recordVo
     * @return
     */
    public Long addReservationRecord(ReservationRecordVo recordVo) {
        return reservationRecordComponent.addReservationRecord(recordVo);
    }

    /**
     * 更新预约记录
     * @param recordVo
     * @return
     */
    public void updateParkingRecord(ReservationRecordVo recordVo) {
        reservationRecordComponent.updateParkingRecord(recordVo);
    }

    /**
     * 根据id获取预约记录
     * @param id
     * @return
     */
    public ReservationRecordVo findById(Long id) {
        return reservationRecordComponent.findById(id);
    }

    /**
     * 预约记录列表
     * @param searchVo
     * @return
     */
    public PageResult<ReservationRecordVo> page(ReservationRecordSearchVo searchVo) {
        return reservationRecordComponent.page(searchVo);
    }

    /**
     * 预约记录同行人数合计
     * @param searchVo
     * @return
     */
    public ReservationRecordVo sumReservationRecord(ReservationRecordSearchVo searchVo) {
        return reservationRecordComponent.sumReservationRecord(searchVo);
    }
}
