package com.senox.web.service;

import com.senox.car.vo.ParkingActivitySearchVo;
import com.senox.car.vo.ParkingActivityVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.ParkingActivityComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Service
public class ParkingActivityService {
    private final ParkingActivityComponent activityComponent;

    /**
     * 添加活动
     *
     * @param activityVo 活动
     */
    public void addActivity(ParkingActivityVo activityVo) {
        activityComponent.addActivity(activityVo);
    }

    /**
     * 更新活动
     *
     * @param activityVo 活动
     */
    public void updateActivity(ParkingActivityVo activityVo) {
        if (!WrapperClassUtils.biggerThanLong(activityVo.getId(), 0)) {
            throw new InvalidParameterException();
        }
        activityComponent.updateActivity(activityVo);
    }

    /**
     * 根据id获取活动
     *
     * @param id 活动id
     * @return 返回获取到的活动
     */
    public ParkingActivityVo findActivityById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return activityComponent.findActivityById(id);
    }

    /**
     * 根据id删除活动
     *
     * @param id 活动id
     */
    public void deleteActivityById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        activityComponent.deleteActivityById(id);
    }

    /**
     * 活动列表分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityVo> activityListPage(ParkingActivitySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return activityComponent.activityListPage(searchVo);
    }
}
