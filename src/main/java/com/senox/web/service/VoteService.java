package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.user.vo.*;
import com.senox.web.component.VoteComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/17 11:12
 */
@Service
@RequiredArgsConstructor
public class VoteService {

    private final VoteComponent voteComponent;

    /**
     * 添加投票分类
     * @param categoryVo
     */
    public void saveVoteCategory(VoteCategoryVo categoryVo) {
        voteComponent.saveVoteCategory(categoryVo);
    }

    /**
     * 根据id获取投票分类
     * @param id
     * @return
     */
    public VoteCategoryVo findCategoryById(Long id) {
        return voteComponent.findCategoryById(id);
    }

    /**
     * 根据id删除投票类别
     * @param id
     */
    public void deleteVoteCategoryById(Long id) {
        voteComponent.deleteVoteCategoryById(id);
    }

    /**
     * 投票类别分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteCategoryVo> pageCategoryResult(VoteCategorySearchVo searchVo) {
        return voteComponent.pageCategoryResult(searchVo);
    }

    /**
     * 新增投票资源
     * @param resourcesVo
     */
    public void saveVoteResources(VoteResourcesVo resourcesVo) {
        voteComponent.saveVoteResources(resourcesVo);
    }

    /**
     * 根据id获取投票资源
     * @param id
     * @return
     */
    public VoteResourcesVo findResourcesById(Long id) {
        return voteComponent.findResourcesById(id);
    }

    /**
     * 根据id删除投票资源
     * @param id
     */
    public void deleteVoteResourcesById(Long id) {
        voteComponent.deleteVoteResourcesById(id);
    }

    /**
     * 投票资源分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteResourcesVo> pageResourcesResult(VoteResourcesSearchVo searchVo) {
        return voteComponent.pageResourcesResult(searchVo);
    }

    /**
     * 新增投票记录
     * @param recordsVo
     */
    public void saveVoteRecords(VoteRecordsVo recordsVo) {
        voteComponent.saveVoteRecords(recordsVo);
    }
}
