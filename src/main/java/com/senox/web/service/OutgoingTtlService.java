package com.senox.web.service;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import com.senox.web.component.OutgoingTtlComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 14:08
 */
@Service
@RequiredArgsConstructor
public class OutgoingTtlService {

    private final OutgoingTtlComponent outgoingTtlComponent;

    /**
     * 批量添加太太乐外发
     * @param outgoingTtlVos
     */
    public void batchAddOutgoingTtl(List<OutgoingTtlVo> outgoingTtlVos) {
        outgoingTtlComponent.batchAddOutgoingTtl(outgoingTtlVos);
    }

    /**
     * 添加太太乐外发
     * @param outgoingTtlVo
     * @return
     */
    public Long addOutgoingTtl(OutgoingTtlVo outgoingTtlVo) {
        return outgoingTtlComponent.addOutgoingTtl(outgoingTtlVo);
    }

    /**
     * 更新太太乐外发
     * @param outgoingTtlVo
     */
    public void updateOutgoingTtl(OutgoingTtlVo outgoingTtlVo) {
        outgoingTtlComponent.updateOutgoingTtl(outgoingTtlVo);
    }

    /**
     * 根据Id获取太太乐外发
     * @param id
     * @return
     */
    public OutgoingTtlVo findById(Long id) {
        return outgoingTtlComponent.findById(id);
    }

    /**
     * 根据Id删除太太乐外发
     * @param id
     */
    public void deleteById(Long id) {
        outgoingTtlComponent.deleteById(id);
    }

    /**
     * 太太乐外发分页
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult(OutgoingTtlSearchVo searchVo) {
        return outgoingTtlComponent.pageResult(searchVo);
    }
}
