package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.BicycleBillSettlementPageResult;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import com.senox.web.component.BicycleChargesScheduledTaskComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@RequiredArgsConstructor
@Component
public class BicycleChargesScheduledTaskService {
    private final BicycleChargesScheduledTaskComponent taskComponent;


    /**
     * 添加
     *
     * @param task 任务
     */
    public void add(BicycleChargesScheduledTaskVo task) {
        taskComponent.add(task);
    }

    /**
     * 更新
     *
     * @param task 任务
     */
    public void update(BicycleChargesScheduledTaskVo task) {
        if (!WrapperClassUtils.biggerThanLong(task.getId(), 0)) {
            return;
        }
        taskComponent.update(task);
    }

    /**
     * 删除
     *
     * @param taskId 任务id
     */
    public void delete(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0)) {
            return;
        }
        taskComponent.delete(taskId);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<BicycleChargesScheduledTaskVo> list(BicycleChargesScheduledTaskSearchVo searchVo) {
        return taskComponent.list(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<BicycleChargesScheduledTaskVo> listPage(BicycleChargesScheduledTaskSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleBillSettlementPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return taskComponent.listPage(searchVo);
    }

    /**
     * 根据id查找计划任务
     *
     * @param id id
     * @return 返回查找到的计划任务
     */
    public BicycleChargesScheduledTaskVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return taskComponent.findById(id);
    }
}
