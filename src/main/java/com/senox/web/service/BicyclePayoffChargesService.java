package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import com.senox.web.component.BicyclePayoffChargesComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-4-2
 */
@RequiredArgsConstructor
@Service
public class BicyclePayoffChargesService {
    private final BicyclePayoffChargesComponent payoffChargesComponent;

    /**
     * 更新
     *
     * @param payoffChargesVo 应付费用
     */
    public void update(BicyclePayoffChargesVo payoffChargesVo) {
        if (null == payoffChargesVo || !WrapperClassUtils.biggerThanLong(payoffChargesVo.getId(), 0)) {
            return;
        }
        payoffChargesComponent.update(payoffChargesVo);
    }

    /**
     * 查找应付费用
     *
     * @return 返回应付费用
     */
    public BicyclePayoffChargesVo find() {
        return payoffChargesComponent.find();
    }
}
