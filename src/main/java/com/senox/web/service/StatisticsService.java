package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import com.senox.realty.vo.RealtyStatisticsVo;
import com.senox.realty.vo.StatisticsSearchVo;
import com.senox.web.component.StatisticsComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/6 10:16
 */
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final StatisticsComponent statisticsComponent;

    /**
     * 物业统计分页
     * @param searchVo
     * @return
     */
    public PageResult<RealtyStatisticsVo> realtyStatisticsPageResult(StatisticsSearchVo searchVo) {
        return statisticsComponent.realtyStatisticsPageResult(searchVo);
    }

    /**
     * 根据统计日期获取物业统计记录
     * @param statisticsDate
     * @return
     */
    public RealtyStatisticsVo findRealtyStatisticsByDate(LocalDate statisticsDate) {
        return statisticsComponent.findRealtyStatisticsByDate(statisticsDate);
    }

    /**
     * 广告位统计分页
     * @param searchVo
     * @return
     */
    public PageResult<AdvertisingStatisticsVo> advertisingStatisticsPageResult(StatisticsSearchVo searchVo) {
        return statisticsComponent.advertisingStatisticsPageResult(searchVo);
    }

    /**
     * 根据统计日期获取广告位统计记录
     * @param statisticsDate
     * @return
     */
    public AdvertisingStatisticsVo findAdvertisingStatisticsByDate(LocalDate statisticsDate) {
        return statisticsComponent.findAdvertisingStatisticsByDate(statisticsDate);
    }
}
