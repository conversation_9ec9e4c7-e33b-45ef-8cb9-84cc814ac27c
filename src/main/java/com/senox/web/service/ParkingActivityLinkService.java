package com.senox.web.service;

import com.senox.car.vo.ParkingActivityItemSearchVo;
import com.senox.car.vo.ParkingActivityItemVo;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.web.component.ParkingActivityLinkComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-1-9
 */
@RequiredArgsConstructor
@Service
public class ParkingActivityLinkService {
    private final ParkingActivityLinkComponent activityLinkComponent;


    /**
     * 添加活动链接
     *
     * @param activityItemVo 链接
     * @return 返回链接
     */
    public String addActivityLink(ParkingActivityItemVo activityItemVo) {
        return activityLinkComponent.addActivityLink(activityItemVo);
    }

    /**
     * 更新活动链接
     *
     * @param activityItemVo 活动链接
     */
    public void updateActivityLink(ParkingActivityItemVo activityItemVo) {
        if (!WrapperClassUtils.biggerThanLong(activityItemVo.getId(), 0)) {
            throw new InvalidParameterException();
        }
        activityLinkComponent.updateActivityLink(activityItemVo);
    }

    /**
     * 根据id获取活动链接
     *
     * @param id 活动链接id
     * @return 返回获取到的活动链接
     */
    public ParkingActivityItemVo findActivityLinkById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return activityLinkComponent.findActivityLinkById(id);
    }

    /**
     * 根据code获取活动链接
     *
     * @param code 活动链接编码
     * @return 返回获取到的活动链接
     */
    public ParkingActivityItemVo findActivityLinkByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new InvalidParameterException();
        }
        return activityLinkComponent.findActivityLinkByCode(code);
    }

    /**
     * 根据id删除活动链接
     *
     * @param id 活动链接id
     */
    public void deleteActivityLinkById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        activityLinkComponent.deleteActivityLinkById(id);
    }

    /**
     * 绑定活动链接
     *
     * @param activityItemVo 活动链接
     * @return 返回活动链接
     */
    public ParkingActivityItemVo bindActivityLink(ParkingActivityItemVo activityItemVo) {
        return activityLinkComponent.bindActivityLink(activityItemVo);
    }

    /**
     * 活动链接分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<ParkingActivityItemVo> activityLinkListPage(ParkingActivityItemSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return activityLinkComponent.activityLinkListPage(searchVo);
    }
}
