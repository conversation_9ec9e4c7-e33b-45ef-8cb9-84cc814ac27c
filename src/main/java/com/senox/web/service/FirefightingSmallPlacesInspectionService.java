package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionBriefVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionVo;
import com.senox.realty.vo.FirefightingStoreInspectionBriefVo;
import com.senox.realty.vo.FirefightingStoreInspectionSearchVo;
import com.senox.web.component.FirefightingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/25 17:07
 */
@Service
@RequiredArgsConstructor
public class FirefightingSmallPlacesInspectionService {

    private final FirefightingComponent firefightingComponent;

    /**
     * 添加三小场所、出租屋消防巡检记录
     * @param inspection
     * @return
     */
    public Long addSmallPlacesInspection(FirefightingSmallPlacesInspectionVo inspection) {
        return firefightingComponent.addSmallPlacesInspection(inspection);
    }

    /**
     * 更新三小场所、出租屋消防巡检记录
     * @param inspection
     */
    public void updateSmallPlacesInspection(FirefightingSmallPlacesInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        firefightingComponent.updateSmallPlacesInspection(inspection);
    }

    /**
     * 删除三小场所、出租屋消防巡检记录
     * @param id
     */
    public void deleteSmallPlacesInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        firefightingComponent.deleteSmallPlacesInspection(id);
    }

    /**
     * 获取三小场所、出租屋消防巡检记录
     * @param id
     * @return
     */
    public FirefightingSmallPlacesInspectionVo findSmallPlacesInspectionById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? firefightingComponent.findSmallPlacesInspectionById(id) : null;
    }

    /**
     * 三小场所、出租屋消防巡检记录数统计
     * @param search
     * @return
     */
    public int countSmallPlacesInspection(FirefightingSmallPlacesInspectionSearchVo search) {
        return firefightingComponent.countSmallPlacesInspection(search);
    }

    /**
     * 三小场所、出租屋消防巡检记录列表页
     * @param search
     * @return
     */
    public PageResult<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspectionPage(FirefightingSmallPlacesInspectionSearchVo search) {
        return firefightingComponent.listSmallPlacesInspectionPage(search);
    }


}
