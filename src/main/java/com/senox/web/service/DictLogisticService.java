package com.senox.web.service;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import com.senox.web.component.DictLogisticComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-20
 */
@RequiredArgsConstructor
@Service
public class DictLogisticService {
    private final DictLogisticComponent dictLogisticComponent;

    /**
     * 添加物流字典
     * @param category 类型
     * @param dictLogisticVo 物流字典
     */
    public void addDictLogistic(DictLogisticCategory category,DictLogisticVo dictLogisticVo) {
        dictLogisticComponent.addDictLogistic(category,dictLogisticVo);
    }

    /**
     * 更新物流字典
     *
     * @param dictLogisticVo 物流字典
     */
    public void updateDictLogistic(DictLogisticVo dictLogisticVo) {
        if (!WrapperClassUtils.biggerThanLong(dictLogisticVo.getId(), 0)) {
            throw new InvalidParameterException();
        }
        dictLogisticComponent.updateDictLogistic(dictLogisticVo);
    }

    /**
     * 删除物流字典
     *
     * @param dictLogisticId 物流字典id
     */
    public void deleteDictLogisticById(Long dictLogisticId) {
        if (!WrapperClassUtils.biggerThanLong(dictLogisticId, 0)) {
            throw new InvalidParameterException();
        }
        dictLogisticComponent.deleteDictLogisticById(dictLogisticId);
    }

    /**
     * 搬运工客户列表
     * @param category 类型
     * @param searchVo 查询
     * @return 返回物流字典列表
     */
    public PageResult<DictLogisticVo> listDictLogistic(DictLogisticCategory category,DictLogisticSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return dictLogisticComponent.listDictLogistic(category,searchVo);
    }

    /**
     * 司机列表
     *
     * @param maxSize 最大条数
     * @return 返回司机字典列表
     */
    public List<DictLogisticVo> listDictLogisticByDriver(Integer maxSize) {
        DictLogisticSearchVo search = new DictLogisticSearchVo();
        search.setPageNo(1);
        search.setPageSize(maxSize);
        PageResult<DictLogisticVo> driverPageResult = dictLogisticComponent.listDictLogistic(DictLogisticCategory.DRIVER, search);
        return driverPageResult.getDataList();
    }
}
