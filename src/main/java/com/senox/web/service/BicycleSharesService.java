package com.senox.web.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import com.senox.web.component.BicycleSharesComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-9-20
 */
@RequiredArgsConstructor
@Service
public class BicycleSharesService {
    private final BicycleSharesComponent sharesComponent;


    /**
     * 添加佣金
     *
     * @param sharesVo 佣金
     */
    public void addShares(BicycleSharesVo sharesVo) {
        sharesComponent.addShares(sharesVo);
    }

    /**
     * 删除佣金
     *
     * @param sharesId 佣金id
     */
    public void deleteShares(Long sharesId) {
        if (!WrapperClassUtils.biggerThanLong(sharesId, 0)) {
            return;
        }
        sharesComponent.deleteShares(sharesId);
    }

    /**
     * 修改佣金
     */
    public void updateShares(BicycleSharesVo sharesVo) {
        if (!WrapperClassUtils.biggerThanLong(sharesVo.getId(), 0)) {
            return;
        }
        sharesComponent.updateShares(sharesVo);
    }

    /**
     * 佣金列表
     *
     * @param searchVo 查询参数
     */
    public PageResult<BicycleSharesVo> listShares(BicycleSharesSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return sharesComponent.listShares(searchVo);
    }
}
