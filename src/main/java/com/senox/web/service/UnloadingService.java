package com.senox.web.service;

import com.senox.common.vo.PageResult;
import com.senox.tms.vo.*;
import com.senox.web.component.UnloadingComponent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 9:11
 */
@RequiredArgsConstructor
@Service
public class UnloadingService {

    private final UnloadingComponent unloadingComponent;

    /**
     * 批量添加字典
     * @param dictVos
     */
    public void batchAddDict(List<UnloadingDictVo> dictVos) {
        unloadingComponent.batchAddDict(dictVos);
    }

    /**
     * 添加字典
     * @param dictVo
     */
    public void addDict(UnloadingDictVo dictVo) {
        unloadingComponent.addDict(dictVo);
    }

    /**
     * 更新字典
     * @param dictVo
     */
    public void updateDict(UnloadingDictVo dictVo) {
        unloadingComponent.updateDict(dictVo);
    }

    /**
     * 根据id查询字典
     * @param id
     * @return
     */
    public UnloadingDictVo findDictById(Long id) {
        return unloadingComponent.findDictById(id);
    }

    /**
     * 根据id删除字典
     * @param id
     */
    public void deleteDictById(Long id) {
        unloadingComponent.deleteDictById(id);
    }

    /**
     * 字典分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingDictVo> dictPageResult(UnloadingDictSearchVo searchVo) {
        return unloadingComponent.dictPageResult(searchVo);
    }

    /**
     * 保存搬运工
     * @param workerVo
     */
    public void saveWorker(UnloadingWorkerVo workerVo) {
        unloadingComponent.saveWorker(workerVo);
    }

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    public void updateWorkerStatus(Long id, Integer status) {
        unloadingComponent.updateWorkerStatus(id, status);
    }

    /**
     * 根据搬运工编号更新搬运工状态
     * @param workerNo
     * @param status
     */
    public void updateWorkerStatusByWorkerNo(String workerNo, Integer status) {
        unloadingComponent.updateWorkerStatusByWorkerNo(workerNo, status);
    }

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    public void updateFaceUrl(UnloadingWorkerFaceUrlVo faceUrlVo) {
        unloadingComponent.updateFaceUrl(faceUrlVo);
    }

    /**
     * 指定位置排序
     * @param id
     * @param num
     */
    public void appointResetOrderNum(Long id, Integer num) {
        unloadingComponent.appointResetOrderNum(id, num);
    }

    /**
     * 搬运工排序置底
     * @param id
     */
    public void bottomUp(Long id) {
        unloadingComponent.bottomUp(id);
    }

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    public UnloadingWorkerVo findWorkById(Long id) {
        return unloadingComponent.findWorkById(id);
    }

    /**
     * 搬运工请假
     * @param workerVo
     */
    public void workerLeave(UnloadingWorkerVo workerVo) {
        unloadingComponent.workerLeave(workerVo);
    }

    /**
     * 根据id删除搬运工
     * @param id
     */
    public void deleteWorker(Long id) {
        unloadingComponent.deleteWorker(id);
    }

    /**
     * 搬运工总数
     * @param searchVo
     * @return
     */
    public int countWorker(UnloadingWorkerSearchVo searchVo) {
        return unloadingComponent.countWorker(searchVo);
    }

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorkerVo> listWorker(UnloadingWorkerSearchVo searchVo) {
        return unloadingComponent.listWorker(searchVo);
    }

    /**
     * 搬运工分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorkerVo> pageWorker(UnloadingWorkerSearchVo searchVo) {
        return unloadingComponent.pageWorker(searchVo);
    }

    /**
     * 搬运工考勤记录分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingAttendanceVo> pageAttendance(UnloadingAttendanceSearchVo searchVo) {
        return unloadingComponent.pageAttendance(searchVo);
    }

    /**
     * 更新考勤记录备注
     * @param id
     * @param remark
     */
    public void updateRemark(Long id, String remark) {
        unloadingComponent.updateRemark(id, remark);
    }

    /**
     * 添加搬运工设备权限
     * @param accessVoList
     */
    public void addWorkerAccess(List<UnloadingWorkerAccessVo> accessVoList) {
        unloadingComponent.addWorkerAccess(accessVoList);
    }

    /**
     * 根据id删除搬运工设备权限
     * @param id
     */
    public void deleteAccessById(Long id) {
        unloadingComponent.deleteAccessById(id);
    }

    /**
     * 根据Id获取搬运工设备权限
     * @param workerId
     * @return
     */
    public List<UnloadingWorkerAccessVo> listAccessByWorkerId(Long workerId) {
        return unloadingComponent.listAccessByWorkerId(workerId);
    }

    /**
     * 搬运工顺序列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorkerVo> listSequenceWorker(UnloadingWorkerSearchVo searchVo) {
        return unloadingComponent.listSequenceWorker(searchVo);
    }

    /**
     * 根据id查询搬运工异常日志
     * @param id
     * @return
     */
    public UnloadingWorkerLogVo findWorkerLogById(Long id) {
        return unloadingComponent.findWorkerLogById(id);
    }

    /**
     * 搬运工异常日志分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorkerLogVo> pageWorkerLog(UnloadingWorkerLogSearchVo searchVo) {
        return unloadingComponent.pageWorkerLog(searchVo);
    }

    /**
     * 批量添加排期计划
     * @param batchVo
     */
    public void batchAddSchedule(UnloadingNightScheduleBatchVo batchVo) {
        unloadingComponent.batchAddSchedule(batchVo);
    }

    /**
     * 添加排期计划
     * @param scheduleVo
     */
    public void addSchedule(UnloadingNightScheduleVo scheduleVo) {
        unloadingComponent.addSchedule(scheduleVo);
    }

    /**
     * 更新排期计划
     * @param scheduleVo
     */
    public void updateSchedule(UnloadingNightScheduleVo scheduleVo) {
        unloadingComponent.updateSchedule(scheduleVo);
    }

    /**
     * 根据id查询排期计划
     * @param id
     * @return
     */
    public UnloadingNightScheduleVo findScheduleById(Long id) {
        return unloadingComponent.findScheduleById(id);
    }

    /**
     * 根据日期获取排期计划
     * @param scheduleDate
     * @return
     */
    public List<UnloadingNightScheduleVo> findByScheduleDate(LocalDate scheduleDate) {
        return unloadingComponent.findByScheduleDate(scheduleDate);
    }

    /**
     * 根据id删除排期计划
     * @param id
     */
    public void deleteScheduleById(Long id) {
        unloadingComponent.deleteScheduleById(id);
    }

    /**
     * 根据日期删除排期计划
     * @param scheduleDate
     */
    public void deleteByScheduleDate(LocalDate scheduleDate) {
        unloadingComponent.deleteByScheduleDate(scheduleDate);
    }

    /**
     * 搬运工排期计划分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingNightScheduleVo> pageSchedule(UnloadingNightScheduleSearchVo searchVo) {
        return unloadingComponent.pageSchedule(searchVo);
    }

    /**
     * 添加搬运订单
     * @param orderVo
     */
    public void saveOrder(UnloadingOrderVo orderVo) {
        unloadingComponent.saveOrder(orderVo);
    }

    /**
     * 删除搬运订单
     * @param id
     */
    public void deleteOrder(Long id) {
        unloadingComponent.deleteOrder(id);
    }

    /**
     * 根据id搬运订单及详细
     * @param id
     * @return
     */
    public UnloadingOrderVo findDetailById(Long id) {
        return unloadingComponent.findDetailById(id);
    }

    /**
     * 搬运订单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageResult(UnloadOrderSearchVo searchVo) {
        return unloadingComponent.pageResult(searchVo);
    }

    /**
     * 搬运订单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo) {
        return unloadingComponent.sumOrder(searchVo);
    }

    /**
     * 搬运订单详细分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageDetailResult(UnloadOrderSearchVo searchVo) {
        return unloadingComponent.pageDetailResult(searchVo);
    }

    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    public void appointWorkerNum(Long orderId, Integer workerNum) {
        unloadingComponent.appointWorkerNum(orderId, workerNum);
    }

    /**
     * 分派搬运工
     * @param orderVo
     */
    public void assignWorkers(UnloadingOrderVo orderVo) {
        unloadingComponent.assignWorkers(orderVo);
    }

    /**
     * 完成订单
     * @param orderId
     * @param amount
     */
    public void finishOrder(Long orderId, BigDecimal amount) {
        unloadingComponent.finishOrder(orderId, amount);
    }

    /**
     * 新增加急费
     * @param orderId
     * @param urgentAmount
     */
    public void addUrgentAmount(Long orderId, BigDecimal urgentAmount) {
        unloadingComponent.addUrgentAmount(orderId, urgentAmount);
    }

    /**
     * 取消订单
     * @param orderId
     */
    public void cancelOrder(Long orderId) {
        unloadingComponent.cancelOrder(orderId);
    }

    /**
     * 补录搬运订单
     * @param orderVo
     * @return 返回的订单号
     */
    public String supplementOrder(UnloadingOrderVo orderVo) {
        return unloadingComponent.supplementOrder(orderVo);
    }

    /**
     * 订单顺序分派
     * @param assignVo
     */
    public void sequenceAssignWorkers(UnloadingOrderWorkersAssignVo assignVo) {
        unloadingComponent.sequenceAssignWorkers(assignVo);
    }

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    public UnloadingOperateAnalysisVo operateAnalysisStatistics(UnloadingStatisticSearchVo searchVo) {
        return unloadingComponent.operateAnalysisStatistics(searchVo);
    }

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    public UnloadingCountRankingVo rankingStatistics(UnloadingStatisticSearchVo searchVo) {
        return unloadingComponent.rankingStatistics(searchVo);
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderCountVo> statistics(UnloadingStatisticSearchVo searchVo) {
        return unloadingComponent.statistics(searchVo);
    }

    /**
     * 添加分佣
     * @param sharesVo
     */
    public void saveShares(UnloadingSharesVo sharesVo) {
        unloadingComponent.saveShares(sharesVo);
    }

    /**
     * 根据id获取分佣
     * @param id
     * @return
     */
    public UnloadingSharesVo findSharesById(Long id) {
        return unloadingComponent.findSharesById(id);
    }

    /**
     * 根据id删除分佣
     * @param id
     */
    public void deleteSharesById(Long id) {
        unloadingComponent.deleteSharesById(id);
    }

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingSharesVo> pageShares(UnloadingSharesSearchVo searchVo) {
        return unloadingComponent.pageShares(searchVo);
    }

    /**
     * 生成应收账单
     * @param monthVo
     */
    public void generateBill(UnloadingMonthVo monthVo) {
        unloadingComponent.generateBill(monthVo);
    }

    /**
     * 批量支付应收账单
     * @param ids
     */
    public void batchPayByIds(List<Long> ids) {
        unloadingComponent.batchPayByIds(ids);
    }

    /**
     * 支付应收账单
     * @param searchVo
     */
    public void batchPay(UnloadingOrderBillSearchVo searchVo) {
        unloadingComponent.batchPay(searchVo);
    }

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    public UnloadingOrderBillVo findBillById(Long id) {
        return unloadingComponent.findBillById(id);
    }

    /**
     * 根据id删除应收账单
     * @param id
     */
    public void deleteBillById(Long id) {
        unloadingComponent.deleteBillById(id);
    }

    /**
     * 应收账单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderBillVo> pageBill(UnloadingOrderBillSearchVo searchVo) {
        return unloadingComponent.pageBill(searchVo);
    }

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderBillVo sumBill(UnloadingOrderBillSearchVo searchVo) {
        return unloadingComponent.sumBill(searchVo);
    }

    /**
     * 更新订单应收金额
     * @param orderNo
     * @param amount
     */
    public void updateOrderBill(String orderNo, BigDecimal amount) {
        unloadingComponent.updateOrderBill(orderNo, amount);
    }

    /**
     * 生成应付账单
     * @param monthVo
     */
    public void generateOrderPayoff(UnloadingMonthVo monthVo) {
        unloadingComponent.generateOrderPayoff(monthVo);
    }

    /**
     * 根据id查询应付记录
     * @param id
     * @return
     */
    public UnloadingOrderPayoffVo findPayoffById(Long id) {
        return unloadingComponent.findPayoffById(id);
    }

    /**
     * 根据订单号删除应付记录
     * @param orderNo
     */
    public void deletePayoffByOrderNo(String orderNo) {
        unloadingComponent.deletePayoffByOrderNo(orderNo);
    }

    /**
     * 更新应付金额
     * @param payoffVoList
     */
    public void updateSharesAmount(List<UnloadingOrderPayoffVo> payoffVoList) {
        unloadingComponent.updateSharesAmount(payoffVoList);
    }

    /**
     * 应付分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderPayoffVo> pageOrderPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return unloadingComponent.pageOrderPayoff(searchVo);
    }

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderPayoffVo sumPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return unloadingComponent.sumPayoff(searchVo);
    }

    /**
     * 根据订单编号查询应付记录
     * @param orderNo
     * @return
     */
    public List<UnloadingOrderPayoffVo> listPayoffByOrderNo(String orderNo) {
        return unloadingComponent.listPayoffByOrderNo(orderNo);
    }

    /**
     * 搬运工应付日信息
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderPayoffVo> payoffDailyList(UnloadingOrderPayoffSearchVo searchVo) {
        return unloadingComponent.payoffDailyList(searchVo);
    }

    /**
     * 根据应付月账单id查询应付记录
     * @param monthPayoffId
     * @return
     */
    public List<UnloadingOrderPayoffVo> findByMonthPayoffId(Long monthPayoffId) {
        return unloadingComponent.findByMonthPayoffId(monthPayoffId);
    }

    /**
     * 生成月应付账单
     * @param payoffVo
     */
    public void generateMonthPayoff(UnloadingMonthPayoffVo payoffVo) {
        unloadingComponent.generateMonthPayoff(payoffVo);
    }

    /**
     * 更新月应付账单备注
     * @param remarkVo
     */
    public void updateRemark(UnloadingMonthPayoffRemarkVo remarkVo) {
        unloadingComponent.updateRemark(remarkVo);
    }

    /**
     * 根据id查询月应付账单
     * @param id
     * @return
     */
    public UnloadingOrderMonthPayoffVo findMonthPayoffById(Long id) {
        return unloadingComponent.findMonthPayoffById(id);
    }

    /**
     * 根据id删除月应付账单
     * @param id
     */
    public void deleteMonthPayoff(Long id) {
        unloadingComponent.deleteMonthPayoff(id);
    }

    /**
     * 月应付账单批量支付
     * @param ids
     */
    public void batchMonthPayoffByIds(List<Long> ids) {
        unloadingComponent.batchMonthPayoffByIds(ids);
    }

    /**
     * 月应付账单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderMonthPayoffVo> pageMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return unloadingComponent.pageMonthPayoff(searchVo);
    }

    /**
     * 月应付账单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderMonthPayoffVo sumMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return unloadingComponent.sumMonthPayoff(searchVo);
    }

    /**
     * 查询搬运工情况
     * @param searchVo
     * @return
     */
    public UnloadingDayCountWorkerVo workerStatistics(UnloadingStatisticSearchVo searchVo) {
        return unloadingComponent.workerStatistics(searchVo);
    }
}
