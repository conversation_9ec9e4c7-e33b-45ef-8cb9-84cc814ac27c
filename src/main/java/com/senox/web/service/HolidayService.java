package com.senox.web.service;

import com.senox.user.vo.HolidaySearchVo;
import com.senox.user.vo.HolidayVo;
import com.senox.web.component.EmployeeComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/19 11:51
 */
@Service
public class HolidayService {

    @Autowired
    private EmployeeComponent employeeComponent;

    /**
     * 保存假期
     * @param holidays
     */
    public void saveHolidays(List<HolidayVo> holidays) {
        if (CollectionUtils.isEmpty(holidays)) {
            return;
        }
        employeeComponent.saveHoliday(holidays);
    }

    /**
     * 删除假期
     * @param dateList
     */
    public void deleteHolidays(List<LocalDate> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            return;
        }
        employeeComponent.deleteHoliday(dateList);
    }

    /**
     * 假期列表
     * @param searchVo
     * @return
     */
    public List<HolidayVo> listHoliday(HolidaySearchVo searchVo) {
        if (searchVo.getStartDate() == null
                || searchVo.getEndDate() == null || searchVo.getStartDate().isAfter(searchVo.getEndDate())) {
            return Collections.emptyList();
        }
        return employeeComponent.listHoliday(searchVo);
    }
}
