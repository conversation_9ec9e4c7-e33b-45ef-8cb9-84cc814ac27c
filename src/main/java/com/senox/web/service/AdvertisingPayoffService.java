package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.realty.vo.AdvertisingPayoffDetailVo;
import com.senox.realty.vo.AdvertisingPayoffSearchVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import com.senox.web.component.AdvertisingComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/4 17:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvertisingPayoffService {

    private final AdvertisingComponent advertisingComponent;

    /**
     * 生成广告应付账单
     * @param contractNo
     */
    public void generatePayoff(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return;
        }

        advertisingComponent.generatePayoff(contractNo);
    }

    /**
     * 更新广告应付账单
     * @param payoff
     */
    public void updatePayoff(AdvertisingPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            return;
        }

        advertisingComponent.updatePayoff(payoff);
    }

    /**
     * 删除广告应付账单
     * @param id
     */
    public void deletePayoff(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        advertisingComponent.deletePayoff(id);
    }

    /**
     * 更新应付账单状态
     * @param billPaid
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds()) || payway == null) {
            return;
        }

        advertisingComponent.updatePayoffStatus(billPaid, payway);
    }

    /**
     * 广告合同应付账单
     * @param contractNo
     * @return
     */
    public List<AdvertisingPayoffVo> listPayoffByContract(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return Collections.emptyList();
        }

        return advertisingComponent.listPayoffByContract(contractNo);
    }

    /**
     * 广告应付账单合计
     * @param search
     * @return
     */
    public AdvertisingPayoffDetailVo sumPayoff(AdvertisingPayoffSearchVo search) {
        return advertisingComponent.sumPayoff(search);
    }

    /**
     * 广告应付账单页
     * @param search
     * @return
     */
    public PageResult<AdvertisingPayoffDetailVo> listPayoffPage(AdvertisingPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return advertisingComponent.listPayoffPage(search);
    }
}
