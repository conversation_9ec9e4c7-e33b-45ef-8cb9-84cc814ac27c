package com.senox.web.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.vo.AreaVo;
import com.senox.web.component.DictionaryComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/14 17:03
 */
@Service
public class AreaService {

    @Autowired
    private DictionaryComponent dictionaryComponent;

    /**
     * 添加 省/市
     * @param area
     * @return
     */
    public Long addArea(AreaVo area) {
        if (StringUtils.isBlank(area.getSerialNo()) || StringUtils.isBlank(area.getName())) {
            return 0L;
        }
        if (area.getParentId() == null) {
            area.setParentId(0L);
        }

        return dictionaryComponent.addArea(area);
    }

    /**
     * 更新省/市
     * @param area
     */
    public void updateArea(AreaVo area) {
        if (area.getId() == null) {
            return;
        }
        dictionaryComponent.updateArea(area);
    }

    /**
     * 删除地区
     * @param id
     */
    public void delete(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        AreaVo area = new AreaVo();
        area.setId(id);
        area.setDisabled(Boolean.TRUE);
        updateArea(area);
    }

    /**
     * 根据id查找地区
     * @param id
     * @return
     */
    public AreaVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return dictionaryComponent.findAreaById(id);
    }

    /**
     * 省列表
     * @return
     */
    public List<AreaVo> listProvince() {
        return dictionaryComponent.listProvince();
    }

    /**
     * 市列表
     * @param parentId
     * @return
     */
    public List<AreaVo> listCity(Long parentId) {
        if (!WrapperClassUtils.biggerThanLong(parentId, 0)) {
            return Collections.emptyList();
        }
        return dictionaryComponent.listCity(parentId);
    }


}
