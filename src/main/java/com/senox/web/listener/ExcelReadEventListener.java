package com.senox.web.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:20
 */
@Slf4j
@Getter
public class ExcelReadEventListener<T> extends AnalysisEventListener<T> {

    //忽略的行数
    private final int headRowNum;
    public ExcelReadEventListener(int headRowNum) {
        this.headRowNum = headRowNum;
    }

    //读取的数据
    private final List<T> list = new ArrayList<>();
    //合并单元格的数据
    private final List<CellExtra> cellExtraList = new ArrayList<>();

    @Override
    public void invoke(T excelData, AnalysisContext analysisContext) {
        list.add(excelData);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        CellExtraTypeEnum type = extra.getType();
        //如果是合并单元格的数据，并且大于忽略的行数
        if (Objects.requireNonNull(type) == CellExtraTypeEnum.MERGE && (extra.getRowIndex() >= headRowNum)) {
            cellExtraList.add(extra);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }

    public void mergeExcelData(List<T> excelDataList, List<CellExtra> cellExtraList) {
        cellExtraList.forEach(cellExtra -> {
            //忽略前三行表头
            int firstRowIndex = cellExtra.getFirstRowIndex() - headRowNum;
            int lastRowIndex = cellExtra.getLastRowIndex() - headRowNum;
            int firstColumnIndex = cellExtra.getFirstColumnIndex();
            int lastColumnIndex = cellExtra.getLastColumnIndex();
            //获取初始值
            Object initValue = getInitValueFromList(firstRowIndex, firstColumnIndex, excelDataList);
            //设置值
            for (int i = firstRowIndex; i <= lastRowIndex; i++) {
                for (int j = firstColumnIndex; j <= lastColumnIndex; j++) {
                    setInitValueToList(initValue, i, j, excelDataList);
                }
            }
        });
    }

    public void setInitValueToList(Object filedValue, Integer rowIndex, Integer columnIndex, List<T> data) {
        T object = data.get(rowIndex);
        for (Field field : object.getClass().getDeclaredFields()) {
            ReflectionUtils.makeAccessible(field);
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null && (annotation.index() == columnIndex)) {
                ReflectionUtils.setField(field, object, filedValue);
                break;
            }
        }
    }

    public Object getInitValueFromList(Integer firstRowIndex, Integer firstColumnIndex, List<T> data) {
        Object filedValue = null;
        T object = data.get(firstRowIndex);
        for (Field field : object.getClass().getDeclaredFields()) {
            ReflectionUtils.makeAccessible(field);
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null && (annotation.index() == firstColumnIndex)) {
                filedValue = ReflectionUtils.getField(field, object);
                break;
            }
        }
        return filedValue;
    }
}