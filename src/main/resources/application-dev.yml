spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************
    username: root
    password: vvwo9YWDPDXB7m%G
  redis:
    host: **************
    port: 6379
    password: WT0UdHepNsSnYb81
    timeout: 10000
  rabbitmq:
    addresses: *************:5672
    username: smart
    password: smart
    virtual-host: /smart
    connection-timeout: 15000
    consumer-threads: 5
  zipkin:
    enabled: false

