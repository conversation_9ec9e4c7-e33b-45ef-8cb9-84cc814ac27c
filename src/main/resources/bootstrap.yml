nacos:
  server-addr: *************:8848
  namespace: senox-dev

spring:
  application:
    name: senox-web
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 50MB
  cloud:
    nacos:
      config:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.server-addr}
        file-extension: yaml
      discovery:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.server-addr}
  redis:
    letture:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 500
        max-wait: 1000

management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /guard

info:
  app:
    name: ${spring.application.name}
    version: 1.0.0

