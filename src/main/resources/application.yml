server:
  port: 8084

spring:
  profiles:
    active: dev
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      # 连接允许在池中闲置的最长时间
      idle-timeout: 600000
      # maximum number of milliseconds that a client will wait for a connection
      connection-timeout: 30000
      # maximum lifetime in milliseconds of a connection in the pool after it is closed
      max-lifetime: 1800000
  redis:
    letture:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 500
        max-wait: 1000


senox:
  user:
    # 允许用户同时登录
    multiLogin: false
  adminFilter:
    open: false
    excludeUrls: /web/login,/web/logout,/web/device/report/covid19Access,/web/device/signalTone/event/receive
    mustUrls: /web/adminUser/info

ribbon:
  ReadTimeout: 20000
  ConnectTimeout: 20000
