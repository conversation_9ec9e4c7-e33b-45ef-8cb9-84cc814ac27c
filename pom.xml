<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.senox.web</groupId>
    <artifactId>senox-web</artifactId>
    <version>1.1.0-SNAPSHOT</version>

    <parent>
        <artifactId>senox-base</artifactId>
        <groupId>com.senox</groupId>
        <version>1.2.6-SNAPSHOT</version>
    </parent>

    <properties>
        <start-class>com.senox.web.WebApplication</start-class>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.senox</groupId>
            <artifactId>common</artifactId>
            <version>1.2.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.user</groupId>
            <artifactId>senox-user-api</artifactId>
            <version>1.2.9-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.realty</groupId>
            <artifactId>senox-realty-api</artifactId>
            <version>1.3.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.car</groupId>
            <artifactId>senox-car-api</artifactId>
            <version>1.1.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.pm</groupId>
            <artifactId>senox-payment-api</artifactId>
            <version>1.2.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.dm</groupId>
            <artifactId>senox-device-api</artifactId>
            <version>1.2.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.wechat</groupId>
            <artifactId>senox-wechat-mp-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.wms</groupId>
            <artifactId>senox-wms-api</artifactId>
            <version>1.2.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.cold</groupId>
            <artifactId>senox-cold-api</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.tms</groupId>
            <artifactId>senox-tms-api</artifactId>
            <version>1.1.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.senox.flow</groupId>
            <artifactId>senox-flow-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
